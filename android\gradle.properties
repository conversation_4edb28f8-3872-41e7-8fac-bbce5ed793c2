# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m

# 指定 Gradle 使用的 Java 版本
# Android Gradle Plugin 8.x 需要 Java 17+
# 本地开发环境使用 macOS 路径，CI/CD 会动态覆盖此设置
# org.gradle.java.home=D:/Program Files/Java/jdk-17
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# my-release-key.properties
MYAPP_RELEASE_STORE_FILE=D:project\\workspace\\AileCode\\keystore\\new-aile-key.jks
MYAPP_RELEASE_KEY_ALIAS=new-aile-key
MYAPP_RELEASE_STORE_PASSWORD=77066188
MYAPP_RELEASE_KEY_PASSWORD=77066188
