import React, { useState, useEffect, useRef } from 'react';
import { IndexBar, List, Tag, ErrorBlock } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import './ContactTab.css';
import { useAppSelector } from '../../../app/hooks';
import { logService } from '../../../services/system/logService';
import { AvatarImage } from '@/components/common';
import stateService from '../../../services/stateService';
import { contactService } from '../../../services/core/tenant';
import type { Contact } from '../../../services/db/initSql';
import { pinyin } from 'pinyin';

// 定義聯繫人展示接口，用於UI顯示
interface ContactDisplay {
  id: string;
  name: string;
  firstLetter: string;
  color: number;
  isNewJoin?: boolean;
  avatar?: string;
  avatarId: string | null;
}

interface ContactTabProps {
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
  active?: boolean; // 添加新屬性，表示該標籤是否激活
}

// 获取姓名的英文首字母（中文转拼音）
const getNameFirstLetter = (name: string): string => {
  if (!name || !name.trim()) {
    return '#'; // 空名称归类到 # 分组
  }

  const firstChar = name.trim().charAt(0);

  // 如果是英文字母，直接返回大写
  if (/^[A-Za-z]$/.test(firstChar)) {
    return firstChar.toUpperCase();
  }

  // 如果是中文字符，转换为拼音首字母
  if (/^[\u4e00-\u9fa5]$/.test(firstChar)) {
    try {
      const pinyinResult = pinyin(firstChar, {
        style: 'first_letter', // 只获取首字母
        heteronym: false // 不返回多音字的所有读音
      });

      if (pinyinResult && pinyinResult[0] && pinyinResult[0][0]) {
        return pinyinResult[0][0].toUpperCase();
      }
    } catch (error) {
      logService.warn('拼音转换失败', { char: firstChar, error });
    }
  }

  // 其他字符（数字、符号等）归类到 # 分组
  return '#';
};

const ContactTab: React.FC<ContactTabProps> = ({ onScroll, active = false }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 從 Redux 獲取當前租戶ID
  const currentTenantId = useAppSelector((state) => state.tenant.currentTenantId);

  // 用于跟踪租户切换的ref
  const prevTenantIdRef = useRef<string | null>(null);

  // 分页配置
  const PAGE_SIZE = 50;
  
  // 从数据库分页加载联系人数据
  const loadContactsFromDB = async (clearExisting: boolean = false) => {
    if (!currentTenantId) {
      logService.warn('無法加載聯繫人：當前租戶ID為空');
      setLoading(false);
      return;
    }

    try {
      if (clearExisting) {
        // 租户切换时清空现有数据
        setContacts([]);
        setLoading(true);
        setError(null);
        logService.info('租戶切換，清空現有聯繫人數據', { tenantId: currentTenantId });
      } else {
        setLoading(true);
        setError(null);
      }

      // 分页加载联系人数据，每页加载完立即显示
      let allContacts: Contact[] = [];
      let pageIndex = 0;
      let hasMore = true;

      while (hasMore) {
        const contactList = await contactService.getContactList({
          tenantId: currentTenantId,
          pageIndex,
          pageSize: PAGE_SIZE
        });

        if (contactList.length === 0) {
          hasMore = false;
        } else {
          // 直接追加新数据（数据库查询已经按ID去重）
          allContacts = [...allContacts, ...contactList];

          // 每页加载完立即更新页面显示
          setContacts([...allContacts]);

          pageIndex++;

          // 如果返回的数据少于页面大小，说明没有更多数据了
          if (contactList.length < PAGE_SIZE) {
            hasMore = false;
          }
        }
      }
      setLoading(false);

      logService.info('從數據庫分頁加載聯繫人完成', {
        tenantId: currentTenantId,
        totalCount: allContacts.length,
        pagesLoaded: pageIndex,
        clearExisting
      });
    } catch (error) {
      logService.error('從數據庫加載聯繫人失敗', { error: error as Error, tenantId: currentTenantId });
      setError('加載聯繫人失敗');
      setLoading(false);
    }
  };

  // 在標籤激活時加载数据
  useEffect(() => {
    if (active && currentTenantId) {
      logService.debug('聯繫人標籤頁激活，重新加載聯繫人數據',{ tenantId: currentTenantId });
      // 标签激活时不清空数据，直接更新
      loadContactsFromDB(false);
    }
  }, [active, currentTenantId]);

  // 监听租户切换
  useEffect(() => {
    if (currentTenantId && currentTenantId !== prevTenantIdRef.current) {
      if (prevTenantIdRef.current !== null) {
        logService.info('租戶切換，重新加載聯繫人數據', {
          from: prevTenantIdRef.current,
          to: currentTenantId
        });
        // 租户切换时清空现有数据重新加载
        if (active) {
          loadContactsFromDB(true);
        }
      }
      prevTenantIdRef.current = currentTenantId;
    }
  }, [currentTenantId, active]);

  // 監聽聯繫人列表變更事件（同步完成后的通知）
  useEffect(() => {
    const unsubscribe = stateService.on('contactListChanged', () => {
      if (currentTenantId && active) {
        logService.debug('監測到聯繫人列表變更，重新加載聯繫人數據');
        // 联系人列表变更时不清空数据，直接更新
        loadContactsFromDB(false);
      }
    });

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [currentTenantId, active]);

  // 將聯繫人數據轉換為顯示格式
  const contactDisplays: ContactDisplay[] = contacts.map(contact => {
    // 若 name 為空，顯示『未定義』
    const name = contact.name && contact.name.trim() ? contact.name : '未定義';
    // 使用新的函数获取英文首字母（中文转拼音）
    const firstLetter = getNameFirstLetter(name);
    // 檢查是否為新加入（最近 1 天內創建的聯絡人）
    const isNewJoin = contact.createTime
      ? (Date.now() - contact.createTime <  24 * 60 * 60 * 1000)
      : false;
    const color = parseInt(contact.id) % 14 + 1;
    return {
      id: contact.id,
      name,
      firstLetter,
      color,
      isNewJoin,
      avatarId: contact.avatarId || null,
    };
  });

  // Separate new join contacts
  const newJoinContacts = contactDisplays.filter(contact => contact.isNewJoin);
  const regularContacts = contactDisplays.filter(contact => !contact.isNewJoin);

  // Group regular contacts by first letter
  const groupedContacts = regularContacts.reduce<Record<string, ContactDisplay[]>>((acc, contact) => {
    if (!acc[contact.firstLetter]) {
      acc[contact.firstLetter] = [];
    }
    acc[contact.firstLetter].push(contact);
    return acc;
  }, {});

  // Handle contact avatar click
  const handleContactClick = (contactId: string) => {
    navigate(`/client/${contactId}`);
  };

  // Render an individual contact list item
  const renderContactItem = (contact: ContactDisplay) => (
    <List.Item
      key={contact.id}
      className="contact-list-item"
    >
      <div className="contact-list-item-content">
        <div
          className="contact-avatar"
          onClick={() => handleContactClick(contact.id)}
        >
          <AvatarImage
            avatarId={contact.avatarId}
            name={contact.name}
            size={32}
            alt={contact.name}
            className="contact-avatar-img"
          />
        </div>
        <span className="contact-name">{contact.name}</span>
        {contact.isNewJoin && (
          <Tag color="primary" fill="outline" className="new-join-tag">
            最新加入
          </Tag>
        )}
      </div>
    </List.Item>
  );

  // Get all unique letters for the index - 只使用常規聯繫人，不包含新加入聯繫人
  // 自定义排序：字母按字母顺序排列，# 符号排在最后
  const allLetters = [...new Set(regularContacts.map(contact => contact.firstLetter))].sort((a, b) => {
    if (a === '#' && b !== '#') return 1;  // # 排在后面
    if (a !== '#' && b === '#') return -1; // # 排在后面
    return a.localeCompare(b); // 其他字符按字母顺序排列
  });

  // 檢查是否有聯繫人數據
  const hasContacts = contacts.length > 0;
  const hasRegularContacts = regularContacts.length > 0;

  // 計算是否需要使用最小高度模式
  // 當總聯繫人數量較少時（少於10個），使用動態高度
  const shouldUseMinimalHeight = contacts.length < 10;

  // 計算容器的最佳高度 - 完全移除高度限制
  const containerStyle = shouldUseMinimalHeight ? {
    height: 'auto',
    minHeight: 'auto',
    maxHeight: 'none',
    overflow: 'visible' as const,
    position: 'relative' as const
  } : {};

  // 參考 MessageTab 結構，使用 flex 容器包裝整個組件
  return (
    <div className="contact-tab-container">
      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加載中...</p>
        </div>
      ) : error ? (
        // 錯誤狀態顯示
        <div className="contact-tab-error-state">
          <ErrorBlock
            status='default'
            title="加載失敗"
            description={error}
          />
        </div>
      ) : !hasContacts ? (
        // 空狀態顯示
        <div className="contact-tab-empty-state">
          <ErrorBlock
            status='empty'
            title="尚無任何聯繫人"
            description=""
          />
        </div>
      ) : (
        <div
          className={`contact-tab-scrollable-container ${shouldUseMinimalHeight ? 'minimal-height' : ''}`}
          style={containerStyle}
          onScroll={onScroll}
        >
          {/* New Join Contacts Section at the top */}
          {newJoinContacts.length > 0 && (
            <div className="new-join-section">
              <List>
                {newJoinContacts.map(renderContactItem)}
              </List>
            </div>
          )}

          {/* 可滾動的聯繫人列表區域 */}
          {hasRegularContacts ? (
            shouldUseMinimalHeight ? (
              // 聯繫人數量少時，使用簡單列表而非IndexBar
              <div className="simple-contact-list">
                {allLetters.map((letter) => {
                  const letterContacts = groupedContacts[letter] || [];
                  return (
                    <div key={letter} className="simple-letter-group">
                      <div className="simple-letter-title">{letter}</div>
                      <List>
                        {letterContacts.map(renderContactItem)}
                      </List>
                    </div>
                  );
                })}
              </div>
            ) : (
              // 聯繫人數量多時，使用IndexBar
              <div className="contact-list-container">
                <IndexBar
                  sticky
                  className="contact-index-bar"
                >
                  {allLetters.map((letter) => {
                    const letterContacts = groupedContacts[letter] || [];
                    return (
                      <IndexBar.Panel
                        index={letter}
                        title={letter}
                        key={letter}
                      >
                        <List>
                          {letterContacts.map(renderContactItem)}
                        </List>
                      </IndexBar.Panel>
                    );
                  })}
                </IndexBar>
              </div>
            )
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ContactTab; 