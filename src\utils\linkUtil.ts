/**
 * URL 連結處理工具函數
 */

export interface LinkPart {
  text: string;
  isUrl: boolean;
  href?: string;
}

/**
 * 解析文本中的 URL 連結
 * @param text 要解析的文本
 * @returns 解析後的文本片段數組
 */
export function parseTextWithLinks(text: string): LinkPart[] {
  if (!text) return [{ text: '', isUrl: false }];

  // URL 正則表達式，支援 http、https、ftp 協議和 www 開頭的網址
  // 排除常見的標點符號（逗號、分號、問號、驚嘆號），但保留點號和冒號（URL 需要）
  const urlRegex = /(https?:\/\/[^\s,;!?]+|ftp:\/\/[^\s,;!?]+|www\.[^\s,;!?]+)/g;
  
  // 將文本分割為普通文本和 URL 部分
  const parts = text.split(urlRegex);
  const result: LinkPart[] = [];
  
  parts.forEach((part) => {
    if (!part) return; // 跳過空字符串
    
    // 檢查是否為 URL
    const isUrl = /(https?:\/\/[^\s,;!?]+|ftp:\/\/[^\s,;!?]+|www\.[^\s,;!?]+)/.test(part);
    
    if (isUrl) {
      // 確保 URL 有協議前綴
      let href = part;
      if (part.startsWith('www.')) {
        href = `https://${part}`;
      }
      
      result.push({
        text: part,
        isUrl: true,
        href
      });
    } else {
      // 普通文本
      result.push({
        text: part,
        isUrl: false
      });
    }
  });
  
  return result;
}

/**
 * 檢查字符串是否包含 URL
 * @param text 要檢查的文本
 * @returns 是否包含 URL
 */
export function hasLinks(text: string): boolean {
  const urlRegex = /(https?:\/\/[^\s,;!?]+|ftp:\/\/[^\s,;!?]+|www\.[^\s,;!?]+)/;
  return urlRegex.test(text);
}

/**
 * 提取文本中的所有 URL
 * @param text 要提取的文本
 * @returns URL 數組
 */
export function extractUrls(text: string): string[] {
  const urlRegex = /(https?:\/\/[^\s,;!?]+|ftp:\/\/[^\s,;!?]+|www\.[^\s,;!?]+)/g;
  return text.match(urlRegex) || [];
}
