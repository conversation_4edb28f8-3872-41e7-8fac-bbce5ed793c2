/* 侧边栏占位内容样式 */
.sidebar-placeholder {
  padding: 16px;
  background: #fff;
  height: 100%;
}

/* 用户信息 */
.sidebar-placeholder-user {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 12px;
}

.sidebar-placeholder-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.sidebar-placeholder-username {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 团队标题 */
.sidebar-placeholder-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 团队列表区域 */
.sidebar-placeholder-teams {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-placeholder-team-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sidebar-placeholder-team-item:hover {
  background: #e9ecef;
}

.sidebar-placeholder-team-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.sidebar-placeholder-team-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.sidebar-placeholder-add-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.sidebar-placeholder-team-main {
  flex: 1;
}

.sidebar-placeholder-team-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 加载提示 */
.sidebar-placeholder-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  margin-top: 8px;
}

.sidebar-placeholder-loading-text {
  font-size: 14px;
  color: #999;
  position: relative;
}

/* 加载动画 */
.sidebar-placeholder-loading-text::after {
  content: '';
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border: 2px solid #ddd;
  border-top: 2px solid #1677FF;
  border-radius: 50%;
  animation: sidebar-placeholder-spin 1s linear infinite;
}

@keyframes sidebar-placeholder-spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-placeholder {
    padding: 12px;
  }
  
  .sidebar-placeholder-team-item {
    padding: 10px;
  }
}
