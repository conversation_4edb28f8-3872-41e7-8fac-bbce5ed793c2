/**
 * 主题配置
 */

// 颜色主题
export const THEME_COLORS = {
  // 主色调
  PRIMARY: {
    50: '#e3f2fd',
    100: '#bbdefb',
    500: '#2196f3',
    600: '#1976d2',
    700: '#1565c0',
    900: '#0d47a1',
  },
  
  // 辅助色
  SECONDARY: {
    50: '#f3e5f5',
    100: '#e1bee7',
    500: '#9c27b0',
    600: '#8e24aa',
    700: '#7b1fa2',
    900: '#4a148c',
  },
  
  // 状态色
  SUCCESS: '#4caf50',
  WARNING: '#ff9800',
  ERROR: '#f44336',
  INFO: '#2196f3',
  
  // 中性色
  GRAY: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // 背景色
  BACKGROUND: {
    DEFAULT: '#ffffff',
    PAPER: '#ffffff',
    LEVEL1: '#f5f5f5',
    LEVEL2: '#eeeeee',
  },
  
  // 文字色
  TEXT: {
    PRIMARY: '#212121',
    SECONDARY: '#757575',
    DISABLED: '#bdbdbd',
    HINT: '#9e9e9e',
  },
} as const;

// 字体配置
export const THEME_FONTS = {
  FAMILY: {
    PRIMARY: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    MONO: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  },
  
  SIZE: {
    XS: '0.75rem',    // 12px
    SM: '0.875rem',   // 14px
    BASE: '1rem',     // 16px
    LG: '1.125rem',   // 18px
    XL: '1.25rem',    // 20px
    '2XL': '1.5rem',  // 24px
    '3XL': '1.875rem', // 30px
    '4XL': '2.25rem', // 36px
  },
  
  WEIGHT: {
    LIGHT: 300,
    NORMAL: 400,
    MEDIUM: 500,
    SEMIBOLD: 600,
    BOLD: 700,
  },
} as const;

// 间距配置
export const THEME_SPACING = {
  XS: '0.25rem',   // 4px
  SM: '0.5rem',    // 8px
  BASE: '1rem',    // 16px
  LG: '1.5rem',    // 24px
  XL: '2rem',      // 32px
  '2XL': '3rem',   // 48px
  '3XL': '4rem',   // 64px
} as const;

// 圆角配置
export const THEME_RADIUS = {
  NONE: '0',
  SM: '0.25rem',   // 4px
  BASE: '0.5rem',  // 8px
  LG: '0.75rem',   // 12px
  XL: '1rem',      // 16px
  FULL: '9999px',
} as const;

// 阴影配置
export const THEME_SHADOWS = {
  SM: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  BASE: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  LG: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  XL: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
} as const;
