import React, { useState, useEffect } from 'react';
import { Button, Card, Space, List, Tag, Toast, Input } from 'antd-mobile';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import { 
  addMessage, 
  updateMessageStatus, 
  updateMessagesStatusBySequence,
  selectRoomMessages 
} from '@/app/slices/messageSlice';
import { authService } from '@/services/core/auth';
import stateService, { MemberChangedAction } from '@/services/stateService';
import MessageStatusIndicator from '@/components/chat/MessageStatusIndicator';
import type { IChatMessage } from '@/types/chat.types';

/**
 * 消息状态测试组件
 * 用于测试已读已到状态管理功能
 */
export const MessageStatusTest: React.FC = () => {
  const dispatch = useAppDispatch();
  const [roomId] = useState('test-room-status');
  const [messageContent, setMessageContent] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [currentUserId] = useState(authService.getUserId() || 'test-user-1');
  
  // 获取当前房间的消息
  const messages = useAppSelector(selectRoomMessages(roomId));
  const allMessages = messages.flatMap(group => group.messages);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // 模拟发送消息
  const handleSendMessage = () => {
    if (!messageContent.trim()) {
      Toast.show({ content: '请输入消息内容', position: 'bottom' });
      return;
    }

    const messageId = `msg-${Date.now()}`;
    const sequence = Date.now(); // 使用时间戳作为序列号
    
    const newMessage: IChatMessage = {
      id: messageId,
      content: messageContent,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      timestamp: new Date().toISOString(),
      isUser: true,
      sender: {
        id: currentUserId,
        name: '测试用户',
        avatar: ''
      },
      status: 'sending',
      type: 'Text' as any,
      sequence
    };

    dispatch(addMessage({ roomId, message: newMessage }));
    addLog(`发送消息: ${messageContent} (序列号: ${sequence})`);
    
    // 模拟发送成功
    setTimeout(() => {
      dispatch(updateMessageStatus({
        roomId,
        messageId,
        tempId: messageId,
        status: 'sent',
        sequence
      }));
      addLog(`消息发送成功: ${messageId}`);
    }, 1000);

    setMessageContent('');
  };

  // 模拟接收到已到达状态
  const handleSimulateDelivered = () => {
    const userMessages = allMessages.filter(msg => msg.isUser && msg.sequence);
    if (userMessages.length === 0) {
      Toast.show({ content: '没有可更新的消息', position: 'bottom' });
      return;
    }

    const maxSequence = Math.max(...userMessages.map(msg => msg.sequence!));
    
    // 模拟收到memberChanged事件
    stateService.notifyMemberChanged({
      action: MemberChangedAction.Received,
      roomId,
      memberId: 'other-user',
      sequence: maxSequence
    });

    addLog(`模拟已到达状态，最大序列号: ${maxSequence}`);
  };

  // 模拟接收到已读状态
  const handleSimulateRead = () => {
    const userMessages = allMessages.filter(msg => msg.isUser && msg.sequence);
    if (userMessages.length === 0) {
      Toast.show({ content: '没有可更新的消息', position: 'bottom' });
      return;
    }

    const maxSequence = Math.max(...userMessages.map(msg => msg.sequence!));
    
    // 模拟收到memberChanged事件
    stateService.notifyMemberChanged({
      action: MemberChangedAction.Readed,
      roomId,
      memberId: 'other-user',
      sequence: maxSequence
    });

    addLog(`模拟已读状态，最大序列号: ${maxSequence}`);
  };

  // 批量更新消息状态
  const handleBatchUpdate = (status: 'delivered' | 'read', maxSequence: number) => {
    dispatch(updateMessagesStatusBySequence({
      roomId,
      maxSequence,
      status,
      ownerId:currentUserId
    }));
    addLog(`批量更新消息状态为 ${status}，序列号 <= ${maxSequence}`);
  };

  // 监听memberChanged事件
  useEffect(() => {
    const unsubscribe = stateService.on('memberChanged', (event: any) => {
      const { action, roomId: eventRoomId, sequence } = event || {};
      
      if (eventRoomId !== roomId || !sequence) {
        return;
      }
      
      addLog(`收到memberChanged事件: ${action}, 序列号: ${sequence}`);
      
      if (action === MemberChangedAction.Received) {
        handleBatchUpdate('delivered', sequence);
      } else if (action === MemberChangedAction.Readed) {
        handleBatchUpdate('read', sequence);
      }
    });
    
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [roomId]);

  // 清空日志
  const handleClearLogs = () => {
    setLogs([]);
  };

  // 获取状态统计
  const getStatusStats = () => {
    const stats = {
      sending: 0,
      sent: 0,
      delivered: 0,
      read: 0,
      failed: 0
    };
    
    allMessages.forEach(msg => {
      if (msg.status && stats.hasOwnProperty(msg.status)) {
        stats[msg.status as keyof typeof stats]++;
      }
    });
    
    return stats;
  };

  const statusStats = getStatusStats();

  return (
    <div style={{ padding: '16px' }}>
      <Card title="消息状态测试" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <strong>房间ID:</strong> {roomId}
          </div>
          <div>
            <strong>用户ID:</strong> {currentUserId}
          </div>
          <div>
            <strong>消息统计:</strong>
            <Space wrap>
              <Tag color="default">发送中: {statusStats.sending}</Tag>
              <Tag color="primary">已发送: {statusStats.sent}</Tag>
              <Tag color="success">已到达: {statusStats.delivered}</Tag>
              <Tag color="success">已读: {statusStats.read}</Tag>
              <Tag color="danger">失败: {statusStats.failed}</Tag>
            </Space>
          </div>
        </Space>
      </Card>

      <Card title="发送消息" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input
            placeholder="输入消息内容"
            value={messageContent}
            onChange={setMessageContent}
            onEnterPress={handleSendMessage}
          />
          <Button 
            block 
            color="primary" 
            onClick={handleSendMessage}
            disabled={!messageContent.trim()}
          >
            发送消息
          </Button>
        </Space>
      </Card>

      <Card title="状态模拟" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button 
            block 
            color="success" 
            onClick={handleSimulateDelivered}
          >
            模拟消息已到达
          </Button>
          <Button 
            block 
            color="success" 
            onClick={handleSimulateRead}
          >
            模拟消息已读
          </Button>
        </Space>
      </Card>

      <Card title="消息列表" style={{ marginBottom: '16px' }}>
        {allMessages.length > 0 ? (
          <List>
            {allMessages.map((message) => (
              <List.Item
                key={message.id}
                extra={
                  <Space>
                    <MessageStatusIndicator 
                      status={message.status}
                      size="medium"
                    />
                    <Tag color="default">
                      {message.sequence || 'N/A'}
                    </Tag>
                  </Space>
                }
              >
                <div>
                  <div><strong>{message.content}</strong></div>
                  <div style={{ fontSize: '12px', color: '#999' }}>
                    状态: {message.status} • 时间: {message.time}
                  </div>
                </div>
              </List.Item>
            ))}
          </List>
        ) : (
          <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
            暂无消息
          </div>
        )}
      </Card>

      <Card 
        title="操作日志" 
        extra={
          <Button size="mini" fill="outline" onClick={handleClearLogs}>
            清空
          </Button>
        }
      >
        <div style={{ 
          maxHeight: '200px', 
          overflowY: 'auto', 
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <div key={index} style={{ marginBottom: '4px' }}>
                {log}
              </div>
            ))
          ) : (
            <div style={{ color: '#999' }}>暂无日志</div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default MessageStatusTest;
