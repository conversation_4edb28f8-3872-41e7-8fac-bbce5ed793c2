import { test, expect } from '@playwright/test';

test.describe('添加成員二維碼頁', () => {
  test.beforeEach(async ({ page }) => {
    // 假設路由為 /user/add-member-qrcode
    await page.goto('/user/add-member-qrcode');
  });

  test('應正確渲染主要結構與國際化文字', async ({ page }) => {
    await expect(page.getByText('邀請加入')).toBeVisible();
    await expect(page.getByText('完成')).toBeVisible();
    await expect(page.getByText('王小明醫師')).toBeVisible();
    await expect(page.getByText('下載')).toBeVisible();
    await expect(page.getByText('傳送邀請碼')).toBeVisible();
    await expect(page.getByText('分享QR Code')).toBeVisible();
  });

  test('應顯示所有圖標與頭像', async ({ page }) => {
    await expect(page.getByAltText('back')).toBeVisible();
    await expect(page.getByAltText('qrcode')).toBeVisible();
    await expect(page.getByAltText('avatar')).toBeVisible();
    await expect(page.getByAltText('download')).toBeVisible();
    await expect(page.getByAltText('travel')).toBeVisible();
    await expect(page.getByAltText('system-qrcode')).toBeVisible();
  });

  test('點擊返回按鈕應返回上一頁', async ({ page }) => {
    // 模擬點擊返回
    await page.getByTestId('back-btn').click();
    // 這裡可根據實際路由跳轉驗證
    // 例如：expect(page).toHaveURL('/user')
  });
}); 