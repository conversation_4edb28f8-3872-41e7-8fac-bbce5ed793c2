// 資料庫建表 SQL 與型別定義

export interface Tenant {
  id: string;
  type?: string;
  description?: string;
  industry?: string;
  industrySub?: string;
  scale?: string;
  code?: string;
  name?: string;
  shortName?: string;
  avatarId?: string;
  isLastTenant?: boolean;
  unReadCount?: number;
  officialServiceNumberId?: string;
  manageServiceNumberId?: string;
  accountId?: string;
}

export interface Contact {
  id: string;
  createTime?: number;
  updateTime?: number;
  name?: string;
  age?: number;
  gender?: string;
  birthday?: string;
  phone?: string;
  email?: string;
  company?: string;
  isAileCompany?: boolean;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  companyDuty?: string;
  companyDepartment?: string;
  parentAddressBookId?: string;
  bloodType?: string;
  maritalStatus?: string;
  languages?: string[];
  interests?: string;
  status?: string;
  accountId?: string;
  tenantId?: string;
  channel?: string;
  isJoinAile?: boolean;
  isBindAile?: boolean;
  isCollectInfo?: boolean;
  alias?: string;
  description?: string;
  businessCardId?: string;
  openId?: string;
  avatarId?: string;
}

export interface User {
  id: string;
  createTime: number;
  updateTime: number;
  name?: string;
  avatarId?: string;
  mood?: string;
  age?: number;
  gender?: string;
  birthday?: string;
  status?: string;
  accountId: string;
  tenantId: string;
  channel?: string;
  personRoomId?: string;
  joinType?: string;
  openId?: string;
  isJoinAile?: boolean;
  isBindAile?: boolean;
  isCollectInfo?: boolean;
  homePagePicId?: string;
}

export interface Room {
  id: string;
  createTime?: number;
  updateTime?: number;
  unreadCount?: number;
  name?: string;
  type?: string;
  tenantId?: string;
  accountId?: string;
  ownerId?: string;
  isCustomName?: boolean;
  avatarId?: string;
  lastSequence?: number;
  memberCount?: number;
  memberIds?: string[];
  deleted?: boolean;
  status?: string;
  lastMessage?: string;
  homePagePicId?: string;
  isTop?: boolean;
  isMute?: boolean;
  dfrTime?: number;
  topTime?: number;
  member_deleted?: boolean;
  serviceNumberId?: string;
  isExternal?: boolean;
  provisionalIds?: string[];
  isTransfer?: boolean;
  transferReason?: string;
  sessionStatus?: string;
  sessionId?: string;
  agentId?: string;
  isOwnerStop?: boolean;
  warned?: boolean;
  businessId?: string;
  businessName?: string;
  businessStatus?: string;
  businessDescription?: string;
  businessEndTime?: number;
  mainRoomId?: string;
  editingMessage?: string;
  weight?: number;
  isPin?:boolean;
}

export interface RoomMembers {
  id: string;
  createTime?: number;
  updateTime?: number;
  memberId?: string;
  type?: string;
  roomId?: string;
  roomType?: string;
  accountId?: string;
  tenantId?: string;
  status?: string;
  privilege?: string;
  lastReceivedSequence?: number;
  lastReadSequence?: number;
  firstSequence?: number;
  dfrTime?: number | null;
  mute?: boolean | null;
  top?: boolean | null;
  topTime?: number | null;
  mainRoomId?: string | null;
  serviceNumberId?: string | null;
  lastMessage?: string | null; // JSON string of lastMessage object
}

export interface Messages {
  id: string;
  type?: string;
  sourceType?: string;
  senderName?: string;
  senderId?: string;
  accountId?: string;
  sendTime?: number;
  roomId?: string;
  sequence?: number;
  tenantId?: string;
  osType?: string;
  channel?: string;
  appointChannel?: string;
  content?: string;
  tag?: string;
  themeId?: string;
  nearMessageId?: string;
  sessionId?: string;
  channelMessageId?: string;
  flag?: number;
  status?: string;
}

export interface FailedMessages {
  id: string;
  type?: string;
  sourceType?: string;
  senderName?: string;
  senderId?: string;
  accountId?: string;
  sendTime?: number;
  roomId?: string;
  tenantId?: string;
  osType?: string;
  channel?: string;
  appointChannel?: string;
  content?: string;
  tag?: string;
  themeId?: string;
  nearMessageId?: string;
  flag?: number;
}

export interface ServiceNumber {
  id: string;
  createTime?: number;
  updateTime?: number;
  name?: string;
  type?: string;
  code?: string;
  description?: string;
  openType?: string;
  status?: string;
  avatarId?: string;
  tenantId?: string;
  memberRoomId?: string;
}

export interface KVStore {
  key: string;
  value: string;
  createTime?: number;
  updateTime?: number;
}

export const CREATE_TABLE_SQL = [
  `CREATE TABLE IF NOT EXISTS Tenant (
    id TEXT PRIMARY KEY NOT NULL,
    type TEXT,
    description TEXT,
    industry TEXT,
    industrySub TEXT,
    scale TEXT,
    code TEXT,
    name TEXT,
    shortName TEXT,
    avatarId TEXT,
    isLastTenant INTEGER,
    unReadCount INTEGER,
    officialServiceNumberId TEXT,
    manageServiceNumberId TEXT,
    accountId TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS Contact (
    id TEXT PRIMARY KEY NOT NULL,
    createTime INTEGER,
    updateTime INTEGER,
    name TEXT,
    age INTEGER,
    gender TEXT,
    birthday TEXT,
    phone TEXT,
    email TEXT,
    company TEXT,
    isAileCompany INTEGER,
    companyAddress TEXT,
    companyPhone TEXT,
    companyEmail TEXT,
    companyDuty TEXT,
    companyDepartment TEXT,
    parentAddressBookId TEXT,
    bloodType TEXT,
    maritalStatus TEXT,
    languages TEXT,
    interests TEXT,
    status TEXT,
    accountId TEXT,
    tenantId TEXT,
    channel TEXT,
    isJoinAile INTEGER,
    isBindAile INTEGER,
    isCollectInfo INTEGER,
    alias TEXT,
    description TEXT,
    businessCardId TEXT,
    openId TEXT,
    avatarId TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS User (
    id TEXT PRIMARY KEY NOT NULL,
    createTime INTEGER,
    updateTime INTEGER,
    name TEXT,
    avatarId TEXT,
    mood TEXT,
    age INTEGER,
    gender TEXT,
    birthday TEXT,
    status TEXT,
    accountId TEXT,
    tenantId TEXT NOT NULL,
    channel TEXT,
    personRoomId TEXT,
    joinType TEXT,
    openId TEXT,
    isJoinAile INTEGER,
    isBindAile INTEGER,
    isCollectInfo INTEGER,
    homePagePicId TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS Room (
    id TEXT PRIMARY KEY NOT NULL,
    createTime INTEGER,
    updateTime INTEGER,
    unreadCount INTEGER,
    name TEXT,
    type TEXT,
    tenantId TEXT,
    accountId TEXT,
    ownerId TEXT,
    isCustomName INTEGER,
    avatarId TEXT,
    lastSequence INTEGER,
    memberCount INTEGER,
    memberIds TEXT,
    deleted INTEGER,
    status TEXT,
    lastMessage TEXT,
    homePagePicId TEXT,
    isTop INTEGER,
    isMute INTEGER,
    dfrTime INTEGER,
    topTime INTEGER,
    member_deleted INTEGER,
    serviceNumberId TEXT,
    isExternal INTEGER,
    provisionalIds TEXT,
    isTransfer INTEGER,
    transferReason TEXT,
    sessionStatus TEXT,
    sessionId TEXT,
    agentId TEXT,
    isOwnerStop INTEGER,
    warned INTEGER,
    businessId TEXT,
    businessName TEXT,
    businessStatus TEXT,
    businessDescription TEXT,
    businessEndTime INTEGER,
    mainRoomId TEXT,
    editingMessage TEXT,
    weight INTEGER,
    isPin INTEGER
  );`,
  `CREATE TABLE IF NOT EXISTS RoomMembers (
    id TEXT PRIMARY KEY NOT NULL,
    createTime INTEGER,
    updateTime INTEGER,
    memberId TEXT,
    type TEXT,
    roomId TEXT,
    roomType TEXT,
    accountId TEXT,
    tenantId TEXT,
    status TEXT,
    privilege TEXT,
    lastReceivedSequence INTEGER,
    lastReadSequence INTEGER,
    firstSequence INTEGER,
    dfrTime INTEGER,
    mute INTEGER,
    top INTEGER,
    topTime INTEGER,
    mainRoomId TEXT,
    serviceNumberId TEXT,
    lastMessage TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS Messages (
    id TEXT PRIMARY KEY NOT NULL,
    type TEXT,
    sourceType TEXT,
    senderName TEXT,
    senderId TEXT,
    accountId TEXT,
    sendTime INTEGER,
    roomId TEXT,
    sequence INTEGER,
    tenantId TEXT,
    osType TEXT,
    channel TEXT,
    appointChannel TEXT,
    content TEXT,
    tag TEXT,
    themeId TEXT,
    nearMessageId TEXT,
    sessionId TEXT,
    channelMessageId TEXT,
    flag INTEGER,
    status TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS FailedMessages (
    id TEXT PRIMARY KEY NOT NULL,
    type TEXT,
    sourceType TEXT,
    senderName TEXT,
    senderId TEXT,
    accountId TEXT,
    sendTime INTEGER,
    roomId TEXT,
    tenantId TEXT,
    osType TEXT,
    channel TEXT,
    appointChannel TEXT,
    content TEXT,
    tag TEXT,
    themeId TEXT,
    nearMessageId TEXT,
    flag INTEGER
  );`,
  `CREATE TABLE IF NOT EXISTS ServiceNumber (
    id TEXT PRIMARY KEY NOT NULL,
    createTime INTEGER,
    updateTime INTEGER,
    name TEXT,
    type TEXT,
    code TEXT,
    description TEXT,
    openType TEXT,
    status TEXT,
    avatarId TEXT,
    tenantId TEXT,
    memberRoomId TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS KVStore (
    key TEXT PRIMARY KEY NOT NULL,
    value TEXT,
    createTime INTEGER,
    updateTime INTEGER
  );`
];

/**
 * 數據庫索引創建SQL語句
 * 用於優化查詢性能 - 针对聊天室消息查询优化
 */
export const CREATE_INDEX_SQL = [
  // Messages表核心索引 - 优化基于序列号的聊天室消息查询
  `CREATE INDEX IF NOT EXISTS idx_messages_room_tenant_sequence
   ON Messages (roomId, tenantId, sequence DESC);`,

  // 序列号范围查询优化索引（支持 BETWEEN 查询）
  `CREATE INDEX IF NOT EXISTS idx_messages_sequence_range
   ON Messages (roomId, sequence ASC);`,

  // 历史消息查询优化索引（支持 sequence < ? 查询）
  `CREATE INDEX IF NOT EXISTS idx_messages_before_sequence
   ON Messages (roomId, tenantId, sequence DESC);`,

  // 增量同步查询优化索引（支持 sequence > ? 查询）
  `CREATE INDEX IF NOT EXISTS idx_messages_after_sequence
   ON Messages (roomId, tenantId, sequence ASC);`,

  // 备用时间索引（兼容性保留）
  `CREATE INDEX IF NOT EXISTS idx_messages_room_tenant_time
   ON Messages (roomId, tenantId, sendTime DESC, sequence DESC);`,

  // 发送者消息查询索引
  `CREATE INDEX IF NOT EXISTS idx_messages_sender_time
   ON Messages (senderId, sendTime DESC);`,

  // 全局序列号索引（用于数据同步）
  `CREATE INDEX IF NOT EXISTS idx_messages_sequence_global
   ON Messages (sequence);`,

  // Room表索引
  `CREATE INDEX IF NOT EXISTS idx_room_tenant_weight
   ON Room (tenantId, weight DESC);`,

  `CREATE INDEX IF NOT EXISTS idx_room_last_sequence
   ON Room (lastSequence DESC);`,

  `CREATE INDEX IF NOT EXISTS idx_room_tenant_update
   ON Room (tenantId, updateTime DESC);`,

  // RoomMembers表索引
  `CREATE INDEX IF NOT EXISTS idx_room_members_room_member
   ON RoomMembers (roomId, memberId);`,

  `CREATE INDEX IF NOT EXISTS idx_room_members_member_room
   ON RoomMembers (memberId, roomId);`,

  // Contact表索引
  `CREATE INDEX IF NOT EXISTS idx_contact_tenant
   ON Contact (tenantId, id);`,

  // User表索引
  `CREATE INDEX IF NOT EXISTS idx_user_tenant
   ON User (tenantId, id);`,

  // ServiceNumber表索引
  `CREATE INDEX IF NOT EXISTS idx_service_number_tenant
   ON ServiceNumber (tenantId, id);`,

  // Tenant表索引 - 优化团队列表查询性能
  `CREATE INDEX IF NOT EXISTS idx_tenant_account_name
   ON Tenant (accountId, name ASC);`,

  `CREATE INDEX IF NOT EXISTS idx_tenant_name_search
   ON Tenant (name, shortName, code);`,

  `CREATE INDEX IF NOT EXISTS idx_tenant_last_tenant
   ON Tenant (isLastTenant, accountId);`
];