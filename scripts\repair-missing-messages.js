/**
 * 消息序列号修复脚本
 * 用于检测和修复聊天室中缺失的消息序列号
 * 
 * 使用方法:
 * 1. 在浏览器控制台中运行此脚本
 * 2. 或者在React应用中导入并使用相关函数
 */

// 示例：检测单个聊天室的消息缺失情况
async function detectSingleRoom() {
  const roomId = 'your-room-id'; // 替换为实际的聊天室ID
  const expectedLastSequence = 100; // 替换为期望的最后序列号
  
  try {
    console.log('🔍 开始检测消息缺失...');
    
    // 如果在React应用中，使用以下方式
    // import { MessageSequenceRepair } from '../src/utils/messageSequenceRepair';
    // const result = await MessageSequenceRepair.detectMissingSequences(roomId, expectedLastSequence);
    
    // 如果在浏览器控制台中，使用以下方式（需要先加载工具）
    const result = await window.MessageSequenceRepair.detectMissingSequences(roomId, expectedLastSequence);
    
    console.log('✅ 检测完成:', result);
    
    if (!result.isComplete) {
      console.log(`❌ 发现缺失消息: ${result.totalMissing} 条`);
      console.log('📋 修复计划:');
      result.repairPlan.forEach(plan => console.log(`   ${plan}`));
    } else {
      console.log('✅ 消息完整，无需修复');
    }
    
  } catch (error) {
    console.error('❌ 检测失败:', error);
  }
}

// 示例：修复单个聊天室的消息缺失（预演模式）
async function repairSingleRoomDryRun() {
  const roomId = 'your-room-id'; // 替换为实际的聊天室ID
  const expectedLastSequence = 100; // 替换为期望的最后序列号
  
  try {
    console.log('🎭 开始预演修复...');
    
    const result = await window.MessageSequenceRepair.repairMissingSequences(
      roomId, 
      expectedLastSequence, 
      { 
        dryRun: true, // 预演模式，不实际执行
        batchDelay: 300,
        maxBatches: 10
      }
    );
    
    console.log('✅ 预演完成:', result);
    
  } catch (error) {
    console.error('❌ 预演失败:', error);
  }
}

// 示例：实际修复单个聊天室的消息缺失
async function repairSingleRoomActual() {
  const roomId = 'your-room-id'; // 替换为实际的聊天室ID
  const expectedLastSequence = 100; // 替换为期望的最后序列号
  
  // 确认提示
  const confirmed = confirm(`确定要修复房间 ${roomId} 的消息缺失吗？这将调用API获取缺失的消息。`);
  if (!confirmed) {
    console.log('❌ 用户取消修复操作');
    return;
  }
  
  try {
    console.log('🚀 开始实际修复...');
    
    const result = await window.MessageSequenceRepair.repairMissingSequences(
      roomId, 
      expectedLastSequence, 
      { 
        dryRun: false, // 实际执行
        batchDelay: 300,
        maxBatches: 10
      }
    );
    
    console.log('✅ 修复任务启动:', result);
    
    if (result.success) {
      console.log('🎉 修复任务已启动，请等待API调用完成');
      console.log('💡 可以在网络面板中查看API调用进度');
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  }
}

// 示例：批量检测多个聊天室
async function batchDetectRooms() {
  const roomConfigs = [
    { roomId: 'room1', expectedLastSequence: 100 },
    { roomId: 'room2', expectedLastSequence: 200 },
    { roomId: 'room3', expectedLastSequence: 150 }
    // 添加更多聊天室配置...
  ];
  
  try {
    console.log('🔍 开始批量检测...');
    
    const results = await window.MessageSequenceRepair.batchDetectMissingSequences(roomConfigs);
    
    console.log('✅ 批量检测完成');
    console.log('📊 详细结果:', results);
    
    // 找出需要修复的房间
    const needRepair = results.filter(r => !r.isComplete && r.totalMissing > 0);
    
    if (needRepair.length > 0) {
      console.log(`\n❌ 发现 ${needRepair.length} 个房间需要修复:`);
      needRepair.forEach(room => {
        console.log(`   ${room.roomId}: ${room.summary}`);
      });
      
      console.log('\n💡 可以使用以下代码逐个修复:');
      needRepair.forEach(room => {
        console.log(`   MessageSequenceRepair.repairMissingSequences('${room.roomId}', expectedLastSequence);`);
      });
    } else {
      console.log('✅ 所有房间的消息都完整');
    }
    
  } catch (error) {
    console.error('❌ 批量检测失败:', error);
  }
}

// 示例：针对您提到的具体问题 - 序列号40,35,34的缺失
async function repairSpecificSequences() {
  const roomId = 'your-room-id'; // 替换为实际的聊天室ID
  
  console.log('🔍 检测序列号40,35,34等缺失问题...');
  
  try {
    // 假设最新的序列号是40，检测从1到40的完整性
    const result = await window.MessageSequenceRepair.detectMissingSequences(roomId, 40);
    
    console.log('📊 检测结果:');
    console.log(`   数据库序列号范围: ${result.dbSequenceRange ? `${result.dbSequenceRange.min}-${result.dbSequenceRange.max}` : '无数据'}`);
    console.log(`   缺失序列号: ${result.missingSequences.join(', ')}`);
    console.log(`   缺失范围: ${result.missingRanges.map(r => `${r.start}-${r.end}`).join(', ')}`);
    
    // 检查是否包含您提到的序列号
    const targetSequences = [34, 35, 40];
    const missingTargets = targetSequences.filter(seq => result.missingSequences.includes(seq));
    
    if (missingTargets.length > 0) {
      console.log(`❌ 确认缺失目标序列号: ${missingTargets.join(', ')}`);
      
      // 询问是否修复
      const shouldRepair = confirm(`发现缺失序列号 ${missingTargets.join(', ')}，是否立即修复？`);
      if (shouldRepair) {
        await window.MessageSequenceRepair.repairMissingSequences(roomId, 40);
        console.log('🚀 修复任务已启动');
      }
    } else {
      console.log('✅ 目标序列号都存在');
    }
    
  } catch (error) {
    console.error('❌ 检测失败:', error);
  }
}

// 显示使用指南
function showUsageGuide() {
  console.log(`
🔧 消息序列号修复脚本使用指南
===============================

在浏览器控制台中运行以下函数:

1. 检测单个聊天室:
   detectSingleRoom()

2. 预演修复(不实际执行):
   repairSingleRoomDryRun()

3. 实际修复:
   repairSingleRoomActual()

4. 批量检测多个聊天室:
   batchDetectRooms()

5. 修复特定序列号缺失问题:
   repairSpecificSequences()

6. 显示MessageSequenceRepair工具帮助:
   MessageSequenceRepair.showHelp()

注意事项:
- 请先修改脚本中的roomId和expectedLastSequence
- 建议先使用预演模式查看修复计划
- 实际修复会调用API，请确保网络连接正常
  `);
}

// 自动显示使用指南
console.log('📋 消息序列号修复脚本已加载');
showUsageGuide();

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    detectSingleRoom,
    repairSingleRoomDryRun,
    repairSingleRoomActual,
    batchDetectRooms,
    repairSpecificSequences,
    showUsageGuide
  };
}
