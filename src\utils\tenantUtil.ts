import { logService } from '../services/system/logService';
import aileDBService from '../services/db/aileDBService';
import { getLocalStorage } from './storage';
import { ConstantUtil } from './constantUtil';

/**
 * 租戶和賬號管理相關工具函數
 */
export class TenantUtil {
  /**
   * 檢查數據庫健康狀態，並在必要時嘗試修復
   * 用於在遇到數據庫問題時進行自我修復
   */
  public static async checkAndRepairDatabase(): Promise<boolean> {
    try {
      const accountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
      
      if (!accountId) {
        logService.warn('檢查數據庫健康狀態失敗：找不到有效的賬號ID');
        return false;
      }
      
      // 檢查數據庫是否已初始化
      if (!aileDBService.isInitialized()) {
        logService.warn('數據庫未初始化，嘗試修復', { accountId });
        
        // 嘗試初始化數據庫
        const initResult = await aileDBService.initForAccount(accountId);
        
        if (initResult) {
          logService.info('數據庫修復成功', { accountId });
          return true;
        } else {
          logService.error('數據庫修復失敗', { accountId });
          return false;
        }
      }
      
      // 檢查數據庫連接是否正常
      try {
        // 執行簡單查詢測試連接
        await aileDBService.run('SELECT 1');
        logService.debug('數據庫連接正常');
        return true;
      } catch (queryError) {
        logService.warn('數據庫連接異常，嘗試修復', { error: queryError });
        
        // 嘗試關閉並重新初始化數據庫
        try {
          await aileDBService.close();
          // 添加延遲確保資源釋放
          await new Promise(resolve => setTimeout(resolve, 300));
          const reinitResult = await aileDBService.initForAccount(accountId);
          
          if (reinitResult) {
            logService.info('數據庫連接修復成功', { accountId });
            return true;
          } else {
            logService.error('數據庫連接修復失敗', { accountId });
            return false;
          }
        } catch (repairError) {
          logService.error('數據庫修復過程出錯', { error: repairError });
          return false;
        }
      }
    } catch (error) {
      logService.error('檢查數據庫健康狀態過程出錯', { error });
      return false;
    }
  }
  
  /**
   * 獲取當前租戶ID，並確保它是有效的
   * 如果租戶ID無效，會嘗試從其他來源獲取
   */
  public static getCurrentTenantId(): string | null {
    // 從 localStorage 獲取當前租戶ID
    const tenantId = getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
    
    if (tenantId) {
      return tenantId;
    }
    return null;
  }
  
  /**
   * 生成帶有租戶前綴的鍵名
   * 用於確保不同租戶的鍵值存儲相互隔離
   * @param key 原始鍵名
   * @returns 帶有租戶前綴的鍵名
   */
  public static getTenantPrefixedKey(key: string): string {
    const tenantId = this.getCurrentTenantId();
    if (tenantId) {
      return `${tenantId}::${key}`;
    }
    
    // 如果找不到租戶ID，仍然加上特殊前綴以示區別
    logService.warn('生成租戶前綴鍵時未找到租戶ID，使用通用前綴');
    return `global::${key}`;
  }
  
  /**
   * 檢測是否發生了賬號切換
   * @param newAccountId 新賬號ID
   * @returns 是否為賬號切換
   */
  public static isAccountSwitching(newAccountId: string | null): boolean {
    if (!newAccountId) return false;
    
    const currentAccountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
    return !!currentAccountId && currentAccountId !== newAccountId;
  }
}

/**
 * 獲取當前租戶ID，並確保它是有效的
 * 如果租戶ID無效，會嘗試從其他來源獲取
 */
export const getCurrentTenantId = (): string | null => {
  return TenantUtil.getCurrentTenantId();
};

/**
 * 獲取當前租戶ID，如果不存在則拋出錯誤
 * @throws Error 當找不到租戶ID時
 */
export const getRequiredTenantId = (): string => {
  const tenantId = getCurrentTenantId();
  if (!tenantId) {
    throw new Error('未獲取到當前租戶Id');
  }
  return tenantId;
};

export default TenantUtil; 