export enum LogInState {
  None = 'none', // 无状态
  LoggingIn = 'logging_in', // 登入中
  LoggedIn = 'logged_in', // 已登入账号
  LoggedInTenant = 'logged_in_tenant', // 已登入租户
  LoggedOut = 'logged_out', // 已登出
  ForceLoggedOut = 'force_logged_out', // 强制登出
  LoginError = 'login_error', // 登入错误
  LoginErrorInTenant = 'login_error_tenant', // 登入错误
  OtherLogin = 'other_login', // 其他设备登入
  ReLoggingIn = 're_logging_in', // 重新登入中
  ReLoginError = 're_login_error', // 重新登入错误
  ReLoginOk = 're_login_ok', // 重新登入成功
  ForceOffline = 'force_offline', // 强制下线
}

/**
 * P1 权重枚举 - 最高优先级 (2位)
 * 数值越大，优先级越高
 */
export enum P1WeightEnum {
  Default = 0,        // 默认(缺省值)
  MyService = 1,      // 我的服务中
  InService = 2,      // 服务中
  JustReceived = 3,   // 刚进件
}

/**
 * P2 权重枚举 - 次要优先级 (1位)
 * 主要用于标识未读状态
 */
export enum P2WeightEnum {
  Default = 0,        // 默认(缺省值)
  Unread = 1,         // 未读
}

/**
 * P3 权重枚举 - 标签优先级位掩码 (8位)
 * 每个位对应一个标签，可多选
 */
export enum P3WeightEnum {
  Default = 1,        // 默认(缺省值) - 第0位
  Other = 2,          // 其他(缺省值) - 第1位
  MarkUnread = 4,     // 标记未读 - 第2位
  Draft = 8,          // 草稿 - 第3位
  SendFailed = 16,    // 发送失败 - 第4位
  AtMe = 32,          // @我 - 第5位
  Favorite = 64,      // 我的最爱 - 第6位
  Top = 128,          // 置顶 - 第7位
}