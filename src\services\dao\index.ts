// 基礎 DAO 類
export { BaseDao, getRequiredTenantId, getCurrentTenantId } from './base/BaseDao';

// DAO 實例導入
import tenantDao from './TenantDao';
import contactDao from './ContactDao';
import kvStoreDao from './KVStoreDao';
import userDao from './UserDao';
import messageDao from './MessageDao';
import roomDao from './RoomDao';
import serviceNumberDao from './ServiceNumberDao';

// DAO 實例導出
export {
  tenantDao,
  contactDao,
  kvStoreDao,
  userDao,
  messageDao,
  roomDao,
  serviceNumberDao
};

// DAO 類導出
export { default as RoomDao } from './RoomDao';
export { default as UserDao } from './UserDao';
export { default as MessageDao } from './MessageDao';
export { default as ContactDao } from './ContactDao';
export { default as TenantDao } from './TenantDao';
export { default as ServiceNumberDao } from './ServiceNumberDao';
export { default as KVStoreDao } from './KVStoreDao';