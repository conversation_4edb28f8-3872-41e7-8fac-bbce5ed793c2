/**
 * @file constantUtil.ts
 * @description 产品通用常量工具类，统一管理全局常量和常用静态方法
 * <AUTHOR>
 */
import capacitorConfig from '../../capacitor.config';

/**
 * Aile App 常量工具类
 * 
 * 统一管理应用中的所有常量，包括配置项、存储键、UI常量等
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */



/**
 * 常量工具类（产品级）
 * 命名风格参考 logService，采用 PascalCase 类名和 camelCase 文件名
 */
export class ConstantUtil {
  /** 应用名称 */
  public static readonly APP_NAME = capacitorConfig.appName || 'Aile App';
  /** 应用ID */
  public static readonly APP_ID = capacitorConfig.appId || 'cloud.aile.aile';
  /** 版本号 */
  public static readonly VERSION = (capacitorConfig as any).appVersion || '5.0.0';
  /** 构建号 */
  public static readonly BUILD = (capacitorConfig as any).buildNumber || '1';
  /** 应用渠道 */
  public static readonly APP_CHANNEL = "AileLite";
  /** 默认分页大小 */
  public static readonly DEFAULT_PAGE_SIZE = 20;
  /** Token 本地存储 key (与 authSlice 保持一致) */
  public static readonly TOKEN_KEY = 'authToken';
  /** Refresh Token 本地存储 key */
  public static readonly REFRESH_TOKEN_KEY = 'refreshToken';
  /** 商務號ID本地存储 key */
  public static readonly BOSS_SERVICENUMBERID = 'bossServiceNumberId';
  /** 支持的环境 */
  public static readonly ENVIRONMENTS = ['local', 'development', 'staging', 'production'] as const;
  /** 是否启用推送功能 */
  public static readonly USE_PUSH = false;

  // ========================
  // LocalStorage Keys
  // ========================
  /** 日志本地存储 key */
  public static readonly LOGS_STORAGE_KEY = 'aile_app_logs';
  /** 设备信息本地存储 key */
  public static readonly DEVICE_INFO_STORAGE_KEY = 'aile_device_info';
  /** 设备ID本地存储 key */
  public static readonly DEVICE_ID_STORAGE_KEY = 'aile_device_id';
  /** 账户ID本地存储 key */
  public static readonly ACCOUNT_ID_KEY = 'account_id';
  /** 主题本地存储 key */
  public static readonly THEME_STORAGE_KEY = 'theme';
  /** 侧边栏折叠状态本地存储 key */
  public static readonly SIDEBAR_COLLAPSED_STORAGE_KEY = 'sidebarCollapsed';
  /** 推送註冊資訊本地存儲 key */
  public static readonly PUSH_REGISTRATION_KEY = 'push_registration';
  /** 設備ID本地存儲 key（兼容直接 localStorage 用法） */
  public static readonly DEVICE_ID_KEY = 'device_id';
  /** 當前租戶ID本地存儲 key */
  public static readonly CURRENT_TENANT_ID_KEY = 'current_tenant_id';
  /** 租戶切換來源標記 key */
  public static readonly TENANT_SWITCH_SOURCE_KEY = 'tenant_switch_source';
  /** 租戶信息本地存儲 key */
  public static readonly TENANT_INFO_KEY = 'tenant_info';
  /**
   * UI 置頂房間本地存儲 key
   */
  public static readonly ROOM_PINNED_IDS_KEY = 'ROOM_PINNED_IDS';
  /** 登入用戶信息本地存儲 key */
  public static readonly LOGIN_USER_KEY = 'loginUser';
  /** 登入租戶信息本地存儲 key */
  public static readonly LOGIN_TENANT_KEY = 'loginTenant';
  /** 登入賬號信息本地存儲 key */
  public static readonly LOGIN_ACCOUNT_KEY = 'loginAccount';
  /** 轉租戶信息本地存儲 key */
  public static readonly TRANS_TENANT_KEY = 'transTenant';
  /** Redux 狀態持久化 key */
  public static readonly REDUX_STATE_KEY = 'reduxState';
  /** 用戶首選環境存儲 key */
  public static readonly PREFERRED_ENV_KEY = 'preferred_env';

  // ========================
  // 加密相关常量
  // ========================
  /** 默认加密密钥 */
  public static readonly DEFAULT_SECRET_KEY = '****************';

  // ========================
  // UI 相关常量
  // ========================
  /** 默认加载文案 */
  public static readonly DEFAULT_LOADING_TEXT = '加载中...';
  /** 默认错误文案 */
  public static readonly DEFAULT_ERROR_TEXT = '操作失败，请稍后重试';
  /** 默认成功文案 */
  public static readonly DEFAULT_SUCCESS_TEXT = '操作成功';

  // ========================
  // LINE SDK 相關常量
  // ========================

  /** LINE 令牌本地存儲 key */
  public static readonly LINE_TOKEN_KEY = 'line_access_token';
  /** LINE 用戶資料本地存儲 key */
  public static readonly LINE_USER_PROFILE_KEY = 'line_user_profile';
  /** LINE 登錄 Scope */
  public static readonly LINE_LOGIN_SCOPE = 'openid profile';
  /** LINE 登錄狀態參數 */
  public static readonly LINE_LOGIN_STATE = 'aile_app_login';

  // ========================
  // 業務相關常量
  // ========================
  
  // 登入狀態相關常量
  /** 用戶線上狀態 */
  public static readonly LOGIN_STATUS_ONLINE = '1';
  /** 用戶離線狀態 */
  public static readonly LOGIN_STATUS_OFFLINE = '0';
  
  // 登入類型常量
  /** OTP登入類型 */
  public static readonly LOGIN_TYPE_OTP = '3';
  /** LINE登入類型 */
  public static readonly LOGIN_TYPE_LINE = '2';
  /** 一般登入類型 */
  public static readonly LOGIN_TYPE_NORMAL = '1';
  
  /** 最大文件上传大小（字节）- 10MB */
  public static readonly MAX_FILE_SIZE = 10 * 1024 * 1024;
  /** 支持的图片格式 */
  public static readonly SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'webp'] as const;
  /** 支持的视频格式 */
  public static readonly SUPPORTED_VIDEO_FORMATS = ['mp4', 'webm', 'avi', 'mov'] as const;
  /** 支持的音频格式 */
  public static readonly SUPPORTED_AUDIO_FORMATS = ['mp3', 'wav', 'ogg', 'aac', 'flac'] as const;

  // ========================
  // 时间相关常量
  // ========================
  /** 十分钟的毫秒数 */
  public static readonly TEN_MINUTES = 10 * 60 * 1000;
  /** 一天的秒数 */
  public static readonly TTL_ONE_DAY = 1 * 24 * 60 * 60;
  /** 一周的秒数 */
  public static readonly TTL_ONE_WEEK = 7 * 24 * 60 * 60;
  /** 一年的秒数 */
  public static readonly TTL_ONE_YEAR = 365 * 24 * 60 * 60;
  /** 十年的秒数 */
  public static readonly TTL_TEN_YEAR = 10 * 365 * 24 * 60 * 60;

  // ========================
  // 图片和视频尺寸常量
  // ========================
  /** 图片消息最大显示尺寸 */
  public static readonly IMAGE_MESSAGE_MAXSIZE_NORMAL = { width: 220, height: 220 };
  /** 图片回复消息最大显示尺寸 */
  public static readonly IMAGE_MESSAGE_MAXSIZE_REPLY = { width: 75, height: 75 };
  /** 视频消息最大显示尺寸 */
  public static readonly VIDEO_MESSAGE_MAXSIZE_NORMAL = { width: 220, height: 220 };
  /** 视频回复消息最大显示尺寸 */
  public static readonly VIDEO_MESSAGE_MAXSIZE_REPLY = { width: 75, height: 75 };

  // ========================
  // 數據庫配置常量
  // ========================
  /** 數據庫版本 */
  public static readonly DB_VERSION = 1;
  /** 數據庫加密模式 */
  public static readonly DB_ENCRYPTION_MODE = 'no-encryption';
  /** 數據庫連接超時時間（毫秒） */
  public static readonly DB_CONNECTION_TIMEOUT = 30000;
  /** 數據庫查詢超時時間（毫秒） */
  public static readonly DB_QUERY_TIMEOUT = 10000;

  // 數據庫表名常量
  /** 租戶表名 */
  public static readonly DB_TABLE_TENANT = 'tenant';
  /** 客戶表名 */
  public static readonly DB_TABLE_CONTACT = 'contact';
  /** 員工表名 */
  public static readonly DB_TABLE_USER = 'user';
  /** 聊天室表名 */
  public static readonly DB_TABLE_ROOM = 'room';
  /** 聊天室成員表名 */
  public static readonly DB_TABLE_ROOM_MEMBER = 'room_member';
  /** 消息表名 */
  public static readonly DB_TABLE_MESSAGE = 'message';

  // 數據庫查詢限制常量
  /** 默認分頁大小 */
  public static readonly DB_DEFAULT_PAGE_SIZE = 20;
  /** 最大分頁大小 */
  public static readonly DB_MAX_PAGE_SIZE = 100;
  /** 消息批量處理最大數量 */
  public static readonly DB_MAX_MESSAGE_BATCH = 50;
  /** 最大查詢結果數量 */
  public static readonly DB_MAX_QUERY_RESULTS = 1000;

  // ========================
  // 静态工具方法
  // ========================

  /**
   * 获取当前时间戳（毫秒）
   */
  public static now(): number {
    return Date.now();
  }

  /**
   * 生成唯一ID（简单版）
   */
  public static uuid(): string {
    return (
      Date.now().toString(36) + Math.random().toString(36).substr(2, 8)
    ).toUpperCase();
  }

  /**
   * 获取格式化的当前日期时间
   * @returns 格式化日期时间字符串 YYYY-MM-DD HH:mm:ss
   */
  public static getNowFormatDate(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 生成随机整数
   * @param min 最小值
   * @param max 最大值
   * @returns 随机整数
   */
  public static randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 生成随机RGB颜色
   * @returns RGB颜色字符串
   */
  public static randomRgbColor(): string {
    const r = this.randomInt(0, 255);
    const g = this.randomInt(0, 255);
    const b = this.randomInt(0, 255);
    return `rgb(${r}, ${g}, ${b})`;
  }

  /**
   * 判断值是否为null或undefined
   * @param obj 要检查的值
   * @returns 是否为null或undefined
   */
  public static isNull(obj: any): boolean {
    return obj === null || obj === undefined;
  }

  /**
   * 判断数字是否为null、undefined或NaN
   * @param obj 要检查的数字
   * @returns 是否为无效数字
   */
  public static isNumberNull(obj: any): boolean {
    return this.isNull(obj) || isNaN(obj);
  }

  /**
   * 判断布尔值是否为null或undefined
   * @param obj 要检查的布尔值
   * @returns 是否为null或undefined
   */
  public static isBooleanNull(obj: any): boolean {
    return this.isNull(obj);
  }

  /**
   * 检查字符串是否为有效的JSON格式
   * @param item 要检查的字符串
   * @returns 是否为有效JSON
   */
  public static isJson(item: any): boolean {
    let jsonItem = typeof item !== 'string' ? JSON.stringify(item) : item;
    
    try {
      jsonItem = JSON.parse(jsonItem);
    } catch (e) {
      return false;
    }
    
    if (typeof jsonItem === 'object' && jsonItem !== null) {
      return true;
    }
    
    return false;
  }

  /**
   * 秒数转时间格式
   * @param secs 秒数
   * @returns 时间格式字符串 HH:mm:ss
   */
  public static second2time(secs: number): string {
    const hours = Math.floor(secs / 3600);
    const minutes = Math.floor((secs % 3600) / 60);
    const seconds = Math.floor(secs % 60);
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * 时长格式化
   * @param secs 秒数
   * @returns 格式化的时长字符串
   */
  public static durationFormat(secs: number): string {
    const minutes = Math.floor(secs / 60);
    const seconds = Math.floor(secs % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * 判断文件扩展名是否为视频格式
   * @param extension 文件扩展名
   * @returns 是否为视频格式
   */
  public static isVideo(extension: string): boolean {
    const ext = extension.toLowerCase().replace('.', '');
    return this.SUPPORTED_VIDEO_FORMATS.includes(ext as any);
  }

  /**
   * 将Blob转换为Base64字符串
   * @param blob Blob对象
   * @returns Base64字符串的Promise
   */
  public static blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
      reader.readAsDataURL(blob);
    });
  }

  /**
   * 将Base64字符串转换为Blob对象
   * @param base64Data Base64字符串
   * @returns Blob对象
   */
  public static convertBase64ToBlob(base64Data: string): Blob {
    const base64WithoutPrefix = base64Data.split(',')[1] || base64Data;
    const byteCharacters = atob(base64WithoutPrefix);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray]);
  }

  /**
   * 从Base64数据创建File对象
   * @param base64Data Base64字符串
   * @param fileName 文件名
   * @param mimeType MIME类型
   * @returns File对象
   */
  public static getFileFromBase64(base64Data: string, fileName: string, mimeType: string): File {
    const blob = this.convertBase64ToBlob(base64Data);
    return new File([blob], fileName, { type: mimeType });
  }

  /**
   * 获取图片尺寸
   * @param file 图片文件
   * @returns 图片尺寸的Promise
   */
  public static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * 获取视频尺寸和时长
   * @param file 视频文件
   * @returns 视频信息的Promise
   */
  public static getVideoDimensions(file: File): Promise<{ width: number; height: number; duration: number }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.onloadedmetadata = () => {
        resolve({
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration
        });
        URL.revokeObjectURL(video.src);
      };
      video.onerror = reject;
      video.src = URL.createObjectURL(file);
    });
  }

  /**
   * SVG转Base64
   * @param svg SVG字符串
   * @returns Base64字符串
   */
  public static svgToBase64(svg: string): string {
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  /**
   * 从URL获取查询参数
   * @param url URL字符串
   * @param key 参数名
   * @returns 参数值或null
   */
  public static getQueryVariable(url: string, key: string): string | null {
    const query = url.split('?')[1];
    if (!query) return null;
    
    const vars = query.split('&');
    for (const variable of vars) {
      const pair = variable.split('=');
      if (decodeURIComponent(pair[0]) === key) {
        return decodeURIComponent(pair[1]);
      }
    }
    return null;
  }

  /**
   * 简单的链接替换函数
   * @param value 包含链接的文本
   * @returns 替换后的HTML字符串
   */
  public static replaceLink(value: string): string {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return value.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
  }

  /**
   * 判断是否为支持的环境
   * @param env 环境名
   */
  public static isValidEnv(env: string): boolean {
    return (this.ENVIRONMENTS as readonly string[]).includes(env);
  }

  /**
   * 根据环境获取配置
   * @param environment 环境名称
   * @returns 是否为有效环境
   */
  public static isValidEnvironment(environment: string): boolean {
    return this.ENVIRONMENTS.includes(environment as any);
  }

  /**
   * 获取文件类型是否支持
   * @param extension 文件扩展名
   * @returns 文件类型分类
   */
  public static getFileTypeCategory(extension: string): 'image' | 'video' | 'audio' | 'unknown' {
    const ext = extension.toLowerCase();
    
    if (this.SUPPORTED_IMAGE_FORMATS.includes(ext as any)) {
      return 'image';
    }
    
    if (this.SUPPORTED_VIDEO_FORMATS.includes(ext as any)) {
      return 'video';
    }
    
    if (this.SUPPORTED_AUDIO_FORMATS.includes(ext as any)) {
      return 'audio';
    }
    
    return 'unknown';
  }



  // ========================
  // 輔助方法
  // ========================
  
  /**
   * 格式化登入狀態顯示文字
   * @param status 登入狀態
   * @returns 格式化後的狀態文字
   */
  public static formatLoginStatus(status: string): string {
    return status === this.LOGIN_STATUS_ONLINE ? '線上' : '離線';
  }

  /**
   * 格式化登入類型顯示文字
   * @param type 登入類型
   * @returns 格式化後的類型文字
   */
  public static formatLoginType(type: string): string {
    switch (type) {
      case this.LOGIN_TYPE_NORMAL:
        return '一般登入';
      case this.LOGIN_TYPE_LINE:
        return 'LINE登入';
      case this.LOGIN_TYPE_OTP:
        return 'OTP驗證碼登入';
      default:
        return `未知類型(${type})`;
    }
  }

  /**
   * 格式化手機號碼顯示
   * @param countryCode 國家代碼
   * @param mobile 手機號碼
   * @returns 格式化後的手機號碼
   */
  public static formatPhoneNumber(countryCode: string, mobile: string): string {
    return `${countryCode} ${mobile}`;
  }



  public static readonly MESSAGE_TYPE_TEXT = 'Text';
  public static readonly MESSAGE_TYPE_IMAGE = 'Image';
  public static readonly MESSAGE_TYPE_AUDIO = 'Audio';
  public static readonly MESSAGE_TYPE_VIDEO = 'Video';
  public static readonly MESSAGE_TYPE_FILE = 'File';
  public static readonly MESSAGE_TYPE_EVENT = 'Event';



  /**
   * 頭像圖片快取 key
   * @param avatarId 頭像ID
   * @param size 尺寸
   */
  public static AVATAR_IMAGE_CACHE_KEY(avatarId: string, size: string): string {
    return `avatar_image_${avatarId}_${size}`;
  }



  public static readonly QRCODE_SERVERPATH = 'https://aile.com.tw/';

    public static readonly QRCODE_SEPERATOR = '?code=';
}

/**
 * 导出常量类型定义
 */
export type Environment = typeof ConstantUtil.ENVIRONMENTS[number];
export type SupportedImageFormat = typeof ConstantUtil.SUPPORTED_IMAGE_FORMATS[number];
export type SupportedVideoFormat = typeof ConstantUtil.SUPPORTED_VIDEO_FORMATS[number];
export type SupportedAudioFormat = typeof ConstantUtil.SUPPORTED_AUDIO_FORMATS[number]; 

