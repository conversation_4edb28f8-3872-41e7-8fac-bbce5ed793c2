/**
 * 刘海屏适配测试页面
 * 用于验证安全区域和状态栏配置是否正确
 */

import React, { useEffect, useState } from 'react';
import { Card, Space, Button, Toast } from 'antd-mobile';
import { statusBarService } from '../../services/platform';
import { Capacitor } from '@capacitor/core';
import { PAGE_COLORS } from '../../config/app/pageColors';
import { useStatusBarColor } from '../../hooks/useStatusBarColor';

const NotchTestPage: React.FC = () => {
  const [statusBarInfo, setStatusBarInfo] = useState<any>(null);
  const [isNative, setIsNative] = useState(false);
  const [currentTestColor, setCurrentTestColor] = useState('#FFFFFF');

  // 使用状态栏颜色管理Hook
  const { setColor, getCurrentColor, pageColor, isSupported } = useStatusBarColor({
    customColor: currentTestColor,
    autoDetectStyle: true
  });

  useEffect(() => {
    setIsNative(Capacitor.isNativePlatform());
    loadStatusBarInfo();
  }, []);

  const loadStatusBarInfo = async () => {
    try {
      const info = await statusBarService.getInfo();
      setStatusBarInfo(info);
    } catch (error) {
      console.error('Failed to get status bar info:', error);
    }
  };

  const handleSetLightTheme = async () => {
    try {
      await statusBarService.configureTheme(false);
      Toast.show('已设置为浅色主题');
      loadStatusBarInfo();
    } catch (error) {
      Toast.show('设置失败');
    }
  };

  const handleSetDarkTheme = async () => {
    try {
      await statusBarService.configureTheme(true);
      Toast.show('已设置为深色主题');
      loadStatusBarInfo();
    } catch (error) {
      Toast.show('设置失败');
    }
  };

  const handleHideStatusBar = async () => {
    try {
      await statusBarService.hide();
      Toast.show('状态栏已隐藏');
      loadStatusBarInfo();
    } catch (error) {
      Toast.show('操作失败');
    }
  };

  const handleShowStatusBar = async () => {
    try {
      await statusBarService.show();
      Toast.show('状态栏已显示');
      loadStatusBarInfo();
    } catch (error) {
      Toast.show('操作失败');
    }
  };

  // 测试不同页面颜色
  const handleTestPageColor = (colorType: string) => {
    let color = '#FFFFFF';
    switch (colorType) {
      case 'home-admin':
        color = PAGE_COLORS.HOME.ADMIN;
        break;
      case 'home-user':
        color = PAGE_COLORS.HOME.USER;
        break;
      case 'chat-customer':
        color = PAGE_COLORS.CHAT.CUSTOMER;
        break;
      case 'chat-team':
        color = PAGE_COLORS.CHAT.TEAM;
        break;
      case 'chat-system':
        color = PAGE_COLORS.CHAT.SYSTEM;
        break;
      default:
        color = PAGE_COLORS.DEFAULT;
    }

    setCurrentTestColor(color);
    setColor(color);
    Toast.show(`已设置为${colorType}颜色: ${color}`);
  };

  return (
    <div className="notch-test-page" style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '16px'
    }}>
      {/* 顶部安全区域指示器 */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        height: 'env(safe-area-inset-top, 0px)',
        backgroundColor: 'rgba(255, 0, 0, 0.3)',
        zIndex: 9998,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: 'white'
      }}>
        顶部安全区域: {`env(safe-area-inset-top)`}
      </div>

      {/* 左侧安全区域指示器 */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        bottom: 0,
        width: 'env(safe-area-inset-left, 0px)',
        backgroundColor: 'rgba(0, 255, 0, 0.3)',
        zIndex: 9998,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        writingMode: 'vertical-rl',
        fontSize: '12px',
        color: 'white'
      }}>
        左侧安全区域
      </div>

      {/* 右侧安全区域指示器 */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: 'env(safe-area-inset-right, 0px)',
        backgroundColor: 'rgba(0, 0, 255, 0.3)',
        zIndex: 9998,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        writingMode: 'vertical-rl',
        fontSize: '12px',
        color: 'white'
      }}>
        右侧安全区域
      </div>

      {/* 底部安全区域指示器 */}
      <div style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        height: 'env(safe-area-inset-bottom, 0px)',
        backgroundColor: 'rgba(255, 255, 0, 0.3)',
        zIndex: 9998,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: 'black'
      }}>
        底部安全区域: {`env(safe-area-inset-bottom)`}
      </div>

      {/* 主要内容区域 */}
      <div style={{ 
        paddingTop: 'max(env(safe-area-inset-top, 0px), 20px)',
        paddingBottom: 'env(safe-area-inset-bottom, 0px)',
        paddingLeft: 'env(safe-area-inset-left, 0px)',
        paddingRight: 'env(safe-area-inset-right, 0px)'
      }}>
        <Card title="刘海屏适配测试" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <div style={{ marginBottom: 8, fontWeight: 'bold' }}>平台信息:</div>
              <div style={{ fontSize: 14, color: '#666' }}>
                <div>平台: {Capacitor.getPlatform()}</div>
                <div>是否原生: {isNative ? '是' : '否'}</div>
                <div>状态栏支持: {isSupported ? '是' : '否'}</div>
                <div>当前页面颜色: {pageColor}</div>
                <div>当前状态栏颜色: {getCurrentColor()}</div>
                <div>用户代理: {navigator.userAgent.substring(0, 50)}...</div>
              </div>
            </div>

            <div>
              <div style={{ marginBottom: 8, fontWeight: 'bold' }}>安全区域信息:</div>
              <div style={{ fontSize: 12, fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                <div>顶部: env(safe-area-inset-top)</div>
                <div>底部: env(safe-area-inset-bottom)</div>
                <div>左侧: env(safe-area-inset-left)</div>
                <div>右侧: env(safe-area-inset-right)</div>
              </div>
            </div>

            {statusBarInfo && (
              <div>
                <div style={{ marginBottom: 8, fontWeight: 'bold' }}>状态栏信息:</div>
                <div style={{ fontSize: 12, fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                  <pre>{JSON.stringify(statusBarInfo, null, 2)}</pre>
                </div>
              </div>
            )}

            {isNative && (
              <div>
                <div style={{ marginBottom: 8, fontWeight: 'bold' }}>状态栏控制:</div>
                <Space wrap>
                  <Button size="small" onClick={handleSetLightTheme}>浅色主题</Button>
                  <Button size="small" onClick={handleSetDarkTheme}>深色主题</Button>
                  <Button size="small" onClick={handleShowStatusBar}>显示状态栏</Button>
                  <Button size="small" onClick={handleHideStatusBar}>隐藏状态栏</Button>
                </Space>
              </div>
            )}

            <div>
              <div style={{ marginBottom: 8, fontWeight: 'bold' }}>页面颜色测试:</div>
              <Space wrap>
                <Button size="small" onClick={() => handleTestPageColor('home-admin')}>主页(管理员)</Button>
                <Button size="small" onClick={() => handleTestPageColor('home-user')}>主页(用户)</Button>
                <Button size="small" onClick={() => handleTestPageColor('chat-customer')}>客户聊天</Button>
                <Button size="small" onClick={() => handleTestPageColor('chat-team')}>团队聊天</Button>
                <Button size="small" onClick={() => handleTestPageColor('chat-system')}>系统聊天</Button>
                <Button size="small" onClick={() => handleTestPageColor('default')}>默认颜色</Button>
              </Space>
            </div>

            <div style={{
              padding: 12,
              backgroundColor: '#e6f7ff',
              borderRadius: 4,
              border: '1px solid #91d5ff'
            }}>
              <div style={{ fontWeight: 'bold', color: '#1890ff', marginBottom: 4 }}>
                测试说明
              </div>
              <div style={{ fontSize: 14, color: '#666' }}>
                <ul style={{ paddingLeft: 20, margin: 0 }}>
                  <li>彩色边框显示安全区域范围</li>
                  <li>红色：顶部安全区域（刘海屏/状态栏）</li>
                  <li>绿色：左侧安全区域</li>
                  <li>蓝色：右侧安全区域</li>
                  <li>黄色：底部安全区域（Home指示器）</li>
                  <li>内容应该在安全区域内正常显示</li>
                </ul>
              </div>
            </div>
          </Space>
        </Card>

        {/* 测试内容填充 */}
        <Card title="内容测试区域" style={{ marginBottom: 16 }}>
          <div style={{ height: '200vh', background: 'linear-gradient(to bottom, #f0f0f0, #e0e0e0)' }}>
            <div style={{ padding: 20, textAlign: 'center' }}>
              <h3>滚动测试内容</h3>
              <p>这是一个很长的内容区域，用于测试滚动时安全区域的表现。</p>
              {Array.from({ length: 50 }, (_, i) => (
                <div key={i} style={{ padding: 10, borderBottom: '1px solid #ddd' }}>
                  测试内容行 {i + 1} - 检查内容是否被刘海屏或状态栏遮挡
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default NotchTestPage;
