import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import UserSignUpPage from './UserSignUpPage';
import { store } from '../../app/store';
import { ROUTE_BUSINESS_REGISTER } from '../../config/app/routes';

// 模拟导航函数
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// 模拟Toast组件
jest.mock('antd-mobile', () => {
  const actual = jest.requireActual('antd-mobile');
  return {
    ...actual,
    Toast: {
      show: jest.fn(),
    },
  };
});

// 模拟URL.createObjectURL和URL.revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

// 测试包装器
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('UserSignUpPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders register page correctly', () => {
    renderWithProviders(<UserSignUpPage />);
    
    // 验证页面标题渲染正确
    expect(screen.getByText('請輸入您的姓名')).toBeInTheDocument();
    
    // 验证姓名输入框渲染正确
    expect(screen.getByText('姓名')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入您的姓名')).toBeInTheDocument();
    
    // 验证继续按钮渲染正确
    expect(screen.getByText('繼續')).toBeInTheDocument();
    
    // 验证继续按钮初始状态应该是禁用的
    expect(screen.getByText('繼續').closest('button')).toBeDisabled();
  });
  
  test('validates name input with max 18 characters', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入您的姓名');
    
    // 输入超过18个字符的姓名
    fireEvent.change(nameInput, { target: { value: '这是一个超过十八个字符的非常长的姓名测试' } });
    fireEvent.blur(nameInput);
    
    // 验证显示错误信息
    expect(screen.getByText('姓名字數上限18個字元')).toBeInTheDocument();
    
    // 输入有效的姓名
    fireEvent.change(nameInput, { target: { value: '王小明' } });
    fireEvent.blur(nameInput);
    
    // 验证错误信息消失
    expect(screen.queryByText('姓名字數上限18個字元')).not.toBeInTheDocument();
  });
  
  test('validates empty name input', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入您的姓名');
    
    // 输入空姓名并失焦
    fireEvent.change(nameInput, { target: { value: '' } });
    fireEvent.blur(nameInput);
    
    // 验证显示错误信息
    expect(screen.getByText('請輸入您的姓名')).toBeInTheDocument();
  });
  
  test('validates avatar file size', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    // 创建一个大于2MB的文件
    const largeFile = new File(['x'.repeat(3 * 1024 * 1024)], 'large-image.jpg', { type: 'image/jpeg' });
    
    // 模拟文件选择
    fireEvent.change(fileInput, { target: { files: [largeFile] } });
    
    // 验证显示错误信息
    expect(screen.getByText('頭像請選擇小於2M的圖片')).toBeInTheDocument();
    
    // 创建一个小於2MB的文件
    const smallFile = new File(['x'.repeat(1 * 1024 * 1024)], 'small-image.jpg', { type: 'image/jpeg' });
    
    // 模拟文件选择
    fireEvent.change(fileInput, { target: { files: [smallFile] } });
    
    // 验证错误信息消失
    expect(screen.queryByText('頭像請選擇小於2M的圖片')).not.toBeInTheDocument();
  });
  
  test('validates avatar file type', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    // 创建一个非图片文件
    const textFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
    
    // 模拟文件选择
    fireEvent.change(fileInput, { target: { files: [textFile] } });
    
    // 验证显示错误信息
    expect(screen.getByText('請上傳圖片格式文件')).toBeInTheDocument();
  });
  
  test('enables continue button when name is valid', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入您的姓名');
    const continueButton = screen.getByText('繼續').closest('button');
    
    // 初始状态按钮应该是禁用的
    expect(continueButton).toBeDisabled();
    
    // 仅输入有效的姓名
    fireEvent.change(nameInput, { target: { value: '王小明' } });
    
    // 按钮应该启用，因为头像不再是必填项
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    });
  });
  
  test('shows first character of name as avatar placeholder', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入您的姓名');
    
    // 输入姓名
    fireEvent.change(nameInput, { target: { value: '王小明' } });
    
    // 验证显示首字
    expect(screen.getByText('王')).toBeInTheDocument();
  });
  
  test('handles continue button click and navigates to business register', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入您的姓名');
    const continueButton = screen.getByText('繼續').closest('button') as HTMLButtonElement;
    
    // 仅填写姓名
    fireEvent.change(nameInput, { target: { value: '王小明' } });
    
    // 等待按钮启用
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    });
    
    // 点击继续按钮
    fireEvent.click(continueButton);
    
    // 验证Toast被调用
    await waitFor(() => {
      expect(require('antd-mobile').Toast.show).toHaveBeenCalledWith(expect.objectContaining({
        content: '個人註冊成功',
      }));
    });
    
    // 验证导航到商务号注册页面
    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_BUSINESS_REGISTER);
  });
  
  test('can proceed without uploading avatar', async () => {
    renderWithProviders(<UserSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入您的姓名');
    const continueButton = screen.getByText('繼續').closest('button') as HTMLButtonElement;
    
    // 只填姓名，不上传头像
    fireEvent.change(nameInput, { target: { value: '王小明' } });
    
    // 按钮应可点击
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    });
    
    // 点击继续
    fireEvent.click(continueButton);
    
    // 验证导航到商务号注册页面
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(ROUTE_BUSINESS_REGISTER);
    });
  });
}); 