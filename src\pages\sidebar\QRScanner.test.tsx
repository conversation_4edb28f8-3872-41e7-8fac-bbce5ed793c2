import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import QRScanner from './QRScanner';

// Mock dependencies
jest.mock('@capacitor-mlkit/barcode-scanning', () => ({
  BarcodeScanner: {
    addListener: jest.fn(),
    startScan: jest.fn(),
    removeAllListeners: jest.fn(),
    stopScan: jest.fn(),
  },
  BarcodeFormat: {
    QrCode: 'QR_CODE',
  },
}));

jest.mock('antd-mobile', () => ({
  Toast: {
    show: jest.fn(),
  },
  Button: ({ 
    children, 
    className, 
    onClick, 
    disabled 
  }: { 
    children: React.ReactNode; 
    className?: string; 
    onClick?: () => void; 
    disabled?: boolean;
  }) => (
    <button className={className} onClick={onClick} disabled={disabled}>
      {children}
    </button>
  ),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('@/services/system/logService', () => ({
  logService: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@/services/platform/deviceService', () => ({
  default: {
    getPlatform: jest.fn(() => 'web'),
    isWeb: jest.fn(() => true),
    isNative: jest.fn(() => false),
  },
}));

// Mock barcode-detector polyfill
jest.mock('barcode-detector', () => ({
  BarcodeDetector: jest.fn().mockImplementation(() => ({
    detect: jest.fn().mockResolvedValue([]),
  })),
}));

// Mock navigator.mediaDevices
Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: jest.fn(() => [
        { stop: jest.fn() }
      ]),
    }),
  },
});

jest.mock('@/utils/cryptoUtil', () => ({
  CryptoUtil: {
    decryptApiResponse: jest.fn(),
  },
}));

jest.mock('@/utils/constantUtil', () => ({
  ConstantUtil: {
    QRCODE_SEPERATOR: '?code=',
    DEFAULT_SECRET_KEY: 'test-key',
  },
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('QRScanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render scanner container with header and input', () => {
    renderWithRouter(<QRScanner />);

    expect(screen.getByText('掃一掃')).toBeInTheDocument();
    expect(screen.getByText('送出')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入邀請碼')).toBeInTheDocument();
    expect(document.querySelector('.qr-scanner-container')).toBeInTheDocument();
    expect(document.querySelector('.scan-box')).toBeInTheDocument();
    expect(document.querySelector('.scanner-header')).toBeInTheDocument();
    expect(document.querySelector('.scanner-bottom')).toBeInTheDocument();
  });

  it('should have proper CSS classes for Figma design', () => {
    renderWithRouter(<QRScanner />);

    const container = document.querySelector('.qr-scanner-container');
    const scanBox = document.querySelector('.scan-box');
    const inputContainer = document.querySelector('.input-container');
    const inputWrapper = document.querySelector('.input-wrapper');

    expect(container).toBeInTheDocument();
    expect(scanBox).toBeInTheDocument();
    expect(inputContainer).toBeInTheDocument();
    expect(inputWrapper).toBeInTheDocument();
  });

  it('should render mask elements for overlay effect', () => {
    renderWithRouter(<QRScanner />);

    const maskTop = document.querySelector('.mask-top');
    const maskBottom = document.querySelector('.mask-bottom');
    const maskLeft = document.querySelector('.mask-left');
    const maskRight = document.querySelector('.mask-right');

    expect(maskTop).toBeInTheDocument();
    expect(maskBottom).toBeInTheDocument();
    expect(maskLeft).toBeInTheDocument();
    expect(maskRight).toBeInTheDocument();
  });
}); 