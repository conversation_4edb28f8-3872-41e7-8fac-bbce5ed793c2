/* 掃描器激活時的全局樣式 - 使用:global確保生效 */
:global(body.barcode-scanner-active) {
  background: transparent !important;
}

:global(html.barcode-scanner-active) {
  background: transparent !important;
}

/* 覆蓋App容器的背景色 */
:global(body.barcode-scanner-active .app) {
  background-color: transparent !important;
}

/* 覆蓋root容器的背景色 */
:global(body.barcode-scanner-active #root) {
  background: transparent !important;
}

/* 覆蓋狀態欄安全區域的背景色 */
:global(body.barcode-scanner-active .status-bar-safe-area) {
  background-color: transparent !important;
}

/* 覆蓋底部安全區域的背景色 */
:global(body.barcode-scanner-active .bottom-safe-area) {
  background-color: transparent !important;
}

/* 覆蓋安全區域應用容器的背景色 */
:global(body.barcode-scanner-active .safe-area-app) {
  background: transparent !important;
}

/* 掃描器容器 */
.qr-scanner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: transparent;  /* 容器透明，讓相機預覽可見 */
  z-index: 9999;
  pointer-events: none;  /* 允許相機預覽接收觸摸事件 */
}

/* 頭部樣式 - 白色背景 */
.scanner-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 60px;
  background: #ffffff;  /* 白色背景 */
  display: flex;
  align-items: center;
  padding: 0 16px;
  z-index: 10002;
  pointer-events: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);  /* 底部陰影 */
}

.back-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background: rgba(0, 0, 0, 0.1);  /* 黑色半透明懸停效果 */
  border-radius: 4px;
}

.back-button svg path {
  stroke: #333;  /* 黑色箭頭 */
}

.scanner-title {
  flex: 1;
  text-align: center;
  color: #333;  /* 黑色文字 */
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  margin-right: 40px; /* 平衡返回按鈕的寬度 */
}

/* 四個方向的遮罩 - 黑色背景 */
/* 上方遮罩 */
.mask-top {
  position: fixed;
  top: 60px;  /* 從頭部下方開始 */
  left: 0;
  width: 100vw;
  height: 36px;  /* 到掃描框頂部的36px間距 */
  background: #000000;  /* 純黑色背景 */
  z-index: 2;
  pointer-events: none;
}

/* 下方遮罩 - 從掃描框底部到輸入區域頂部，完整遮擋 */
.mask-bottom {
  position: fixed;
  top: calc(60px + 36px + 280px);  /* 頭部 + 上間距 + 掃描框高度 */
  left: 0;
  width: 100vw;
  height: 48px;  /* 增加高度確保完全遮擋到輸入區域 */
  background: #000000;  /* 純黑色背景 */
  z-index: 2;
  pointer-events: none;
}

/* 底部剩餘區域遮罩 */
.mask-bottom-extra {
  position: fixed;
  top: calc(60px + 36px + 280px + 48px + 80px);  /* 更新下方遮罩結束位置 */
  left: 0;
  width: 100vw;
  height: calc(100vh - 60px - 36px - 280px - 48px - 80px);  /* 調整剩餘高度 */
  background: #000000;  /* 純黑色背景 */
  z-index: 2;
  pointer-events: none;
}

/* 左側遮罩 */
.mask-left {
  position: fixed;
  top: calc(60px + 36px);  /* 從掃描框頂部開始 */
  left: 0;
  width: calc(50vw - 151.5px);  /* 計算左側遮罩寬度 (50vw - 303px/2) */
  height: 280px;  /* 與掃描框高度一致 */
  background: #000000;  /* 純黑色背景 */
  z-index: 2;
  pointer-events: none;
}

/* 右側遮罩 */
.mask-right {
  position: fixed;
  top: calc(60px + 36px);  /* 從掃描框頂部開始 */
  right: 0;
  width: calc(50vw - 151.5px);  /* 計算右側遮罩寬度 (50vw - 303px/2) */
  height: 280px;  /* 與掃描框高度一致 */
  background: #000000;  /* 純黑色背景 */
  z-index: 2;
  pointer-events: none;
}

/* 掃描框 - 與輸入框容器寬度一致 */
.scan-box {
  width: 303px;   /* 與輸入框容器內容寬度一致 (375px - 72px邊距) */
  height: 280px;  /* 保持高度 */
  border: 3px solid #fff;
  background: transparent;
  /*border-radius: 12px;*/
  position: fixed;  /* 使用fixed確保不被覆蓋 */
  left: 50%;
  transform: translateX(-50%);  /* 水平居中 */
  top: calc(60px + 36px);  /* 頭部高度 + 36px間距 */
  z-index: 10;   /* 在遮罩之上 */
  pointer-events: none;  /* 掃描框本身不攔截觸摸事件 */
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);  /* 白色光暈效果 */
  overflow: hidden;  /* 確保內部元素不溢出 */
}

/* 掃描框四角裝飾 */
.scan-box {
  position: relative;
  overflow: hidden;
}

.scan-box::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  width: 20px;
  height: 20px;
  border: 3px solid #1677FF;
  border-right: none;
  border-bottom: none;
}

.scan-box::after {
  content: '';
  position: absolute;
  top: -3px;
  right: -3px;
  width: 20px;
  height: 20px;
  border: 3px solid #1677FF;
  border-left: none;
  border-bottom: none;
}

/* 掃描框底部兩角 */
.scan-box .corner-bottom-left {
  content: '';
  position: absolute;
  bottom: -3px;
  left: -3px;
  width: 20px;
  height: 20px;
  border: 3px solid #1677FF;
  border-right: none;
  border-top: none;
}

.scan-box .corner-bottom-right {
  content: '';
  position: absolute;
  bottom: -3px;
  right: -3px;
  width: 20px;
  height: 20px;
  border: 3px solid #1677FF;
  border-left: none;
  border-top: none;
}

/* 掃描線動畫 */
.scan-box .scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1677FF, transparent);
  animation: scanLine 2s linear infinite;
}

@keyframes scanLine {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(246px);
  }
}

/* 底部輸入區域 - Figma 設計風格 */
.scanner-bottom {
  position: fixed;
  top: calc(60px + 36px + 280px + 48px);  /* 頭部 + 上間距 + 掃描框 + 下間距 */
  left: 0;
  width: 100vw;
  background-color: #000000;  /* 黑色背景 */
  z-index: 10002;
  pointer-events: auto;
  display: flex;
  justify-content: center;
  padding: 48px 0;  /* 調整垂直間距 */
}

.input-container {
  width: 100%;
  max-width: 375px; /* 最大寬度限制 */
  padding: 0 36px; /* 左右邊距 */
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px; /* 按照 Figma 設計中的間距 */
  width: 100%;
}

.code-input {
  flex: 1;
  height: 48px;
  border-radius: 4px;
  /* border: 1px solid #ffffff; */
  padding: 0 16px;
  font-size: 16px;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: #ffffff !important;
  color: #000;
  outline: none;
  max-width: calc(100% - 88px); /* 為發送按鈕預留空間 (76px按鈕 + 12px間距) */
}

.code-input::placeholder {
  color: #CCCCCC; /* 按照 Figma 設計中的佔位符顏色 */
  font-size: 16px;
  font-weight: 400;
}

/* .code-input:focus {
  border-color: #1677FF;
} */

/* 覆蓋 Ant Design Mobile 的按鈕樣式 */
.submit-button {
  padding: 8px 16px !important;
  font-size: 17px !important;
  font-weight: 400 !important;
  white-space: nowrap !important;
  border-radius: 4px !important;
  background-color: #1677FF !important;
  line-height: 1.4em !important;
  min-width: 76px !important; /* 增加最小寬度確保按鈕可見 */
  width: 76px !important; /* 固定寬度 */
  height: 48px !important; /* 與輸入框相同的高度 */
  flex-shrink: 0 !important; /* 防止收縮 */
}

.submit-button:disabled {
  background-color: #E5E5E5 !important;
  color: #999999 !important;
  cursor: not-allowed !important;
}

/* 移除掃描指示器 */

/* 響應式設計 */
@media (max-width: 480px) {
  .scan-box {
    width: calc(100vw - 48px); /* 小屏幕上與輸入框容器寬度一致 (24px邊距 × 2) */
    height: 260px;
  }

  .mask-left {
    width: calc(50vw - (100vw - 48px)/2); /* 動態計算左側遮罩寬度 */
    height: 260px;
  }

  .mask-right {
    width: calc(50vw - (100vw - 48px)/2); /* 動態計算右側遮罩寬度 */
    height: 260px;
  }

  /* 調整其他遮罩尺寸以適配小屏幕的掃描框 */
  .mask-bottom {
    top: calc(60px + 36px + 260px);
    height: 48px;
  }

  .scanner-bottom {
    top: calc(60px + 36px + 260px + 48px);
  }

  .mask-bottom-extra {
    top: calc(60px + 36px + 260px + 48px + 80px);
    height: calc(100vh - 60px - 36px - 260px - 48px - 80px);
  }

  .input-container {
    padding: 0 24px;
  }

  .code-input {
    max-width: calc(100% - 88px); /* 小屏幕上也保持為發送按鈕預留空間 */
  }
}

/* 扫描框内的Loading样式 */
.scan-box-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 24px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 10;
}

.scan-box-loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #333;
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .code-input {
    background-color: #ffffff !important;
    color: #000;
    border-color: #ffffff;
  }

  .code-input::placeholder {
    color: #CCCCCC;
  }

  .scan-box-loading {
    background-color: rgba(40, 40, 40, 0.95);
  }

  .scan-box-loading-text {
    color: #fff;
  }
}