import { AccountSwitchManager } from './accountSwitchManager';
import aileDBService from '../services/db/aileDBService';
import { getLocalStorage, setLocalStorage } from './storage';
import { ConstantUtil } from './constantUtil';

// Mock dependencies
jest.mock('../services/system/logService', () => ({
  logService: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('../services/db/aileDBService', () => ({
  isInitialized: jest.fn(),
  initForAccount: jest.fn(),
  saveToStore: jest.fn(),
  flushWriteQueue: jest.fn(),
  verifyStorageCapability: jest.fn(),
  close: jest.fn(),
}));

jest.mock('./storage', () => ({
  getLocalStorage: jest.fn(),
  setLocalStorage: jest.fn(),
}));

describe('AccountSwitchManager', () => {
  let accountSwitchManager: AccountSwitchManager;
  
  beforeEach(() => {
    accountSwitchManager = AccountSwitchManager.getInstance();
    jest.clearAllMocks();
  });

  describe('switchAccount', () => {
    it('should return false for empty accountId', async () => {
      const result = await accountSwitchManager.switchAccount('');
      expect(result).toBe(false);
    });

    it('should skip switching if account is the same', async () => {
      const accountId = 'test-account-123';
      (getLocalStorage as jest.Mock).mockReturnValue(accountId);
      
      const result = await accountSwitchManager.switchAccount(accountId);
      expect(result).toBe(true);
    });

    it('should successfully switch accounts with database verification', async () => {
      const currentAccountId = 'current-account';
      const newAccountId = 'new-account';
      
      (getLocalStorage as jest.Mock).mockReturnValue(currentAccountId);
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);
      (aileDBService.initForAccount as jest.Mock).mockResolvedValue(true);
      (aileDBService.verifyStorageCapability as jest.Mock).mockResolvedValue(true);
      (aileDBService.saveToStore as jest.Mock).mockResolvedValue(undefined);
      // (aileDBService.flushWriteQueue as jest.Mock).mockResolvedValue(undefined);
      
      const result = await accountSwitchManager.switchAccount(newAccountId);
      
      expect(result).toBe(true);
      expect(setLocalStorage).toHaveBeenCalledWith(ConstantUtil.ACCOUNT_ID_KEY, newAccountId);
      expect(aileDBService.initForAccount).toHaveBeenCalledWith(newAccountId);
      expect(aileDBService.verifyStorageCapability).toHaveBeenCalled();
      expect(aileDBService.close).toHaveBeenCalled();
    });

    it('should handle database initialization failure', async () => {
      const currentAccountId = 'current-account';
      const newAccountId = 'new-account';
      
      (getLocalStorage as jest.Mock).mockReturnValue(currentAccountId);
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);
      (aileDBService.initForAccount as jest.Mock).mockResolvedValue(false);
      (aileDBService.saveToStore as jest.Mock).mockResolvedValue(undefined);
      // (aileDBService.flushWriteQueue as jest.Mock).mockResolvedValue(undefined);
      
      const result = await accountSwitchManager.switchAccount(newAccountId);
      
      expect(result).toBe(false);
      expect(setLocalStorage).toHaveBeenCalledWith(ConstantUtil.ACCOUNT_ID_KEY, currentAccountId);
    });

    it('should handle storage verification failure gracefully', async () => {
      const currentAccountId = 'current-account';
      const newAccountId = 'new-account';
      
      (getLocalStorage as jest.Mock).mockReturnValue(currentAccountId);
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);
      (aileDBService.initForAccount as jest.Mock).mockResolvedValue(true);
      (aileDBService.verifyStorageCapability as jest.Mock).mockResolvedValue(false);
      (aileDBService.saveToStore as jest.Mock).mockResolvedValue(undefined);
      // (aileDBService.flushWriteQueue as jest.Mock).mockResolvedValue(undefined);
      
      const result = await accountSwitchManager.switchAccount(newAccountId);
      
      expect(result).toBe(true); // Should still succeed even if verification fails
      expect(aileDBService.verifyStorageCapability).toHaveBeenCalled();
    });
  });

  describe('forceLogout', () => {
    it('should successfully logout current account', async () => {
      const currentAccountId = 'current-account';
      
      (getLocalStorage as jest.Mock).mockReturnValue(currentAccountId);
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);
      (aileDBService.saveToStore as jest.Mock).mockResolvedValue(undefined);
      // (aileDBService.flushWriteQueue as jest.Mock).mockResolvedValue(undefined);
      
      const result = await accountSwitchManager.forceLogout();
      
      expect(result).toBe(true);
      expect(setLocalStorage).toHaveBeenCalledWith(ConstantUtil.ACCOUNT_ID_KEY, null);
      expect(aileDBService.close).toHaveBeenCalled();
    });

    it('should handle no current account gracefully', async () => {
      (getLocalStorage as jest.Mock).mockReturnValue(null);
      
      const result = await accountSwitchManager.forceLogout();
      
      expect(result).toBe(true);
    });
  });

  describe('getSwitchStatus', () => {
    it('should return current switch status', () => {
      const accountId = 'test-account';
      (getLocalStorage as jest.Mock).mockReturnValue(accountId);
      
      const status = accountSwitchManager.getSwitchStatus();
      
      expect(status).toEqual({
        switching: false,
        queueLength: 0,
        currentAccountId: accountId
      });
    });
  });
});
