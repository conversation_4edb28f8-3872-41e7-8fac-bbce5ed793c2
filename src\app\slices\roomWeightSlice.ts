import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from '@/types/aile.enum';
import { RoomWeightDto, WeightCalculationResult } from '@/types/room.types';
import { RoomWeightScoreUtil } from '@/utils/roomWeightScoreUtil';
import { AppThunk } from '../store';
import { logService } from '@/services/system/logService';

/**
 * 聊天室权重状态接口
 */
export interface RoomWeightState {
  /** 聊天室权重映射表 roomId -> WeightCalculationResult */
  roomWeights: Record<string, WeightCalculationResult>;
  /** 排序后的聊天室ID列表 */
  sortedRoomIds: string[];
  /** 是否正在计算权重 */
  isCalculating: boolean;
  /** 最后更新时间 */
  lastUpdated: number;
}

const initialState: RoomWeightState = {
  roomWeights: {},
  sortedRoomIds: [],
  isCalculating: false,
  lastUpdated: 0,
};

const roomWeightSlice = createSlice({
  name: 'roomWeight',
  initialState,
  reducers: {
    /**
     * 设置单个聊天室权重
     */
    setRoomWeight: (
      state,
      action: PayloadAction<{ roomId: string; weightResult: WeightCalculationResult }>
    ) => {
      const { roomId, weightResult } = action.payload;
      state.roomWeights[roomId] = weightResult;
      state.lastUpdated = Date.now();
      
      // 重新排序
      state.sortedRoomIds = Object.keys(state.roomWeights).sort((a, b) => {
        const scoreA = state.roomWeights[a].score;
        const scoreB = state.roomWeights[b].score;
        return RoomWeightScoreUtil.compareScores(scoreB, scoreA); // 降序排列
      });
    },

    /**
     * 批量设置聊天室权重
     */
    setRoomWeights: (
      state,
      action: PayloadAction<Record<string, WeightCalculationResult>>
    ) => {
      state.roomWeights = { ...state.roomWeights, ...action.payload };
      state.lastUpdated = Date.now();
      
      // 重新排序
      state.sortedRoomIds = Object.keys(state.roomWeights).sort((a, b) => {
        const scoreA = state.roomWeights[a].score;
        const scoreB = state.roomWeights[b].score;
        return RoomWeightScoreUtil.compareScores(scoreB, scoreA); // 降序排列
      });
    },

    /**
     * 移除聊天室权重
     */
    removeRoomWeight: (state, action: PayloadAction<string>) => {
      const roomId = action.payload;
      delete state.roomWeights[roomId];
      state.sortedRoomIds = state.sortedRoomIds.filter(id => id !== roomId);
      state.lastUpdated = Date.now();
    },

    /**
     * 批量移除聊天室权重
     */
    removeRoomWeights: (state, action: PayloadAction<string[]>) => {
      const roomIds = action.payload;
      roomIds.forEach(roomId => {
        delete state.roomWeights[roomId];
      });
      state.sortedRoomIds = state.sortedRoomIds.filter(id => !roomIds.includes(id));
      state.lastUpdated = Date.now();
    },

    /**
     * 设置计算状态
     */
    setCalculating: (state, action: PayloadAction<boolean>) => {
      state.isCalculating = action.payload;
    },

    /**
     * 清空所有权重
     */
    clearAllWeights: (state) => {
      state.roomWeights = {};
      state.sortedRoomIds = [];
      state.lastUpdated = Date.now();
    },

    /**
     * 重新排序聊天室
     */
    resortRooms: (state) => {
      state.sortedRoomIds = Object.keys(state.roomWeights).sort((a, b) => {
        const scoreA = state.roomWeights[a].score;
        const scoreB = state.roomWeights[b].score;
        return RoomWeightScoreUtil.compareScores(scoreB, scoreA); // 降序排列
      });
      state.lastUpdated = Date.now();
    },
  },
});

export const {
  setRoomWeight,
  setRoomWeights,
  removeRoomWeight,
  removeRoomWeights,
  setCalculating,
  clearAllWeights,
  resortRooms,
} = roomWeightSlice.actions;

// Thunk Actions

/**
 * 计算单个聊天室权重
 */
export const calculateRoomWeight = (
  roomId: string,
  roomWeightDto: RoomWeightDto
): AppThunk => async (dispatch) => {
  try {
    dispatch(setCalculating(true));
    
    const weightResult = RoomWeightScoreUtil.calculateScore(roomWeightDto);
    dispatch(setRoomWeight({ roomId, weightResult }));
    
    logService.debug('计算聊天室权重成功', { roomId, weightResult });
  } catch (error) {
    logService.error('计算聊天室权重失败', { roomId, error });
  } finally {
    dispatch(setCalculating(false));
  }
};

/**
 * 重新计算单个聊天室权重
 */
export const recalculateRoomWeight = (
  roomId: string,
  updatedRoomWeightDto: RoomWeightDto
): AppThunk => async (dispatch, getState) => {
  try {
    dispatch(setCalculating(true));
    
    const state = getState().roomWeight;
    const existingWeight = state.roomWeights[roomId];
    
    if (!existingWeight) {
      // 如果不存在原权重，直接计算新权重
      dispatch(calculateRoomWeight(roomId, updatedRoomWeightDto));
      return;
    }
    
    const weightResult = RoomWeightScoreUtil.reCalculateScore(
      existingWeight.score,
      updatedRoomWeightDto
    );
    dispatch(setRoomWeight({ roomId, weightResult }));
    
    logService.debug('重新计算聊天室权重成功', { roomId, weightResult });
  } catch (error) {
    logService.error('重新计算聊天室权重失败', { roomId, error });
  } finally {
    dispatch(setCalculating(false));
  }
};

/**
 * 批量计算聊天室权重
 */
export const calculateRoomWeights = (
  roomWeights: Record<string, RoomWeightDto>
): AppThunk => async (dispatch) => {
  try {
    dispatch(setCalculating(true));
    
    const weightResults: Record<string, WeightCalculationResult> = {};
    
    Object.entries(roomWeights).forEach(([roomId, roomWeightDto]) => {
      try {
        weightResults[roomId] = RoomWeightScoreUtil.calculateScore(roomWeightDto);
      } catch (error) {
        logService.error('计算单个聊天室权重失败', { roomId, error });
      }
    });
    
    dispatch(setRoomWeights(weightResults));
    
    logService.debug('批量计算聊天室权重成功', { 
      totalRooms: Object.keys(roomWeights).length,
      successCount: Object.keys(weightResults).length 
    });
  } catch (error) {
    logService.error('批量计算聊天室权重失败', { error });
  } finally {
    dispatch(setCalculating(false));
  }
};

/**
 * 更新聊天室P1权重
 */
export const updateRoomP1Weight = (
  roomId: string,
  p1: P1WeightEnum
): AppThunk => async (dispatch) => {
  dispatch(recalculateRoomWeight(roomId, { p1 }));
};

/**
 * 更新聊天室P2权重
 */
export const updateRoomP2Weight = (
  roomId: string,
  p2: P2WeightEnum
): AppThunk => async (dispatch) => {
  dispatch(recalculateRoomWeight(roomId, { p2 }));
};

/**
 * 更新聊天室P3权重
 */
export const updateRoomP3Weight = (
  roomId: string,
  p3: P3WeightEnum[]
): AppThunk => async (dispatch) => {
  dispatch(recalculateRoomWeight(roomId, { p3 }));
};

/**
 * 更新聊天室活跃时间
 */
export const updateRoomActiveTime = (
  roomId: string,
  timestamp?: number
): AppThunk => async (dispatch) => {
  dispatch(recalculateRoomWeight(roomId, { timestamp: timestamp ?? Date.now() }));
};

export default roomWeightSlice.reducer;
