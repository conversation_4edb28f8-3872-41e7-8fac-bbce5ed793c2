
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { DotLoading, Toast } from 'antd-mobile';
import './Sidebar.css';
import addIcon from '../../assets/icons/sidebar/add-icon.svg';
import userAddIcon from '../../assets/icons/sidebar/user-add-icon.svg';
import stateService from '@/services/stateService';
import { logService } from '@/services/system/logService';
import { SidebarPerformanceMonitor } from '@/utils/performanceUtils';
import { SidebarPerformanceTest } from '@/utils/sidebarPerformanceTest';
import { AvatarImage, SidebarPlaceholder } from '@/components/common';
import { useTenantRelations } from '@/hooks/useTenantRelations';
import { useSidebarPreloader } from '@/hooks/useSidebarPreloader';
import { RelationTenantVO } from '@/services/core/tenant/tenantTypes';
import { useTenantSwitcher } from '@/app/hooks/useTenantSwitcher';
import { useAppDispatch } from '@/app/hooks';
import { useEffect } from 'react';
import { applyTenantToken } from '@/app/slices/tenantSlice';
import { ROUTE_SIDEBAR_QR_SCANNER, generateRoute } from '@/config/app/routes';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [isSwitchingTenant, setIsSwitchingTenant] = useState<string | null>(null);
  const { switchTenant } = useTenantSwitcher();
  const dispatch = useAppDispatch();

  // 使用自定義 Hook 獲取租戶列表數據，优化加载策略
  const {
    tenantRelations,
    isLoading,
    isError,
    lastFetched,
    refreshTenantRelations
  } = useTenantRelations({
    // 侧边栏场景：不自动获取数据，由侧边栏控制
    autoFetch: false,
    showLoadingOnEmpty: false
  });

  // 获取预加载器状态
  const { isPreloaded, hasData: preloaderHasData } = useSidebarPreloader();

  // 獲取登入用戶資訊
  let loginUser = null;
  try {
    loginUser = stateService?.loginUser?.();
  } catch (error) {
    logService.error('獲取登入用戶信息失敗', { error });
  }
  const avatarId: string | null = loginUser?.avatarId ?? null;
  const userName = loginUser?.name || '';
  const currentAccountId = loginUser?.accountId || '';
  
  // 側邊欄開啟時的數據獲取策略優化
  useEffect(() => {
    if (isOpen) {
      // 开始性能监控
      SidebarPerformanceMonitor.startSidebarOpen();

      // 在开发环境中运行性能测试
      if (process.env.NODE_ENV === 'development') {
        SidebarPerformanceTest.testReduxStateAccess();
      }

      // 檢查數據是否需要刷新
      const now = Date.now();
      const dataAge = lastFetched ? now - lastFetched : Infinity;
      const hasData = tenantRelations.length > 0;

      logService.info('側邊欄打開，檢查數據狀態', {
        hasData,
        dataAge: dataAge === Infinity ? '無數據' : Math.floor(dataAge / 1000 / 60) + '分鐘',
        count: tenantRelations.length,
        isPreloaded,
        preloaderHasData
      });

      // 優化策略：
      // 1. 如果有數據，立即顯示，然後根據數據新鮮度決定是否後台刷新
      // 2. 如果沒有數據但正在加載，显示加载状态但不重复请求
      // 3. 如果沒有數據且未在加载，显示加载状态并请求数据
      if (hasData) {
        // 有數據時立即顯示，然後根據數據新鮮度決定是否後台刷新
        logService.info('側邊欄打開：立即顯示現有數據', { dataAge, count: tenantRelations.length });

        // 立即结束性能监控，因为已经有数据可以显示
        SidebarPerformanceMonitor.endSidebarOpen('cache', tenantRelations.length);

        // 根據數據新鮮度決定是否後台刷新
        if (dataAge > 10 * 60 * 1000) {
          // 數據超過10分鐘，後台靜默刷新
          logService.info('側邊欄打開：數據較舊，啟動後台刷新', { dataAge });
          // 使用 setTimeout 確保不阻塞 UI 渲染
          setTimeout(() => {
            refreshTenantRelations(true, false);
          }, 100);
        } else {
          // 數據較新，不需要刷新
          logService.info('側邊欄打開：數據較新，無需刷新', { dataAge });
        }
      } else if (isLoading) {
        // 正在加载中，不重复请求，只显示加载状态
        logService.info('側邊欄打開：數據正在加載中，等待完成');
      } else {
        // 沒有數據且未在加载，顯示加載狀態並請求數據
        logService.info('側邊欄打開：無數據且未在加載，開始請求數據');
        refreshTenantRelations(true, true);
      }
    }
  }, [isOpen, lastFetched]); // 添加 lastFetched 依賴以便正確判斷數據年齡

  // 监控后台数据刷新完成
  useEffect(() => {
    if (isOpen && !isLoading && tenantRelations.length > 0) {
      // 后台数据刷新完成，记录日志但不重复结束性能监控
      logService.debug('側邊欄後台數據刷新完成', { count: tenantRelations.length });
    }
  }, [isOpen, isLoading, tenantRelations.length]);
  
  // 排序租戶列表：將 accountId 與當前登錄賬號相同的放在第一位，其餘根據 createTime 倒序
  const myTeams = tenantRelations.filter(t => t.accountId === currentAccountId);
  const otherTeams = tenantRelations
    .filter(t => t.accountId !== currentAccountId)
    .sort((a, b) => (b.createTime || 0) - (a.createTime || 0));
  const sortedTenantRelations = [...myTeams, ...otherTeams];
  
  // 處理加入團隊點擊
  const handleJoinTeamClick = () => {
    logService.info('點擊加入其他團隊按鈕', {});
    onClose(); // 關閉側邊欄
    navigate(ROUTE_SIDEBAR_QR_SCANNER); // 導航到二維碼掃描頁面
  };

  // 處理添加成員點擊
  const handleAddMemberClick = (e: React.MouseEvent, tenant: RelationTenantVO) => {
    e.stopPropagation(); // 阻止事件冒泡，避免觸發團隊選擇
    logService.info('點擊添加成員按鈕', {
      tenantId: tenant.id,
      tenantName: tenant.name,
      avatarId: tenant.avatarId
    });
    onClose(); // 關閉側邊欄

    // 使用 generateRoute 生成路由，並通過state傳遞額外信息
    navigate(generateRoute.addMemberQRCode(tenant.id), {
      state: {
        tenantId: tenant.id,
        tenantName: tenant.name
      }
    });
  };

  // 處理團隊選擇
  const handleTeamSelect = async (tenant: RelationTenantVO) => {
    if (!tenant.id) return;

    const tenantId = tenant.id;

    // 如果是當前活躍的租戶，直接關閉側邊欄
    if (tenant.isLastTenant) {
      logService.info('選擇當前活躍租戶，無需切換', { tenantId });
      onClose();
      return;
    }

    // 設置切換狀態
    setIsSwitchingTenant(tenantId);

    try {
      logService.info('開始切換租戶', { tenantId, tenantName: tenant.name });

      // 調用 Redux thunk 切換租戶
      const resultAction = await dispatch(applyTenantToken(tenantId));

      if (resultAction.meta.requestStatus === 'fulfilled') {
        // 切換成功，更新本地狀態
        switchTenant(tenantId, {
          id: tenantId,
          name: tenant.name || '',
          shortName: tenant.shortName || '',
          code: tenant.code || '',
          type: tenant.type || '',
          description: tenant.description || '',
          avatarId: tenant.avatarId || '',
          accountId: tenant.accountId || ''
        });

        logService.info('租戶切換成功', {
          tenantId,
          tenantName: tenant.name
        });

        Toast.show({
          content: `已切換到 ${tenant.name || '團隊'}`,
          duration: 2000
        });

        // 關閉側邊欄
        onClose();
      } else {
        const error = (resultAction.payload as string) || '切換團隊失敗，請稍後再試';
        logService.error('租戶切換失敗', {
          tenantId,
          error
        });

        Toast.show({
          content: error,
          duration: 3000
        });
      }
    } catch (error) {
      logService.error('切換租戶時發生異常', {
        tenantId,
        error: error as Error
      });

      Toast.show({
        content: '切換團隊失敗，請稍後再試',
        duration: 3000
      });
    } finally {
      setIsSwitchingTenant(null);
    }
  };

  // 處理重試加載
  const handleRetryLoad = () => {
    // 顯示加載狀態並強制刷新
    refreshTenantRelations(true, true);
  };

  // 智能显示策略
  const hasData = tenantRelations.length > 0;
  const shouldShowPlaceholder = isLoading && !hasData;

  return (
    <div className={`sidebar ${isOpen ? 'open' : ''}`} onClick={onClose}>
      <div className="sidebar-content" onClick={(e) => e.stopPropagation()}>
        {/* 优先显示占位内容，提供即时可用的界面 */}
        {shouldShowPlaceholder ? (
          <SidebarPlaceholder userName={userName} avatarId={avatarId} />
        ) : (
          <>
            <div className="sidebar-user">
              <AvatarImage
                avatarId={avatarId}
                size="40"
                apiSize="s"
                className="sidebar-avatar"
                alt={userName}
                name={userName}
              />
              <div className="sidebar-username">{userName}</div>
            </div>

            <div className="sidebar-team-title">{t('團隊')}</div>

            <div className="sidebar-teams">
              {/* 只有和自己賬號相同的租戶才顯示 add 按鈕 */}
              {sortedTenantRelations.some(tenant => tenant.accountId === currentAccountId) && (
                <div className="sidebar-team-item">
                  <div className="sidebar-team-content" onClick={handleJoinTeamClick}>
                    <div className="sidebar-team-prefix">
                      <img src={addIcon} alt="add" className="sidebar-add-icon" />
                    </div>
                    <div className="sidebar-team-main">
                      <div className="sidebar-team-text">{t('加入其他團隊')}</div>
                    </div>
                  </div>
                </div>
              )}
          
          {isError && !isLoading && (
            <div className="sidebar-error" onClick={handleRetryLoad}>
              <span>{t('載入失敗，點擊重試')}</span>
            </div>
          )}
          
          {!isLoading && !isError && sortedTenantRelations.map((tenant: RelationTenantVO) => (
            <div 
              key={tenant.id} 
              className={`sidebar-team-item ${tenant.isLastTenant ? 'active' : ''} ${isSwitchingTenant === tenant.id ? 'switching' : ''}`}
              onClick={() => handleTeamSelect(tenant)}
            >
              <div className="sidebar-team-content">
                <AvatarImage 
                  avatarId={tenant.avatarId || null} 
                  size="40" 
                  className="sidebar-team-avatar" 
                  alt={tenant.name || ''} 
                  name={tenant.name || ''} 
                />
                <div className="sidebar-team-main">
                  <div className="sidebar-team-text">{tenant.name || ''}</div>
                </div>
                { tenant.unReadCount !== undefined && tenant.unReadCount > 0 && (
                  <div className="sidebar-team-badge">
                    <div className="sidebar-badge-dot"></div>
                  </div>
                )}
                {/* 只有和自己賬號相同的租戶才顯示 user add 圖標 */}
                {tenant.accountId === currentAccountId && (
                  <div className="sidebar-team-add" onClick={(e) => handleAddMemberClick(e, tenant)}>
                    <img src={userAddIcon} alt="user add" className="sidebar-user-add-icon" />
                  </div>
                )}
                {/* 顯示切換中的加載狀態 */}
                {isSwitchingTenant === tenant.id && (
                  <div className="sidebar-team-switching">
                    <DotLoading color='#1677FF' />
                  </div>
                )}
              </div>
            </div>
          ))}
          
              {!isLoading && !isError && sortedTenantRelations.length === 0 && (
                <div className="sidebar-empty">
                  <span>{t('暫無團隊數據')}</span>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};


export default Sidebar; 