.client-profile-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}

.client-profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 0 28px 0;
  background-color: rgba(0, 0, 0, 0.2);
  background-image: url('../../assets/images/client/profile-header-bg.jpg');
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
}

.client-profile-navbar {
  width: 375px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 12px 40px;
}

.client-profile-navbar-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-left: auto;
  gap: 16px;
  width: 97px;
}

.client-profile-close-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.client-profile-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.client-profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.client-profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.client-profile-name-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.client-profile-name {
  font-family: Arial;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.4em;
  text-align: center;
  color: #FFFFFF;
}

.client-profile-edit-icon {
  width: 20px;
  height: 20px;
}

.client-profile-action-buttons {
  display: flex;
  flex-direction: row;
  gap: 12px;
  margin-top: 12px;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 48px;
  background-color: #FFFFFF;
  border: 1px solid #EEEEEE;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
}

.action-button img {
  width: 16.67px;
  height: 16.67px;
}

.client-profile-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 24px 0 0;
  background-color: #F5F5F5;
  box-shadow: inset 0px -1px 0px 0px rgba(255, 255, 255, 1);
  flex: 1;
}

.client-profile-form-item {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  padding: 0 16px;
  gap: 3px;
}

.form-item-content {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  padding: 0 12px;
  border-bottom: 1px solid #EEEEEE;
  background-color: #FFFFFF;
  border-radius: 8px;
}

.form-item-main {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  gap: 4px;
  padding: 8px 0;
}

.form-item-title {
  font-family: SF Pro, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #333333;
}

.form-item-selector {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.client-tag {
  background-color: rgba(22, 119, 255, 0.15) !important;
  font-family: SF Pro, sans-serif !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  line-height: 1.4em !important;
  color: #1677FF !important;
  padding: 2px 4px !important;
  border-radius: 2px !important;
}

.form-item-note {
  font-family: SF Pro, sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
  color: #000000;
}

.client-profile-dropdown {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 12px;
  align-self: stretch;
  margin: 0 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
  border-radius: 8px;
}

.dropdown-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 0;
  width: 100%;
}

.dropdown-title {
  font-family: SF Pro, sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
  color: #333333;
  flex: 1;
}

.dropdown-arrow {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 0 0 4px;
  width: 20px;
  height: 16px;
}

.dropdown-arrow svg {
  width: 12.67px;
  height: 7.48px;
} 