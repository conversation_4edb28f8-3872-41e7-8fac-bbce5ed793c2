import CryptoJS from 'crypto-js';
import { logService } from '../services/system/logService';
import { ConstantUtil } from './constantUtil';

/**
 * 加密工具类
 * 提供常用的加密解密功能
 */
export class CryptoUtil {
  private static readonly DEFAULT_SECRET_KEY = ConstantUtil.DEFAULT_SECRET_KEY;

  /**
   * AES 加密
   * @param text 要加密的文本
   * @param secretKey 密钥（可选，使用默认密钥）
   * @returns 加密后的字符串
   */
  static encrypt(text: string, secretKey: string = this.DEFAULT_SECRET_KEY): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(text, secretKey).toString();
      logService.info('Text encrypted successfully');
      return encrypted;
    } catch (error) {
      logService.error('Failed to encrypt text:', error);
      throw error;
    }
  }

  /**
   * AES 解密
   * @param encryptedText 加密的文本
   * @param secretKey 密钥（可选，使用默认密钥）
   * @returns 解密后的字符串
   */
  static decrypt(encryptedText: string, secretKey: string = this.DEFAULT_SECRET_KEY): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedText, secretKey);
      const originalText = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!originalText) {
        throw new Error('Failed to decrypt text - invalid key or corrupted data');
      }
      
      logService.info('Text decrypted successfully');
      return originalText;
    } catch (error) {
      logService.error('Failed to decrypt text:', error);
      throw error;
    }
  }

  /**
   * MD5 哈希
   * @param text 要哈希的文本
   * @returns MD5 哈希值
   */
  static md5(text: string): string {
    try {
      const hash = CryptoJS.MD5(text).toString();
      logService.debug('MD5 hash generated');
      return hash;
    } catch (error) {
      logService.error('Failed to generate MD5 hash:', error);
      throw error;
    }
  }

  /**
   * SHA256 哈希
   * @param text 要哈希的文本
   * @returns SHA256 哈希值
   */
  static sha256(text: string): string {
    try {
      const hash = CryptoJS.SHA256(text).toString();
      logService.debug('SHA256 hash generated');
      return hash;
    } catch (error) {
      logService.error('Failed to generate SHA256 hash:', error);
      throw error;
    }
  }

  /**
   * 生成随机字符串
   * @param length 字符串长度
   * @returns 随机字符串
   */
  static generateRandomString(length: number = 32): string {
    try {
      const randomBytes = CryptoJS.lib.WordArray.random(length / 2);
      const randomString = randomBytes.toString();
      logService.debug('Random string generated');
      return randomString;
    } catch (error) {
      logService.error('Failed to generate random string:', error);
      throw error;
    }
  }

  /**
   * Base64 编码
   * @param text 要编码的文本
   * @returns Base64 编码字符串
   */
  static base64Encode(text: string): string {
    try {
      const encoded = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(text));
      logService.debug('Base64 encoding completed');
      return encoded;
    } catch (error) {
      logService.error('Failed to encode Base64:', error);
      throw error;
    }
  }

  /**
   * Base64 解码
   * @param encodedText Base64 编码的文本
   * @returns 解码后的字符串
   */
  static base64Decode(encodedText: string): string {
    try {
      const decoded = CryptoJS.enc.Base64.parse(encodedText).toString(CryptoJS.enc.Utf8);
      logService.debug('Base64 decoding completed');
      return decoded;
    } catch (error) {
      logService.error('Failed to decode Base64:', error);
      throw error;
    }
  }

  /**
   * API 响应数据解密
   * 使用 AES-ECB 模式解密后端响应数据
   * 流程：Base64 → AES解密 → UTF8 → JSON
   * @param encryptedData 加密的响应数据（Base64 编码）
   * @param secretKey 解密密钥，默认使用 '7706618877066188'
   * @returns 解密后的 JSON 对象
   */
  static decryptApiResponse<T = any>(encryptedData: string, secretKey: string = '7706618877066188'): T {
    try {
      logService.debug('开始解密 API 响应数据');
      
      // 1. Base64 解码为字节数组
      const encryptedBytes = CryptoJS.enc.Base64.parse(encryptedData);
      
      // 2. 使用 AES-ECB 模式解密
      const key = CryptoJS.enc.Utf8.parse(secretKey);
      const decryptedBytes = CryptoJS.AES.decrypt(
        { ciphertext: encryptedBytes } as any,
        key,
        {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
        }
      );
      
      // 3. 转换为 UTF8 字符串
      const decryptedText = decryptedBytes.toString(CryptoJS.enc.Utf8);
      
      if (!decryptedText) {
        throw new Error('解密失败：解密后的文本为空');
      }
      
      // 4. 解析 JSON
      const jsonData = JSON.parse(decryptedText);
      
     // logService.info('API 响应数据解密成功');
      return jsonData;
    } catch (error) {
      logService.error('API 响应数据解密失败:', error);
      throw new Error(`数据解密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * API 请求数据加密
   * 使用 AES-ECB 模式加密请求数据
   * 流程：JSON → UTF8 → AES加密 → Base64
   * @param data 要加密的数据对象
   * @param secretKey 加密密钥，默认使用 '7706618877066188'
   * @returns 加密后的 Base64 字符串
   */
  static encryptApiRequest(data: any, secretKey: string = '7706618877066188'): string {
    try {
      logService.debug('开始加密 API 请求数据');
      
      // 1. 转换为 JSON 字符串
      const jsonString = JSON.stringify(data);
      
      // 2. 使用 AES-ECB 模式加密
      const key = CryptoJS.enc.Utf8.parse(secretKey);
      const encrypted = CryptoJS.AES.encrypt(jsonString, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
      
      // 3. 转换为 Base64
      const encryptedBase64 = encrypted.toString();
      
     // logService.info('API 请求数据加密成功');
      return encryptedBase64;
    } catch (error) {
      logService.error('API 请求数据加密失败:', error);
      throw new Error(`数据加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

export default CryptoUtil; 