# Aile App v5.0.0-1 發版說明

## ✨ 新功能

### 1. **帳號系統與多團隊管理**
- **多元化登入**: 支持 LINE 快速登入及手機 OTP 驗證碼登入，滿足不同用戶偏好。
- **個性化用戶註冊**: 用戶可以設定個人姓名和頭像，建立個人化身份。
- **商務號註冊流程**: 提供完整的商務/團隊資訊填寫流程，包括公司名稱、Logo、網址、電話和地址，快速建立團隊。
- **多團隊無縫切換**: 側邊欄提供清晰的團隊列表，用戶可一鍵切換不同團隊（租戶），實現多重身份的順暢管理。
- **團隊邀請機制**:
    - **二維碼邀請**: 可生成專屬的團隊邀請二維碼，新成員掃碼即可申請加入。
    - **邀請碼邀請**: 提供 6 位數字邀請碼，方便在不同場景下分享和邀請。
- **管理員權限**: 區分管理員與普通成員，管理員擁有專屬的「帳號」分頁，提供更進階的管理功能。

### 2. **核心通訊與聊天功能**
- **全功能聊天室**: 實現了功能完善的聊天界面，目前僅支援純文字格式（不包含粗體、斜體等富文本格式）的發送與接收。
                同時支援表情符號（Emojis）的解析，支援在訊息中識別並將URL 連結轉換為可點擊的形式。
- **消息狀態實時同步**: 精準顯示消息的「發送中」、「已送達」、「已讀」狀態，並支持失敗重試和刪除。
- **智能消息分組與排序**:
    - **分頁管理**: 將聊天室分為「機器人」、「服務中」、「已處理」三類，方便客服人員高效處理不同階段的對話。
    - **高級權重排序**: 在「服務中」列表，根據會話狀態（新進件、本人服務中、他人服務中）、未讀消息、置頂、收藏、草稿等多維度進行智能排序，確保最緊急的對話優先處理。
    - **無限滾動加載**: 流暢加載歷史消息，優化用戶體驗。
- **多樣化聊天室類型**:
    - **客服聊天室**: 專為客服場景設計，包含服務時長計算、結束服務等功能。
    - **團隊聊天室**: 方便團隊內部成員溝通協作。
    - **個人/系統聊天室**: 用於個人筆記或接收系統通知。

### 3. **聯繫人與客戶管理**
- **字母索引聯繫人列表**: 採用 A-Z 快速索引，方便用戶快速查找和定位聯繫人。
- **新成員高亮**: 最近加入的聯繫人會置頂顯示並有特殊標記，便於快速識別。
- **聯繫人詳情**: （此處為根據現有結構推斷，未來可擴展）點擊聯繫人可查看詳細資料。

### 4. **應用框架與用戶體驗**
- **主頁標籤式導航**: 底部 TabBar 提供「消息」、「任務」、「客戶」、「備忘」、「帳號」（管理員可見）五大核心功能入口，結構清晰。
- **骨架屏與加載優化**: 在數據加載時顯示骨架屏（Skeleton Screen），並根據數據緩存狀態智能調整顯示策略，提升頁面加載的感知性能。
- **跨平台兼容**: 基於 Capacitor 框架，確保在 Web、iOS 和 Android 平台提供一致的功能和體驗。

## 🚀 性能優化
- **數據預加載與緩存**:
    - **首頁數據**: 智能檢測 Redux 和本地數據庫緩存，實現首頁秒開體驗。
    - **側邊欄**: 採用預加載和數據新鮮度檢查策略，打開側邊欄時立即顯示緩存數據，並在後台靜默刷新，提升響應速度。
- **滾動性能優化**: 聊天室和列表頁面採用了優化的滾動處理機制，保證大量數據下的流暢滾動。
- **圖片懶加載與緩存**: 頭像等圖片資源採用懶加載和本地緩存策略，減少不必要的網絡請求。

## 🐛 問題修復
- 此版本為初始功能構建版本，主要集中在新功能開發。

## 📝 已知問題
- 「任務」和「備忘」頁面為初步構建，核心功能待後續版本完善。
- 聊天室內的文件消息目前僅做基本信息展示，尚未實現文件的上傳、下載和預覽功能。 