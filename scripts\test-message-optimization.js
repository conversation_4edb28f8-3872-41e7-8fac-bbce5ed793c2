#!/usr/bin/env node

/**
 * 消息初始化优化验证脚本
 * 用于验证优化后的逻辑是否正确处理数据缺失问题
 */

const ConstantUtil = {
  DEFAULT_PAGE_SIZE: 20
};

/**
 * 模拟数据库查询结果
 */
function simulateDbQuery(roomId, messageCount, startSequence = 1) {
  const messages = [];
  for (let i = 0; i < messageCount; i++) {
    messages.push({
      id: `msg-${startSequence + i}`,
      roomId,
      sequence: startSequence + i,
      content: `Message ${startSequence + i}`,
      sendTime: Date.now() - (messageCount - i) * 1000,
      senderId: 'user-1'
    });
  }
  return messages;
}

/**
 * 分析数据完整性
 */
function analyzeDataIntegrity(roomId, lastSequence, dbMessages) {
  console.log(`\n🔍 分析房间 ${roomId} 的数据完整性`);
  console.log(`   lastSequence: ${lastSequence}`);
  console.log(`   DB消息数: ${dbMessages.length}`);

  if (dbMessages.length === 0) {
    console.log(`   ❌ 数据库无数据，需要从API获取所有消息 (1-${lastSequence})`);
    return {
      isComplete: false,
      missingCount: lastSequence,
      missingRange: { start: 1, end: lastSequence },
      apiCalls: Math.ceil(lastSequence / ConstantUtil.DEFAULT_PAGE_SIZE)
    };
  }

  const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0);
  const dbMaxSequence = Math.max(...sequences);
  const dbMinSequence = Math.min(...sequences);

  console.log(`   DB序列号范围: ${dbMinSequence} - ${dbMaxSequence}`);

  const isComplete = dbMaxSequence >= lastSequence;
  const missingCount = Math.max(0, lastSequence - dbMaxSequence);

  if (isComplete) {
    console.log(`   ✅ 数据完整，无需API调用`);
    return {
      isComplete: true,
      missingCount: 0,
      missingRange: null,
      apiCalls: 0
    };
  } else {
    const missingRange = { start: dbMaxSequence + 1, end: lastSequence };
    const apiCalls = Math.ceil(missingCount / ConstantUtil.DEFAULT_PAGE_SIZE);
    
    console.log(`   ❌ 数据缺失 ${missingCount} 条消息 (${missingRange.start}-${missingRange.end})`);
    console.log(`   🔄 需要 ${apiCalls} 次API调用来补充数据`);
    
    return {
      isComplete,
      missingCount,
      missingRange,
      apiCalls
    };
  }
}

/**
 * 测试场景
 */
const testScenarios = [
  {
    name: '场景1：数据完整',
    roomId: 'room-1',
    lastSequence: 20,
    dbMessages: simulateDbQuery('room-1', 20, 1), // 1-20
    expected: { apiCalls: 0, missingCount: 0 }
  },
  {
    name: '场景2：数据缺失（原问题场景）',
    roomId: 'room-2',
    lastSequence: 16,
    dbMessages: simulateDbQuery('room-2', 8, 1), // 1-8，缺失9-16
    expected: { apiCalls: 1, missingCount: 8 }
  },
  {
    name: '场景3：完全缺失',
    roomId: 'room-3',
    lastSequence: 20,
    dbMessages: [], // 无数据
    expected: { apiCalls: 1, missingCount: 20 }
  },
  {
    name: '场景4：大量缺失',
    roomId: 'room-4',
    lastSequence: 100,
    dbMessages: simulateDbQuery('room-4', 10, 1), // 1-10，缺失11-100
    expected: { apiCalls: 5, missingCount: 90 } // (100-10)/20 = 4.5，向上取整为5
  },
  {
    name: '场景5：部分缺失',
    roomId: 'room-5',
    lastSequence: 35,
    dbMessages: simulateDbQuery('room-5', 15, 1), // 1-15，缺失16-35
    expected: { apiCalls: 1, missingCount: 20 }
  }
];

/**
 * 运行测试
 */
function runTests() {
  console.log('🚀 开始消息初始化优化验证');
  console.log('=' .repeat(80));

  let passedTests = 0;
  let totalTests = testScenarios.length;

  testScenarios.forEach((scenario, index) => {
    console.log(`\n📋 ${scenario.name}`);
    console.log('─'.repeat(60));

    const result = analyzeDataIntegrity(
      scenario.roomId,
      scenario.lastSequence,
      scenario.dbMessages
    );

    // 验证结果
    const apiCallsMatch = result.apiCalls === scenario.expected.apiCalls;
    const missingCountMatch = result.missingCount === scenario.expected.missingCount;
    const testPassed = apiCallsMatch && missingCountMatch;

    console.log(`\n📊 验证结果:`);
    console.log(`   预期API调用: ${scenario.expected.apiCalls}, 实际: ${result.apiCalls} ${apiCallsMatch ? '✅' : '❌'}`);
    console.log(`   预期缺失数: ${scenario.expected.missingCount}, 实际: ${result.missingCount} ${missingCountMatch ? '✅' : '❌'}`);
    console.log(`   测试结果: ${testPassed ? '✅ 通过' : '❌ 失败'}`);

    if (testPassed) {
      passedTests++;
    }
  });

  console.log('\n' + '='.repeat(80));
  console.log(`🏁 测试完成: ${passedTests}/${totalTests} 通过`);

  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！优化逻辑正确。');
  } else {
    console.log('⚠️  部分测试失败，需要检查逻辑。');
  }

  // 输出优化前后对比
  console.log('\n💡 优化前后对比:');
  console.log('优化前问题:');
  console.log('  ❌ 使用 lastSequence 作为 beforeSequence，限制查询范围');
  console.log('  ❌ 无法检测数据缺失');
  console.log('  ❌ hasNextPage 判断不准确');
  
  console.log('\n优化后改进:');
  console.log('  ✅ 不使用 sequence 过滤，获取真正的最新消息');
  console.log('  ✅ 比较 lastSequence 与 DB 最大序列号，检测数据缺失');
  console.log('  ✅ 分批获取缺失数据，避免单次API调用过大');
  console.log('  ✅ 详细日志记录，便于问题排查');
}

/**
 * 模拟优化前的逻辑（有问题的版本）
 */
function simulateOldLogic(roomId, lastSequence) {
  console.log(`\n🔴 模拟优化前的逻辑 (有问题)`);
  console.log(`   roomId: ${roomId}, lastSequence: ${lastSequence}`);
  
  // 模拟使用 lastSequence 作为 beforeSequence 的查询
  // 这会导致只能查询到 sequence <= lastSequence 的消息
  const dbMessages = simulateDbQuery(roomId, 8, 1); // 假设只查到8条
  
  console.log(`   查询条件: sequence <= ${lastSequence}`);
  console.log(`   查询结果: ${dbMessages.length} 条消息`);
  console.log(`   问题: 无法检测到数据缺失，认为数据完整`);
  
  return dbMessages;
}

/**
 * 模拟优化后的逻辑
 */
function simulateNewLogic(roomId, lastSequence) {
  console.log(`\n🟢 模拟优化后的逻辑 (已修复)`);
  console.log(`   roomId: ${roomId}, lastSequence: ${lastSequence}`);
  
  // 不使用 sequence 过滤，获取真正的最新消息
  const dbMessages = simulateDbQuery(roomId, 8, 1); // 假设DB中有8条
  
  console.log(`   查询条件: 无 sequence 限制`);
  console.log(`   查询结果: ${dbMessages.length} 条消息`);
  
  const analysis = analyzeDataIntegrity(roomId, lastSequence, dbMessages);
  console.log(`   检测结果: ${analysis.isComplete ? '数据完整' : '数据缺失'}`);
  
  if (!analysis.isComplete) {
    console.log(`   修复操作: 从API获取缺失的 ${analysis.missingCount} 条消息`);
  }
  
  return dbMessages;
}

// 运行主测试
runTests();

// 演示优化前后的差异
console.log('\n' + '='.repeat(80));
console.log('📋 优化前后逻辑对比演示');
console.log('─'.repeat(80));

const demoRoomId = 'demo-room';
const demoLastSequence = 16;

simulateOldLogic(demoRoomId, demoLastSequence);
simulateNewLogic(demoRoomId, demoLastSequence);

console.log('\n✨ 验证完成！');
