import { <PERSON>le<PERSON>pi, API_ENDPOINTS } from '../../api/aileApi';
import { ConstantUtil } from '../../../utils/constantUtil';
import { logService } from '../../system/logService';
import CryptoUtil from '../../../utils/cryptoUtil';
import { userDao } from '../../dao';
import { getCurrentTenantId } from '../../../utils/tenantUtil';
import { getLocalStorage } from '../../../utils/storage';
import { envConfig } from '../../../config/env';
import deviceService from '../../platform/deviceService';

/**
 * 用戶介面定義
 */
export interface UserVO {
  id: string;
  name: string;
  createTime?: number;
  updateTime?: number;
  avatarId?: string | null;
  mood?: string | null;
  age?: number;
  gender?: string | null;
  birthday?: string | null;
  status?: string;
  accountId?: string;
  tenantId: string;
  channel?: string;
  personRoomId?: string;
  joinType?: string;
  openId?: string;
  isJoinAile?: boolean;
  isBindAile?: boolean;
  isCollectInfo?: boolean;
  homePagePicId?: string;
  [key: string]: any;
}

/**
 * 批量獲取用戶資料響應介面
 */
export interface UserItemsResponse {
  code?: string;
  data?: {
    items: UserVO[];
    totalCount?: number;
  };
  msg?: string;
  status?: number;
  success?: boolean;
  timeCost?: number;
  [property: string]: any;
}

/**
 * 批量獲取用戶資料請求介面
 */
export interface UserItemsRequest {
  userIds: string[];
  tenantId?: string;
}

/**
 * 單一獲取用戶資料響應介面
 */
export interface UserItemResponse {
  code?: string;
  data?: UserVO;
  msg?: string;
  status?: number;
  success?: boolean;
  timeCost?: number;
  [property: string]: any;
}

/**
 * 單一獲取用戶資料請求介面
 */
export interface UserItemRequest {
  userId: string;
  tenantId?: string;
}

/**
 * 用戶列表請求介面
 */
export interface UserListRequest {
  pageSize: number;
  refreshTime: number;
  tenantId?: string;
}

/**
 * 用戶列表響應介面
 */
export interface UserListResponse {
  code?: string;
  data?: {
    totalCount: number;
    count: number;
    hasNextPage: boolean;
    pageIndex: number;
    refreshTime: number;
    items: UserVO[];
  };
  msg?: string;
  status?: number;
  success?: boolean;
  timeCost?: number;
  [property: string]: any;
}

/**
 * 批量獲取用戶資料
 * @param params 包含用戶ID列表的請求參數
 * @returns 批量用戶資料響應
 */
export async function fetchUserItems(params: UserItemsRequest): Promise<UserItemsResponse> {
  try {

    const response = await AileApi.fetchUserItems(params);
    if (response?.data?.items && response.data.items.length > 0) {
        const userItems = response.data.items;
        const userItemsLength = userItems.length;
        try {
          await userDao.upsertUsers(userItems);
          logService.info('fetchUserItems 成功保存用戶資料到本地DB', { 
            userCount: userItemsLength 
          });
        } catch (dbError) {
          logService.error('fetchUserItems 保存用戶資料到本地DB失敗', { 
            error: dbError, 
            userCount: userItemsLength 
          });
        }
      }
    logService.info('fetchUserItems success', { params, response });
    return response;
  } catch (error) {
    logService.error('fetchUserItems error', { params, error });
    throw error;
  }
}

/**
 * 獲取用戶列表
 * @param params 分頁請求參數
 * @returns 用戶列表響應
 */
export async function fetchUserList(params: UserListRequest): Promise<UserListResponse> {
  try {
    // 如果未提供租戶ID，使用當前租戶ID
    if (!params.tenantId) {
      const tenantId = getCurrentTenantId();
      if (!tenantId) {
        logService.error('fetchUserList 未獲取到當前租戶Id', { params });
        throw new Error('未獲取到當前租戶Id');
      }
      params.tenantId = tenantId;
    }

    const response = await AileApi.fetchUserList(params) as UserListResponse;
    if (response?.data?.items && response.data.items.length > 0) {
      const userItems = response.data.items;
      const userItemsLength = userItems.length;
      try {
        await userDao.upsertUsers(userItems);
        logService.info('fetchUserList 成功保存用戶資料到本地DB', { 
          userCount: userItemsLength 
        });
      } catch (dbError) {
        logService.error('fetchUserList 保存用戶資料到本地DB失敗', { 
          error: dbError, 
          userCount: userItemsLength 
        });
      }
    }
    logService.info('fetchUserList success', { params, response });
    return response;
  } catch (error) {
    logService.error('fetchUserList error', { params, error });
    throw error;
  }
}

/**
 * 根據用戶ID獲取用戶資料
 * 首先嘗試從本地數據庫獲取，如果找不到則調用API
 * @param userId 用戶ID
 * @returns 用戶資料或null
 */
export async function getUserById(userId: string): Promise<UserVO | null> {
  try {
    // 首先從本地數據庫查詢
    const localUser = await userDao.getUserById(userId);
    if (localUser) {
      logService.info('getUserById 從本地數據庫獲取成功', { userId });
      return localUser;
    }

    // 本地未找到，調用API
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      logService.error('getUserById 未獲取到當前租戶Id', { userId });
      return null;
    }

    const params: UserItemRequest = {
      userId,
      tenantId
    };

    logService.info('getUserById 本地未找到，調用API', { params });
    const response = await AileApi.fetchUserItem(params) as UserItemResponse;
    if (response?.success && response.data) {
      // 保存到本地數據庫
      try {
        await userDao.upsertUsers([response.data]);
        logService.info('getUserById 成功保存用戶資料到本地DB', { userId });
      } catch (dbError) {
        logService.error('getUserById 保存用戶資料到本地DB失敗', {
          error: dbError,
          userId
        });
      }
      return response.data;
    }

    logService.info('getUserById API調用未返回有效數據', { userId, response });
    return null;
  } catch (error) {
    logService.error('getUserById error', { userId, error });
    throw error;
  }
}

/**
 * 同步獲取API基礎URL
 * @returns API基礎URL
 */
function getAPIBaseURL(): string {
  return envConfig.API_BASE_URL || 'http://localhost:3000';
}

/**
 * 同步獲取認證token
 * @returns 認證token或null
 */
function getAuthTokenSync(): string | null {
  return getLocalStorage<string | null>(ConstantUtil.TOKEN_KEY, null);
}

/**
 * 同步獲取設備信息
 * @returns 設備信息對象
 */
function getDeviceDataSync(): any {
  return deviceService.getDeviceDataHeader();
}

/**
 * 同步調用API獲取用戶資料
 * @param params 請求參數
 * @returns API響應或null
 */
function fetchUserItemSync(params: UserItemRequest): UserItemResponse | null {
  try {
    // 獲取API基礎URL
    const baseURL = getAPIBaseURL();
    const fullURL = baseURL + API_ENDPOINTS.USER.ITEM;

    // 使用同步XMLHttpRequest調用API
    const xhr = new XMLHttpRequest();
    xhr.open('POST', fullURL, false); // false表示同步

    // 設置請求頭
    xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');

    // 加密請求數據
    const encryptedData = CryptoUtil.encryptApiRequest(params);
    xhr.setRequestHeader('x-aile-siguare', CryptoUtil.sha256(encryptedData));

    // 添加認證token
    const token = getAuthTokenSync();
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.setRequestHeader('satoken', token);
    }

    // 添加設備信息
    const deviceData = getDeviceDataSync();
    if (deviceData) {
      xhr.setRequestHeader('deviceData', encodeURIComponent(JSON.stringify(deviceData)));
    }

    // 發送請求
    xhr.send(encryptedData);

    if (xhr.status === 200) {
      // const responseRaw = JSON.parse(xhr.responseText);
      const response = CryptoUtil.decryptApiResponse(xhr.responseText) as UserItemResponse;
      logService.info('fetchUserItemSync 成功', { params, response });
      return response;
    } else {
      logService.error('fetchUserItemSync HTTP錯誤', {
        status: xhr.status,
        statusText: xhr.statusText,
        params
      });
      return null;
    }
  } catch (error) {
    logService.error('fetchUserItemSync error', { params, error });
    return null;
  }
}


/**
 * 根據用戶ID同步獲取用戶資料
 * 首先嘗試從本地數據庫獲取，如果找不到則同步調用API
 * @param userId 用戶ID
 * @returns 用戶資料或null
 */
export function getUserByIdSync(userId: string): UserVO | null {
  try {
    // 本地未找到，同步調用API
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      logService.error('getUserByIdSync 未獲取到當前租戶Id', { userId });
      return null;
    }
    const params: UserItemRequest = {
      userId,
      tenantId
    };
    const response = fetchUserItemSync(params);

    if (response?.success && response.data) {
      return response.data;
    }

    logService.info('getUserByIdSync API調用未返回有效數據', { userId, response });
    return null;
  } catch (error) {
    logService.error('getUserByIdSync error', { userId, error });
    return null; // 同步版本不拋出異常，返回null
  }
}

export default {
  fetchUserItems,
  getUserById,
  getUserByIdSync,
  fetchUserList
};