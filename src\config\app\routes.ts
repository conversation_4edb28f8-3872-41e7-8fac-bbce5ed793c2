/**
 * 路由配置
 */

// 統一路由常量，與 ConstantUtil 保持一致
export const ROUTES = {
  // 認證相關
  AUTH: {
    LOGIN: '/login',
    OTP_LOGIN: '/login/otp-login',
    USER_SIGNUP: '/login/user-signup',
    BUSINESS_SIGNUP: '/login/business-signup',
    LINE_CALLBACK: '/callback',
  },
  
  // 主頁面
  HOME: {
    ROOT: '/',
    DASHBOARD: '/home',
  },
  
  // 聊天相關
  CHAT: {
    ROOM: '/chat-room/:roomId',
    CUSTOMER_ROOM: '/customer-chat/:roomId',
    SYSTEM_ROOM: '/system-chat-room/:roomId',
    TEAM_ROOM: '/home/<USER>/:roomId',
    CHAT_LIST: '/chat-list',
  },
  
  // 用户相關
  USER: {
    CLIENT_PROFILE: '/client/:clientId',
    PROFILE: '/profile',
    SETTINGS: '/settings',
    ADD_MEMBER_QRCODE: '/user/add-member-qrcode',
    ADD_MEMBER_QRCODE_WITH_TENANT: '/user/add-member-qrcode/:tenantId',
    QRCODE_SCAN: '/user/qrcode-scan',
  },

  // Sidebar相關
  SIDEBAR: {
    QR_SCANNER: '/sidebar/qrscan',
  },
  
  // 錯誤頁面
  ERROR: {
    NOT_FOUND: '/404',
    SERVER_ERROR: '/500',
    UNAUTHORIZED: '/401',
  },
} as const;

// 路由參數類型
export interface RouteParams {
  roomId?: string;
  clientId?: string;
  tenantId?: string;
}

// 生成路由的輔助函數
export const generateRoute = {
  chatRoom: (roomId: string) => `/chat-room/${roomId}`,
  customerRoom: (roomId: string) => `/customer-chat/${roomId}`,
  systemRoom: (roomId: string) => `/system-chat-room/${roomId}`,
  teamRoom: (roomId: string) => `/home/<USER>/${roomId}`,
  clientProfile: (clientId: string) => `/client/${clientId}`,
  addMemberQRCode: (tenantId: string) => `/user/add-member-qrcode/${tenantId}`,
};

// 導出單個常量以便直接使用
export const ROUTE_LOGIN = ROUTES.AUTH.LOGIN;
export const ROUTE_OTP_LOGIN = ROUTES.AUTH.OTP_LOGIN;
export const ROUTE_USER_SIGNUP = ROUTES.AUTH.USER_SIGNUP;
export const ROUTE_BUSINESS_REGISTER = ROUTES.AUTH.BUSINESS_SIGNUP;
export const ROUTE_LINE_CALLBACK = ROUTES.AUTH.LINE_CALLBACK;
export const ROUTE_HOME = ROUTES.HOME.DASHBOARD;
export const ROUTE_CLIENT_PROFILE = ROUTES.USER.CLIENT_PROFILE;
export const ROUTE_CHAT_LIST = ROUTES.CHAT.CHAT_LIST;
export const ROUTE_CHAT_ROOM = ROUTES.CHAT.ROOM;
export const ROUTE_SYSTEM_CHAT_ROOM = ROUTES.CHAT.SYSTEM_ROOM;
export const ROUTE_CUSTOMER_CHAT_ROOM = ROUTES.CHAT.CUSTOMER_ROOM;
export const ROUTE_TEAM_CHAT_ROOM = ROUTES.CHAT.TEAM_ROOM;
export const ROUTE_ADD_MEMBER_QRCODE = ROUTES.USER.ADD_MEMBER_QRCODE;
export const ROUTE_ADD_MEMBER_QRCODE_WITH_TENANT = ROUTES.USER.ADD_MEMBER_QRCODE_WITH_TENANT;
export const ROUTE_QRCODE_SCAN = ROUTES.USER.QRCODE_SCAN;
export const ROUTE_SIDEBAR_QR_SCANNER = ROUTES.SIDEBAR.QR_SCANNER;
