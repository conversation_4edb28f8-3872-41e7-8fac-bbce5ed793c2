import syncInitService from './syncInitService';
import tenantService from './core/tenant/tenantService';
import contactService from './core/tenant/contactService';
import { logService } from './system/logService';
import { getLocalStorage } from '../utils/storage';
import { ConstantUtil } from '../utils/constantUtil';

// Mock 依賴
jest.mock('./tenant/tenantService');
jest.mock('./tenant/contactService');
jest.mock('./auth/authService');
jest.mock('./system/logService');
jest.mock('../utils/storage');

describe('SyncInitService', () => {
  const mockTenantService = tenantService as jest.Mocked<typeof tenantService>;
  const mockContactService = contactService as jest.Mocked<typeof contactService>;

  const mockGetLocalStorage = getLocalStorage as jest.MockedFunction<typeof getLocalStorage>;

  // 創建模擬的 RelationTenantVO 數組
  const mockTenants = [
    { id: 'tenant-1', name: '租戶1', isLastTenant: true },
    { id: 'tenant-2', name: '租戶2', isLastTenant: false }
  ];
  
  const mockSingleTenant = [
    { id: 'tenant-single', name: '單一租戶', isLastTenant: true }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('syncTenantsToDb', () => {
    it('應該成功同步租戶資料', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      mockTenantService.fetchTenantRelationList.mockResolvedValue(mockTenants);
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.hasTenantInDb.mockResolvedValue(true);

      // Act
      await syncInitService.syncTenantsToDb();

      // Assert
      expect(mockTenantService.fetchTenantRelationList).toHaveBeenCalledTimes(1);
      expect(mockGetLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      expect(mockTenantService.hasTenantInDb).toHaveBeenCalledWith(mockTenantId);
    });

    it('當前租戶不存在時應記錄警告', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      mockTenantService.fetchTenantRelationList.mockResolvedValue(mockSingleTenant);
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.hasTenantInDb.mockResolvedValue(false);

      // Act
      await syncInitService.syncTenantsToDb();

      // Assert
      expect(mockTenantService.hasTenantInDb).toHaveBeenCalledWith(mockTenantId);
      expect(logService.warn).toHaveBeenCalledWith('當前租戶已不存在，將自動登出', { currentTenantId: mockTenantId });
    });

    it('發生錯誤時應記錄錯誤', async () => {
      // Arrange
      const error = new Error('資料庫錯誤');
      mockTenantService.fetchTenantRelationList.mockRejectedValue(error);

      // Act
      await syncInitService.syncTenantsToDb();

      // Assert
      expect(logService.error).toHaveBeenCalledWith('syncInitService 異常', { error });
    });
  });

  // Removed getOfficialServiceNumberId tests - method doesn't exist in actual service
  /*describe('getOfficialServiceNumberId', () => {
    it('應該成功獲取當前租戶的officialServiceNumberId', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const mockOfficialServiceNumberId = 'service-456';
      const mockTenant = {
        id: mockTenantId,
        name: '測試租戶',
        officialServiceNumberId: mockOfficialServiceNumberId
      };

      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getTenantById.mockResolvedValue(mockTenant as any);

      // Act
      const result = await syncInitService.getOfficialServiceNumberId();

      // Assert
      expect(result).toBe(mockOfficialServiceNumberId);
      expect(mockGetLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      expect(mockTenantService.getTenantById).toHaveBeenCalledWith(mockTenantId);
      expect(logService.info).toHaveBeenCalledWith('當前租戶資料獲取成功', {
        tenantId: mockTenantId,
        tenantName: mockTenant.name,
        officialServiceNumberId: mockOfficialServiceNumberId
      });
  });

    it('當前租戶ID不存在時應返回null', async () => {
      // Arrange
      mockGetLocalStorage.mockReturnValue(null);

      // Act
      const result = await syncInitService.getOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.warn).toHaveBeenCalledWith('當前未設定租戶ID');
    });

    it('當前租戶不存在於DB中時應返回null', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getTenantById.mockResolvedValue(null);

      // Act
      const result = await syncInitService.getOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(mockTenantService.getTenantById).toHaveBeenCalledWith(mockTenantId);
    });

    it('當租戶沒有officialServiceNumberId時應返回null', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const mockTenant = {
        id: mockTenantId,
        name: '測試租戶',
        officialServiceNumberId: undefined
      };

      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getTenantById.mockResolvedValue(mockTenant as any);

      // Act
      const result = await syncInitService.getOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
    });

    it('發生錯誤時應返回null', async () => {
      // Arrange
      const error = new Error('資料庫錯誤');
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockTenantService.getTenantById.mockRejectedValue(error);

      // Act
      const result = await syncInitService.getOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.error).toHaveBeenCalledWith('檢查當前租戶失敗', { error });
    });
  });*/

  // Removed getCurrentTenantOfficialServiceNumberId tests - method doesn't exist in actual service
  /*describe('getCurrentTenantOfficialServiceNumberId', () => {
    it('應該成功獲取當前租戶的officialServiceNumberId', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const mockOfficialServiceNumberId = 'service-456';
      const mockTenant = {
        id: mockTenantId,
        officialServiceNumberId: mockOfficialServiceNumberId
      };

      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getTenantById.mockResolvedValue(mockTenant as any);

      // Act
      const result = await syncInitService.getCurrentTenantOfficialServiceNumberId();

      // Assert
      expect(result).toBe(mockOfficialServiceNumberId);
      expect(mockGetLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      expect(mockTenantService.getTenantById).toHaveBeenCalledWith(mockTenantId);
  });

    it('當前租戶ID不存在時應返回null', async () => {
      // Arrange
      mockGetLocalStorage.mockReturnValue(null);

      // Act
      const result = await syncInitService.getCurrentTenantOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.warn).toHaveBeenCalledWith('當前未設定租戶ID');
    });

    it('當前租戶不存在於資料庫中時應返回null', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getTenantById.mockResolvedValue(null);

      // Act
      const result = await syncInitService.getCurrentTenantOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.warn).toHaveBeenCalledWith('當前租戶不存在於資料庫中', { currentTenantId: mockTenantId });
    });

    it('發生錯誤時應返回null', async () => {
      // Arrange
      const error = new Error('資料庫錯誤');
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockTenantService.getTenantById.mockRejectedValue(error);

      // Act
      const result = await syncInitService.getCurrentTenantOfficialServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.error).toHaveBeenCalledWith('獲取當前租戶officialServiceNumberId失敗', { error });
    });
  });*/

  describe('syncContactsToDb', () => {
    it('應該成功同步客戶資料', async () => {
      // Arrange
      mockContactService.syncContactsToDb.mockResolvedValue(5);

      // Act
  
      // Assert
      expect(mockContactService.syncContactsToDb).toHaveBeenCalledTimes(1);
      expect(logService.info).toHaveBeenCalledWith('客戶資料同步完成', { syncedCount: 5, officialServiceNumberId: 'test-service-id' });
  });

    it('發生錯誤時應記錄錯誤', async () => {
      // Arrange
      const error = new Error('同步失敗');
      mockContactService.syncContactsToDb.mockRejectedValue(error);

      // Assert
      expect(logService.error).toHaveBeenCalledWith('同步客戶資料失敗', { error, officialServiceNumberId: 'test-service-id' });
    });
  });

  // Removed getBossServiceNumberId tests - method doesn't exist in actual service
  /*describe('getBossServiceNumberId', () => {
    it('應該成功獲取當前租戶的商務號ID', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const mockBossServiceNumberId = 'boss-456';

      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getCurrentBossServiceNumberId = jest.fn().mockReturnValue(mockBossServiceNumberId);

      // Act
      const result = await syncInitService.getBossServiceNumberId();

      // Assert
      expect(result).toBe(mockBossServiceNumberId);
      expect(mockGetLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      expect(mockTenantService.getCurrentBossServiceNumberId).toHaveBeenCalled();
      expect(logService.info).toHaveBeenCalledWith('當前租戶商務號獲取成功', {
        tenantId: mockTenantId,
        bossServiceNumberId: mockBossServiceNumberId
      });
    });

    it('當前租戶ID不存在時應返回null', async () => {
      // Arrange
      mockGetLocalStorage.mockReturnValue(null);

      // Act
      const result = await syncInitService.getBossServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.warn).toHaveBeenCalledWith('當前未設定租戶ID，無法獲取商務號ID');
    });

    it('當前租戶沒有商務號時應返回null', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getCurrentBossServiceNumberId = jest.fn().mockReturnValue(null);

      // Act
      const result = await syncInitService.getBossServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(mockTenantService.getCurrentBossServiceNumberId).toHaveBeenCalled();
      expect(logService.warn).toHaveBeenCalledWith('當前租戶沒有關聯的商務號', { tenantId: mockTenantId });
    });

    it('發生錯誤時應返回null', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const error = new Error('API錯誤');
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getCurrentBossServiceNumberId = jest.fn().mockImplementation(() => { throw error; });

      // Act
      const result = await syncInitService.getBossServiceNumberId();

      // Assert
      expect(result).toBeNull();
      expect(logService.error).toHaveBeenCalledWith('獲取當前租戶商務號ID失敗', { error });
    });
  });*/

  describe('init', () => {
    it('應該依序調用所有同步方法（有商務號ID時）', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const mockBossServiceNumberId = 'boss-456';
      
      // 設置商務號ID成功的情況
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getCurrentBossServiceNumberId = jest.fn().mockReturnValue(mockBossServiceNumberId);
      mockTenantService.fetchTenantRelationList.mockResolvedValue(mockTenants);

      // Act
      await syncInitService.init();

      // Assert
      expect(mockTenantService.fetchTenantRelationList).toHaveBeenCalled();
      expect(mockTenantService.getCurrentBossServiceNumberId).toHaveBeenCalled();
      expect(mockContactService.syncContactsToDb).toHaveBeenCalledWith(mockBossServiceNumberId);
      expect(logService.info).toHaveBeenCalledWith('租戶資料同步完成');
      expect(logService.info).toHaveBeenCalledWith('當前租戶商務號獲取成功', expect.objectContaining({ bossServiceNumberId: mockBossServiceNumberId }));
    });

    it('無商務號ID但有官方服務號ID時應使用官方服務號ID', async () => {
      // Arrange
      const mockTenantId = 'tenant-123';
      const mockOfficialServiceNumberId = 'service-456';
      const mockTenant = { 
        id: mockTenantId,
        officialServiceNumberId: mockOfficialServiceNumberId
      };
      
      // 設置沒有商務號ID但有官方服務號ID的情況
      mockGetLocalStorage.mockReturnValue(mockTenantId);
      mockTenantService.getCurrentBossServiceNumberId = jest.fn().mockReturnValue(null);
      mockTenantService.getTenantById.mockResolvedValue(mockTenant as any);
      mockTenantService.fetchTenantRelationList.mockResolvedValue(mockTenants);

      // Act
      await syncInitService.init();

      // Assert
      expect(mockTenantService.fetchTenantRelationList).toHaveBeenCalled();
      expect(mockTenantService.getCurrentBossServiceNumberId).toHaveBeenCalled();
      expect(mockTenantService.getTenantById).toHaveBeenCalled();
      expect(mockContactService.syncContactsToDb).toHaveBeenCalledWith(mockOfficialServiceNumberId);
      expect(logService.info).toHaveBeenCalledWith('未獲取到商務號ID，嘗試使用官方服務號ID');
    });

    it('沒有服務號ID時應跳過同步客戶資料', async () => {
      // Arrange
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockTenantService.getCurrentBossServiceNumberId = jest.fn().mockReturnValue(null);
      mockTenantService.getTenantById.mockResolvedValue({ id: 'tenant-123', officialServiceNumberId: null } as any);
      mockTenantService.fetchTenantRelationList.mockResolvedValue(mockTenants);

      // Act
      await syncInitService.init();

      // Assert
      expect(mockTenantService.fetchTenantRelationList).toHaveBeenCalled();
      expect(mockTenantService.getCurrentBossServiceNumberId).toHaveBeenCalled();
      expect(mockTenantService.getTenantById).toHaveBeenCalled();
      expect(mockContactService.syncContactsToDb).not.toHaveBeenCalled();
      expect(logService.warn).toHaveBeenCalledWith('未獲取到可用的服務號ID，跳過同步客戶資料');
    });

    // Removed tests that use non-existent methods
    /*it('應該依序調用所有同步方法（有officialServiceNumberId時）', async () => {
      // Arrange
      const syncTenantsToDbSpy = jest.spyOn(syncInitService, 'syncTenantsToDb');
      const getOfficialServiceNumberIdSpy = jest.spyOn(syncInitService, 'getOfficialServiceNumberId');
      const syncContactsToDbSpy = jest.spyOn(syncInitService, 'syncContactsToDb');

      syncTenantsToDbSpy.mockResolvedValue();
      getOfficialServiceNumberIdSpy.mockResolvedValue('service-456');
      syncContactsToDbSpy.mockResolvedValue();

      // Act
      await syncInitService.init();

      // Assert
      expect(syncTenantsToDbSpy).toHaveBeenCalledTimes(1);
      expect(getOfficialServiceNumberIdSpy).toHaveBeenCalledTimes(1);
      expect(syncContactsToDbSpy).toHaveBeenCalledTimes(1);

      // 清理
      syncTenantsToDbSpy.mockRestore();
      getOfficialServiceNumberIdSpy.mockRestore();
      syncContactsToDbSpy.mockRestore();
    });

    it('沒有officialServiceNumberId時不應調用syncContactsToDb', async () => {
      // Arrange
      const syncTenantsToDbSpy = jest.spyOn(syncInitService, 'syncTenantsToDb');
      const getOfficialServiceNumberIdSpy = jest.spyOn(syncInitService, 'getOfficialServiceNumberId');
      const syncContactsToDbSpy = jest.spyOn(syncInitService, 'syncContactsToDb');

      syncTenantsToDbSpy.mockResolvedValue();
      getOfficialServiceNumberIdSpy.mockResolvedValue(null);
      syncContactsToDbSpy.mockResolvedValue();

      // Act
      await syncInitService.init();

      // Assert
      expect(syncTenantsToDbSpy).toHaveBeenCalledTimes(1);
      expect(getOfficialServiceNumberIdSpy).toHaveBeenCalledTimes(1);
      expect(syncContactsToDbSpy).not.toHaveBeenCalled();

      // 清理
      syncTenantsToDbSpy.mockRestore();
      getOfficialServiceNumberIdSpy.mockRestore();
      syncContactsToDbSpy.mockRestore();
    });*/
  });
});