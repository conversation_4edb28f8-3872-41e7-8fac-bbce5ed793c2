import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '@/app/hooks';
import { logout, restoreAuthState, restoreAuthToken, setAccount } from '@/app/slices/authSlice';
import { clearTenantState, applyTenantToken } from '@/app/slices/tenantSlice';
import { loadPersistedState } from '@/app/store';
import initAppService from '@/services/system/initAppService';
import { logService } from '@/services/system/logService';

export interface UseAppInitializationOptions {
  /** 是否自动初始化 */
  autoInit?: boolean;
  /** 初始化完成回调 */
  onInitComplete?: (success: boolean, error?: Error) => void;
}

export interface AppInitializationState {
  /** 是否正在初始化 */
  isInitializing: boolean;
  /** 初始化是否完成 */
  isInitialized: boolean;
  /** 初始化是否成功 */
  isSuccess: boolean;
  /** 初始化错误 */
  error: Error | null;
  /** 初始化进度信息 */
  progress: string;
}

/**
 * 应用初始化 Hook
 * 协调 Redux 状态管理和服务层的初始化流程
 */
export function useAppInitialization(options: UseAppInitializationOptions = {}) {
  const { autoInit = true, onInitComplete } = options;
  
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  
  const [state, setState] = useState<AppInitializationState>({
    isInitializing: false,
    isInitialized: false,
    isSuccess: false,
    error: null,
    progress: '准备初始化...'
  });

  /**
   * 执行应用初始化
   */
  const initialize = async (): Promise<boolean> => {
    if (state.isInitializing) {
      logService.warn('应用正在初始化中，跳过重复调用');
      return false;
    }

    setState(prev => ({
      ...prev,
      isInitializing: true,
      error: null,
      progress: '开始初始化应用...'
    }));

    try {
      logService.info('开始应用初始化流程');

      // 1. 恢复认证状态
      setState(prev => ({ ...prev, progress: '恢复认证状态...' }));
      await dispatch(restoreAuthState());
      logService.info('已恢复认证状态');

      // 2. 恢复 Redux 持久化状态
      setState(prev => ({ ...prev, progress: '恢复应用状态...' }));
      if (loadPersistedState) {
        loadPersistedState();
        logService.info('已恢复 Redux 持久化状态');
      }

      // 3. 调用核心初始化服务
      setState(prev => ({ ...prev, progress: '验证用户身份...' }));
      const initResult = await initAppService.initializeCore({
        navigate,
        loadPersistedState
      });

      if (!initResult.success) {
        // 初始化失败，处理不同的错误情况
        if (initResult.error?.message === 'no_token_found') {
          logService.info('未找到令牌，用户需要登录');
          setState(prev => ({ ...prev, progress: '需要用户登录' }));
        } else if (initResult.error?.message === 'invalid_token') {
          logService.info('令牌无效，清除认证状态');
          setState(prev => ({ ...prev, progress: '清除无效认证状态...' }));
          
          // 清除 Redux 中的认证状态
          dispatch(logout());
          dispatch(clearTenantState());
        }

        setState(prev => ({
          ...prev,
          isInitializing: false,
          isInitialized: true,
          isSuccess: false,
          error: initResult.error || new Error('初始化失败'),
          progress: '初始化失败'
        }));

        onInitComplete?.(false, initResult.error || new Error('初始化失败'));
        return false;
      }

      // 4. 初始化成功，更新 Redux 状态
      setState(prev => ({ ...prev, progress: '更新应用状态...' }));
      
      if (initResult.accountData) {
        // 恢复认证状态
        dispatch(restoreAuthToken(initResult.accountData.tokenId || null));
        dispatch(setAccount(initResult.accountData));
        logService.info('已恢复用户认证状态');
      }

      if (initResult.tenantId) {
        // 应用租户令牌
        setState(prev => ({ ...prev, progress: '设置租户信息...' }));
        try {
          await dispatch(applyTenantToken(initResult.tenantId)).unwrap();
          logService.info('已应用租户令牌', { tenantId: initResult.tenantId });
        } catch (tenantError) {
          logService.error('应用租户令牌失败', { error: tenantError, tenantId: initResult.tenantId });
          // 租户设置失败不影响整体初始化
        }
      }

      setState(prev => ({
        ...prev,
        isInitializing: false,
        isInitialized: true,
        isSuccess: true,
        error: null,
        progress: '初始化完成'
      }));

      logService.info('应用初始化完成');
      onInitComplete?.(true);
      return true;

    } catch (error) {
      const initError = error as Error;
      logService.error('应用初始化失败', { error: initError });

      setState(prev => ({
        ...prev,
        isInitializing: false,
        isInitialized: true,
        isSuccess: false,
        error: initError,
        progress: '初始化失败'
      }));

      onInitComplete?.(false, initError);
      return false;
    }
  };

  /**
   * 重置初始化状态
   */
  const reset = () => {
    setState({
      isInitializing: false,
      isInitialized: false,
      isSuccess: false,
      error: null,
      progress: '准备初始化...'
    });
  };

  // 自动初始化
  useEffect(() => {
    if (autoInit && !state.isInitialized && !state.isInitializing) {
      initialize();
    }
  }, [autoInit]);

  return {
    ...state,
    initialize,
    reset
  };
}

export default useAppInitialization;
