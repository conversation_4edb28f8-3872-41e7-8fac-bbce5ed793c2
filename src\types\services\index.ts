/**
 * 服务相关类型定义
 */

// 服务基础接口
export interface BaseService {
  initialize(): Promise<void>;
  dispose(): void;
}

// 认证服务类型
export interface AuthService extends BaseService {
  login(credentials: LoginCredentials): Promise<AuthResult>;
  logout(): Promise<void>;
  refreshToken(): Promise<string>;
  getCurrentUser(): Promise<User | null>;
}

// 登录凭据类型
export interface LoginCredentials {
  username?: string;
  password?: string;
  phone?: string;
  code?: string;
  token?: string;
}

// 认证结果类型
export interface AuthResult {
  success: boolean;
  token?: string;
  refreshToken?: string;
  user?: User;
  expiresIn?: number;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  tenantId?: string;
}

// 数据库服务类型
export interface DatabaseService extends BaseService {
  query<T = any>(sql: string, params?: any[]): Promise<T[]>;
  execute(sql: string, params?: any[]): Promise<number>;
  transaction<T>(callback: () => Promise<T>): Promise<T>;
}

// 缓存服务类型
export interface CacheService extends BaseService {
  get<T = any>(key: string): Promise<T | null>;
  set<T = any>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

// 日志服务类型
export interface LogService extends BaseService {
  debug(message: string, data?: any): void;
  info(message: string, data?: any): void;
  warn(message: string, data?: any): void;
  error(message: string, error?: Error | any): void;
}
