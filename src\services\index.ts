// 服务统一导出

import authService from './core/auth/authService';
export { authService };

// 统一导出其他服务
export * from './core/auth';
export * from './system';
// 导出 db 相关服务的具体模块而非整个目录
export { default as sqliteService } from './db/sqliteService';
export { default as aileDBService, closeAileDB, initAileDB } from './db/aileDBService';
export * from './core/tenant';
export * from './platform';
// 导出 chat 模块，避免 Type 名称冲突
export { messageService } from './core/chat';
export { fetchRoomServiceWeightSync, fetchRoomServiceRobotList, fetchRoomServiceEndList, fetchRoomItem, syncChatMember } from './core/chat/roomService';
export type { MessageData, SendMessageRequest, SendMessageResponse } from './core/chat/messageService';
export type { RoomVO, ChatMemberVO, RoomType, Status } from './core/chat/roomService.types';
export type { SessionStatus } from './core/chat/roomService';