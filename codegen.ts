// codegen.ts
import { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {

  schema: 'https://2f365eba9ba4.ngrok-free.app/graphql',

  documents: 'src/graphql/**/*.graphql', // <-- 指向 .graphql 檔案

  // 指定生成的型別檔案的路徑和名稱
  generates: {
    './src/gql/': { 
      preset: 'client', 
      plugins: [
        // 'typescript', // 這些插件會被 preset 包含，但明確列出可幫助理解
        // 'typescript-operations',
        // 'typescript-react-apollo',
      ],
      presetConfig: {
        gqlTagName: 'gql', // 確保 Code Generator 知道您的 GraphQL 標籤是 gql
      },
    },
    './graphql.schema.json': { // 可選：生成 GraphQL Schema 的 JSON 表示，有助於 IDE 支援
      plugins: ['introspection'],
    },
  },
};

export default config;