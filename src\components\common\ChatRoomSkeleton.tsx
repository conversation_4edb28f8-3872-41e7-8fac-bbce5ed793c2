import React from 'react';
import './ChatRoomSkeleton.css';

interface ChatRoomSkeletonProps {
  chatType?: 'customer' | 'system' | 'team' | 'my';
  showTasksBar?: boolean;
}

/**
 * 聊天室骨架屏组件
 * 模拟ChatRoom的完整布局结构
 */
const ChatRoomSkeleton: React.FC<ChatRoomSkeletonProps> = ({ 
  chatType = 'customer',
  showTasksBar = false
}) => {
  return (
    <div className="chat-room-skeleton">
      {/* Header 骨架 */}
      <div className="chat-skeleton-header">
        <div className="chat-skeleton-nav-left">
          <div className="skeleton-back-button"></div>
          <div className="skeleton-line skeleton-line-medium"></div>
        </div>
        <div className="chat-skeleton-nav-right">
          {chatType === 'customer' && (
            <>
              <div className="skeleton-nav-icon"></div>
              <div className="skeleton-nav-icon"></div>
            </>
          )}
        </div>
      </div>

      {/* Tasks Bar 骨架 - 仅客户聊天室显示 */}
      {showTasksBar && chatType === 'customer' && (
        <div className="chat-skeleton-tasks-bar">
          <div className="skeleton-task-item">
            <div className="skeleton-task-icon"></div>
            <div className="skeleton-line skeleton-line-short"></div>
          </div>
          <div className="skeleton-task-item">
            <div className="skeleton-task-icon"></div>
            <div className="skeleton-line skeleton-line-short"></div>
          </div>
          <div className="skeleton-task-item">
            <div className="skeleton-task-icon"></div>
            <div className="skeleton-line skeleton-line-short"></div>
          </div>
        </div>
      )}

      {/* Chat Content 骨架 */}
      <div className="chat-skeleton-content">
        {/* 模拟聊天消息 */}
        {Array.from({ length: 6 }).map((_, index) => {
          const isUserMessage = index % 3 === 0; // 每3条消息中有1条是用户消息

          if (isUserMessage) {
            // 用户消息 - 右对齐
            return (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginBottom: '16px',
                  width: '100%'
                }}
              >
                <div
                  style={{
                    width: '70%',
                    maxWidth: '70%',
                    backgroundColor: '#386591',
                    borderRadius: '12px',
                    padding: '12px',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  <div
                    className="skeleton-line"
                    style={{ width: '90%' }}
                  ></div>
                  {index % 2 === 0 && (
                    <div
                      className="skeleton-line"
                      style={{ width: '60%' }}
                    ></div>
                  )}
                </div>
              </div>
            );
          } else {
            // 其他消息 - 左对齐
            return (
              <div
                key={index}
                className="chat-skeleton-message other-message"
              >
                <div className="skeleton-message-avatar"></div>
                <div className="skeleton-message-content">
                  <div className="skeleton-line skeleton-line-long"></div>
                  {index % 2 === 0 && (
                    <div className="skeleton-line skeleton-line-medium"></div>
                  )}
                </div>
              </div>
            );
          }
        })}
      </div>

      {/* Input Area 骨架 */}
      <div className="chat-skeleton-input-area">
        <div className="chat-skeleton-toolbar">
          <div className="skeleton-toolbar-icon"></div>
          <div className="skeleton-toolbar-icon"></div>
          <div className="skeleton-toolbar-icon"></div>
        </div>
        <div className="skeleton-input-field">
          <div className="skeleton-line skeleton-line-input"></div>
        </div>
        <div className="skeleton-toolbar-icon"></div>
      </div>
    </div>
  );
};

export default ChatRoomSkeleton;
