import React, { useEffect, useState } from 'react';
import { downloadAndCacheAvatarImage } from '@/utils/avatarImageUtil';
import { useTranslation } from 'react-i18next';
import { logService } from '@/services/system/logService';
import { ConstantUtil } from '@/utils/constantUtil';
import { getUserForage } from '@/utils/userForage';
import stateService from '@/services/stateService';

export interface AvatarImageProps {
  avatarId: string | null;
  size?: number | string; // 支援自定義尺寸，預設 40。可以是數字、數字字符串或預設尺寸 ('XS'=24, 'S'=32, 'M'=40, 'L'=48, 'XL'=56)
  apiSize?: string; // API 調用時的尺寸參數，預設 's'
  name?: string;
  alt?: string;
  className?: string;
  fallbackSrc?: string;
}

const AVATAR_COLORS = [
  '#1ABC9C', '#3498DB', '#6699CC', '#F1C40F', '#8E44AD', '#B45B3E',
  '#E74C3C', '#D35400', '#479AC7', '#2C3E50', '#7F8C8D', '#336699',
  '#66CCCC', '#00B271'
];

function createAvatarNameWithOriginName(name: string): string {
  const text = name.trim();
  if (!text) return 'U';
  const separators = [' ', '-', '_', '(', '.'];
  for (const sep of separators) {
    if (text.includes(sep)) {
      const arr = text.split(sep).filter(Boolean);
      if (arr.length >= 2) {
        return (arr[0][0] + arr[1][0]).toUpperCase();
      }
    }
  }
  return text.slice(0, 2).toUpperCase();
}

function generateRandomColorOfAvatarName(name: string): string {
  const code = Array.from(name).reduce((acc, c) => acc + c.charCodeAt(0), 0);
  return AVATAR_COLORS[code % AVATAR_COLORS.length];
}

/**
 * 頭像圖片組件，支援快取與異步加載，avatarId 為 null 或加載中時顯示 name 首字
 */
export const AvatarImage: React.FC<AvatarImageProps> = React.memo(({
  avatarId,
  size = 40,
  apiSize = 's',
  name,
  alt,
  className,
  fallbackSrc
}) => {
  const { t } = useTranslation();
  const [src, setSrc] = useState<string | null>(null);
  const [loading, setLoading] = useState(false); // 默認不loading
  const [error, setError] = useState(false);

  // 计算实际像素尺寸的辅助函数
  const calculatePixelSize = (sizeValue: number | string): number => {
    if (typeof sizeValue === 'number') {
      return sizeValue;
    }

    const sizeStr = sizeValue || '40';
    const parsedSize = parseInt(sizeStr, 10);

    if (!isNaN(parsedSize)) {
      return parsedSize;
    }

    // 预设尺寸映射
    const sizeMap: { [key: string]: number } = {
      'XS': 24,
      'S': 32,
      'M': 40,
      'L': 48,
      'XL': 56
    };
    return sizeMap[sizeStr.toUpperCase()] || 40;
  };

  useEffect(() => {
    let mounted = true;
    if (!avatarId) {
      setSrc(null);
      setLoading(false);
      setError(false);
      return;
    }

    // 先快速檢查緩存
    const checkCacheAndLoad = async () => {
      const loginAccount = stateService.loginAccount();
      if (!loginAccount?.accountId) {
        if (mounted) setError(true);
        return;
      }

      const key = ConstantUtil.AVATAR_IMAGE_CACHE_KEY(avatarId, typeof size === 'number' ? String(size) : size);
      const forage = getUserForage(loginAccount.accountId);

      try {
        const cached = await forage.getItem<string>(key);
        if (cached && mounted) {
          // 緩存命中，直接設置圖片，不顯示loading
          setSrc(cached);
          setLoading(false);
          setError(false);
          return;
        }
      } catch (err) {
        // 緩存檢查失敗，繼續正常流程
        logService.warn('頭像緩存檢查失敗', { avatarId, error: err });
      }

      // 緩存未命中，開始loading並下載
      if (mounted) {
        setLoading(true);
        setError(false);
        setSrc(null);

        downloadAndCacheAvatarImage(avatarId, typeof size === 'number' ? String(size) : size, apiSize)
          .then((base64) => {
            if (!mounted) return;
            if (base64) {
              setSrc(base64);
            } else {
              setError(true);
            }
          })
          .catch((err) => {
            logService.error('頭像圖片組件加載失敗', { avatarId, size, apiSize, err });
            if (mounted) setError(true);
          })
          .finally(() => {
            if (mounted) setLoading(false);
          });
      }
    };

    checkCacheAndLoad();

    return () => {
      mounted = false;
    };
  }, [avatarId, size, apiSize]);

  const displayName = name && name.trim() ? name : '未定義';

  // 创建首字母头像
  const renderNameAvatar = () => {
    const initials = createAvatarNameWithOriginName(displayName);
    const bgColor = generateRandomColorOfAvatarName(initials);
    const px = calculatePixelSize(size);
    const fontSize = Math.round(px * 0.48);

    return (
      <div
        className={`avatar-fallback flex items-center justify-center rounded-full select-none ${className || ''}`}
        style={{ width: px, height: px, background: bgColor, fontSize }}
        aria-label={alt || displayName}
        data-testid="avatar-fallback"
      >
        {initials}
      </div>
    );
  };

  // 沒有頭像ID時顯示文字頭像
  if (!avatarId) {
    return renderNameAvatar();
  }

  // 加載中顯示文字頭像
  if (loading) {
    return renderNameAvatar();
  }

  // 加載錯誤時的處理
  if (error) {
    return fallbackSrc ? (
      <img src={fallbackSrc} alt={alt || t('common.avatar')}
        className={className} data-testid="avatar-fallback" />
    ) : (
      renderNameAvatar()
    );
  }

  // 有圖片時顯示真實頭像
  if (src) {
    const imgSize = calculatePixelSize(size);
    return (
      <img
        src={src}
        alt={alt || t('common.avatar')}
        className={className}
        style={{ width: imgSize, height: imgSize }}
        data-testid="avatar-image"
      />
    );
  }

  // 默認顯示文字頭像
  return renderNameAvatar();
});

export default AvatarImage; 