/**
 * @file aileApi.ts
 * @description 統一 API 層，集中管理所有 API 調用和 API 常量
 * <AUTHOR> Team
 */

import { httpService } from '../system/httpService';
import { CryptoUtil } from '../../utils/cryptoUtil';
import { firstValueFrom } from 'rxjs';

// ========================
// API 路徑常量
// ========================
export const API_ENDPOINTS = {
  // 認證相關
  AUTH: {
    LOGIN: '/openapi/account/login',
    OTP_SEND: '/openapi/account/checkcode/send',
    TOKEN_CHECK: '/openapi/account/token/check',
    ACCOUNT_LOGIN: '/account/v1/login',
    OTP_VALIDATE: '/account/otp/v1/validate',
    MOBILE_SET: '/account/v1/mobile/set',
  },
  
  // 租戶相關
  TENANT: {
    RELATION_LIST: '/openapi/tenant/relation/list',
    PERSON_CREATE: '/openapi/tenant/person/create',
    TOKEN_APPLY: '/openapi/token/apply',
    CONTACT_LIST: '/tenant/contact/v1/list',
    CONTACT_ITEM: '/openapi/tenant/contact/item/{id}',
    SN_PERSON_BOSS: '/tenant/servicenumber/v1/person/{tenantId}/boss',
    INVITATION_CREATE: '/tenant/invitation/v1/create',
    INVITATION_TRIGGER: '/openapi/tenant/invitation/trigger',
  },
  
  // 聊天相關
  CHAT: {
    MESSAGE_SEND: '/openapi/chat/message/send',
    MESSAGE_READ: '/openapi/chat/message/v2/read',
    ROOM_ITEM: '/openapi/chat/room/item',
    SYNC_MESSAGE: '/openapi/base/sync/message',
    FROM_APPOINT: '/openapi/chat/from/appoint',
    START_SERVICE: '/openapi/chat/service/start',
  },
  
  // 用戶相關
  USER: {
    ITEMS: '/openapi/user/items',
    ITEM: '/openapi/user/item',
    LIST: '/tenant/employee/v1/list',
    PROFILE_UPDATE: '/openapi/user/profile/update',
  },
  
  // 房間相關
  ROOM: {
    SERVICE_WEIGHT_SYNC: '/room/v1/service/weight/sync',
    SERVICE_ROBOT_LIST: '/room/v1/service/robot/list',
    SERVICE_END_LIST: '/room/v1/service/end/list',
  },
  
  // 基礎同步相關
  BASE: {
    SYNC_CHATMEMBER: '/openapi/base/sync/chatmember',
  },
  
  // 代理相關
  AGENT: {
    SERVICED: '/openapi/servicenumber/chatroom/agent/serviced',
    STOP: '/tenant/servicenumber/session/v1/agent/stop',
  },
  
  // 附件相關
  ATTACHMENT: {
    AVATAR_DOWNLOAD: '/openapi/base/attachment/avatar/download',
    AVATAR_BASE64: '/openapi/base/attachment/avatar/base64',
  },

  // 其他
  OTHER: {
    UPDADATE_TOKEN: '/api/push/update-token',
  },
} as const;

// ========================
// API 請求/響應類型定義
// ========================

// 認證相關類型
export interface OtpRequest {
  mobile: string;
  type?: string;
}

export interface OtpResponse {
  success: boolean;
  data?: {
    onceToken: string;
    expireTime: number;
  };
  msg?: string;
}

export interface LoginRequest {
  onceToken?: string;
  checkCode?: string;
  type: string;
  mobile?: string;
  password?: string;
}

export interface LoginResponse {
  success: boolean;
  data?: {
    authToken: string;
    refreshToken: string;
    account: any;
    user: any;
    tenants: any[];
  };
  msg?: string;
}

// 消息相關類型
export interface SendMessageRequest {
  roomId: string;
  content: string;
  type: string;
  tenantId: string;
}

export interface SendMessageResponse {
  success: boolean;
  data?: any;
  msg?: string;
}

// 租戶相關類型
export interface TenantRelationRequest {
  // 根據實際需求定義
}

export interface TenantRelationResponse {
  success: boolean;
  data?: any[];
  msg?: string;
}

// ========================
// 統一的 API 調用類
// ========================
export class AileApi {
  
  // ========================
  // 認證相關 API
  // ========================
  
  /**
   * 發送 OTP 驗證碼
   */
  static async sendOtp(request: OtpRequest): Promise<OtpResponse> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AUTH.OTP_SEND,
        request,
        { headers: { 'x-no-auth': 'true' } },
        true
      )
    );
    return CryptoUtil.decryptApiResponse<OtpResponse>(response);
  }

  /**
   * 用戶登錄
   */
  static async login(request: LoginRequest): Promise<LoginResponse> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AUTH.LOGIN,
        request,
        { headers: { 'x-no-auth': 'true' } },
        true
      )
    );
    return CryptoUtil.decryptApiResponse<LoginResponse>(response);
  }

  /**
   * 檢查賬戶 Token
   */
  static async checkAccountToken(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AUTH.TOKEN_CHECK,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 賬戶登錄
   */
  static async accountLogin(request: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AUTH.ACCOUNT_LOGIN,
        request,
        { headers: { 'x-no-auth': 'true' } },
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 驗證 OTP
   */
  static async validateOtp(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AUTH.OTP_VALIDATE,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 設置手機號碼
   */
  static async setMobile(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AUTH.MOBILE_SET,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  // ========================
  // 租戶相關 API
  // ========================
  
  /**
   * 獲取租戶關係列表
   */
  static async fetchTenantRelationList(params: TenantRelationRequest): Promise<TenantRelationResponse> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.RELATION_LIST,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse<TenantRelationResponse>(response);
  }

  /**
   * 創建個人租戶
   */
  static async createPersonTenant(formData: FormData): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<any>(
        API_ENDPOINTS.TENANT.PERSON_CREATE,
        formData,
        {},
        false // 不加密整體
      )
    );
    return response;
  }

  /**
   * 申請租戶 Token
   */
  static async applyTenantToken(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.TOKEN_APPLY,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取聯絡人列表
   */
  static async fetchContactList(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.CONTACT_LIST,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取聯絡人詳情
   */
  static async fetchContactItem(contactId: string, params: any): Promise<any> {
    const url = API_ENDPOINTS.TENANT.CONTACT_ITEM.replace('{id}', contactId);
    const response = await firstValueFrom(
      httpService.post<string>(
        url,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取商務號負責人
   */
  static async fetchServiceNumberPersonBoss(tenantId: string, params: any): Promise<any> {
    const url = API_ENDPOINTS.TENANT.SN_PERSON_BOSS.replace('{tenantId}', tenantId);
    const response = await firstValueFrom(
      httpService.post<string>(
        url,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 申請租戶Token
   */
  static async applyToken(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.TOKEN_APPLY,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 創建邀請碼
   */
  static async createInvitation(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.INVITATION_CREATE,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 創建租戶邀請
   */
  static async createTenantInvitation(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.INVITATION_CREATE,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }



  // ========================
  // 聊天相關 API
  // ========================

  /**
   * 發送消息
   */
  static async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.CHAT.MESSAGE_SEND,
        request,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse<SendMessageResponse>(response);
  }

  /**
   * 標記消息已讀
   */
  static async markMessageRead(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.CHAT.MESSAGE_READ,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取客戶最後進線資料
   */
  static async fromAppoint(request: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.CHAT.FROM_APPOINT,
        request,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 開始客戶服務
   */
  static async startService(request: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.CHAT.START_SERVICE,
        request,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取房間詳情
   */
  static async fetchRoomItem(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.CHAT.ROOM_ITEM,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  // ========================
  // 用戶相關 API
  // ========================

  /**
   * 批量獲取用戶資料
   */
  static async fetchUserItems(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.USER.ITEMS,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取用戶列表
   */
  static async fetchUserList(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.USER.LIST,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取單個用戶資料
   */
  static async fetchUserItem(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.USER.ITEM,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 更新用戶資料
   */
  static async updateUserProfile(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.USER.PROFILE_UPDATE,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 更新用戶資料（支援 FormData）
   */
  static async updateUserProfileWithFile(formData: FormData): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<any>(
        API_ENDPOINTS.USER.PROFILE_UPDATE,
        formData,
        {},
        false
      )
    );
    try {
      return CryptoUtil.decryptApiResponse(response);
    } catch {
      return response;
    }
  }

  // ========================
  // 房間相關 API
  // ========================

  /**
   * 同步房間服務權重
   */
  static async fetchRoomServiceWeightSync(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.ROOM.SERVICE_WEIGHT_SYNC,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取機器人房間列表
   */
  static async fetchRoomServiceRobotList(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.ROOM.SERVICE_ROBOT_LIST,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取結束服務房間列表
   */
  static async fetchRoomServiceEndList(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.ROOM.SERVICE_END_LIST,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  // ========================
  // 基礎同步相關 API
  // ========================

  /**
   * 同步聊天室成員
   */
  static async syncChatMember(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.BASE.SYNC_CHATMEMBER,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 同步消息
   */
  static async syncMessage(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.CHAT.SYNC_MESSAGE,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  // ========================
  // 代理相關 API
  // ========================

  /**
   * 代理服務完成
   */
  static async agentServiced(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AGENT.SERVICED,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 停止代理服務
   */
  static async agentStop(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.AGENT.STOP,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  // ========================
  // 附件相關 API
  // ========================

  /**
   * 下載頭像附件
   */
  static async downloadAvatar(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.ATTACHMENT.AVATAR_DOWNLOAD,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 獲取頭像 Base64
   */
  static async getAvatarBase64(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.ATTACHMENT.AVATAR_BASE64,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  /**
   * 觸發租戶邀請
   */
  static async triggerTenantInvitation(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<string>(
        API_ENDPOINTS.TENANT.INVITATION_TRIGGER,
        params,
        {},
        true
      )
    );
    return CryptoUtil.decryptApiResponse(response);
  }

  // ========================
  // 其他 API
  // ========================

  /**
   * 更新推送 Token
   */
  static async updatePushToken(params: any): Promise<any> {
    const response = await firstValueFrom(
      httpService.post<any>(
        API_ENDPOINTS.OTHER.UPDADATE_TOKEN,
        params,
        {},
        false
      )
    );
    return response;
  }
}
