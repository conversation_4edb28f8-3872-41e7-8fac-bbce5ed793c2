import { logService } from '@/services/system/logService';

/**
 * 性能监控工具类
 * 用于监控关键操作的性能指标
 */
export class PerformanceMonitor {
  private static timers: Map<string, number> = new Map();

  /**
   * 开始计时
   * @param label 计时标签
   */
  static start(label: string): void {
    this.timers.set(label, performance.now());
  }

  /**
   * 结束计时并记录
   * @param label 计时标签
   * @param context 额外上下文信息
   */
  static end(label: string, context?: Record<string, any>): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      logService.warn('性能监控：未找到对应的开始时间', { label });
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(label);

    logService.info(`性能监控：${label}`, {
      duration: Math.round(duration * 100) / 100 + 'ms',
      ...context
    });

    return duration;
  }

  /**
   * 监控异步操作
   * @param label 操作标签
   * @param operation 异步操作
   * @param context 额外上下文信息
   */
  static async monitor<T>(
    label: string,
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    this.start(label);
    try {
      const result = await operation();
      this.end(label, { success: true, ...context });
      return result;
    } catch (error) {
      this.end(label, { success: false, error: (error as Error).message, ...context });
      throw error;
    }
  }

  /**
   * 监控同步操作
   * @param label 操作标签
   * @param operation 同步操作
   * @param context 额外上下文信息
   */
  static measure<T>(
    label: string,
    operation: () => T,
    context?: Record<string, any>
  ): T {
    this.start(label);
    try {
      const result = operation();
      this.end(label, { success: true, ...context });
      return result;
    } catch (error) {
      this.end(label, { success: false, error: (error as Error).message, ...context });
      throw error;
    }
  }
}

/**
 * 侧边栏性能监控专用工具
 */
export class SidebarPerformanceMonitor {
  /**
   * 监控侧边栏打开性能
   */
  static startSidebarOpen(): void {
    PerformanceMonitor.start('sidebar_open');
  }

  /**
   * 结束侧边栏打开性能监控
   * @param dataSource 数据来源（cache/api/db）
   * @param itemCount 团队数量
   */
  static endSidebarOpen(dataSource: 'cache' | 'api' | 'db', itemCount: number): void {
    PerformanceMonitor.end('sidebar_open', {
      dataSource,
      itemCount,
      operation: 'sidebar_team_list_load'
    });
  }

  /**
   * 监控团队数据获取性能
   */
  static async monitorTeamDataFetch<T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    return PerformanceMonitor.monitor('team_data_fetch', operation, context);
  }
}

export default PerformanceMonitor;
