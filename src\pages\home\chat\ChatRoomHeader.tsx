import React from 'react';
import { ChatRoomType } from '../../../types/chat.types';
import './ChatRoomIcons.css';
import { 
  LeftOutline 
} from 'antd-mobile-icons';
import { MenuOutlined } from '@ant-design/icons';
import DeviceOutline from '@/assets/icons/chat/device-icon.svg';
import SearchOutline from '@/assets/icons/chat/search-outline.svg';

// 定义图标组件的属性
interface ChatRoomHeaderProps {
  type: ChatRoomType;
  title: string;
  onBackClick: () => void;
  badgeCount?: number;
  memberCount?: number;
}

// 聊天室头部组件
const ChatRoomHeader: React.FC<ChatRoomHeaderProps> = ({
  type,
  title,
  onBackClick,
  badgeCount = 0,
  memberCount = 0,
}) => {
  // CSS类前缀
  const prefix = type === ChatRoomType.MY ? '' : `${type}-`;
  
  // 获取带前缀的类名
  const getClassName = (baseClass: string) => {
    return `base-${baseClass} ${prefix}${baseClass}`;
  };
  
  return (
    <div className={getClassName('chat-navbar')}>
      <div className={getClassName('chat-navbar-left')}>
        <div className={`chat-icon back-icon ${getClassName('chat-back-button')}`} onClick={onBackClick}>
          <LeftOutline />
        </div>
        {type !== ChatRoomType.TEAM && <div className={getClassName('chat-room-title')}>{title}</div>}
        {type === ChatRoomType.TEAM && memberCount > 0 && (
          <div className="team-member-count">{memberCount}</div>
        )}
      </div>
      {type === ChatRoomType.TEAM && <div className={getClassName('chat-room-title-center')}>{title}</div>}
      <div className={getClassName('chat-navbar-right')}>
        {/* 根据聊天室类型显示不同的图标 */}
        {type === ChatRoomType.CUSTOMER && (
          <div className="icon-container search-container">
            <img src={SearchOutline} className="chat-icon navbar-icon search-outline-icon" />
          </div>
        )}
        {type === ChatRoomType.MY && (
          <>
            <div className="icon-container search-container">
              <img src={SearchOutline} className="chat-icon navbar-icon search-icon" />
            </div>
            <div className="icon-container device-container">
              <img src={DeviceOutline} className="chat-icon navbar-icon device-icon" />
            </div>
          </>
        )}
        {type === ChatRoomType.TEAM && (
          <>
            <div className="icon-container search-container">
              <img src={SearchOutline} className="chat-icon navbar-icon search-outline-icon" />
            </div>
            <div className="icon-container menu-container">
              <MenuOutlined className="chat-icon navbar-icon menu-icon" />
            </div>
          </>
        )}
        {type !== ChatRoomType.TEAM && (
          <div className="icon-container menu-container">
            <MenuOutlined className="chat-icon navbar-icon menu-icon" />
            {badgeCount > 0 && type === ChatRoomType.MY && (
              <div className="badge-content">
                <span>{badgeCount}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatRoomHeader; 