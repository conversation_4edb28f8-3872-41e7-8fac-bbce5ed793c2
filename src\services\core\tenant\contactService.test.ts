import contactService from './contactService';
import aileDBService from '../../db/aileDBService';
// TODO: 更新测试以使用 AileApi 而不是直接的 httpService
// import httpService from '../../system/httpService';
import { logService } from '../../system/logService';
import { ConstantUtil } from '../../../utils/constantUtil';
import { getLocalStorage } from '../../../utils/storage';
import { firstValueFrom } from 'rxjs';
import type { Contact } from '../../db/initSql';
import CryptoUtil from '../../../utils/cryptoUtil';

// Mock dependencies
jest.mock('../../db/aileDBService');
jest.mock('../../system/httpService');
jest.mock('../../system/logService');
jest.mock('../../../utils/storage');
jest.mock('rxjs');
jest.mock('../../../utils/cryptoUtil');

const mockAileDBService = aileDBService as jest.Mocked<typeof aileDBService>;
// TODO: 更新测试以使用 AileApi 而不是直接的 httpService
// const mockHttpService = httpService as jest.Mocked<typeof httpService>;
const mockLogService = logService as jest.Mocked<typeof logService>;
const mockGetLocalStorage = getLocalStorage as jest.MockedFunction<typeof getLocalStorage>;
const mockFirstValueFrom = firstValueFrom as jest.MockedFunction<typeof firstValueFrom>;
const mockCryptoUtil = CryptoUtil as jest.Mocked<typeof CryptoUtil>;

describe('ContactService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchTenantContactList', () => {
    it('應該成功獲取客戶列表（非加密響應）', async () => {
      const mockResponse = {
        status: 0,
        success: true,
        data: {
          items: [
            {
              id: 'contact-1',
              name: '測試客戶',
              email: '<EMAIL>',
              phone: '0912345678',
              createTime: Date.now(),
              updateTime: Date.now()
            }
          ],
          count: 1,
          hasNextPage: false,
          pageIndex: 0,
          refreshTime: Date.now()
        },
        msg: '成功',
        code: '0'
      };

      mockFirstValueFrom.mockResolvedValue(mockResponse);

      const result = await contactService.fetchTenantContactList({
        pageIndex: 0,
        pageSize: 20,
        serviceNumberId: 'service-123'
      });

      expect(result).toEqual(mockResponse);
      // TODO: 更新测试以使用 AileApi 而不是直接的 httpService
      // expect(mockHttpService.post).toHaveBeenCalledWith(
      //   ConstantUtil.API_TENANT_CONTACT_LIST,
      //   {
      //     pageIndex: 0,
      //     pageSize: 20,
      //     direction: 'asc',
      //     orderBy: 'id',
      //     refreshTime: 0,
      //     serviceNumberId: 'service-123'
      //   }
      // );
      expect(mockLogService.info).toHaveBeenCalledWith(
        'API 返回非加密數據，直接使用'
      );
      expect(mockLogService.info).toHaveBeenCalledWith(
        '從API獲取客戶列表成功',
        expect.objectContaining({
          count: 1,
          timeCost: expect.any(Number)
        })
      );
    });

    it('應該成功解密並獲取客戶列表（加密響應）', async () => {
      const encryptedResponse = {
        data: 'encrypted-data-string' // 模擬加密的字符串響應
      };

      const decryptedResponse = {
        status: 0,
        success: true,
        data: {
          items: [
            {
              id: 'contact-1',
              name: '測試客戶',
              email: '<EMAIL>',
              phone: '0912345678',
              createTime: Date.now(),
              updateTime: Date.now()
            }
          ],
          count: 1,
          hasNextPage: false,
          pageIndex: 0,
          refreshTime: Date.now()
        },
        msg: '成功',
        code: '0'
      };

      mockFirstValueFrom.mockResolvedValue(encryptedResponse);
      mockCryptoUtil.decryptApiResponse.mockReturnValue(decryptedResponse);

      const result = await contactService.fetchTenantContactList({
        serviceNumberId: 'service-123'
      });

      expect(result).toEqual(decryptedResponse);
      expect(mockCryptoUtil.decryptApiResponse).toHaveBeenCalledWith('encrypted-data-string');
      expect(mockLogService.info).toHaveBeenCalledWith(
        'API 返回加密數據，正在進行解密'
      );
      expect(mockLogService.info).toHaveBeenCalledWith(
        '從API獲取客戶列表成功',
        expect.objectContaining({
          count: 1
        })
      );
    });

    it('應該處理API錯誤', async () => {
      const error = new Error('Network error');
      mockFirstValueFrom.mockRejectedValue(error);

      const result = await contactService.fetchTenantContactList();

      expect(result).toEqual({
        status: -1,
        success: false,
        data: {
          items: [],
          count: 0,
          hasNextPage: false,
          pageIndex: 0,
          refreshTime: 0
        },
        msg: 'Network error',
        code: '-1'
      });
      expect(mockLogService.error).toHaveBeenCalledWith(
        '從API獲取客戶列表失敗',
        expect.objectContaining({
          error,
          params: expect.any(Object)
        })
      );
    });
  });

  describe('getContactById', () => {
    it('應該成功獲取客戶詳情', async () => {
      const mockContact: Contact = {
        id: 'contact-1',
        name: '測試客戶',
        email: '<EMAIL>',
        createTime: Date.now(),
        updateTime: Date.now()
      };

      mockAileDBService.get.mockResolvedValue(mockContact);

      const result = await contactService.getContactById('contact-1');

      expect(result).toEqual(mockContact);
      expect(mockAileDBService.get).toHaveBeenCalledWith(
        'SELECT * FROM Contact WHERE id = ?',
        ['contact-1']
      );
    });

    it('應該處理客戶不存在的情況', async () => {
      mockAileDBService.get.mockResolvedValue(null);

      const result = await contactService.getContactById('non-existent');

      expect(result).toBeNull();
    });

    it('應該處理數據庫錯誤', async () => {
      const error = new Error('Database error');
      mockAileDBService.get.mockRejectedValue(error);

      const result = await contactService.getContactById('contact-1');

      expect(result).toBeNull();
      expect(mockLogService.error).toHaveBeenCalledWith(
        '獲取客戶詳情失敗',
        expect.objectContaining({
          error,
          id: 'contact-1'
        })
      );
    });
  });

  describe('saveContact', () => {
    const mockContact: Contact = {
      id: 'contact-1',
      name: '測試客戶',
      email: '<EMAIL>',
      phone: '0912345678',
      isJoinAile: true,
      isBindAile: false,
      languages: ['ZH_TW', 'EN_US'],
      createTime: Date.now(),
      updateTime: Date.now()
    };

    it('應該成功更新現有客戶', async () => {
      // Mock 客戶已存在
      mockAileDBService.get.mockResolvedValue(mockContact);
      mockAileDBService.run.mockResolvedValue({ changes: 1, lastInsertRowid: 1 } as any);

      const result = await contactService.saveContact(mockContact);

      expect(result).toBe(true);
      expect(mockAileDBService.run).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE Contact SET'),
        expect.arrayContaining(['contact-1'])
      );
    });

    it('應該成功插入新客戶', async () => {
      // Mock 客戶不存在
      mockAileDBService.get.mockResolvedValue(null);
      mockAileDBService.run.mockResolvedValue({ changes: 1, lastInsertRowid: 1 } as any);

      const result = await contactService.saveContact(mockContact);

      expect(result).toBe(true);
      expect(mockAileDBService.run).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO Contact'),
        expect.arrayContaining([
          'contact-1',
          '測試客戶',
          '<EMAIL>',
          '0912345678',
          1, // isJoinAile converted to 1
          0, // isBindAile converted to 0
          JSON.stringify(['ZH_TW', 'EN_US']) // languages array stringified
        ])
      );
    });

    it('應該處理保存失敗', async () => {
      const error = new Error('Save failed');
      mockAileDBService.get.mockRejectedValue(error);

      const result = await contactService.saveContact(mockContact);

      expect(result).toBe(false);
      expect(mockLogService.error).toHaveBeenCalledWith(
        '保存客戶信息失敗',
        expect.objectContaining({
          error,
          contact: mockContact
        })
      );
    });
  });

  describe('saveContactsToDb', () => {
    const mockContact: Contact = {
      id: 'contact-1',
      name: '測試客戶',
      email: '<EMAIL>',
      createTime: Date.now(),
      updateTime: Date.now()
    };

    it('應該成功保存多個客戶', async () => {
      const mockContacts = [
        mockContact,
        { ...mockContact, id: 'contact-2', name: '測試客戶2' }
      ];

      // 模擬 saveContact 方法成功
      jest.spyOn(contactService, 'saveContact').mockResolvedValue(true);

      const result = await contactService.saveContactsToDb(mockContacts);

      expect(result).toBe(2);
      expect(contactService.saveContact).toHaveBeenCalledTimes(2);
      expect(mockLogService.info).toHaveBeenCalledWith(
        '客戶信息保存完成',
        expect.objectContaining({
          totalCount: 2,
          successCount: 2
        })
      );
    });

    it('應該處理空列表', async () => {
      const result = await contactService.saveContactsToDb([]);

      expect(result).toBe(0);
      expect(mockLogService.warn).toHaveBeenCalledWith('沒有客戶信息需要保存');
    });

    it('應該處理保存失敗', async () => {
      const mockContacts = [
        mockContact,
        { ...mockContact, id: 'contact-2', name: '測試客戶2' }
      ];

      // 模擬 saveContact 方法失敗
      jest.spyOn(contactService, 'saveContact').mockResolvedValue(false);

      const result = await contactService.saveContactsToDb(mockContacts);

      expect(result).toBe(0);
      expect(contactService.saveContact).toHaveBeenCalledTimes(2);
    });

    it('應該處理整體錯誤', async () => {
      const error = new Error('Batch save error');
      jest.spyOn(contactService, 'saveContact').mockRejectedValue(error);

      const result = await contactService.saveContactsToDb([mockContact]);

      expect(result).toBe(0);
      expect(mockLogService.error).toHaveBeenCalledWith(
        '批量保存客戶信息失敗',
        expect.objectContaining({ error })
      );
    });
  });

  describe('syncContactsToDb', () => {
    const mockApiResponse = {
      status: 0,
      success: true,
      data: {
        items: [
          {
            id: 'contact-1',
            name: '客戶1',
            email: '<EMAIL>',
            createTime: Date.now(),
            updateTime: Date.now()
          },
          {
            id: 'contact-2',
            name: '客戶2',
            email: '<EMAIL>',
            createTime: Date.now(),
            updateTime: Date.now()
          }
        ],
        count: 2,
        hasNextPage: false,
        pageIndex: 0,
        refreshTime: 1234567890
      },
      msg: '成功',
      code: '0'
    };

    const mockTenant = { officialServiceNumberId: 'service-123' };

    it('應該成功同步客戶數據', async () => {
      // 模擬 API 調用返回
      jest.spyOn(contactService, 'fetchTenantContactList').mockResolvedValue(mockApiResponse);
      
      // 模擬 saveContactsToDb 方法
      jest.spyOn(contactService, 'saveContactsToDb').mockResolvedValue(2);
      
      // 模擬獲取當前租戶
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockAileDBService.get.mockImplementation((sql, _params) => {
        if (sql.includes('SELECT officialServiceNumberId FROM Tenant')) {
          return Promise.resolve(mockTenant);
        }
        return Promise.resolve(null);
      });

      // 模擬 saveRefreshTime 方法
      jest.spyOn(contactService as any, 'saveRefreshTime').mockResolvedValue(true);

      const result = await contactService.syncContactsToDb();

      expect(result).toBe(2);
      expect(mockGetLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      expect(mockAileDBService.get).toHaveBeenCalledWith(
        'SELECT officialServiceNumberId FROM Tenant WHERE id = ?',
        ['tenant-123']
      );
      expect(contactService.fetchTenantContactList).toHaveBeenCalledWith({
        serviceNumberId: 'service-123',
        refreshTime: 0
      });
      expect(contactService.saveContactsToDb).toHaveBeenCalledWith(mockApiResponse.data.items);
      expect((contactService as any).saveRefreshTime).toHaveBeenCalledWith(
        'service-123',
        mockApiResponse.data.refreshTime
      );
    });

    it('應該處理租戶沒有officialServiceNumberId的情況', async () => {
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockAileDBService.get.mockResolvedValue({ officialServiceNumberId: null });

      const result = await contactService.syncContactsToDb();

      expect(result).toBe(0);
      expect(mockLogService.warn).toHaveBeenCalledWith(
        '當前租戶沒有設置 officialServiceNumberId，無法同步客戶數據',
        { tenantId: 'tenant-123' }
      );
    });

    it('應該處理沒有當前租戶的情況', async () => {
      mockGetLocalStorage.mockReturnValue(null);

      const result = await contactService.syncContactsToDb();

      expect(result).toBe(0);
      expect(mockLogService.warn).toHaveBeenCalledWith('沒有當前租戶，無法同步客戶數據');
    });

    it('應該處理API返回空數據的情況', async () => {
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockAileDBService.get.mockResolvedValue(mockTenant);
      
      // API 返回空數據
      jest.spyOn(contactService, 'fetchTenantContactList').mockResolvedValue({
        ...mockApiResponse,
        data: {
          ...mockApiResponse.data,
          items: []
        }
      });

      const result = await contactService.syncContactsToDb();

      expect(result).toBe(0);
      expect(mockLogService.info).toHaveBeenCalledWith('API返回0個客戶，無需同步');
    });

    it('應該處理API錯誤', async () => {
      mockGetLocalStorage.mockReturnValue('tenant-123');
      mockAileDBService.get.mockResolvedValue(mockTenant);
      
      const error = new Error('API error');
      jest.spyOn(contactService, 'fetchTenantContactList').mockRejectedValue(error);

      const result = await contactService.syncContactsToDb();

      expect(result).toBe(0);
      expect(mockLogService.error).toHaveBeenCalledWith(
        '從API獲取客戶列表失敗',
        expect.objectContaining({ error })
      );
    });
  });

  describe('getContactList', () => {
    it('應該成功查詢本地客戶列表', async () => {
      const mockContacts = [
        {
          id: 'contact-1',
          name: '客戶1',
          isJoinAile: 1,
          isBindAile: 0,
          languages: JSON.stringify(['ZH_TW'])
        },
        {
          id: 'contact-2',
          name: '客戶2',
          isJoinAile: 0,
          isBindAile: 1,
          languages: JSON.stringify(['EN_US'])
        }
      ];

      mockAileDBService.all.mockResolvedValue(mockContacts);

      const result = await contactService.getContactList({
        tenantId: 'tenant-123',
        key: '客戶',
        pageIndex: 0,
        pageSize: 10
      });

      expect(result).toHaveLength(2);
      expect(result[0].isJoinAile).toBe(true);
      expect(result[0].isBindAile).toBe(false);
      expect(result[0].languages).toEqual(['ZH_TW']);
      expect(result[1].isJoinAile).toBe(false);
      expect(result[1].isBindAile).toBe(true);
      expect(result[1].languages).toEqual(['EN_US']);

      expect(mockAileDBService.all).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM Contact WHERE'),
        expect.arrayContaining(['tenant-123', '%客戶%', '%客戶%', '%客戶%', '%客戶%', 10, 0])
      );
    });

    it('應該處理查詢錯誤', async () => {
      const error = new Error('Query error');
      mockAileDBService.all.mockRejectedValue(error);

      const result = await contactService.getContactList();

      expect(result).toEqual([]);
      expect(mockLogService.error).toHaveBeenCalledWith(
        '查詢客戶列表失敗',
        expect.objectContaining({
          error,
          params: {}
        })
      );
    });
  });

  describe('deleteContact', () => {
    it('應該成功刪除客戶', async () => {
      mockAileDBService.run.mockResolvedValue({ changes: 1, lastInsertRowid: 1 } as any);

      const result = await contactService.deleteContact('contact-1');

      expect(result).toBe(true);
      expect(mockAileDBService.run).toHaveBeenCalledWith(
        'DELETE FROM Contact WHERE id = ?',
        ['contact-1']
      );
    });

    it('應該處理刪除失敗', async () => {
      const error = new Error('Delete error');
      mockAileDBService.run.mockRejectedValue(error);

      const result = await contactService.deleteContact('contact-1');

      expect(result).toBe(false);
      expect(mockLogService.error).toHaveBeenCalledWith(
        '刪除客戶失敗',
        expect.objectContaining({
          error,
          id: 'contact-1'
        })
      );
    });
  });

  describe('getLastRefreshTime', () => {
    it('應該成功獲取保存的refreshTime', async () => {
      const mockRefreshTime = {
        value: '1234567890'
      };
      mockAileDBService.get.mockResolvedValue(mockRefreshTime);

      const method = (contactService as any).getLastRefreshTime;
      const result = await method.call(contactService, 'service-123');

      expect(result).toBe(1234567890);
      expect(mockAileDBService.get).toHaveBeenCalledWith(
        'SELECT value FROM KVStore WHERE key = ?',
        ['contact_refresh_time_service-123']
      );
    });

    it('應該處理沒有保存refreshTime的情況', async () => {
      mockAileDBService.get.mockResolvedValue(null);

      const method = (contactService as any).getLastRefreshTime;
      const result = await method.call(contactService, 'service-123');

      expect(result).toBeNull();
    });

    it('應該處理查詢錯誤', async () => {
      const error = new Error('Query error');
      mockAileDBService.get.mockRejectedValue(error);

      const method = (contactService as any).getLastRefreshTime;
      const result = await method.call(contactService, 'service-123');

      expect(result).toBeNull();
      expect(mockLogService.error).toHaveBeenCalledWith(
        '獲取上次同步的refreshTime失敗',
        expect.objectContaining({
          error,
          serviceNumberId: 'service-123'
        })
      );
    });
  });

  describe('saveRefreshTime', () => {
    it('應該成功更新已存在的refreshTime', async () => {
      mockAileDBService.get.mockResolvedValue({ value: '1000000000' });
      mockAileDBService.run.mockResolvedValue({ changes: 1, lastInsertRowid: 1 } as any);

      const method = (contactService as any).saveRefreshTime;
      const result = await method.call(contactService, 'service-123', 1234567890);

      expect(result).toBe(true);
      expect(mockAileDBService.get).toHaveBeenCalledWith(
        'SELECT value FROM KVStore WHERE key = ?',
        ['contact_refresh_time_service-123']
      );
      expect(mockAileDBService.run).toHaveBeenCalledWith(
        'UPDATE KVStore SET value = ?, updateTime = ? WHERE key = ?',
        ['1234567890', expect.any(Number), 'contact_refresh_time_service-123']
      );
    });

    it('應該成功插入新的refreshTime', async () => {
      mockAileDBService.get.mockResolvedValue(null);
      mockAileDBService.run.mockResolvedValue({ changes: 1, lastInsertRowid: 1 } as any);

      const method = (contactService as any).saveRefreshTime;
      const result = await method.call(contactService, 'service-123', 1234567890);

      expect(result).toBe(true);
      expect(mockAileDBService.run).toHaveBeenCalledWith(
        'INSERT INTO KVStore (key, value, createTime, updateTime) VALUES (?, ?, ?, ?)',
        ['contact_refresh_time_service-123', '1234567890', expect.any(Number), expect.any(Number)]
      );
    });

    it('應該處理保存失敗', async () => {
      const error = new Error('Save error');
      mockAileDBService.get.mockRejectedValue(error);

      const method = (contactService as any).saveRefreshTime;
      const result = await method.call(contactService, 'service-123', 1234567890);

      expect(result).toBe(false);
      expect(mockLogService.error).toHaveBeenCalledWith(
        '保存refreshTime失敗',
        expect.objectContaining({
          error,
          serviceNumberId: 'service-123',
          refreshTime: 1234567890
        })
      );
    });
  });

  describe('ensureKVStoreTableExists', () => {
    it('應該創建KVStore表', async () => {
      mockAileDBService.run.mockResolvedValue({ changes: 0, lastInsertRowid: 0 } as any);
      
      const method = (contactService as any).ensureKVStoreTableExists;
      await method.call(contactService);
      
      expect(mockAileDBService.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS KVStore'),
        []
      );
      expect(mockLogService.info).toHaveBeenCalledWith('確保 KVStore 表存在');
    });
    
    it('應該處理創建失敗', async () => {
      const error = new Error('Create table error');
      mockAileDBService.run.mockRejectedValue(error);
      
      const method = (contactService as any).ensureKVStoreTableExists;
      await method.call(contactService);
      
      expect(mockLogService.error).toHaveBeenCalledWith(
        '創建 KVStore 表失敗',
        expect.objectContaining({
          error
        })
      );
    });
  });
}); 