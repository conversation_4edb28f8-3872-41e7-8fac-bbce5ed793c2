import authService from './authService';
describe('authService', () => {
  it('login 應為 function', () => {
    expect(typeof authService.login).toBe('function');
  });
  it('logout 應為 function', () => {
    expect(typeof authService.logout).toBe('function');
  });
  it('getAccount 應為 function', () => {
    expect(typeof authService.getAccount).toBe('function');
  });
  it('getAuthToken 應為 function', () => {
    expect(typeof authService.getAuthToken).toBe('function');
  });
  // 可擴展：mock httpService 測試異常分支
}); 