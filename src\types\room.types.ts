import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from './aile.enum';

// 房間權重同步請求參數
export interface IWeightRoomListRequest {
  direction?: string;
    /**
     * 關鍵字，關鍵字
     */
    key?: string;
    /**
     * 排序欄位，排序欄位
     */
    orderBy?: string;
    /**
     * 頁數，頁數
     */
    pageIndex?: number;
    /**
     * 每頁筆數，每頁筆數
     */
    pageSize?: number;
    /**
     * 刷新時間，刷新時間
     */
    refreshTime?: number;
    /**
     * 服務號ID，服務號ID
     */
    serviceNumberId?: string;
    /**
     * 權重，權重
     */
    weight?: number;
    [property: string]: any;
}

// 服務房間列表請求參數
export interface IServiceRoomListRequest {
    /**
     * 排序方向，排序方向
     */
    direction?: string;
    /**
     * 關鍵字，關鍵字
     */
    key?: string;
    /**
     * 排序欄位，排序欄位
     */
    orderBy?: string;
    /**
     * 頁數，頁數
     */
    pageIndex?: number;
    /**
     * 每頁筆數，每頁筆數
     */
    pageSize?: number;
    /**
     * 刷新時間，刷新時間
     */
    refreshTime?: number;
    serviceNumberId?: string;
    [property: string]: any;
}

/**
 * 聊天室权重DTO
 */
export interface RoomWeightDto {
  /** P1 最高优先级 */
  p1?: P1WeightEnum;
  /** P2 次要优先级 */
  p2?: P2WeightEnum;
  /** P3 标签优先级列表 */
  p3?: P3WeightEnum[];
  /** 时间戳(毫秒) */
  timestamp?: number;
}

/**
 * 权重计算结果
 */
export interface WeightCalculationResult {
  /** 最终计算的Score */
  score: number;
  /** P1值 */
  p1: number;
  /** P2值 */
  p2: number;
  /** P3值 */
  p3: number;
  /** P4值(时间戳秒) */
  p4: number;
}

/**
 * Score解析结果
 */
export interface ScoreParseResult {
  /** P1值 */
  p1: number;
  /** P2值 */
  p2: number;
  /** P3值 */
  p3: number;
  /** P4值(时间戳秒) */
  p4: number;
}