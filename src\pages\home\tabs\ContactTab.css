.contact-tab-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #FFFFFF;
}

/* 添加新的滚动容器样式 */
.contact-tab-scrollable-container {
  min-height: calc(100vh - 80px);
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #FFFFFF;
}

/* 當聯繫人數量較少時的最小高度模式 */
.contact-tab-scrollable-container.minimal-height {
  min-height: auto !important;
  height: fit-content !important;
  max-height: none !important;
  overflow-y: visible !important;
  display: flex !important;
  flex-direction: column !important;
}

.contact-index-bar {
  height: 100%;
  --adm-index-bar-sticky-offset-top: 0px;
}

/* 在最小高度模式下的IndexBar */
.minimal-height .contact-index-bar {
  height: auto;
}

.contact-index-bar .adm-index-bar-sidebar-interacting{
  width: unset !important;
}

.contact-list {
  --adm-color-background: #FFFFFF;
  --adm-color-border: #F5F5F5;
}

.adm-list-body {
  border: none;
}

.adm-list-item {
  padding: 0;
}

.index-bar-panel {
  padding: 0;
}

.index-bar-panel .adm-index-bar-panel-title {
  background-color: #F5F5F5;
  padding: 0 12px;
  height: 35px;
  line-height: 35px;
  font-family: Arial;
  font-size: 13px;
  font-weight: 400;
  color: #999999;
}

/* Sidebar styles - 提高特异性以覆盖 antd-mobile 默认样式 */
.contact-tab-container .adm-index-bar-sidebar {
  position: fixed !important;
  right: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 9999 !important;
  background-color: transparent !important;
  pointer-events: auto !important;
}

.adm-index-bar-sidebar .adm-index-bar-sidebar-item {
  font-family: Arial;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 1.4;
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 4px 0;
}

.adm-index-bar-sidebar .adm-index-bar-sidebar-item-active {
  color: #FFFFFF;
  background-color: #1677FF;
  border-radius: 62px;
}

/* Item styles */
.contact-item {
  padding: 0;
}

.contact-item .adm-list-item-content {
  padding: 12px 12px 12px 0;
  border-bottom: 1px solid #EEEEEE;
}

.contact-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-name {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4;
  color: #333333;
}

.contact-tag {
  font-family: Arial;
  font-size: 11px;
  line-height: 1.09;
  color: #1677FF;
  border-color: #1677FF;
  padding: 2px 4px;
  border-radius: 2px;
  height: auto;
  --text-color: #1677FF;
}

.contact-tag .adm-tag-content {
  padding: 0;
}

.empty-list {
  height: 0;
}

/* ContactTab Styles */
.contact-tab-content {
  height: 100%;
  background-color: #E4F4FD;
  position: relative;
}

/* New Join Section */
.new-join-section {
  background-color: #FFFFFF;
}

/* 聯繫人列表容器 - 模仿 chat-list */
.contact-list-container {
  flex: 1;
  background-color: #E4F4FD;
  -webkit-overflow-scrolling: touch;
  height: calc(100vh - 140px);
  overflow-y: auto;
  padding-bottom: 30px;
}

/* 索引條樣式只留下基本樣式 */
.contact-index-bar {
  background-color: transparent;
}

/* 索引項樣式只留下基本顯示相關 */
.adm-index-bar-sidebar-item {
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

.adm-index-bar-sidebar-item-active {
  color: #FFFFFF;
  background-color: #1677FF;
  border-radius: 50%;
}

/* 標題樣式 */
.index-bar-anchor-title {
  background-color: #F5F5F5;
  padding: 0 12px;
  font-size: 13px;
  color: #999999;
  line-height: 35px;
}

/* 列表項目樣式 */
.contact-list-item {
  padding: 0 0 0 12px;
}

.contact-list-item:first-child{
  padding-top: 12px;
}

.contact-list-item .adm-list-item-content{
  border-bottom: var(--border-inner);
  border-top: none !important;
  height: 56px;
}

.contact-list-item .adm-list-item-content-main{
  padding: unset !important;
}

.contact-list-item-content {
  display: flex;
  align-items: center;
  padding: 12px 12px 12px 0;
  gap: 8px;
}

/* 頭像樣式 */
.contact-avatar {
  width: 32px;
  height: 32px;
  border-radius: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  font-size: 16px;
  overflow: hidden;
  cursor: pointer;
}

/* 頭像顏色 */
.avatar-color-1 { background-color: #fe7a45; }
.avatar-color-2 { background-color: #3D4DAA; }
.avatar-color-3 { background-color: #13c2c2; }
.avatar-color-4 { background-color: #52c41a; }
.avatar-color-5 { background-color: #722ed1; }
.avatar-color-6 { background-color: #eb2f96; }
.avatar-color-7 { background-color: #fa8c16; }
.avatar-color-8 { background-color: #a0d911; }
.avatar-color-9 { background-color: #1890ff; }
.avatar-color-10 { background-color: #f5222d; }
.avatar-color-11 { background-color: #faad14; }
.avatar-color-12 { background-color: #2f54eb; }
.avatar-color-13 { background-color: #eb2f96; }
.avatar-color-14 { background-color: #52c41a; }


/* 最新加入標籤樣式 */
.new-join-tag {
  color: #1677FF;
  border: 1px solid #1677FF;
  border-radius: 2px;
  font-size: 11px;
  padding: 2px 4px;
} 

/* 在最小高度模式下的聯繫人列表容器 */
.minimal-height .contact-list-container {
  height: auto !important;
  overflow-y: visible !important;
  padding-bottom: 10px;
  flex: none !important;
  min-height: auto !important;
  max-height: none !important;
}

.contact-list-item-content {
  display: flex;
  align-items: center;
}

.contact-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

/* 新加入標籤樣式 */
.new-join-tag {
  margin-left: 8px;
  font-size: 12px;
}

/* 新加入聯繫人區域樣式 */
.contact-tab-scrollable-container, .contact-list-container, .new-join-section {
  background-color: #fff;
}

/* 索引欄樣式 */
.contact-index-bar {
  --adm-index-bar-sidebar-item-size: 16px;
}

/* 頭像顏色變量 */
.avatar-color-1 { background-color: #ff5722; }
.avatar-color-2 { background-color: #e91e63; }
.avatar-color-3 { background-color: #9c27b0; }
.avatar-color-4 { background-color: #673ab7; }
.avatar-color-5 { background-color: #3f51b5; }
.avatar-color-6 { background-color: #2196f3; }
.avatar-color-7 { background-color: #03a9f4; }
.avatar-color-8 { background-color: #00bcd4; }
.avatar-color-9 { background-color: #009688; }
.avatar-color-10 { background-color: #4caf50; }
.avatar-color-11 { background-color: #8bc34a; }
.avatar-color-12 { background-color: #cddc39; }
.avatar-color-13 { background-color: #ffc107; }
.avatar-color-14 { background-color: #ff9800; }

/* 加載中容器樣式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 303px;
}

/* 加載中旋轉器樣式 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空狀態容器樣式 - 參考 MessageTab 的實現，鋪滿中間屏幕 */
.contact-tab-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 0;
  height: 100%;
  padding: 40px 20px;
  box-sizing: border-box;
  background-color: #FFFFFF;
}



/* 優化聯繫人列表容器高度 */
.contact-list-container {
  flex: 1;
  background-color: #E4F4FD;
  -webkit-overflow-scrolling: touch;
  min-height: 200px; /* 設置最小高度避免過小 */
}

/* 在最小高度模式下完全移除最小高度限制 */
.minimal-height .contact-list-container {
  min-height: auto;
}

/* 當沒有常規聯繫人時，調整新加入聯繫人區域樣式 */
.minimal-height .new-join-section {
  margin-bottom: 20px;
}

/* 聯繫人數量較少時的底部提示樣式 */
.few-contacts-hint {
  padding: 15px 20px;
  text-align: center;
  background-color: #f0f9ff;
  margin: 15px 10px 20px 10px;
  border-radius: 8px;
  border: 1px solid #e0f2fe;
}

.few-contacts-hint p {
  margin: 0;
  color: #0369a1;
  font-size: 13px;
  line-height: 1.4;
}

/* 優化最小高度模式下的間距 */
.minimal-height {
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
}

/* 確保最小高度模式下內容緊湊顯示 */
.minimal-height .new-join-section,
.minimal-height .contact-list-container,
.minimal-height .few-contacts-hint {
  flex-shrink: 0;
}

/* 確保在最小高度模式下不會出現不必要的滾動 */
.minimal-height .contact-list-container {
  min-height: auto;
}

/* 在最小高度模式下隱藏或調整IndexBar側邊欄 */
.minimal-height .adm-index-bar-sidebar {
  display: none;
}

/* 當聯繫人數量較少時，調整IndexBar的顯示 */
.minimal-height .contact-index-bar {
  --adm-index-bar-sidebar-item-size: 0px;
}

/* 強制移除最小高度模式下的所有固定高度 */
.minimal-height,
.minimal-height * {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

/* 但保留必要的元素高度 */
.minimal-height .contact-list-item .adm-list-item-content {
  height: 56px !important;
}

.minimal-height .contact-avatar {
  width: 32px !important;
  height: 32px !important;
}

/* 最小高度模式下的IndexBar完全重置 */
.minimal-height .contact-index-bar,
.minimal-height .adm-index-bar,
.minimal-height .adm-index-bar-panel {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* 確保List組件也遵循規則 */
.minimal-height .adm-list,
.minimal-height .adm-list-body {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* 簡單聯繫人列表樣式（聯繫人數量少時使用） */
.simple-contact-list {
  background-color: #FFFFFF;
  padding: 0;
}

.simple-letter-group {
  margin-bottom: 0;
}

.simple-letter-title {
  background-color: #F5F5F5;
  padding: 8px 12px;
  font-size: 13px;
  color: #999999;
  font-weight: 400;
  line-height: 1.4;
}

.simple-contact-list .adm-list {
  background-color: #FFFFFF;
}

.simple-contact-list .adm-list-body {
  border: none;
} 