/**
 * 组件Props相关类型定义
 */

import { ReactNode, CSSProperties } from 'react';

// 基础组件Props
export interface BaseComponentProps {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
  testId?: string;
}

// 可点击组件Props
export interface ClickableProps extends BaseComponentProps {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
}

// 表单组件Props
export interface FormComponentProps extends BaseComponentProps {
  name?: string;
  value?: any;
  onChange?: (value: any) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

// 列表组件Props
export interface ListComponentProps<T = any> extends BaseComponentProps {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  keyExtractor?: (item: T, index: number) => string;
  loading?: boolean;
  empty?: ReactNode;
  onRefresh?: () => void;
  onLoadMore?: () => void;
}

// 模态框组件Props
export interface ModalProps extends BaseComponentProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  footer?: ReactNode;
  maskClosable?: boolean;
  destroyOnClose?: boolean;
}

// 导航组件Props
export interface NavigationProps extends BaseComponentProps {
  title?: string;
  leftContent?: ReactNode;
  rightContent?: ReactNode;
  onBack?: () => void;
  showBack?: boolean;
}
