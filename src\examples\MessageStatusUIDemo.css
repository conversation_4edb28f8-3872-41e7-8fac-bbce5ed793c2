.message-status-ui-demo {
  padding: 16px;
  max-width: 400px;
  margin: 0 auto;
}

/* 模拟聊天容器 */
.demo-chat-container {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.demo-chat-background {
  background-color: #E4F4FD;
  padding: 16px;
  min-height: 300px;
}

/* 消息行 */
.demo-message-row {
  margin-bottom: 12px;
  display: flex;
  justify-content: flex-end;
}

/* 消息容器 */
.demo-message-container {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.demo-message-container.user {
  align-items: flex-end;
}

/* 消息包装器 */
.demo-message-wrapper {
  display: flex;
  align-items: flex-end;
  margin-bottom: 4px;
}

.demo-message-wrapper.user {
  flex-direction: row;
}

/* 消息时间 */
.demo-message-time {
  font-size: 11px;
  color: #999999;
  margin-right: 8px;
  margin-bottom: 2px;
  white-space: nowrap;
}

/* 消息气泡容器 */
.demo-message-bubble-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.demo-message-bubble-container.user {
  align-items: flex-end;
}

/* 消息气泡 */
.demo-message-bubble {
  background-color: #FFFFFF;
  border-radius: 0 8px 8px 8px;
  padding: 8px 12px;
  font-size: 15px;
  color: #333333;
  word-break: break-word;
  max-width: 280px;
  position: relative;
  line-height: 1.4;
}

.demo-message-bubble.user {
  background-color: #386591;
  color: #FFFFFF;
  border-radius: 8px 0px 8px 8px;
}

/* 消息状态指示器 */
.demo-message-status {
  position: absolute;
  bottom: 2px;
  right: 4px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(2px);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .message-status-ui-demo {
    padding: 12px;
  }
  
  .demo-message-bubble {
    max-width: 240px;
    font-size: 14px;
  }
  
  .demo-message-time {
    font-size: 10px;
  }
}

/* 动画效果 */
.demo-message-bubble {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 悬停效果 */
.demo-message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.demo-message-bubble.user:hover {
  box-shadow: 0 2px 8px rgba(56, 101, 145, 0.3);
}

/* 状态指示器悬停效果 */
.demo-message-status:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .demo-message-bubble {
    border: 1px solid #ccc;
  }
  
  .demo-message-bubble.user {
    border: 1px solid #fff;
  }
  
  .demo-message-status {
    border: 1px solid #999;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .demo-message-bubble {
    animation: none;
  }
  
  .demo-message-bubble:hover {
    transform: none;
    transition: none;
  }
  
  .demo-message-status:hover {
    transform: none;
    transition: none;
  }
}
