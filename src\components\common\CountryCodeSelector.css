/**
 * CountryCodeSelector.css
 * 国家代码选择器样式文件 - 下拉框形式
 */

/* 主容器 */
.country-code-selector {
  position: relative;
  display: inline-block;
}

/* 遮罩层 */
.country-code-selector__overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: transparent;
  z-index: 998;
}

/* 下拉框容器 */
.country-code-selector__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 280px;
}

/* 搜索区域 */
.country-code-selector__search {
  padding: 8px 12px;
  background-color: #F8F9FA;
  border-bottom: 1px solid #E0E0E0;
}

.country-code-selector__search-wrapper {
  position: relative;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #D0D7DE;
  overflow: hidden;
}

.country-code-selector__search-icon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.country-code-selector__search-input {
  width: 100%;
  height: 32px;
  padding: 0 12px 0 32px;
  border: none;
  outline: none;
  font-family: 'PingFang TC', var(--font-family-primary);
  font-size: 14px;
  color: #333333;
  background: transparent;
}

.country-code-selector__search-input::placeholder {
  color: #8B949E;
}

/* 国家列表 */
.country-code-selector__list {
  flex: 1;
  overflow-y: auto;
  background-color: white;
  padding: 0;
  margin: 0;
}

/* 国家列表项 */
.country-code-selector__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid #F1F3F4;
  cursor: pointer;
  transition: background-color 0.15s ease;
  min-height: 40px;
  box-sizing: border-box;
}

.country-code-selector__item:hover {
  background-color: #F6F8FA;
}

.country-code-selector__item:active {
  background-color: #E1E7F0;
}

.country-code-selector__item--selected {
  background-color: #E7F3FF;
}

.country-code-selector__item--selected:hover {
  background-color: #CCE1FF;
}

.country-code-selector__item:last-child {
  border-bottom: none;
}

/* 国家名称 */
.country-code-selector__country-name {
  font-family: 'PingFang TC', var(--font-family-primary);
  font-size: 14px;
  font-weight: 400;
  color: #24292F;
  flex: 1;
  text-align: left;
}

/* 国家代码 */
.country-code-selector__dial-code {
  font-family: 'PingFang TC', var(--font-family-primary);
  font-size: 14px;
  font-weight: 400;
  color: #656D76;
  text-align: right;
  min-width: 50px;
}

/* 当选中时的样式 */
.country-code-selector__item--selected .country-code-selector__country-name {
  color: #0969DA;
  font-weight: 500;
}

.country-code-selector__item--selected .country-code-selector__dial-code {
  color: #0969DA;
  font-weight: 500;
}

/* 无搜索结果时的样式 */
.country-code-selector__item--no-results {
  background-color: transparent !important;
  cursor: default !important;
}

.country-code-selector__item--no-results .country-code-selector__country-name {
  color: #8B949E;
  font-style: italic;
  text-align: center;
}

/* 滚动条样式 */
.country-code-selector__list::-webkit-scrollbar {
  width: 4px;
}

.country-code-selector__list::-webkit-scrollbar-track {
  background: transparent;
}

.country-code-selector__list::-webkit-scrollbar-thumb {
  background-color: #CCCCCC;
  border-radius: 2px;
}

.country-code-selector__list::-webkit-scrollbar-thumb:hover {
  background-color: #999999;
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  .country-code-selector__dropdown {
    background-color: #21262D;
    border-color: #30363D;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
  
  .country-code-selector__search {
    background-color: #161B22;
    border-bottom-color: #30363D;
  }
  
  .country-code-selector__search-wrapper {
    background-color: #0D1117;
    border-color: #30363D;
  }
  
  .country-code-selector__search-input {
    color: #F0F6FC;
  }
  
  .country-code-selector__search-input::placeholder {
    color: #7D8590;
  }
  
  .country-code-selector__list {
    background-color: #21262D;
  }
  
  .country-code-selector__item {
    border-bottom-color: #30363D;
  }
  
  .country-code-selector__item:hover {
    background-color: #262C36;
  }
  
  .country-code-selector__item:active {
    background-color: #2F3741;
  }
  
  .country-code-selector__item--selected {
    background-color: #1F2328;
  }
  
  .country-code-selector__item--selected:hover {
    background-color: #292E33;
  }
  
  .country-code-selector__country-name {
    color: #F0F6FC;
  }
  
  .country-code-selector__dial-code {
    color: #8B949E;
  }
  
  .country-code-selector__item--selected .country-code-selector__country-name,
  .country-code-selector__item--selected .country-code-selector__dial-code {
    color: #58A6FF;
  }
  
  .country-code-selector__item--no-results .country-code-selector__country-name {
    color: #6E7681;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .country-code-selector__dropdown {
    border: 2px solid #000000;
  }
  
  .country-code-selector__search-wrapper {
    border: 2px solid #000000;
  }
  
  .country-code-selector__item {
    border-bottom: 1px solid #000000;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .country-code-selector__item {
    transition: none;
  }
} 