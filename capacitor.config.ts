import { CapacitorConfig } from '@capacitor/cli';

// 扩展 CapacitorConfig 接口以包含应用版本信息
interface ExtendedCapacitorConfig extends CapacitorConfig {
  appVersion?: string;
  buildNumber?: string;
}

const config: ExtendedCapacitorConfig = {
  appId: 'cloud.aile.aile',
  appName: 'Aile App',
  appVersion: '5.0.0',
  buildNumber: '1',
  webDir: 'dist',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    App: {
      // 启用硬件返回按钮处理
      handleBackButton: true
    },
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#FFFFFF',
      androidSplashResourceName: 'splash',
      androidScaleType: 'CENTER_CROP'
    },
    StatusBar: {
      style: 'LIGHT',
      backgroundColor: '#FFFFFF',
      overlaysWebView: false
    },
    PushNotifications: {
      presentationOptions: ['badge', 'sound', 'alert']
    },
    LocalNotifications: {
      smallIcon: 'ic_stat_icon',
      iconColor: '#1677FF'
    },
    // 新的官方 @capacitor/barcode-scanner 插件不需要在這裡配置
  }
};


export default config; 