import React from 'react';
import './MessageStatusIndicator.css';

interface MessageStatusIndicatorProps {
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  size?: 'small' | 'medium' | 'large';
  onFailedClick?: () => void; // 失败状态点击回调函数
}

/**
 * 消息状态指示器组件
 * 显示消息的发送状态：发送中、已发送、已到达、已读、失败
 */
const MessageStatusIndicator: React.FC<MessageStatusIndicatorProps> = ({
  status = 'sent',
  size = 'small',
  onFailedClick
}) => {
  if (!status) {
    // console.log('MessageStatusIndicator: 不显示状态指示器', { isUser, status });
    return null;
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'sending':
        // 不显示任何图标，避免转圈圈效果
        return null;
      case 'sent':
        // 已发送状态不显示任何图标
        return null;
      case 'delivered':
        return (
          <div className={`status-icon status-delivered ${size}`}>
            <svg viewBox="0 0 16 16" className="status-svg">
              <circle cx="8" cy="8" r="6" fill="none" stroke="currentColor" strokeWidth="1.5" />
            </svg>
          </div>
        );
      case 'read':
        return (
          <div className={`status-icon status-read ${size}`}>
            <svg viewBox="0 0 16 16" className="status-svg">
              <circle cx="8" cy="8" r="6" fill="currentColor" />
            </svg>
          </div>
        );
      case 'failed':
        return (
          <div
            className={`status-icon status-failed ${size} ${onFailedClick ? 'clickable' : ''}`}
            onClick={onFailedClick}
          >
            <svg viewBox="0 0 16 16" className="status-svg">
              <circle cx="8" cy="8" r="7" fill="#ff4d4f" />
              <text x="8" y="12" textAnchor="middle" fill="white" fontSize="12" fontWeight="bold">!</text>
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'sending':
        return '发送中';
      case 'sent':
        return '已发送';
      case 'delivered':
        return '已到达';
      case 'read':
        return '已读';
      case 'failed':
        return '发送失败，点击查看选项';
      default:
        return '';
    }
  };

  const statusIcon = getStatusIcon();

  // 如果没有图标（如已发送状态），则不渲染任何内容
  if (!statusIcon) {
    return null;
  }

  return (
    <div
      className={`message-status-indicator ${size}`}
      title={getStatusText()}
      aria-label={getStatusText()}
    >
      {statusIcon}
    </div>
  );
};

export default MessageStatusIndicator;
