import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import tenantReducer from './slices/tenantSlice';
import roomReducer from './slices/roomSlice';
import messageReducer from './slices/messageSlice';
import roomWeightReducer from './slices/roomWeightSlice';
import contactReducer from './slices/contactSlice';
import { logService } from '../services/system/logService';
import { getLocalStorage, setLocalStorage } from '@/utils/storage';
import { ConstantUtil } from '@/utils/constantUtil';
import { setStoreGetter } from './storeUtils';

// 創建 store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    tenant: tenantReducer,
    room: roomReducer,
    message: messageReducer,
    roomWeight: roomWeightReducer,
    contacts: contactReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

// 推斷類型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 设置 storeUtils 中的 store getter
setStoreGetter(() => store);

// 将store保存到全局变量中，以解决循环依赖问题
declare global {
  interface Window {
    __STORE__: typeof store;
  }
}

// 确保在浏览器环境中执行
if (typeof window !== 'undefined') {
  window.__STORE__ = store;
}

// 設置 store 變更監聽器，用於持久化關鍵狀態
if (typeof window !== 'undefined') {
  store.subscribe(() => {
    const state = store.getState() as RootState;
    const persistedState = {
      auth: {
        isAuthenticated: state.auth.isAuthenticated,
        account: state.auth.account,
        authToken: state.auth.authToken
      },
      tenant: {
        currentTenantId: state.tenant.currentTenantId,
        currentTenantInfo: state.tenant.currentTenantInfo,
        // 新增：持久化团队列表数据
        tenantRelations: state.tenant.tenantRelations,
        tenantRelationsLastFetched: state.tenant.tenantRelationsLastFetched
      }
    };

    setLocalStorage(ConstantUtil.REDUX_PERSIST_KEY, persistedState);
    logService.debug('Redux 狀態已持久化', persistedState);
  });
}

/**
 * 從 localStorage 加載持久化的狀態
 */
export const loadPersistedState = (): any => {
  try {
    const persistedState = getLocalStorage<any>(ConstantUtil.REDUX_PERSIST_KEY, null);
    if (persistedState) {
      logService.info('從 localStorage 加載持久化狀態', persistedState);
      return persistedState;
    }
  } catch (error) {
    logService.error('加載持久化狀態失敗:', error);
  }

  return undefined;
};

/**
 * 清除持久化的狀態
 */
export const clearPersistedState = (): boolean => {
  try {
    localStorage.removeItem(ConstantUtil.REDUX_PERSIST_KEY);
    logService.info('已清除持久化狀態');
    return true;
  } catch (error) {
    logService.error('清除持久化狀態失敗:', error);
  }

  return false;
};

export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;

// 将store保存到全局变量中，以解决循环依赖问题
declare global {
  interface Window {
    __STORE__: typeof store;
  }
}

// 确保在浏览器环境中执行
if (typeof window !== 'undefined') {
  window.__STORE__ = store;
}

// 設置 store 變更監聽器，用於持久化關鍵狀態
store.subscribe(() => {
  try {
    const state = store.getState() as RootState;
    const persistedState = {
      auth: {
        isAuthenticated: state.auth.isAuthenticated,
        account: state.auth.account,
        authToken: state.auth.authToken
      },
      tenant: {
        currentTenantId: state.tenant.currentTenantId,
        currentTenantInfo: state.tenant.currentTenantInfo,
        // 新增：持久化团队列表数据
        tenantRelations: state.tenant.tenantRelations,
        tenantRelationsLastFetched: state.tenant.tenantRelationsLastFetched
      }
    };
    
    setLocalStorage(ConstantUtil.REDUX_STATE_KEY, persistedState);
  } catch (e) {
    logService.warn('保存 Redux 狀態到 localStorage 失敗', e as Error);
  }
});

// 創建單獨的函數來加載之前保存的 Redux 狀態
// 這樣在應用重新加載時可以手動恢復狀態
export const loadPersistedState = () => {
  try {
    const savedState = getLocalStorage<any>(ConstantUtil.REDUX_STATE_KEY, null);
    if (savedState) {
      // 恢復認證狀態
      if (savedState.auth) {
        if (savedState.auth.isAuthenticated) {
          store.dispatch({ 
            type: 'auth/restoreAuthToken', 
            payload: savedState.auth.authToken 
          });
        }
        
        if (savedState.auth.user) {
          store.dispatch({ 
            type: 'auth/setAccount', 
            payload: savedState.auth.user 
          });
        }
      }
      
      // 恢復租戶狀態
      if (savedState.tenant) {
        if (savedState.tenant.currentTenantId) {
          store.dispatch({
            type: 'tenant/setCurrentTenantId',
            payload: savedState.tenant.currentTenantId
          });
        }

        if (savedState.tenant.currentTenantInfo) {
          store.dispatch({
            type: 'tenant/setCurrentTenantInfo',
            payload: savedState.tenant.currentTenantInfo
          });
        }

        // 新增：恢复团队列表数据
        if (savedState.tenant.tenantRelations && savedState.tenant.tenantRelations.length > 0) {
          store.dispatch({
            type: 'tenant/setTenantRelations',
            payload: savedState.tenant.tenantRelations
          });

          if (savedState.tenant.tenantRelationsLastFetched) {
            store.dispatch({
              type: 'tenant/setTenantRelationsLastFetched',
              payload: savedState.tenant.tenantRelationsLastFetched
            });
          }

          logService.info('从 localStorage 恢复了团队列表数据', {
            count: savedState.tenant.tenantRelations.length,
            lastFetched: savedState.tenant.tenantRelationsLastFetched
          });
        }
      }
      
      logService.info('從 localStorage 恢復了 Redux 狀態');
      return true;
    }
  } catch (e) {
    logService.warn('從 localStorage 加載 Redux 狀態失敗', e as Error);
  }
  
  return false;
};

export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;