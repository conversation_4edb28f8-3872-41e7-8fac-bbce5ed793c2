import { logService } from "../system/logService";
import { getLocalStorage } from "../../utils/storage";
import { ConstantUtil } from "../../utils/constantUtil";
import sqliteService from "./sqliteService";
import { CREATE_TABLE_SQL, CREATE_INDEX_SQL } from "./initSql";


// 簡化查詢結果類型，移除 columns
export interface QueryResult {
  values: any[][];
}

// 数据库配置
export interface DbConfig {
  name: string;
  version: number;
  encrypted?: boolean;
  mode?: string;
  accountId: string;
}

/**
 * Aile 數據庫服務
 * 提供業務層面的數據庫操作功能，基於 sqliteService 構建
 */
class AileDBService {
  private static instance: AileDBService;
  private db: any = null;
  private dbName: string | null = null;
  private initialized: boolean = false;
  private initializing: boolean = false;
  private initPromise: Promise<boolean> | null = null;
  private lastInitTime: number = 0; // 添加上次初始化时间戳
  private readonly INIT_TIMEOUT_MS: number = 30000; // 30秒超时
  public currentAccountId: string | null = null;

  private constructor() {
    // 私有构造函数，防止外部实例化
  }

  /**
   * 获取AileDBService单例
   */
  public static getInstance(): AileDBService {
    if (!AileDBService.instance) {
      AileDBService.instance = new AileDBService();
    }
    return AileDBService.instance;
  }

  /**
   * 初始化数据库
   * @param config 数据库配置
   * @returns 是否初始化成功
   */
  public async init(config: DbConfig): Promise<boolean> {
    this.currentAccountId = config.accountId;

    if (this.initializing) {
      return this.initPromise as Promise<boolean>;
    }

    if (this.initialized && this.dbName === config.name) {
      logService.info("数据库已初始化", { name: config.name });
      return true;
    }

    this.initializing = true;
    this.lastInitTime = Date.now(); // 记录初始化开始时间
    this.initPromise = this.initializeDatabase(config);

    try {
      const result = await this.initPromise;
      this.initializing = false;
      this.lastInitTime = 0; // 重置初始化时间
      this.initialized = result;
      return result;
    } catch (error) {
      this.initializing = false;
      this.lastInitTime = 0; // 重置初始化时间
      logService.error("初始化数据库失败", error as Error);
      return false;
    }
  }

  /**
   * 初始化账户数据库
   * @param accountId 账户ID
   * @returns 是否初始化成功
   */
  public async initForAccount(accountId: string): Promise<boolean> {
    if (!accountId) {
      throw new Error("initForAccount: accountId 不可為空");
    }
    
    try {
      // 新增: 檢查是否正在進行賬號切換
      const isAccountSwitching = this.currentAccountId && this.currentAccountId !== accountId;
      
      if (isAccountSwitching) {
        logService.info("偵測到賬戶變更，執行完整的關閉流程", {
          from: this.currentAccountId,
          to: accountId
        });
        
        // 步驟1: 強制清理實例狀態
        this.initialized = false;
        
        // 步驟2: 嘗試保存舊數據庫數據到持久存儲
        try {
          if (this.db && this.dbName) {
            logService.info("賬號切換前保存舊數據庫數據", { oldDbName: this.dbName });
            // 直接使用 SQLite 連接保存數據，避免循環調用 saveToStore
            await sqliteService.saveToStore(this.dbName);
          }
        } catch (saveError) {
          logService.warn("賬號切換前保存舊數據庫數據失敗，但繼續關閉流程", { error: saveError });
        }
        
        // 步驟3: 關閉舊數據庫連接
        try {
          if (this.db && this.dbName) {
            logService.info("關閉舊數據庫連接", { oldDbName: this.dbName });
            await sqliteService.closeDatabase(this.dbName, false);
            
            // 步驟4: 添加額外延遲，確保資源釋放
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 步驟5: 使用原生API額外確保連接釋放
            if (window.sqlitePlugin && typeof window.sqlitePlugin.echoTest === 'function') {
              try {
                await window.sqlitePlugin.echoTest();
                logService.info("已確認舊數據庫連接已釋放");
              } catch (echoError) {
                logService.warn("檢查連接狀態失敗，但繼續初始化", { error: echoError });
              }
            }
          }
        } catch (closeError) {
          logService.warn("關閉舊數據庫連接失敗，但繼續初始化", { error: closeError });
        }
        
        // 完全重置實例狀態
        this.db = null;
        this.dbName = null;
        this.initialized = false;
        this.initializing = false;
        this.initPromise = null;
      } else {
        // 一般情況，只關閉當前連接
        logService.info("初始化前，嘗試關閉當前數據庫連接");
        await this.close();
      }
      
      const dbName = `${accountId}_data`;
      logService.info("初始化數據庫", { dbName, accountId });
  
      // 初始化數據庫連接
      const initResult = await this.init({
        name: dbName,
        accountId,
        version: ConstantUtil.DB_VERSION,
        encrypted: false,
        mode: ConstantUtil.DB_ENCRYPTION_MODE,
      });
      
      if (!initResult) {
        throw new Error("數據庫初始化失敗");
      }
      
      
      // 創建表結構
      for (const sql of CREATE_TABLE_SQL) {
        try {
          await this.exec(sql);
          logService.debug("建表成功", { sql: sql.substring(0, 50) + "..." });
        } catch (e) {
          logService.error("建表失敗", { sql: sql.substring(0, 50) + "...", error: e });
          // 繼續執行其他表的創建，不中斷流程
        }
      }

      // 創建索引以優化查詢性能
      for (const sql of CREATE_INDEX_SQL) {
        try {
          await this.exec(sql);
          logService.debug("創建索引成功", { sql: sql.substring(0, 50) + "..." });
        } catch (e) {
          logService.error("創建索引失敗", { sql: sql.substring(0, 50) + "...", error: e });
          // 繼續執行其他索引的創建，不中斷流程
        }
      }
      
      // 如果是賬號切換，在初始化成功後額外執行一次持久化
      if (isAccountSwitching) {
        try {
          logService.info("賬號切換成功，進行初始持久化");
          // 確保基本表結構已保存
          await sqliteService.saveToStore(dbName);
        } catch (postSaveError) {
          logService.warn("賬號切換後初始持久化失敗，但初始化已完成", { error: postSaveError });
        }
      }
      
      return initResult;
    } catch (error) {
      logService.error("初始化數據庫失敗", { error: error as Error, accountId });
      // 確保錯誤時實例狀態一致
      this.db = null;
      this.dbName = null;
      this.initialized = false;
      this.initializing = false;
      return false;
    }
  }

  /**
   * 执行SQL查询
   * @param sql SQL查询语句
   * @param params 查询参数
   * @returns 查询结果
   */
  public async exec(sql: string, params: any[] = []): Promise<QueryResult[]> {
    await this.ensureDbReady();

    try {
      logService.debug("执行SQL查询", { sql, params });
      const result = await this.db!.query(sql, params);
      
      // 檢查 result 是否為空
      if (!result) {
        logService.warn("SQL查詢返回空結果", { sql });
        return [{ values: [] }];
      }
      
      // 直接返回 values，不處理 columns
      return [{ values: result.values || [] }];
    } catch (error) {
      logService.error("执行SQL查询失败", {
        error: error as Error,
        sql,
        params,
      });
      throw error;
    }
  }

  /**
   * 执行SQL查询并返回第一行结果
   * @param sql SQL语句
   * @param params 查询参数
   * @returns 查询结果对象，如果没有结果则返回null
   */
  public async get<T = any>(
    sql: string,
    params: any[] = []
  ): Promise<T | null> {
    await this.ensureDbReady();

    try {
      // 直接使用 db.query 獲取原始結果
      const result = await this.db!.query(sql, params);
      
      // 檢查是否有結果
      if (!result || !result.values || result.values.length === 0) {
        return null;
      }
      
      // 直接返回第一行，不再使用 columns 構建對象
      return result.values[0] as unknown as T;
    } catch (error) {
      logService.error("执行SQL查询失败", {
        error: error as Error,
        sql,
        params,
      });
      throw error;
    }
  }

  /**
   * 执行SQL查询并返回所有结果行
   * @param sql SQL查询语句
   * @param params 查询参数
   * @returns 查询结果的所有行
   */
  public async all<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    await this.ensureDbReady();

    try {
      // 直接使用 db.query 獲取原始結果
      const result = await this.db!.query(sql, params);
      
      // 檢查是否有結果
      if (!result || !result.values || result.values.length === 0) {
        return [];
      }
      
      // 直接返回所有行，不再使用 columns 構建對象數組
      return result.values;
    } catch (error) {
      logService.error("执行SQL查询失败", {
        error: error as Error,
        sql,
        params,
      });
      throw error;
    }
  }

  /**
   * 执行SQL并返回影响的行数
   * @param sql SQL语句
   * @param params 查询参数
   * @returns 影响的行数
   */
  public async run(sql: string, params: any[] = []): Promise<number> {
    await this.ensureDbReady();

    try {
      const result = await this.db!.run(sql, params);
      
      // 移除自動保存邏輯，改為由上層調用者決定何時保存
      // 避免頻繁的 saveToStore 調用，提升性能並降低出錯機率
      
      return result.changes?.changes || 0;
    } catch (error) {
      logService.error("执行SQL语句失败", {
        error: error as Error,
        sql,
        params,
      });
      throw error;
    }
  }

  /**
   * 保存数据库到持久存储
   * 注意：此方法应在批量操作后调用，而非每次写操作后调用
   */
  public async saveToStore(): Promise<void> {
    if (!this.isInitialized() || !this.dbName) {
      logService.warn("尝试保存未初始化的数据库，操作被跳过");
      return;
    }
    
    try {
      // 增加檢查確保數據庫連接有效
      const isConnOpen = await this.checkConnectionIsOpen();
      if (!isConnOpen) {
        logService.warn("數據庫連接已關閉，嘗試重新打開連接", { dbName: this.dbName });
        
        // 如果數據庫連接已關閉，嘗試重新初始化
        if (this.currentAccountId) {
          // 添加短暫延遲，確保資源釋放
          await new Promise(resolve => setTimeout(resolve, 300));
          // 重新初始化連接
          await this.initForAccount(this.currentAccountId);
        } else {
          throw new Error("無法重新打開數據庫連接：缺少 accountId");
        }
        
        // 再次檢查連接是否正常
        const reconnectSuccessful = await this.checkConnectionIsOpen();
        if (!reconnectSuccessful) {
          throw new Error("重新連接數據庫失敗，無法保存數據");
        }
      }
      
      // 使用重試機制調用 saveToStore
      let retryCount = 0;
      const maxRetries = 3;
      
      while (retryCount <= maxRetries) {
        try {
          if (retryCount > 0) {
            const delay = 300 * Math.pow(2, retryCount - 1);
            logService.info(`嘗試第 ${retryCount} 次保存數據到持久存儲，延遲 ${delay}ms`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
          
          await sqliteService.saveToStore(this.dbName);
          logService.debug("数据库已保存到持久存储", { dbName: this.dbName, retryCount });
          return;
        } catch (retryError) {
          if (retryCount === maxRetries) {
            throw retryError;
          }
          logService.warn(`保存到持久存儲失敗，將重試 (${retryCount+1}/${maxRetries})`, 
                         { error: retryError, dbName: this.dbName });
          retryCount++;
        }
      }
    } catch (error) {
      logService.error("保存数据库到持久存储失败", { error: error as Error, dbName: this.dbName });
      throw error;
    }
  }

  /**
   * 关闭数据库
   */
  public async close(): Promise<void> {
    try {
      if (this.db && this.dbName) {
        await sqliteService.closeDatabase(this.dbName, false);
        this.db = null;
        this.initialized = false;
        logService.info("数据库已关闭");
      } else {
        logService.debug("数据库已是关闭状态");
      }
    } catch (error) {
      logService.error("关闭数据库失败", error as Error);
      // 即使關閉失敗，也重置狀態以確保一致性
      this.db = null;
      this.initialized = false;
    }
  }

  /**
   * 初始化数据库的内部实现
   * @param config 数据库配置
   * @returns 是否初始化成功
   */
  private async initializeDatabase(config: DbConfig): Promise<boolean> {
    try {
      // 嘗試先關閉可能已存在的連接
      if (this.db && this.dbName) {
        try {
          await sqliteService.closeDatabase(this.dbName, false);
          this.db = null;
        } catch (closeErr) {
          logService.warn("關閉舊數據庫連接失敗，但將繼續初始化", closeErr as Error);
        }
      }

      logService.info("開始初始化數據庫...", { name: config.name });

      // Web 環境特殊處理
      if (sqliteService.getPlatform() === "web") {
        await sqliteService.initWebStore();
      }
      
      // 打開數據庫連接
      this.db = await sqliteService.openDatabase(
        config.name,
        config.version || 1,
        false
      );
      
      this.dbName = config.name;
      
      logService.info("数据库初始化成功", {
        name: config.name,
        version: config.version,
      });
      return true;
    } catch (error) {
      logService.error("初始化数据库失败", error as Error);
      // 清理任何可能的部分初始化資源
      if (this.dbName) {
        try {
          await sqliteService.closeDatabase(this.dbName, false);
        } catch (e) {
          // 忽略關閉錯誤
        }
      }
      this.db = null;
      this.dbName = null;
      this.initialized = false;
      return false;
    }
  }

  /**
   * 檢查數據庫是否初始化完成
   */
  public isInitialized(): boolean {
    return this.initialized && this.db !== null;
  }

  /**
   * 验证数据库存储功能是否正常
   * @returns 是否可以正常存储数据
   */
  public async verifyStorageCapability(): Promise<boolean> {
    try {
      if (!this.isInitialized()) {
        logService.warn('数据库未初始化，无法验证存储功能');
        return false;
      }

      // 执行简单的查询测试
      await this.run('SELECT 1', []);

      // 尝试保存到持久存储
      await this.saveToStore();

      logService.debug('数据库存储功能验证成功');
      return true;
    } catch (error) {
      logService.error('数据库存储功能验证失败', { error: error as Error });
      return false;
    }
  }
  
  /**
   * 檢查數據庫是否正在初始化中
   * 添加超时检测，避免初始化被中断后状态无法恢复
   */
  public isInitializing(): boolean {
    // 如果初始化标志为 true，但已经超过超时时间，则重置状态
    if (this.initializing && this.lastInitTime > 0) {
      const now = Date.now();
      if (now - this.lastInitTime > this.INIT_TIMEOUT_MS) {
        logService.warn("數據庫初始化似乎已被中斷，重置初始化狀態", {
          lastInitTime: new Date(this.lastInitTime).toISOString(),
          elapsedMs: now - this.lastInitTime
        });
        this.initializing = false;
        this.initPromise = null;
        this.lastInitTime = 0;
        return false;
      }
    }
    return this.initializing;
  }

  /**
   * 公共方法：获取当前数据库名称
   */
  public getDbName(): string | null {
    return this.dbName;
  }

  /**
   * 确保数据库已准备好
   * @private
   */
  private async ensureDbReady(): Promise<void> {
    if (!this.isInitialized()) {
      const accountId = getLocalStorage<string | null>(
        ConstantUtil.ACCOUNT_ID_KEY,
        null
      );
      
      if (!accountId) {
        throw new Error("無法執行SQL: 缺少accountId");
      }
      
      // 检查账号是否变更
      if (this.currentAccountId !== accountId) {
        logService.warn("偵測到帳號切換，強制重建 DB 實例", {
          from: this.currentAccountId,
          to: accountId,
        });
        await this.initForAccount(accountId);
      } else {
        throw new Error("数据库未初始化");
      }
    }
  }

  /**
   * 检查数据库连接是否打开
   * @private
   * @returns 是否打开
   */
  private async checkConnectionIsOpen(): Promise<boolean> {
    if (!this.db || !this.dbName) {
      return false;
    }
    
    try {
      // 執行一個簡單查詢來檢查連接是否有效
      await this.db.query("SELECT 1");
      return true;
    } catch (error) {
      logService.warn("数据库连接检查失败，可能已关闭", { error });
      return false;
    }
  }
}

// 导出AileDBService单例
export const aileDBService = AileDBService.getInstance();

// 導出 close 方法，供登出時主動釋放 DB
export async function closeAileDB() {
  try {

    const dbName = aileDBService.getDbName();
    
    if (aileDBService.isInitialized()) {
      // 確保在關閉前保存所有數據到持久存儲
      try {
        await aileDBService.saveToStore();
        logService.info("數據庫已保存到持久存儲");
      } catch (saveError) {
        logService.warn("關閉前保存數據庫失敗", { error: saveError as Error });
        // 繼續嘗試關閉
      }
      
      await aileDBService.close();
      logService.info("用戶主動登出，已關閉 DB");
      
      // 僅釋放數據庫連接，不刪除數據庫文件
      if (dbName && window.sqlitePlugin && typeof window.sqlitePlugin.echoTest === 'function') {
        try {
          // 執行一個無害的測試確保連接已重置，但不刪除數據庫文件
          await window.sqlitePlugin.echoTest();
          logService.info("已釋放數據庫連接但保留數據文件", { dbName });
        } catch (echoError) {
          logService.warn("檢查連接狀態失敗，但數據庫已正常關閉", { error: echoError });
        }
      }
    } else {
      logService.info("數據庫未初始化，無需關閉");
    }
  } catch (error) {
    logService.warn("關閉數據庫過程中發生錯誤", error as Error);
  }
}

// 默认导出
export default aileDBService;

// 为了保持向后兼容性，保留 initAileDB 函数
export async function initAileDB(accountId: string) {
  return aileDBService.initForAccount(accountId);
} 