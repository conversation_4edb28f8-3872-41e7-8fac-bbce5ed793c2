declare module 'aile-capacitor-line-login' {
  export interface LineLoginConfig {
    channelId: string;
    universalLinkURL?: string;
    redirectUri?: string;
    scope?: string[];
    botPrompt?: string;
    debug?: boolean;
  }

  export interface LoginOptions {
    onlyWebLogin?: boolean;
    botPrompt?: 'normal' | 'aggressive';
    scopes?: string[];
  }

  export interface UserProfile {
    userId: string;
    displayName: string;
    pictureUrl?: string;
    statusMessage?: string;
    language?: string;
  }

  export interface TokenResult {
    accessToken: string;
    expiresIn: number;
    refreshToken: string;
    tokenType: string;
  }

  export interface LoginResult extends TokenResult {
    scope: string;
    userProfile?: UserProfile;
  }

  export const LineLogin: {
    initialize(options: LineLoginConfig): Promise<void>;
    login(options?: LoginOptions): Promise<LoginResult>;
    getUserProfile(): Promise<UserProfile>;
    isLoggedIn(): Promise<{ isLoggedIn: boolean }>;
    logout(): Promise<void>;
    refreshToken(): Promise<TokenResult>;
    echo(options: { value: string }): Promise<{ value: string }>;
  };

  export const LineLoginHelpers: {
    getCurrentPlatform(): 'web' | 'ios' | 'android';
    isPlatformSupported(): boolean;
    isWebPlatform(): boolean;
    isNativePlatform(): boolean;
  };
} 