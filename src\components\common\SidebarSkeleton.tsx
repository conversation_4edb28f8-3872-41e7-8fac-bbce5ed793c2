import React from 'react';
import './SidebarSkeleton.css';

interface SidebarSkeletonProps {
  /** 显示的团队项目数量 */
  itemCount?: number;
}

/**
 * 侧边栏骨架屏组件
 * 用于在团队列表加载时提供更好的用户体验
 */
export const SidebarSkeleton: React.FC<SidebarSkeletonProps> = ({ 
  itemCount = 3 
}) => {
  return (
    <div className="sidebar-skeleton">
      {/* 用户信息骨架 */}
      <div className="sidebar-skeleton-user">
        <div className="sidebar-skeleton-avatar"></div>
        <div className="sidebar-skeleton-username"></div>
      </div>
      
      {/* 团队标题骨架 */}
      <div className="sidebar-skeleton-title"></div>
      
      {/* 团队列表骨架 */}
      <div className="sidebar-skeleton-teams">
        {/* 加入团队按钮骨架 */}
        <div className="sidebar-skeleton-team-item">
          <div className="sidebar-skeleton-team-icon"></div>
          <div className="sidebar-skeleton-team-text"></div>
        </div>
        
        {/* 团队项目骨架 */}
        {Array.from({ length: itemCount }, (_, index) => (
          <div key={index} className="sidebar-skeleton-team-item">
            <div className="sidebar-skeleton-team-avatar"></div>
            <div className="sidebar-skeleton-team-content">
              <div className="sidebar-skeleton-team-name"></div>
            </div>
            <div className="sidebar-skeleton-team-action"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SidebarSkeleton;
