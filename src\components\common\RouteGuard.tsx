import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../app/hooks';
import { ConstantUtil } from '../../utils/constantUtil';
import { logService } from '../../services/system/logService';
import { logout } from '../../app/slices/authSlice';
import { clearTenantState } from '../../app/slices/tenantSlice';
import { closeAileDB } from '../../services/db/aileDBService';
import {
  ROUTE_LOGIN,
  ROUTE_USER_SIGNUP,
  ROUTES
} from '../../config/app/routes';

interface RouteGuardProps {
  children: React.ReactNode;
}

/**
 * 路由守衛組件
 * 負責路由的權限檢查和重定向，不處理初始化邏輯
 */
const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 從 Redux 獲取狀態
  const authState = useAppSelector(state => state.auth);
  const currentTenantId = useAppSelector(state => state.tenant.currentTenantId);
  const authToken = useAppSelector(state => state.auth.authToken);

  // 定義不受限制的路由（登錄相關頁面）
  const publicRoutes = [
    ROUTES.AUTH.LOGIN,
    ROUTES.AUTH.OTP_LOGIN,
    ROUTES.AUTH.USER_SIGNUP,
    ROUTES.AUTH.BUSINESS_SIGNUP,
    ROUTES.AUTH.LINE_CALLBACK,
  ];

  useEffect(() => {
    const currentPath = location.pathname;

    // 登錄相關頁面不受權限限制
    if (publicRoutes.some(route => currentPath === route)) {
      logService.debug('路由守衛：公共路由，無需檢查', { path: currentPath });
      return;
    }

    // 檢查身份驗證狀態
    if (!authState.isAuthenticated || !authToken) {
      logService.warn('路由守衛：用戶未登錄或token缺失，重定向到登錄頁', { path: currentPath });

      // 關閉數據庫連接，避免數據庫連接跨會話共享
      closeAileDB().catch((dbError: Error) => {
        logService.error('路由守衛：關閉數據庫連接失敗', { error: dbError });
      });
      logService.info('路由守衛：已嘗試關閉數據庫連接');

      // 清除可能存在的認證狀態
      dispatch(logout());
      dispatch(clearTenantState());

      navigate(ROUTE_LOGIN, {
        replace: true,
        state: { skipTokenCheck: true }
      });
      return;
    }
    
    if (authState.account?.type === ConstantUtil.LOGIN_TYPE_LINE && !authState.account?.mobile) {
      // 如果line登錄沒有輸入手機號碼，則重定向到登錄頁
      logService.info('路由守衛：line登錄沒有輸入手機號碼', { path: currentPath });
      navigate(ROUTE_LOGIN, { replace: true });
      return;
    }
    
    // 檢查新用戶是否需要完善資料
    if (authState.account?.isInitial === true) {
      if (currentPath !== ROUTE_USER_SIGNUP) {
        logService.info('路由守衛：新用戶需要完善資料', { path: currentPath });
        navigate(ROUTE_LOGIN, { replace: true });
      }
      return;
    }

    // 檢查租戶選擇狀態
    if (!currentTenantId) {
      logService.warn('路由守衛：未選擇租戶，重定向到客戶選擇頁', { path: currentPath });
      navigate(ROUTE_LOGIN, { replace: true });
      return;
    }

    logService.debug('路由守衛：所有檢查通過', {
      path: currentPath,
      isAuthenticated: authState.isAuthenticated,
      tenantId: currentTenantId
    });
  }, [
    location.pathname,
    authState.isAuthenticated,
    authState.account?.isInitial,
    currentTenantId,
    authToken,
    navigate,
    dispatch
  ]);

  return <>{children}</>;
};

export default RouteGuard; 