import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  authService,
  AccountResponse,
  LoginRequest,
  LoginResponse,
  OtpRequest,
  OtpResponse,
  ThirdPartyLoginRequest
} from '../../services/core/auth';

// 定義狀態類型
export interface AuthState {
  isAuthenticated: boolean;
  account: AccountResponse | null;
  authToken: string | null;

  // OTP 登入流程
  sendingOtp: boolean;
  otpSent: boolean;
  sendOtpError: string | null;
  onceToken: string | null;
  otpValidSeconds: number;

  // 登入流程
  loggingIn: boolean;
  loginError: string | null;

  // 全局 loading 狀態（可選）
  loading: boolean;
}



// 初始狀態（不在模塊級別調用服務）
const initialState: AuthState = {
  isAuthenticated: false,
  account: null,
  authToken: null,

  sendingOtp: false,
  otpSent: false,
  sendOtpError: null,
  onceToken: null,
  otpValidSeconds: 0,

  loggingIn: false,
  loginError: null,

  loading: false
};

/**
 * 恢復認證狀態異步 thunk
 */
export const restoreAuthState = createAsyncThunk<
  { account: AccountResponse | null; authToken: string | null; isAuthenticated: boolean },
  void,
  { rejectValue: string }
>(
  'auth/restoreAuthState',
  async (_, { rejectWithValue }) => {
    try {
      const authState = authService.getAuthState();
      return authState;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

/**
 * 發送驗證碼異步 thunk
 */
export const sendOtp = createAsyncThunk<
  OtpResponse,
  OtpRequest,
  { rejectValue: string }
>(
  'auth/sendOtp',
  async (request, { rejectWithValue }) => {
    try {
      return await authService.sendOtp(request);
    } catch (error: any) {
      return rejectWithValue(error.message || '發送驗證碼失敗');
    }
  }
);

/**
 * 登入異步 thunk
 */
export const login = createAsyncThunk<
  LoginResponse,
  LoginRequest,
  { rejectValue: string }
>(
  'auth/login',
  async (request, { rejectWithValue }) => {
    try {
      return await authService.login(request);
    } catch (error: any) {
      return rejectWithValue(error.message || '登入失敗');
    }
  }
);

/**
 * 登出異步 thunk
 */
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const success = await authService.logout();
      if (success) {
        return { success: true };
      } else {
        return rejectWithValue('登出失敗');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || '登出失敗');
    }
  }
);

/**
 * 初始化認證狀態（從本地存儲讀取）
 */
export const initAuth = createAsyncThunk(
  'auth/init',
  async () => {
    return authService.initAuth();
  }
);

/**
 * LINE 登錄異步 thunk
 */
export const loginWithLINE = createAsyncThunk<
  LoginResponse,
    ThirdPartyLoginRequest,
  { rejectValue: string }
>(
  'auth/loginWithLINE',
  async (request, { rejectWithValue }) => {
    try {
      // 這裡應該調用後端 API 驗證 LINE 令牌
      // 暫時使用一個模擬的登錄請求，實際項目中應該替換為真實的 LINE 登錄 API 調用
      const thirdPartyLoginRequest = {
        loginType: request.loginType, // LINE 登錄類型
        thirdChannel: request.thirdChannel,
        useNonMobile: request.useNonMobile,
        scopeId: request.scopeId
      };
      return await authService.loginWithThirdParty(thirdPartyLoginRequest);
    } catch (error: any) {
      return rejectWithValue(error.message || 'LINE 登錄失敗');
    }
  }
);

// 創建 Auth 切片
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.sendOtpError = null;
      state.loginError = null;
    },
    resetOtpState: (state) => {
      state.sendingOtp = false;
      state.otpSent = false;
      state.sendOtpError = null;
      state.onceToken = null;
      state.otpValidSeconds = 0;
    },
    setAccount: (state, action: PayloadAction<Partial<AccountResponse>>) => {
      if (state.account) {
        // 保存修改前的 accountId 以便比較
        const prevAccountId = state.account.accountId;
        
        // 更新 account 狀態
        state.account = { ...state.account, ...action.payload };
        
        // 檢查 accountId 是否變更，只有在 accountId 變更時才觸發數據庫重置
        const currentAccountId = state.account.accountId;
        const accountIdChanged = prevAccountId !== currentAccountId && currentAccountId;
        
        if (!accountIdChanged) {
          // 一般情況：只更新用戶數據，不觸發數據庫重置
          authService.setAccountToLocalStorage(state.account);
        }
        
        // 如果提供了新的 tokenId，則更新它
        if (action.payload.tokenId) {
          authService.saveAuthToken(action.payload.tokenId);
        }
      } else if (action.payload.accountId) {
        // 新用戶登錄情況
        state.account = action.payload as AccountResponse;
        authService.setAccountToLocalStorage(state.account);
        
        if (action.payload.tokenId) {
          authService.saveAuthToken(action.payload.tokenId);
        }
      }
    },
    restoreAuthToken: (state, action: PayloadAction<string | null>) => {
      state.authToken = action.payload;
      state.isAuthenticated = !!action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // 處理恢復認證狀態
      .addCase(restoreAuthState.fulfilled, (state, action) => {
        state.isAuthenticated = action.payload.isAuthenticated;
        state.account = action.payload.account;
        state.authToken = action.payload.authToken;
      })
      .addCase(restoreAuthState.rejected, (state, action) => {
        // 恢復失敗時保持初始狀態
        state.isAuthenticated = false;
        state.account = null;
        state.authToken = null;
      })
      // 處理發送 OTP
      .addCase(sendOtp.pending, (state) => {
        state.sendingOtp = true;
        state.sendOtpError = null;
      })
      .addCase(sendOtp.fulfilled, (state, action) => {
        state.sendingOtp = false;
        state.otpSent = true;
        state.sendOtpError = null;
        if (action.payload.data) {
          state.onceToken = action.payload.data.onceToken;
          state.otpValidSeconds = action.payload.data.validSecond;
        }
      })
      .addCase(sendOtp.rejected, (state, action) => {
        state.sendingOtp = false;
        state.sendOtpError = action.payload as string;
      })
      
      // 處理登出
      .addCase(logout.fulfilled, (state) => {
        // 完全重置到初始狀態
        Object.assign(state, {
          ...initialState,
          isAuthenticated: false,
          account: null,
          authToken: null,
          sendingOtp: false,
          otpSent: false,
          sendOtpError: null,
          onceToken: null,
          otpValidSeconds: 0,
          loggingIn: false,
          loginError: null,
          loading: false
        });
      })
      
      // 處理登錄
      .addCase(login.pending, (state) => {
        state.loggingIn = true;
        state.loginError = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        if (action.payload.data) {
          state.account = action.payload.data;
          state.authToken = action.payload.data.tokenId || null;
          state.isAuthenticated = true;
        }
        state.loggingIn = false;
        state.loginError = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isAuthenticated = false;
        state.loggingIn = false;
        state.loginError = action.payload as string || '登入失敗';
      })
      
      // 處理 LINE 登錄
      .addCase(loginWithLINE.pending, (state) => {
        state.loggingIn = true;
        state.loginError = null;
      })
      .addCase(loginWithLINE.fulfilled, (state, action) => {
        if (action.payload.data) {
          state.account = action.payload.data;
          state.authToken = action.payload.data.tokenId || null;
          state.isAuthenticated = true;
          // 保存到本地存儲
          authService.setAccountToLocalStorage(action.payload.data);
          if (action.payload.data.tokenId) {
            authService.saveAuthToken(action.payload.data.tokenId);
          }
        }
        state.loggingIn = false;
        state.loginError = null;
      })
      .addCase(loginWithLINE.rejected, (state, action) => {
        state.isAuthenticated = false;
        state.loggingIn = false;
        state.loginError = action.payload as string || 'LINE 登錄失敗';
      })
      
      // 處理初始化認證狀態
      .addCase(initAuth.fulfilled, (state, action) => {
        if (action.payload.isAuthenticated) {
          state.account = action.payload.account;
          state.authToken = action.payload.authToken;
          state.isAuthenticated = true;
        }
      });
  }
});

// 導出動作創建器
export const { 
  clearError, 
  resetOtpState,
  setAccount,
  restoreAuthToken
} = authSlice.actions;

// 導出 reducer
export default authSlice.reducer;

// 重新導出 AccountResponse 類型
export type { AccountResponse } from '../../services/core/auth';