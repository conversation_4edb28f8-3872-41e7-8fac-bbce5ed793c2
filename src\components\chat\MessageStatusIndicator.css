.message-status-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  float: right;
  /* 移除margin，由父容器控制位置 */
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* 尺寸变体 */
.status-icon.small {
  width: 5px;
  height: 5px;
}

.status-icon.medium {
  width: 14px;
  height: 14px;
}

.status-icon.large {
  width: 18px;
  height: 18px;
}

/* SVG 图标样式 */
.status-svg {
  width: 100%;
  height: 100%;
}

/* 发送中状态 */
.status-sending {
  color: #bbb;
}

.loading-spinner {
  width: 100%;
  height: 100%;
  border: 1px solid #f0f0f0;
  border-top: 1px solid #bbb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 已发送状态 - 不显示任何图标 */
.status-sent {
  display: none;
}

/* 已到达状态 - 静态空心圆圈 */
.status-delivered {
  border: 1px solid #000;
  opacity: 0.5;
  border-radius: 2.5px;
}

/* 已读状态 - 实心黑色圆圈 */
.status-read {
  color: #000;
  opacity: 1;
  border-radius: 2.5px;
}

/* 发送失败状态 */
.status-failed {
  color: #ff4d4f;
  opacity: 0.9;
  height: 20px !important;
  width: 20px !important;
}

/* 可点击的失败状态 */
.status-failed.clickable {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.status-failed.clickable:hover {
  transform: scale(1.2);
}

.status-failed.clickable:active {
  transform: scale(1.1);
}

/* 悬停效果 */
.message-status-indicator:hover .status-icon {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-icon.small {
    width: 5px;
    height: 5px;
  }
  
  .status-icon.medium {
    width: 14px;
    height: 14px;
  }
  
  .status-icon.large {
    width: 18px;
    height: 18px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .status-sent {
    color: #000;
  }
  
  .status-delivered,
  .status-read {
    color: #006400;
  }
  
  .status-failed {
    color: #8B0000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
  
  .status-icon {
    transition: none;
  }
  
  .message-status-indicator:hover .status-icon {
    transform: none;
  }
}
