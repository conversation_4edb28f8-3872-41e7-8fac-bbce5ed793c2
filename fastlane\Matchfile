gitlab_project("aile_cloud/certificate")


storage_mode("gitlab_secure_files")

type("appstore") # The default type, can be: appstore, adhoc, enterprise or development

app_identifier([
  "cloud.aile.aile"
])

username("<EMAIL>") # Your Apple Developer Portal username

team_id(ENV["DEVELOPER_TEAM_ID"])
# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match
