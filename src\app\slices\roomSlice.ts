import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RoomVO } from '@/services/core/chat/roomService.types';
import { AppThunk } from '../store';
import { getRoomsByFilter, fetchRoomServiceRobotList, fetchRoomServiceEndList, fetchRoomServiceWeightSync } from '@/services/core/chat/roomService';
import tenantService from '@/services/core/tenant/tenantService';
import { ConstantUtil } from '@/utils/constantUtil';
import { logService } from '@/services/system/logService';
import { SessionStatus } from '@/services/core/chat/roomService';


export type PageKey = 'robot' | 'active' | 'processed';

export interface RoomState { 
  robotRooms: RoomVO[];
  activeRooms: RoomVO[];
  processedRooms: RoomVO[];
  robotPage: number;
  activePage: number;
  processedPage: number;
  robotHasNextPage: boolean;
  activeHasNextPage: boolean;
  processedHasNextPage: boolean;
  robotRefreshTime: number;
  processedRefreshTime: number;
  weight: number;
  robotTotalCount: number;
  activeTotalCount: number;
  processedTotalCount: number;
  initialized: boolean;           // 是否已經初始化過
  lastInitTenantId: string | null; // 上次初始化的租戶ID
}

const initialState: RoomState = {
  robotRooms: [],
  activeRooms: [],
  processedRooms: [],
  robotPage: 0,
  activePage: 0,
  processedPage: 0,
  robotHasNextPage: true,
  activeHasNextPage: true,
  processedHasNextPage: true,
  robotRefreshTime: 0,
  processedRefreshTime: 0,
  weight: 0,
  robotTotalCount: 0,
  activeTotalCount: 0,
  processedTotalCount: 0,
  initialized: false,
  lastInitTenantId: null,
};

const roomSlice = createSlice({
  name: 'room',
  initialState,
  reducers: {
    setRooms: (
      state,
      action: PayloadAction<{ type: PageKey; rooms: RoomVO[]; replace?: boolean }>
    ) => {
      const { type, rooms, replace } = action.payload;

      // 去重函数：基于room.id去重，保留最新的数据
      const deduplicateRooms = (existingRooms: RoomVO[], newRooms: RoomVO[]): RoomVO[] => {
        if (replace) return newRooms;

        const existingIds = new Set(existingRooms.map(room => room.id));
        const uniqueNewRooms = newRooms.filter(room => !existingIds.has(room.id));
        return [...existingRooms, ...uniqueNewRooms];
      };

      if (type === 'robot') {
        state.robotRooms = deduplicateRooms(state.robotRooms, rooms);
      } else if (type === 'active') {
        state.activeRooms = deduplicateRooms(state.activeRooms, rooms);
      } else if (type === 'processed') {
        state.processedRooms = deduplicateRooms(state.processedRooms, rooms);
      }
    },
    setPage: (state, action: PayloadAction<{ type: PageKey; page: number }>) => {
      const { type, page } = action.payload;
      if (type === 'robot') state.robotPage = page;
      if (type === 'active') state.activePage = page;
      if (type === 'processed') state.processedPage = page;
    },
    setHasNextPage: (state, action: PayloadAction<{ type: PageKey; hasNextPage: boolean }>) => {
      const { type, hasNextPage } = action.payload;
      if (type === 'robot') state.robotHasNextPage = hasNextPage;
      if (type === 'active') state.activeHasNextPage = hasNextPage;
      if (type === 'processed') state.processedHasNextPage = hasNextPage;
    },
    setRefreshTime: (
      state,
      action: PayloadAction<{ type: PageKey; refreshTime: number }>
    ) => {
      const { type, refreshTime } = action.payload;
      if (type === 'robot') state.robotRefreshTime = refreshTime;
      if (type === 'processed') state.processedRefreshTime = refreshTime;
    },
    setWeight: (state, action: PayloadAction<number>) => {
      state.weight = action.payload;
    },
    setTotalCount: (state, action: PayloadAction<{ type: PageKey; count: number }>) => {
      const { type, count } = action.payload;
      if (type === 'robot') state.robotTotalCount = count;
      if (type === 'active') state.activeTotalCount = count+3;
      if (type === 'processed') state.processedTotalCount = count;
    },
    setInitialized: (state, action: PayloadAction<{ tenantId: string }>) => {
      state.initialized = true;
      state.lastInitTenantId = action.payload.tenantId;
    },
    updateRoomSessionStatus: (state, action: PayloadAction<{ roomId: string; sessionStatus: SessionStatus }>) => {
      const { roomId, sessionStatus } = action.payload;

      // 更新所有房間列表中的對應房間
      const updateRoomInList = (rooms: RoomVO[]) => {
        return rooms.map(room =>
          room.id === roomId
            ? { ...room, sessionStatus }
            : room
        );
      };

      state.robotRooms = updateRoomInList(state.robotRooms);
      state.activeRooms = updateRoomInList(state.activeRooms);
      state.processedRooms = updateRoomInList(state.processedRooms);
    },
    updateRoom: (state, action: PayloadAction<{ 
      roomId: string; 
      room: RoomVO;
      lastMessage?: any;
    }>) => {
      const { roomId, room, lastMessage } = action.payload;
      
      // 更新函數，針對單個房間列表
      const updateRoomInList = (rooms: RoomVO[]): RoomVO[] => {
        const roomIndex = rooms.findIndex(r => r.id === roomId);
        if (roomIndex === -1) {
          // 房間不在列表中，添加到列表頭部
          return [room, ...rooms];
        } else {
          // 房間在列表中，更新房間數據
          return rooms.map(r => r.id === roomId ? {
            ...r,
            ...room,
            lastMessage: lastMessage?.content || r.lastMessage,
            lastSequence: lastMessage?.sequence || r.lastSequence
          } : r);
        }
      };

      // 檢查房間類型，更新對應列表
      if (room.sessionStatus === SessionStatus.RobotActive) {
        // 機器人列表
        state.robotRooms = updateRoomInList(state.robotRooms);
      } else if (room.sessionStatus === SessionStatus.AgentStop) {
        // 已處理列表
        state.processedRooms = updateRoomInList(state.processedRooms);
      } else {
        // 服務中列表 (默認)
        state.activeRooms = updateRoomInList(state.activeRooms);
      }
    },
    resetAll: () => initialState,
  },
});

export const {
  setRooms,
  setPage,
  setHasNextPage,
  setRefreshTime,
  setWeight,
  setTotalCount,
  setInitialized,
  updateRoomSessionStatus,
  updateRoom, // 導出新添加的 action
  resetAll
} = roomSlice.actions;



// Thunk: 從本地 DB 查詢機器人房間列表
export const fetchRobotRoomsFromDB = (page: number): AppThunk => async (dispatch) => {
  try {
    const localData = await getRoomsByFilter('robot', page, ConstantUtil.DB_DEFAULT_PAGE_SIZE);
    dispatch(setRooms({ type: 'robot', rooms: localData, replace: page === 0 }));
    dispatch(setPage({ type: 'robot', page }));
    logService.info('機器人房間本地查詢成功', { page, roomsCount: localData.length });
  } catch (error) {
    logService.error('機器人房間本地查詢失敗', { error, page });
  }
};

// Thunk: 從本地 DB 查詢服務中房間列表
export const fetchActiveRoomsFromDB = (page: number): AppThunk => async (dispatch) => {
  try {
    const localData = await getRoomsByFilter('active', page, ConstantUtil.DB_DEFAULT_PAGE_SIZE);
    dispatch(setRooms({ type: 'active', rooms: localData, replace: page === 0 }));
    dispatch(setPage({ type: 'active', page }));
    logService.info('服務中房間本地查詢成功', { page, roomsCount: localData.length });
  } catch (error) {
    logService.error('服務中房間本地查詢失敗', { error, page });
  }
};

// Thunk: 從本地 DB 查詢已處理房間列表
export const fetchProcessedRoomsFromDB = (page: number): AppThunk => async (dispatch) => {
  try {
    const localData = await getRoomsByFilter('processed', page, ConstantUtil.DB_DEFAULT_PAGE_SIZE);
    dispatch(setRooms({ type: 'processed', rooms: localData, replace: page === 0 }));
    dispatch(setPage({ type: 'processed', page }));
    logService.info('已處理房間本地查詢成功', { page, roomsCount: localData.length });
  } catch (error) {
    logService.error('已處理房間本地查詢失敗', { error, page });
  }
};

// Thunk: 從 API 查詢機器人房間列表
export const fetchRobotRoomsFromAPI = (page: number, options?: { force?: boolean }): AppThunk => async (dispatch, getState) => {
  try {
    const state = getState().room as RoomState;
    const bossServiceNumberId = tenantService.getCurrentBossServiceNumberId();

    const res = await fetchRoomServiceRobotList({
      direction: 'asc',
      pageIndex: page,
      pageSize: ConstantUtil.DB_DEFAULT_PAGE_SIZE,
      refreshTime: options?.force ? 0 : state.robotRefreshTime,
      serviceNumberId: bossServiceNumberId ?? undefined,
    });

    if (res?.data) {
      // 直接將API數據存儲到Redux緩存
      if (res.data.items && res.data.items.length > 0) {
        dispatch(setRooms({ type: 'robot', rooms: res.data.items, replace: page === 0 }));
        dispatch(setPage({ type: 'robot', page }));
      }

      // 更新其他狀態
      if (res.data.refreshTime !== undefined) {
        dispatch(setRefreshTime({ type: 'robot', refreshTime: res.data.refreshTime }));
      }
      dispatch(setHasNextPage({ type: 'robot', hasNextPage: res.data.hasNextPage ?? false }));

      if (res.data.totalCount !== undefined) {
        dispatch(setTotalCount({ type: 'robot', count: res.data.totalCount }));
      }

      logService.info('機器人房間API查詢成功', {
        page,
        itemsCount: res.data.items?.length || 0,
        totalCount: res.data.totalCount,
        hasNextPage: res.data.hasNextPage
      });
    }
  } catch (error) {
    logService.error('機器人房間API加載失敗', { error, page });
  }
};

// Thunk: 從 API 查詢服務中房間列表
export const fetchActiveRoomsFromAPI = (page: number, options?: { force?: boolean }): AppThunk => async (dispatch, getState) => {
  try {
    const state = getState().room as RoomState;
    const bossServiceNumberId = tenantService.getCurrentBossServiceNumberId();

    const res = await fetchRoomServiceWeightSync({
      direction: 'asc',
      pageIndex: page,
      pageSize: ConstantUtil.DB_DEFAULT_PAGE_SIZE,
      serviceNumberId: bossServiceNumberId ?? undefined,
      weight: options?.force ? 0 : state.weight,
    });

    if (res?.data) {
      // 直接將API數據存儲到Redux緩存
      if (res.data.items && res.data.items.length > 0) {
        dispatch(setRooms({ type: 'active', rooms: res.data.items, replace: page === 0 }));
        dispatch(setPage({ type: 'active', page }));
      }

      // 更新其他狀態
      if (res.data.weight !== undefined) {
        dispatch(setWeight(res.data.weight));
      }
      dispatch(setHasNextPage({ type: 'active', hasNextPage: res.data.hasNextPage ?? false }));

      if (res.data.totalCount !== undefined) {
        dispatch(setTotalCount({ type: 'active', count: res.data.totalCount }));
      }

      logService.info('服務中房間API查詢成功', {
        page,
        itemsCount: res.data.items?.length || 0,
        totalCount: res.data.totalCount,
        hasNextPage: res.data.hasNextPage
      });
    }
  } catch (error) {
    logService.error('服務中房間API加載失敗', { error, page });
  }
};

// Thunk: 從 API 查詢已處理房間列表
export const fetchProcessedRoomsFromAPI = (page: number, options?: { force?: boolean }): AppThunk => async (dispatch, getState) => {
  try {
    const state = getState().room as RoomState;
    const bossServiceNumberId = tenantService.getCurrentBossServiceNumberId();

    const res = await fetchRoomServiceEndList({
      direction: 'asc',
      pageIndex: page,
      pageSize: ConstantUtil.DB_DEFAULT_PAGE_SIZE,
      refreshTime: options?.force ? 0 : state.processedRefreshTime,
      serviceNumberId: bossServiceNumberId ?? undefined,
    });

    if (res?.data) {
      // 直接將API數據存儲到Redux緩存
      if (res.data.items && res.data.items.length > 0) {
        dispatch(setRooms({ type: 'processed', rooms: res.data.items, replace: page === 0 }));
        dispatch(setPage({ type: 'processed', page }));
      }

      // 更新其他狀態
      if (res.data.refreshTime !== undefined) {
        dispatch(setRefreshTime({ type: 'processed', refreshTime: res.data.refreshTime }));
      }
      dispatch(setHasNextPage({ type: 'processed', hasNextPage: res.data.hasNextPage ?? false }));

      if (res.data.totalCount !== undefined) {
        dispatch(setTotalCount({ type: 'processed', count: res.data.totalCount }));
      }

      logService.info('已處理房間API查詢成功', {
        page,
        itemsCount: res.data.items?.length || 0,
        totalCount: res.data.totalCount,
        hasNextPage: res.data.hasNextPage
      });
    }
  } catch (error) {
    logService.error('已處理房間API加載失敗', { error, page });
  }
};

export default roomSlice.reducer; 