import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import LoginPage from '../pages/login/LoginPage';
import OtpLoginPage from '../pages/login/OtpLoginPage';
import UserSignUpPage from '../pages/login/UserSignUpPage';
import BusinessSignUpPage from '../pages/login/BusinessSignUpPage';
import LineCallbackPage from '../pages/login/LineCallbackPage';
import HomePage from '../pages/home/<USER>';
import ClientProfilePage from '../pages/home/<USER>';
import ChatRoomPage from '../pages/home/<USER>';
import SystemChatRoomPage from '../pages/home/<USER>';
import CustomerChatRoomPage from '../pages/home/<USER>';
import NotchTestPage from '../pages/test/NotchTestPage';
import { RouteGuard } from '../components/common';
import TeamChatRoomPage from '@/pages/home/<USER>';
import SkeletonTestPage from '@/pages/test/SkeletonTestPage';
import LinkTestPage from '@/pages/test/LinkTestPage';
import AddMemberQRCodePage from '@/pages/sidebar/AddMemberQRCodePage';
import QRScanner from '@/pages/sidebar/QRScanner';
import { useAppSelector } from '../app/hooks';
import { logService } from '../services/system/logService';
import {
  ROUTE_LOGIN,
  ROUTE_OTP_LOGIN,
  ROUTE_USER_SIGNUP,
  ROUTE_BUSINESS_REGISTER,
  ROUTE_LINE_CALLBACK,
  ROUTE_HOME,
  ROUTE_CLIENT_PROFILE,
  ROUTE_CHAT_ROOM,
  ROUTE_SYSTEM_CHAT_ROOM,
  ROUTE_CUSTOMER_CHAT_ROOM,
  ROUTE_TEAM_CHAT_ROOM,
  ROUTE_ADD_MEMBER_QRCODE,
  ROUTE_ADD_MEMBER_QRCODE_WITH_TENANT,
  ROUTE_SIDEBAR_QR_SCANNER
} from '../config/app/routes';

const AppRoutes: React.FC = () => {
  // 獲取認證狀態
  const authState = useAppSelector(state => state.auth);
  const isLoggedIn = authState.isAuthenticated && !!authState.authToken;

  // 根路徑處理組件
  const RootRedirect: React.FC = () => {
    const redirectPath = isLoggedIn ? ROUTE_HOME : ROUTE_LOGIN;
    
    // 記錄重定向路徑
    logService.debug('根路徑重定向', { 
      isLoggedIn, 
      redirectTo: redirectPath,
      hasToken: !!authState.authToken
    });
    
    return <Navigate to={redirectPath} replace />;
  };
  
  return (
    <Routes>
      <Route path="/" element={
        <RouteGuard>
          <RootRedirect />
        </RouteGuard>
      } />

      {/* 登錄相關頁面也需要 RouteGuard 檢查，但允許未認證訪問 */}
      <Route path={ROUTE_LOGIN} element={
        <RouteGuard>
          <LoginPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_OTP_LOGIN} element={
        <RouteGuard>
          <OtpLoginPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_USER_SIGNUP} element={
        <RouteGuard>
          <UserSignUpPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_BUSINESS_REGISTER} element={
        <RouteGuard>
          <BusinessSignUpPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_LINE_CALLBACK} element={
        <RouteGuard>
          <LineCallbackPage />
        </RouteGuard>
      } />
      
      {/* 受保護的路由 */}
      <Route path={ROUTE_HOME} element={
        <RouteGuard>
          <HomePage />
        </RouteGuard>
      } />
      <Route path={ROUTE_CLIENT_PROFILE} element={
        <RouteGuard>
          <ClientProfilePage />
        </RouteGuard>
      } />
      <Route path={ROUTE_CHAT_ROOM} element={
        <RouteGuard>
          <ChatRoomPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_SYSTEM_CHAT_ROOM} element={
        <RouteGuard>
          <SystemChatRoomPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_CUSTOMER_CHAT_ROOM} element={
        <RouteGuard>
          <CustomerChatRoomPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_TEAM_CHAT_ROOM} element={
        <RouteGuard>
          <TeamChatRoomPage />
        </RouteGuard>
      } />
      <Route path={ROUTE_ADD_MEMBER_QRCODE} element={
        <RouteGuard>
          <AddMemberQRCodePage />
        </RouteGuard>
      } />
      <Route path={ROUTE_ADD_MEMBER_QRCODE_WITH_TENANT} element={
        <RouteGuard>
          <AddMemberQRCodePage />
        </RouteGuard>
      } />
      <Route path={ROUTE_SIDEBAR_QR_SCANNER} element={
        <RouteGuard>
          <QRScanner />
        </RouteGuard>
      } />

      {/* 测试页面 - 仅开发环境 */}
      <Route path="/test/skeleton" element={<SkeletonTestPage />} />
      <Route path="/test/notch" element={<NotchTestPage />} />
      <Route path="/test/link" element={<LinkTestPage />} />
    </Routes>
  );
};

export default AppRoutes; 