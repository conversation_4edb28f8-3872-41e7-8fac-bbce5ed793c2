/**
 * @file i18nService.test.ts
 * @description i18nService 单元测试
 * <AUTHOR> Team
 */

import { i18nService, I18nService } from './i18nService';
import type { SupportedLanguage } from '../../locales';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock console
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();

describe('I18nService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('单例模式', () => {
    it('should return the same instance', () => {
      const instance1 = I18nService.getInstance();
      const instance2 = I18nService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should be the same as exported service', () => {
      const instance = I18nService.getInstance();
      expect(instance).toBe(i18nService);
    });
  });

  describe('语言管理', () => {
    it('should return a valid default language initially', () => {
      const currentLang = i18nService.getCurrentLanguage();
      const supportedLanguages = Object.keys(i18nService.getSupportedLanguages());
      expect(supportedLanguages).toContain(currentLang);
    });

    it('should change language successfully', async () => {
      await i18nService.changeLanguage('en-US');
      expect(i18nService.getCurrentLanguage()).toBe('en-US');
    });

    it('should throw error for invalid language', async () => {
      await expect(i18nService.changeLanguage('invalid' as SupportedLanguage))
        .rejects.toThrow('Unsupported language: invalid');
    });

    it('should return correct language name for zh-TW', async () => {
      await i18nService.changeLanguage('zh-TW');
      expect(i18nService.getCurrentLanguageName()).toBe('繁體中文');
    });

    it('should return supported languages list', () => {
      const supportedLanguages = i18nService.getSupportedLanguages();
      expect(supportedLanguages).toHaveProperty('zh-CN');
      expect(supportedLanguages).toHaveProperty('zh-TW');
      expect(supportedLanguages).toHaveProperty('en-US');
    });
  });

  describe('API语言代码转换', () => {
    it('should return correct API language code for zh-TW', async () => {
      await i18nService.changeLanguage('zh-TW');
      expect(i18nService.getApiLanguageCode()).toBe('zh-tw');
    });

    it('should return correct API language code for en-US', async () => {
      await i18nService.changeLanguage('en-US');
      expect(i18nService.getApiLanguageCode()).toBe('en-us');
    });

    it('should return correct API language code for zh-CN', async () => {
      await i18nService.changeLanguage('zh-CN');
      expect(i18nService.getApiLanguageCode()).toBe('zh-cn');
    });
  });

  describe('翻译功能', () => {
    it('should return translation for valid key', () => {
      const translation = i18nService.t('app.welcome');
      expect(typeof translation).toBe('string');
      expect(translation.length).toBeGreaterThan(0);
    });

    it('should return key for missing translation', () => {
      const missingKey = 'missing.key';
      const translation = i18nService.t(missingKey);
      expect(translation).toBe(missingKey);
    });

    it('should check if translation key exists', () => {
      expect(i18nService.exists('app.welcome')).toBe(true);
      expect(i18nService.exists('missing.key')).toBe(false);
    });
  });

  describe('语言变更监听', () => {
    it('should add and remove language change listener', () => {
      const callback = jest.fn();
      const unsubscribe = i18nService.onLanguageChange(callback);
      
      expect(typeof unsubscribe).toBe('function');
      unsubscribe();
    });

    it('should notify listeners when language changes', async () => {
      const callback = jest.fn();
      i18nService.onLanguageChange(callback);
      
      await i18nService.changeLanguage('en-US');
      
      // 给事件循环一些时间来处理异步事件
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(callback).toHaveBeenCalledWith('en-US');
    });

    it('should handle callback errors gracefully', async () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Callback error');
      });
      
      i18nService.onLanguageChange(errorCallback);
      
      // 不应该抛出错误
      await expect(i18nService.changeLanguage('zh-CN')).resolves.not.toThrow();
    });
  });

  describe('兼容性方法', () => {
    it('should support legacy loadFromCache method', () => {
      expect(() => i18nService.loadFromCache()).not.toThrow();
    });

    it('should support legacy switchLanguage method', () => {
      i18nService.switchLanguage('en-US');
      expect(i18nService.getCurrentLanguage()).toBe('en-US');
    });

    it('should support legacy getLanguage method', () => {
      expect(i18nService.getLanguage()).toBe(i18nService.getCurrentLanguage());
    });

    it('should support legacy getCELanguage method', () => {
      expect(i18nService.getCELanguage()).toBe(i18nService.getApiLanguageCode());
    });
  });

  describe('本地存储', () => {
    it('should save language to localStorage on change', async () => {
      await i18nService.changeLanguage('en-US');
      
      // 给异步操作一些时间
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(mockLocalStorage.setItem).toHaveBeenCalled();
    });

    it('should handle localStorage errors gracefully', () => {
      // 测试saveLanguageToStorage方法的错误处理
      // 直接调用私有方法来测试错误处理逻辑
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // 模拟localStorage.setItem抛出错误
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error');
      });
      
      // 测试错误处理 - 通过反射调用私有方法
      const privateMethod = (i18nService as any).saveLanguageToStorage;
      expect(() => privateMethod.call(i18nService, 'zh-CN')).not.toThrow();
      
      // 验证错误被正确记录
      expect(consoleErrorSpy).toHaveBeenCalled();
      
      consoleErrorSpy.mockRestore();
    });
  });

  describe('清理资源', () => {
    it('should destroy service properly', () => {
      expect(() => i18nService.destroy()).not.toThrow();
    });
  });

  afterAll(() => {
    mockConsoleWarn.mockRestore();
    mockConsoleError.mockRestore();
  });
}); 