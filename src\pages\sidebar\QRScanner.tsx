import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { BarcodeScanner, BarcodeFormat } from '@capacitor-mlkit/barcode-scanning';
import { Toast, Button, DotLoading } from 'antd-mobile';
import { useTranslation } from 'react-i18next';
import { useAppDispatch } from '@/app/hooks';
import { fetchTenantRelations, applyTenantToken } from '@/app/slices/tenantSlice';
import { logService } from '@/services/system/logService';
import { CryptoUtil } from '@/utils/cryptoUtil';
import { ConstantUtil } from '@/utils/constantUtil';
import deviceService from '@/services/platform/deviceService';
import './QRScanner.css';

interface ScanningArea {
  width: number;  // 宽度百分比 (0-1)
  height: number; // 高度百分比 (0-1)
  x: number;      // 距离左边百分比 (0-1)
  y: number;      // 距离顶部百分比 (0-1)
}

interface QRScannerProps {
  scanningArea?: ScanningArea;  // 可选的扫描区域配置
}

const QRScanner: React.FC<QRScannerProps> = ({
  scanningArea: customScanningArea
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [manualCode, setManualCode] = useState('');
  const [isWebScanning, setIsWebScanning] = useState(false);
  const [isSwitchingTenant, setIsSwitchingTenant] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const detectorRef = useRef<any>(null);

  // 默认扫描区域配置（仅Android支持）
  // 这些值定义了相对于屏幕的扫描区域，匹配280x280px的掃描框
  const defaultScanningArea: ScanningArea = {
    width: 280 / window.innerWidth,   // 280px相對於屏幕寬度的比例
    height: 280 / window.innerHeight, // 280px相對於屏幕高度的比例
    x: (window.innerWidth - 280) / 2 / window.innerWidth,  // 水平居中
    y: (60 + 36) / window.innerHeight  // 頭部高度 + 36px間距
  };

  // 使用自定义配置或默认配置
  const scanningArea = customScanningArea || defaultScanningArea;

  useEffect(() => {
    logService.info('進入掃碼頁面', {
      platform: deviceService.getPlatform(),
      isWeb: deviceService.isWeb(),
      isNative: deviceService.isNative()
    });
    initializeScanner();

    return () => {
      cleanup();
    };
  }, []);

  // 初始化扫描器
  const initializeScanner = async () => {
    try {
      if (deviceService.isNative()) {
        // 原生平台使用 Capacitor MLKit
        await startNativeScan();
      } else {
        // Web 平台使用 barcode-detector polyfill
        await startWebScan();
      }
    } catch (error) {
      logService.error('初始化掃描器失敗', error);
      Toast.show({
        content: '啟動掃描失敗，請稍後再試',
        duration: 2000
      });
      navigate(-1);
    }
  };

  // 原生平台扫描
  const startNativeScan = async () => {
    try {
      logService.info('開始原生掃描，掃描區域配置：', scanningArea);

      // 隐藏 WebView 背景，让摄像头可见
      document.body.classList.add('barcode-scanner-active');
      document.documentElement.classList.add('barcode-scanner-active');

      // 強制設置所有可能的背景為透明
      document.body.style.background = 'transparent';
      document.body.style.backgroundColor = 'transparent';
      document.documentElement.style.background = 'transparent';
      document.documentElement.style.backgroundColor = 'transparent';

      // 覆蓋App容器背景
      const appElement = document.querySelector('.app') as HTMLElement;
      if (appElement) {
        appElement.style.backgroundColor = 'transparent';
      }

      // 覆蓋root容器背景
      const rootElement = document.getElementById('root');
      if (rootElement) {
        rootElement.style.background = 'transparent';
        rootElement.style.backgroundColor = 'transparent';
      }

      // 监听扫描结果
      const listener = await BarcodeScanner.addListener('barcodesScanned', async (event) => {
        logService.info('掃描結果：', event.barcodes);
        if (event.barcodes && event.barcodes.length > 0) {
          await listener.remove();
          handleScanResult(event.barcodes[0].rawValue);
        }
      });

      // 启动扫描，限制格式为 QRCode，并设置扫描区域（Android 支持）
      await BarcodeScanner.startScan({
        formats: [BarcodeFormat.QrCode],
        // 设置扫描区域（相对屏幕的百分比，仅Android支持）
        // 使用类型断言来绕过TypeScript类型检查，因为scanningArea可能不在官方类型定义中
        ...(scanningArea && { scanningArea } as any)
      });
    } catch (error) {
      logService.error('啟動原生掃描失敗', error);
      throw error;
    }
  };

  // Web 端扫描
  const startWebScan = async () => {
    try {
      logService.info('開始 Web 端掃描');
      setIsWebScanning(true);

      // 请求摄像头权限
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // 后置摄像头
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();
      }

      // 初始化 barcode detector
      const BarcodeDetector = (window as any).BarcodeDetector;
      if (!BarcodeDetector) {
        // 如果浏览器不支持，尝试使用 polyfill
        const { BarcodeDetector: PolyfillBarcodeDetector } = await import('barcode-detector');
        detectorRef.current = new PolyfillBarcodeDetector({
          formats: ['qr_code']
        });
      } else {
        detectorRef.current = new BarcodeDetector({
          formats: ['qr_code']
        });
      }

      // 开始检测循环
      startDetectionLoop();
    } catch (error) {
      logService.error('啟動 Web 掃描失敗', error);
      setIsWebScanning(false);
      throw error;
    }
  };

  // 检测循环
  const startDetectionLoop = () => {
    const detectBarcodes = async () => {
      if (!isWebScanning || !videoRef.current || !canvasRef.current || !detectorRef.current) {
        return;
      }

      try {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        if (!ctx || video.readyState !== video.HAVE_ENOUGH_DATA) {
          requestAnimationFrame(detectBarcodes);
          return;
        }

        // 设置 canvas 尺寸
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // 绘制视频帧到 canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // 检测条码
        const barcodes = await detectorRef.current.detect(canvas);

        if (barcodes && barcodes.length > 0) {
          logService.info('Web 掃描結果：', barcodes);
          const qrCode = barcodes.find((barcode: any) => barcode.format === 'qr_code');
          if (qrCode) {
            await stopWebScan();
            handleScanResult(qrCode.rawValue);
            return;
          }
        }

        // 继续检测
        if (isWebScanning) {
          requestAnimationFrame(detectBarcodes);
        }
      } catch (error) {
        logService.error('條碼檢測失敗', error);
        if (isWebScanning) {
          requestAnimationFrame(detectBarcodes);
        }
      }
    };

    requestAnimationFrame(detectBarcodes);
  };

  // 停止 Web 扫描
  const stopWebScan = async () => {
    try {
      setIsWebScanning(false);

      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }

      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }

      detectorRef.current = null;
    } catch (error) {
      logService.error('停止 Web 掃描失敗', error);
    }
  };

  // 统一的停止扫描方法
  const stopScan = async () => {
    try {
      if (deviceService.isNative()) {
        // 恢復原始樣式
        document.body.classList.remove('barcode-scanner-active');
        document.documentElement.classList.remove('barcode-scanner-active');
        document.body.style.background = '';
        document.body.style.backgroundColor = '';
        document.documentElement.style.background = '';
        document.documentElement.style.backgroundColor = '';

        // 恢復App容器背景
        const appElement = document.querySelector('.app') as HTMLElement;
        if (appElement) {
          appElement.style.backgroundColor = '';
        }

        // 恢復root容器背景
        const rootElement = document.getElementById('root');
        if (rootElement) {
          rootElement.style.background = '';
          rootElement.style.backgroundColor = '';
        }

        await BarcodeScanner.removeAllListeners();
        await BarcodeScanner.stopScan();
      } else {
        await stopWebScan();
      }
    } catch (error) {
      logService.error('停止掃描失敗', error);
    }
  };

  // 清理资源
  const cleanup = async () => {
    await stopScan();
  };

  const handleScanResult = async (scanResult: string) => {
    try {
      await stopScan();
      
      logService.info('處理掃描結果', { scanResult });
      
      // 解析二维码数据
      const decodedData = decodeQRCode(scanResult);
      if (!decodedData) {
        Toast.show({
          content: '無效的邀請二維碼',
          duration: 2000
        });
        navigate(-1);
        return;
      }

      // 解析邀請碼數據 (JSON格式)
      let tenantId: string;
      let inviteCode: string;

      try {
        const parsedData = JSON.parse(decodedData);
        tenantId = parsedData.tenantId;
        inviteCode = parsedData.inviteCode;

        if (!tenantId || !inviteCode) {
          Toast.show({
            content: '邀請碼數據不完整',
            duration: 2000
          });
          navigate(-1);
          return;
        }
      } catch (parseError) {
        logService.error('解析JSON格式邀請碼失敗', { decodedData, parseError });
        Toast.show({
          content: '邀請碼格式錯誤：' + decodedData,
          duration: 2000
        });
        navigate(-1);
        return;
      }

      if (tenantId && inviteCode) {
        try {
          const { default: tenantService } = await import('../../services/core/tenant/tenantService');
          const triggerResult = await tenantService.triggerInvitation(inviteCode);

          if (triggerResult.success) {
            // 成功加入团队后立即停止扫描并显示切换租户的loading
            await stopScan();
            setIsSwitchingTenant(true);

            Toast.show({
              content: '成功加入團隊！',
              position: 'top',
              duration: 3000
            });

            await handleJoinTeamSuccess(tenantId);
          } else {
            Toast.show({
              content: triggerResult.msg || '加入團隊失敗，請稍後再試',
              position: 'top',
              duration: 3000
            });
          }
        } catch (error) {
          Toast.show({
            content: '網絡錯誤，請稍後再試',
            position: 'top',
            duration: 3000
          });
        }

        // 返回上一頁
        // setTimeout(() => navigate(-1), 2000);
      } else {
        Toast.show({
          content: '無效的邀請二維碼',
          duration: 2000
        });
        navigate(-1);
      }
    } catch (error) {
      logService.error('處理掃描結果失敗', error);
      Toast.show({
        content: '處理掃描結果失敗',
        duration: 2000
      });
      navigate(-1);
    }
  };

  // 解析二维码数据
  const decodeQRCode = (scanResult: string): string | null => {
    try {
      if (scanResult.includes(ConstantUtil.QRCODE_SEPERATOR)) {
        const parts = scanResult.split(ConstantUtil.QRCODE_SEPERATOR);
        if (parts.length >= 2) {
          const codeValue = parts[1];
          return CryptoUtil.decryptApiResponse(codeValue, ConstantUtil.DEFAULT_SECRET_KEY);
        }
      }
      return null;
    } catch (error) {
      logService.error('解析二維碼失敗', { error });
      return null;
    }
  };

  const handleClose = async () => {
    await cleanup();
    navigate(-1);
  };

  // 处理成功加入团队后的逻辑
  const handleJoinTeamSuccess = async (tenantId?: string) => {
    try {
      logService.info('开始处理加入团队成功后的逻辑', { tenantId });

      // 1. 如果没有租户ID，先获取当前的租户列表作为对比基准
      let previousTenantIds: string[] = [];
      if (!tenantId) {
        // 从 Redux store 获取当前的租户关系列表
        const { store } = await import('@/app/store');
        const currentState = store.getState();
        const currentTenantRelations = currentState.tenant.tenantRelations;
        previousTenantIds = currentTenantRelations.map(tenant => tenant.id || '').filter(id => id);
        logService.info('记录加入团队前的租户列表', {
          count: previousTenantIds.length,
          tenantIds: previousTenantIds
        });
      }

      // 2. 刷新租户关系列表以获取最新数据
      logService.info('刷新租户关系列表');
      const updatedTenantRelations = await dispatch(fetchTenantRelations({ forceRefresh: true })).unwrap();

      // 3. 如果有租户ID，直接切换到指定租户
      if (tenantId) {
        logService.info('尝试切换到新加入的租户', { tenantId });

        try {
          // 使用 applyTenantToken 切换到新租户
          await dispatch(applyTenantToken(tenantId)).unwrap();

          logService.info('成功切换到新加入的租户', { tenantId });

          Toast.show({
            content: '已切换到新加入的团队',
            duration: 2000,
            position: 'top'
          });
        } catch (switchError) {
          logService.warn('切换到新租户失败，但加入团队成功', {
            tenantId,
            error: switchError
          });

          // 切换失败不影响整体流程，只是不自动切换
          Toast.show({
            content: '加入团队成功，请手动切换到新团队',
            duration: 3000,
            position: 'top'
          });
        } finally {
          // 无论成功失败都要隐藏loading状态
          setIsSwitchingTenant(false);
        }
      } else {
        // 4. 没有租户ID，通过对比找出新增的租户
        logService.info('没有租户ID，通过对比找出新增的租户');

        const currentTenantIds = updatedTenantRelations.map(tenant => tenant.id || '').filter(id => id);
        const newTenantIds = currentTenantIds.filter(id => !previousTenantIds.includes(id));

        logService.info('租户列表对比结果', {
          previousCount: previousTenantIds.length,
          currentCount: currentTenantIds.length,
          newTenantIds
        });

        if (newTenantIds.length === 1) {
          // 找到唯一的新租户，自动切换
          const newTenantId = newTenantIds[0];
          const newTenant = updatedTenantRelations.find(t => t.id === newTenantId);

          logService.info('找到新加入的租户，尝试自动切换', {
            newTenantId,
            tenantName: newTenant?.name
          });

          try {
            await dispatch(applyTenantToken(newTenantId)).unwrap();

            Toast.show({
              content: `已切换到新加入的团队：${newTenant?.name || ''}`,
              duration: 3000,
              position: 'top'
            });
          } catch (switchError) {
            logService.warn('自动切换到新租户失败', {
              newTenantId,
              error: switchError
            });

            Toast.show({
              content: '加入团队成功，请手动切换到新团队',
              duration: 3000,
              position: 'top'
            });
          } finally {
            // 无论成功失败都要隐藏loading状态
            setIsSwitchingTenant(false);
          }
        } else if (newTenantIds.length > 1) {
          // 发现多个新租户，无法确定要切换到哪个
          logService.warn('发现多个新租户，无法自动切换', { newTenantIds });
          Toast.show({
            content: '加入团队成功，请在侧边栏选择要切换的团队',
            duration: 3000,
            position: 'top'
          });
        } else {
          // 没有发现新租户，可能是数据同步问题
          logService.warn('未发现新租户，可能存在数据同步问题');
          Toast.show({
            content: '加入团队成功，请在侧边栏查看',
            duration: 3000,
            position: 'top'
          });
        }
      }

      // 5. 延迟返回上一页，让用户看到成功提示
      setTimeout(() => {
        navigate(-1);
      }, 2000);

    } catch (error) {
      logService.error('处理加入团队成功后的逻辑失败', { error, tenantId });

      // 即使处理失败，也要返回上一页
      Toast.show({
        content: '加入团队成功，但更新界面失败，请手动刷新',
        duration: 3000,
        position: 'top'
      });

      setTimeout(() => {
        navigate(-1);
      }, 2000);
    }
  };

  // 處理手動輸入的邀請碼
  const handleManualSubmit = async () => {
    if (!manualCode.trim()) {
      Toast.show({
        content: '請輸入邀請碼',
        duration: 2000
      });
      return;
    }

    try {
      // await stopScan();

      logService.info('手動輸入邀請碼', { manualCode });

      // Toast.show({ content: '正在加入團隊...', position: 'top' });

      const { default: tenantService } = await import('../../services/core/tenant/tenantService');
      const triggerResult = await tenantService.triggerInvitation(manualCode.trim());

      if (triggerResult.success) {
        // 成功加入团队后立即停止扫描并显示切换租户的loading
        await stopScan();
        setIsSwitchingTenant(true);

        Toast.show({
          content: '成功加入團隊！',
          position: 'top',
          duration: 3000
        });

        // 對於手動輸入，我們沒有直接的 tenantId，只調用不帶參數的方法
        await handleJoinTeamSuccess();
      } else {
        Toast.show({
          content: triggerResult.msg || '加入團隊失敗，請稍後再試',
          position: 'top',
          duration: 3000
        });
      }

      // 返回上一頁
      // setTimeout(() => navigate(-1), 2000);
    } catch (error) {
      logService.error('手動輸入處理失敗', error);
      Toast.show({
        content: '網絡錯誤，請稍後再試',
        position: 'top',
        duration: 3000
      });
    }
  };

  return (
    <div className="qr-scanner-container">
      {/* 頭部 */}
      <div className="scanner-header">
        <button className="back-button" onClick={handleClose}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="#333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <h1 className="scanner-title">{t('掃一掃')}</h1>
      </div>

      {/* Web 端摄像头视频 */}
      {deviceService.isWeb() && (
        <>
          <video
            ref={videoRef}
            className="web-camera-video"
            autoPlay
            playsInline
            muted
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100vw',
              height: '100vh',
              objectFit: 'cover',
              zIndex: 1
            }}
          />
          <canvas
            ref={canvasRef}
            style={{ display: 'none' }}
          />
        </>
      )}

      {/* 四個方向的遮罩 */}
      <div className="mask-top"></div>
      <div className="mask-bottom"></div>
      <div className="mask-left"></div>
      <div className="mask-right"></div>
      <div className="mask-bottom-extra"></div>

      {/* 掃描框 */}
      <div className="scan-box">
        <div className="corner-bottom-left"></div>
        <div className="corner-bottom-right"></div>
        {!isSwitchingTenant && <div className="scan-line"></div>}

        {/* 在扫描区域内显示切换租户Loading */}
        {isSwitchingTenant && (
          <div className="scan-box-loading">
            <DotLoading color="#1677FF" />
            <div className="scan-box-loading-text">{t('正在嘗試切換到新的租戶...')}</div>
          </div>
        )}
      </div>

      {/* 底部輸入區域 */}
      <div className="scanner-bottom">
        <div className="input-container">
          <div className="input-wrapper">
            <input
              type="text"
              className="code-input"
              placeholder={t('請輸入邀請碼')}
              value={manualCode}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, ''); // 只允許數字
                if (value.length <= 6) { // 限制最多6位
                  setManualCode(value);
                }
              }}
              maxLength={6}
              inputMode="numeric"
              pattern="[0-9]*"
            />
            <Button 
              className="submit-button"
              color="primary"
              onClick={handleManualSubmit}
              // disabled={!manualCode.trim()}
              block={false}
              fill="solid"
              shape="default"
              size="middle"
            >
              {t('送出')}
            </Button>
          </div>
        </div>
      </div>


    </div>
  );
};

export default QRScanner; 