import React, { useState } from 'react';
import { Card, Space, List, Tag } from 'antd-mobile';
import MessageStatusIndicator from '@/components/chat/MessageStatusIndicator';
import './MessageStatusUIDemo.css';

/**
 * 消息状态UI演示组件
 * 展示不同状态下的消息气泡和状态指示器效果
 */
export const MessageStatusUIDemo: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<'sending' | 'sent' | 'delivered' | 'read' | 'failed'>('sent');

  const statusOptions = [
    { value: 'sending', label: '发送中', color: 'default' },
    { value: 'sent', label: '已发送', color: 'primary' },
    { value: 'delivered', label: '已到达', color: 'success' },
    { value: 'read', label: '已读', color: 'success' },
    { value: 'failed', label: '发送失败', color: 'danger' }
  ] as const;

  const mockMessages = [
    { id: '1', content: 'Hello! 你好', status: 'read' as const },
    { id: '2', content: '这是一条比较长的消息，用来测试消息气泡的换行效果和状态指示器的位置', status: 'delivered' as const },
    { id: '3', content: '短消息', status: 'sent' as const },
    { id: '4', content: '发送中的消息...', status: 'sending' as const },
    { id: '5', content: '发送失败的消息', status: 'failed' as const }
  ];

  return (
    <div className="message-status-ui-demo">
      <Card title="消息状态UI演示" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <strong>选择状态:</strong>
            <div style={{ marginTop: '8px' }}>
              <Space wrap>
                {statusOptions.map((option) => (
                  <Tag
                    key={option.value}
                    color={selectedStatus === option.value ? option.color : 'default'}
                    onClick={() => setSelectedStatus(option.value)}
                    style={{ cursor: 'pointer' }}
                  >
                    {option.label}
                  </Tag>
                ))}
              </Space>
            </div>
          </div>
          
          <div>
            <strong>状态指示器预览:</strong>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '8px' }}>
              <span>小尺寸:</span>
              <MessageStatusIndicator status={selectedStatus} size="small" />
              <span>中尺寸:</span>
              <MessageStatusIndicator status={selectedStatus} size="medium" />
              <span>大尺寸:</span>
              <MessageStatusIndicator status={selectedStatus} size="large" />
            </div>
          </div>
        </Space>
      </Card>

      <Card title="消息气泡效果预览" style={{ marginBottom: '16px' }}>
        <div className="demo-chat-container">
          {/* 模拟聊天界面背景 */}
          <div className="demo-chat-background">
            {mockMessages.map((message) => (
              <div key={message.id} className="demo-message-row">
                <div className="demo-message-container user">
                  <div className="demo-message-wrapper user">
                    <div className="demo-message-time">17:28</div>
                    <div className="demo-message-bubble-container user">
                      <div className="demo-message-bubble user">
                        {message.content}
                      </div>
                      <div className="demo-message-status">
                        <MessageStatusIndicator 
                          status={message.status}
                          size="small"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      <Card title="状态说明">
        <List>
          {statusOptions.map((option) => (
            <List.Item
              key={option.value}
              prefix={
                <MessageStatusIndicator 
                  status={option.value}
                  size="medium"
                />
              }
              extra={
                <Tag color={option.color}>
                  {option.value}
                </Tag>
              }
            >
              <div>
                <div><strong>{option.label}</strong></div>
                <div style={{ fontSize: '12px', color: '#999' }}>
                  {option.value === 'sending' && '消息正在发送中，显示旋转动画'}
                  {option.value === 'sent' && '消息已发送到服务器，显示空心圆圈'}
                  {option.value === 'delivered' && '消息已到达对方设备，显示空心圆圈带实心点'}
                  {option.value === 'read' && '消息已被对方查看，显示实心圆圈'}
                  {option.value === 'failed' && '消息发送失败，显示红色叉号'}
                </div>
              </div>
            </List.Item>
          ))}
        </List>
      </Card>
    </div>
  );
};

export default MessageStatusUIDemo;
