import React from 'react';
import { Badge, List, Toast, SwipeAction } from 'antd-mobile';
import { ErrorBlock } from 'antd-mobile';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MessageTabSkeleton } from '@/components/common';
import './RoomListTab.css';
import { useEffect, useState, useRef, useCallback } from 'react';
import { fetchRobotRoomsFromDB, fetchActiveRoomsFromDB, fetchProcessedRoomsFromDB, fetchRobotRoomsFromAPI, fetchActiveRoomsFromAPI, fetchProcessedRoomsFromAPI, setInitialized, resetAll } from '@/app/slices/roomSlice';
import { logService } from '@/services/system/logService';
import { RoomType, type RoomVO, Status } from '@/services/core/chat/roomService.types';
import { SessionStatus } from '@/services/core/chat/roomService';
import contactService from '@/services/core/tenant/contactService';
import { getLastMessageSummary } from '@/utils/messageUtil';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import type { RootState } from '@/app/store';
import { getBasicRoomsFromDB } from '@/services/core/chat/roomService';
import AvatarImage from '@/components/common/AvatarImage';
import { useRoomWeight } from '@/hooks/useRoomWeight';
import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from '@/types/aile.enum';
import { generateRoute } from '@/config/app/routes';
import snService from '@/services/core/tenant/snService';
import { roomDao } from '@/services/dao';
import { getPinnedRoomIds, setPinnedRoomIds } from '@/utils/storage';
import stateService, { RoomAction } from '@/services/stateService';
import { getUserById } from '@/services/core/tenant/userService';

// Import icons
// import muteIcon from '../../../assets/icons/mute.svg';
import editIcon from '../../../assets/icons/edit-icon.svg';
import pinIcon from '../../../assets/icons/pin.svg';
import { SystemMessageEventCode } from '@/services/core/chat';

type PageKey = 'robot' | 'active' | 'processed';

// 格式化聊天時間顯示
const formatChatTime = (timestamp: number | undefined): string => {
  if (timestamp === undefined || isNaN(Number(timestamp))) {
    return '';
  }

  const messageDate = new Date(timestamp);
  const now = new Date();
  
  // 檢查是否是今天
  if (messageDate.toDateString() === now.toDateString()) {
    // 今天，顯示時間 HH:mm
    return messageDate.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit', hour12: false });
  }
  
  // 檢查是否是昨天
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (messageDate.toDateString() === yesterday.toDateString()) {
    // 昨天
    return '昨天';
  }
  
  // 檢查是否是本週內（從週一開始計算）
  const nowWeekDay = now.getDay(); // 0是週日，1-6是週一至週六
  const messageWeekDay = messageDate.getDay();

  // 計算本週週一的日期
  const thisWeekMonday = new Date(now);
  const daysFromMonday = nowWeekDay === 0 ? 6 : nowWeekDay - 1; // 週日算作上週
  thisWeekMonday.setDate(now.getDate() - daysFromMonday);
  thisWeekMonday.setHours(0, 0, 0, 0);

  // 檢查消息日期是否在本週內（從週一開始）
  const messageDateOnly = new Date(messageDate);
  messageDateOnly.setHours(0, 0, 0, 0);

  if (messageDateOnly >= thisWeekMonday) {
    // 本週內且不是今天和昨天，顯示星期
    const weekDayNames = ['週日', '週一', '週二', '週三', '週四', '週五', '週六'];
    return weekDayNames[messageWeekDay];
  }

  // 超過本週，顯示 MM-DD
  return `${String(messageDate.getMonth() + 1).padStart(2, '0')}-${String(messageDate.getDate()).padStart(2, '0')}`;
};

const RoomListTab: React.FC<{ onScroll: (e: React.UIEvent<HTMLDivElement>) => void }> = ({ onScroll }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const {
    robotRooms,
    activeRooms,
    processedRooms,
    robotPage,
    activePage,
    processedPage,
    robotHasNextPage,
    activeHasNextPage,
    processedHasNextPage,
    robotTotalCount,
    activeTotalCount,
    processedTotalCount,
    initialized,
    lastInitTenantId,
  } = useAppSelector((state: RootState) => state.room as import('@/app/slices/roomSlice').RoomState);

  const currentTenantId = useAppSelector(state => state.tenant.currentTenantId);
  
  // 記錄上次的租戶ID
  const prevTenantIdRef = useRef<string | null>(null);

  // 使用权重计算Hook
  const roomWeightHook = useRoomWeight();
  const {
    calculateWeights,
    getSortedRoomWeights,
  } = roomWeightHook || {};
  const [activeFilter, setActiveFilter] = useState<PageKey>('active');
  const [robotLoading, setRobotLoading] = useState(false);
  const [activeLoading, setActiveLoading] = useState(false);
  const [processedLoading, setProcessedLoading] = useState(false);
  const [initLoading, setInitLoading] = useState(true);
  const [showLoading, setShowLoading] = useState(false);

  // 監聽租戶切換
  useEffect(() => {
    if (!currentTenantId) {
      logService.warn('當前租戶ID為空，跳過處理');
      return;
    }

    // 只在真正的租戶切換時才執行重置
    if (currentTenantId !== prevTenantIdRef.current && prevTenantIdRef.current !== null) {
      logService.info('租戶ID變更，重置數據並準備重新初始化', {
        from: prevTenantIdRef.current,
        to: currentTenantId
      });

      // 重置數據（這會清空 initialized 狀態）
      dispatch(resetAll());

      // 重置其他本地狀態
      setContactAvatarIds({});
      setServiceNumberAvatarIds({});
      contactAvatarIdsRef.current = {};
      serviceNumberAvatarIdsRef.current = {};
      setContactNames({});
      contactNamesRef.current = {};

      // 重置 loading 狀態，準備重新初始化
      setInitLoading(true);
      setShowLoading(true);

      logService.info('租戶切換重置完成，等待重新初始化', {
        newTenantId: currentTenantId
      });
    }

    // 更新租戶ID引用
    prevTenantIdRef.current = currentTenantId;
  }, [currentTenantId, dispatch]);

  // 當 initLoading 結束時，隱藏 loading 畫面
  useEffect(() => {
    if (!initLoading) {
      logService.info('初始化加載完成，隱藏 loading 畫面', {
        currentTenantId,
        initialized,
        lastInitTenantId
      });
      setShowLoading(false);
    }
  }, [initLoading, currentTenantId, initialized, lastInitTenantId]);

  // 权重排序开关 - 可以用来快速禁用权重排序功能
  const [enableWeightSorting] = useState(false); // 暂时禁用权重排序

  // 新增本地 state 保存 basicRooms
  const [basicRooms, setBasicRooms] = useState<RoomVO[]>([]);

  // 初始化機器人頁簽數據
  const initializeRobotTab = async () => {
    try {
      // 先从DB加载第0页（快速显示）
      await dispatch(fetchRobotRoomsFromDB(0));
      // 然后API加载第0页和第1页（直接存储到缓存）
      await dispatch(fetchRobotRoomsFromAPI(0, { force: true }));
      await dispatch(fetchRobotRoomsFromAPI(1));
    } catch (error) {
      logService.error('初始化機器人頁簽失敗', { error });
    }
  };

  // 初始化服務中頁簽數據
  const initializeActiveTab = async () => {
    try {
      // 先从DB加载第0页（快速显示）
      await dispatch(fetchActiveRoomsFromDB(0));
      // 然后API加载第0页和第1页（直接存储到缓存）
      await dispatch(fetchActiveRoomsFromAPI(0, { force: true }));
      await dispatch(fetchActiveRoomsFromAPI(1));
    } catch (error) {
      logService.error('初始化服務中頁簽失敗', { error });
    }
  };

  // 初始化已處理頁簽數據
  const initializeProcessedTab = async () => {
    try {
      // 先从DB加载第0页（快速显示）
      await dispatch(fetchProcessedRoomsFromDB(0));
      // 然后API加载第0页和第1页（直接存储到缓存）
      await dispatch(fetchProcessedRoomsFromAPI(0, { force: true }));
      await dispatch(fetchProcessedRoomsFromAPI(1));
    } catch (error) {
      logService.error('初始化已處理頁簽失敗', { error });
    }
  };

  // 首頁初始化時查詢 basicRooms
  useEffect(() => {
    getBasicRoomsFromDB().then(setBasicRooms).catch(() => setBasicRooms([]));
  }, [currentTenantId]);

  // 初始化預加載三個分頁前兩頁
  useEffect(() => {
    // 如果當前租戶ID為空，跳過初始化
    if (!currentTenantId) {
      logService.warn('當前租戶ID為空，跳過初始化');
      return;
    }

    // 檢查是否需要初始化
    const needsInitialization = !initialized || lastInitTenantId !== currentTenantId;

    if (!needsInitialization) {
      logService.info('頁面已初始化且租戶未變更，跳過重新加載', {
        initialized,
        lastInitTenantId,
        currentTenantId
      });
      return;
    }

    logService.info('開始初始化頁面數據', {
      initialized,
      lastInitTenantId,
      currentTenantId,
      reason: !initialized ? '首次初始化' : '租戶切換',
      prevTenantId: prevTenantIdRef.current
    });

    // 確保 loading 狀態已設置
    if (!initLoading) {
      setInitLoading(true);
      setShowLoading(true);
    }

    const initializeAllTabs = async () => {
      try {
        logService.info('開始初始化所有頁簽', {
          currentTenantId,
          initialized,
          lastInitTenantId
        });

        // 分別初始化三個頁簽，不使用循環
        await Promise.all([
          initializeRobotTab(),
          initializeActiveTab(),
          initializeProcessedTab()
        ]);

        // 標記為已初始化
        if (currentTenantId) {
          dispatch(setInitialized({ tenantId: currentTenantId }));
          logService.info('所有頁簽初始化完成，已標記為已初始化', {
            tenantId: currentTenantId
          });
        }

      } catch (error) {
        logService.error('初始化所有頁簽失敗', { error });
      } finally {
        setInitLoading(false);
      }
    };

    // 使用 setTimeout 確保在 React 渲染週期之後執行初始化
    // 這有助於避免與租戶切換 useEffect 的競態條件
    setTimeout(() => {
      initializeAllTabs();
    }, 0);
  }, [dispatch, currentTenantId, initialized, lastInitTenantId, initLoading]);

  // 處理 roomChanged 訂閱
  useEffect(() => {
    const subscribe = stateService.on('roomChanged', async (data: {
      action: RoomAction;
      roomId?: string;
      room?: RoomVO | null;
    }) => {
      if ((data.action === RoomAction.lastMessageUpdate || data.action === RoomAction.add) && data.roomId && data.room) {
        // 分別刷新三個頁簽的第0頁數據，不使用循環
        dispatch(fetchRobotRoomsFromDB(0));
        dispatch(fetchActiveRoomsFromDB(0));
        dispatch(fetchProcessedRoomsFromDB(0));
      }
    });
    return () => subscribe();
  }, [dispatch]);

  // 滾動加載
  const handleScroll = async (type: PageKey, e: React.UIEvent<HTMLDivElement>) => {
    let loadingSetter = type === 'robot' ? setRobotLoading : type === 'active' ? setActiveLoading : setProcessedLoading;
    if (type === 'robot' ? robotLoading : type === 'active' ? activeLoading : processedLoading) return;
    const target = e.currentTarget;

    // 判斷是否滾動到底部
    const isScrolledToBottom = target.scrollHeight - target.scrollTop - target.clientHeight < 50;

    if (!isScrolledToBottom) {
      if (onScroll) onScroll(e);
      return;
    }

    // 判斷 hasNextPage
    const hasNextPage = type === 'robot' ? robotHasNextPage : type === 'active' ? activeHasNextPage : processedHasNextPage;
    if (!hasNextPage) {
      // 已經沒有更多數據，不需要顯示任何提示，直接返回
      if (onScroll) onScroll(e);
      return;
    }

    // 有更多數據且滾動到底部，開始加載
    loadingSetter(true);
    const nextPage = (type === 'robot' ? robotPage : type === 'active' ? activePage : processedPage) + 1;

    try {
      // 根據頁簽類型調用對應的DB和API函數
      if (type === 'robot') {
        dispatch(fetchRobotRoomsFromAPI(nextPage));

      } else if (type === 'active') {
        dispatch(fetchActiveRoomsFromAPI(nextPage));
      } else if (type === 'processed') {
        dispatch(fetchProcessedRoomsFromAPI(nextPage));
      }
    } catch (error) {
      logService.error('滾動加載失敗', { error, type, nextPage });
    } finally {
      loadingSetter(false);
    }

    if (onScroll) onScroll(e);
  };

  const filters: { key: PageKey; title: string; hasBadge?: boolean }[] = [
    { key: 'robot', title: t(`機器人(${robotTotalCount || 0})`) },
    { key: 'active', title: t(`服務中(${activeTotalCount || 0})`) },
    { key: 'processed', title: t('已處理'), hasBadge: processedTotalCount > 0 },
  ];

  const renderBadge = (number?: number) => {
    if (typeof number !== 'number' || number <= 0) return null;
    return <Badge content={number > 99 ? '99+' : number} className="unread-badge" />;
  };

  // 使用 useState 緩存客戶頭像 ID
  const [contactAvatarIds, setContactAvatarIds] = useState<Record<string, string>>({});
  // 使用 useRef 記錄最新的 contactAvatarIds，避免重複獲取
  const contactAvatarIdsRef = useRef<Record<string, string>>({});
  
  // 使用 useState 緩存服務號頭像 ID
  const [serviceNumberAvatarIds, setServiceNumberAvatarIds] = useState<Record<string, string>>({});
  // 使用 useRef 記錄最新的 serviceNumberAvatarIds，避免重複獲取
  const serviceNumberAvatarIdsRef = useRef<Record<string, string>>({});
  
  // 當 contactAvatarIds 更新時，同步更新 ref
  useEffect(() => {
    contactAvatarIdsRef.current = contactAvatarIds;
  }, [contactAvatarIds]);
  
  // 當 serviceNumberAvatarIds 更新時，同步更新 ref
  useEffect(() => {
    serviceNumberAvatarIdsRef.current = serviceNumberAvatarIds;
  }, [serviceNumberAvatarIds]);
  
  // 使用 useEffect 在組件加載時獲取聊天室相關的客戶頭像
  useEffect(() => {
    const fetchContactAvatarIds = async () => {
      const rooms = [...robotRooms, ...activeRooms, ...processedRooms, ...basicRooms];
      const serviceRooms = rooms.filter(room => 
        room.type === RoomType.Services && room.ownerId && !room.avatarId
      );
      
      try {
        // 使用 ref 而不是 state 來檢查已經獲取的頭像
        const ownerIds = serviceRooms
          .map(room => room.ownerId as string)
          .filter(id => id && !contactAvatarIdsRef.current[id]);
        
        if (ownerIds.length === 0) return;
        
        // 批量獲取所有需要的聯絡人頭像
        const newAvatarIds: Record<string, string> = {};
        let hasNewAvatarIds = false;
        
        // 逐個查詢客戶資料
        await Promise.all(
          ownerIds.map(async (ownerId) => {
            try {
              const contact = await contactService.getContactById(ownerId);
              if (contact && contact.avatarId) {
                newAvatarIds[ownerId] = contact.avatarId;
                hasNewAvatarIds = true;
              }
            } catch (error) {
              logService.error('獲取客戶頭像失敗', { error: error as Error, ownerId });
            }
          })
        );
        
        // 更新 state
        if (hasNewAvatarIds) {
          setContactAvatarIds(prev => ({
            ...prev,
            ...newAvatarIds
          }));
        }
      } catch (error) {
        logService.error('獲取客戶頭像過程中發生錯誤', { error });
      }
    };

    fetchContactAvatarIds();
  }, [robotRooms, activeRooms, processedRooms, basicRooms]);
  
  // 使用 useEffect 在組件加載時獲取聊天室相關的服務號頭像
  useEffect(() => {
    const fetchServiceNumberAvatarIds = async () => {
      const rooms = [...robotRooms, ...activeRooms, ...processedRooms, ...basicRooms];
      const serviceMemberRooms = rooms.filter(room => 
        room.type === RoomType.ServiceMember && room.serviceNumberId && !room.avatarId
      );
      
      try {
        // 使用 ref 而不是 state 來檢查已經獲取的頭像
        const serviceNumberIds = serviceMemberRooms
          .map(room => room.serviceNumberId as string)
          .filter(id => id && !serviceNumberAvatarIdsRef.current[id]);
        
        if (serviceNumberIds.length === 0) return;
        
        // 批量獲取所有需要的服務號頭像
        const newAvatarIds: Record<string, string> = {};
        let hasNewAvatarIds = false;
        
        // 逐個查詢服務號資料
        await Promise.all(
          serviceNumberIds.map(async (serviceNumberId) => {
            try {
              const serviceNumber = await snService.getServiceNumberById(serviceNumberId);
              if (serviceNumber && serviceNumber.avatarId) {
                newAvatarIds[serviceNumberId] = serviceNumber.avatarId;
                hasNewAvatarIds = true;
              }
            } catch (error) {
              logService.error('獲取服務號頭像失敗', { error: error as Error, serviceNumberId });
            }
          })
        );
        
        // 更新 state
        if (hasNewAvatarIds) {
          setServiceNumberAvatarIds(prev => ({
            ...prev,
            ...newAvatarIds
          }));
        }
      } catch (error) {
        logService.error('獲取服務號頭像過程中發生錯誤', { error });
      }
    };

    fetchServiceNumberAvatarIds();
  }, [robotRooms, activeRooms, processedRooms, basicRooms]);

  const renderChatAvatar = (room: RoomVO) => {
    // 使用最合適的頭像 ID
    let avatarId = room.avatarId || null;
    // let displayName = getRoomDisplayName(room);

    // 如果是服務類型的聊天室，且沒有頭像，嘗試使用客戶的頭像
    if (room.type === RoomType.Services && !avatarId && room.ownerId) {
      avatarId = contactAvatarIds[room.ownerId] || null;
    }
    else if (room.type === RoomType.ServiceMember && !avatarId && room.serviceNumberId) {
      avatarId = serviceNumberAvatarIds[room.serviceNumberId] || null;
    }
    else if ((room.type === RoomType.Person || room.type === RoomType.AccountPerson) && !avatarId) {
      // 使用當前登錄用戶的頭像ID
      const currentUser = stateService.loginUser();
      avatarId = currentUser?.avatarId || null;
    }
    
    return (
      <AvatarImage 
        avatarId={avatarId} 
        size={44} 
        className="chat-avatar"
        name={getRoomDisplayName(room)}
      />
    );
  };
  
  // 處理聊天項目點擊
  const handleChatItemClick = (item: RoomVO) => {
    // 預處理：提前將頭像ID注入到room對象中，避免聊天室頁面重複查詢
    let processedItem = { ...item };
    
    // 如果沒有頭像但有相關ID信息，將我們已經查詢過的頭像ID添加到room對象
    if (!processedItem.avatarId) {
      if (processedItem.type === RoomType.Services && processedItem.ownerId && contactAvatarIds[processedItem.ownerId]) {
        processedItem.avatarId = contactAvatarIds[processedItem.ownerId];
        logService.debug('使用緩存的客戶頭像', { roomId: processedItem.id, ownerId: processedItem.ownerId });
      }
      else if (processedItem.type === RoomType.ServiceMember && processedItem.serviceNumberId && serviceNumberAvatarIds[processedItem.serviceNumberId]) {
        processedItem.avatarId = serviceNumberAvatarIds[processedItem.serviceNumberId];
        logService.debug('使用緩存的服務號頭像', { roomId: processedItem.id, serviceNumberId: processedItem.serviceNumberId });
      }
      else if (processedItem.type === RoomType.Person || processedItem.type === RoomType.AccountPerson) {
        const currentUser = stateService.loginUser();
        if (currentUser?.avatarId) {
          processedItem.avatarId = currentUser.avatarId;
          logService.debug('使用當前用戶頭像', { roomId: processedItem.id });
        }
      }
    }
    
    // 更新本地數據庫中的房間資訊（包含頭像ID）
    if (processedItem.avatarId && processedItem.id) {
      roomDao.upsertRooms([processedItem])
        .catch((err: Error) => logService.error('更新房間頭像資訊失敗', { error: err, roomId: processedItem.id }));
    }
    
    if (processedItem.type === RoomType.ServiceMember) {
      // 團隊聊天室
      if (typeof processedItem.id === 'string') {
        navigate(generateRoute.teamRoom(processedItem.id));
      }
    } else if (processedItem.type === RoomType.Person || processedItem.type === RoomType.AccountPerson) {
      // 我的聊天室
      if (typeof processedItem.id === 'string') {
        navigate(generateRoute.chatRoom(processedItem.id));
      }
    } else if (processedItem.type === RoomType.AileSystem) {
      // 系統聊天室
      if (typeof processedItem.id === 'string') {
        navigate(generateRoute.systemRoom(processedItem.id));
      }
    } else if (processedItem.type === RoomType.Services) {
      // 所有其他聊天項目視為客戶聊天
      if (typeof processedItem.id === 'string') {
        navigate(generateRoute.customerRoom(processedItem.id));
      }
    } else {
      // 其他類型
    }
  };

  const isEmpty = robotRooms.length === 0 && activeRooms.length === 0 && processedRooms.length === 0 && basicRooms.length === 0;

  // active 分頁渲染時合併 basicRooms，去重（以 activeRooms 為主）
  const mergedActiveRooms = activeFilter === 'active'
    ? [
        ...basicRooms.filter(b => !activeRooms.some(a => a.id === b.id)),
        ...activeRooms
      ]
    : activeRooms;

  // 使用 useState 緩存客戶名稱
  const [contactNames, setContactNames] = useState<Record<string, string>>({});
  // 使用 useRef 記錄最新的 contactNames，避免重複獲取
  const contactNamesRef = useRef<Record<string, string>>({});
  
  // 當 contactNames 更新時，同步更新 ref
  useEffect(() => {
    contactNamesRef.current = contactNames;
  }, [contactNames]);
  
  // 使用 useEffect 在組件加載時獲取聊天室相關的客戶名稱
  useEffect(() => {
    const fetchContactNames = async () => {
      const rooms = [...robotRooms, ...activeRooms, ...processedRooms, ...basicRooms];
      const serviceRooms = rooms.filter(room => 
        room.type === RoomType.Services && room.ownerId
      );
      
      try {
        // 使用 ref 而不是 state 來檢查已經獲取的名稱
        const ownerIds = serviceRooms
          .map(room => room.ownerId as string)
          .filter(id => id && !contactNamesRef.current[id]);
        
        if (ownerIds.length === 0) return;
        
        // 批量獲取所有需要的聯絡人資訊
        const newNames: Record<string, string> = {};
        let hasNewNames = false;
        
        // 逐個查詢客戶資料
        await Promise.all(
          ownerIds.map(async (ownerId) => {
            try {
              const contact = await contactService.getContactById(ownerId);
              if (contact && contact.name) {
                newNames[ownerId] = contact.name;
                hasNewNames = true;
              }
            } catch (error) {
              logService.error('獲取客戶名稱失敗', { error: error as Error, ownerId });
            }
          })
        );
        
        // 更新 state
        if (hasNewNames) {
          setContactNames(prev => ({
            ...prev,
            ...newNames
          }));
        }
      } catch (error) {
        logService.error('獲取客戶名稱過程中發生錯誤', { error });
      }
    };
    
    fetchContactNames();
  }, [robotRooms, activeRooms, processedRooms, basicRooms]);

  // 清理聊天室名稱，移除末尾的 " 0"、"(0)" 等模式
  const cleanRoomName = (name: string): string => {
    if (!name) return name;
    // 移除末尾的 " 0"、"(0)"、" (0)"、" 0 " 等模式
    return name.replace(/\s*\(?0\)?\s*$/, '').trim();
  };

  // 根據 type 返回顯示名稱
  const getRoomDisplayName = (room: RoomVO): string => {
    if (!room.type) return t('未定義聊天室');
    
    if (room.type === RoomType.Person || room.type === RoomType.AccountPerson) return t('我的聊天室');
    if (room.type === RoomType.AileSystem) return t('系統聊天室');
    if (room.type === RoomType.ServiceMember) return t('團隊聊天室');
    if (room.type === RoomType.Services) {
      // 如果是服務類型的聊天室，嘗試獲取客戶名稱
      if (room.ownerId && contactNames[room.ownerId]) {
        return cleanRoomName(contactNames[room.ownerId]);
      }
      // 沒有找到客戶名稱，顯示未定義
      return t('未命名聊天室');
    }
    return cleanRoomName(room.name || t('未命名聊天室'));
  };

  // 根据聊天室状态计算权重
  const calculateRoomWeight = (room: RoomVO) => {
    try {
      // 安全检查
      if (!room || !room.id) {
        logService.warn('房间数据无效，使用默认权重', { room });
        return {
          p1: P1WeightEnum.Default,
          p2: P2WeightEnum.Default,
          p3: [P3WeightEnum.Default],
          timestamp: Date.now(),
        };
      }

      // P1: 根据聊天室会话状态设置最高优先级
      let p1 = P1WeightEnum.Default;
      if (room.type === RoomType.Services) {
        // 客服聊天室根据会话状态设置优先级
        switch (room.sessionStatus) {
          case SessionStatus.DistributeActive:
            p1 = P1WeightEnum.JustReceived; // 转人工，刚进件
            break;
          case SessionStatus.AgentActive:
            // 根据agentId判断是否为当前用户的服务
            try {
              const currentUser = stateService.loginUser();
              if (currentUser && room.agentId === currentUser.id) {
                p1 = P1WeightEnum.MyService; // 我的服务中
              } else {
                p1 = P1WeightEnum.InService; // 服务中
              }
            } catch (error) {
              logService.warn('获取当前用户失败，使用默认P1权重', { error });
              p1 = P1WeightEnum.InService; // 默认为服务中
            }
            break;
          case SessionStatus.RobotActive:
            p1 = P1WeightEnum.Default; // 机器人服务中，默认优先级
            break;
          default:
            p1 = P1WeightEnum.Default;
        }
      }

      // P2: 根据未读消息设置次要优先级
      const p2 = (room.unreadCount && room.unreadCount > 0) ? P2WeightEnum.Unread : P2WeightEnum.Default;

      // P3: 根据聊天室标签设置优先级
      const p3Tags: P3WeightEnum[] = [];
      if (room.isPin) p3Tags.push(P3WeightEnum.Top); // 置顶
      if (room.isFavorite) p3Tags.push(P3WeightEnum.Favorite); // 收藏
      if (room.isAIDraft) p3Tags.push(P3WeightEnum.Draft); // AI草稿
      if (room.isDraft) p3Tags.push(P3WeightEnum.Draft); // 草稿
      if (room.hasAtMe) p3Tags.push(P3WeightEnum.AtMe); // @我
      if (room.hasSendFailed) p3Tags.push(P3WeightEnum.SendFailed); // 发送失败

      // 根据房间状态添加特殊标签
      if (room.status === Status.Danger) p3Tags.push(P3WeightEnum.SendFailed); // 危险状态
      if (room.status === Status.Complaint) p3Tags.push(P3WeightEnum.MarkUnread); // 投诉状态

      if (p3Tags.length === 0) p3Tags.push(P3WeightEnum.Default);

      return {
        p1,
        p2,
        p3: p3Tags,
        timestamp: room.updateTime || Date.now(),
      };
    } catch (error) {
      logService.error('计算房间权重失败，使用默认权重', { error, roomId: room?.id });
      return {
        p1: P1WeightEnum.Default,
        p2: P2WeightEnum.Default,
        p3: [P3WeightEnum.Default],
        timestamp: Date.now(),
      };
    }
  };

  // 为active列表计算权重并排序
  useEffect(() => {
    if (activeFilter === 'active' && calculateWeights && enableWeightSorting) {
      try {
        // 重新计算 mergedActiveRooms 避免依赖循环
        const currentMergedRooms = activeFilter === 'active'
          ? [
              ...basicRooms.filter(b => !activeRooms.some(a => a.id === b.id)),
              ...activeRooms
            ]
          : activeRooms;

        if (currentMergedRooms.length > 0) {
          const roomWeights: Record<string, any> = {};

          currentMergedRooms.forEach(room => {
            if (room && room.id) {
              try {
                roomWeights[room.id] = calculateRoomWeight(room);
              } catch (error) {
                logService.error('计算单个房间权重失败', { error, roomId: room.id });
              }
            }
          });

          // 批量计算权重
          if (Object.keys(roomWeights).length > 0) {
            calculateWeights(roomWeights);
          }
        }
      } catch (error) {
        logService.error('权重计算useEffect失败', { error });
      }
    }
  }, [activeFilter, activeRooms, basicRooms, calculateWeights, enableWeightSorting]);

  // 排序：置頂房間在最上面（保留原有逻辑作为备用）
  const sortRoomsByTop = (rooms: RoomVO[]) => {
    return [...rooms].sort((a, b) => (b.isPin ? 1 : 0) - (a.isPin ? 1 : 0) || (b.updateTime || 0) - (a.updateTime || 0));
  };

  // 使用权重排序active列表
  const getSortedActiveRooms = () => {
    try {
      if (activeFilter !== 'active' || !enableWeightSorting) {
        return sortRoomsByTop(mergePinState(mergedActiveRooms));
      }

      // 获取权重排序后的房间ID列表
      const sortedWeights = getSortedRoomWeights ? getSortedRoomWeights() : [];
      if (!sortedWeights || sortedWeights.length === 0) {
        // 如果没有权重数据，使用原有排序
        return sortRoomsByTop(mergePinState(mergedActiveRooms));
      }

      const sortedRooms: RoomVO[] = [];
      const unsortedRooms: RoomVO[] = [];

      // 按权重顺序排列房间
      sortedWeights.forEach(({ roomId }) => {
        if (roomId) {
          const room = mergedActiveRooms.find(r => r.id === roomId);
          if (room) {
            sortedRooms.push(room);
          }
        }
      });

      // 添加没有权重的房间到末尾
      mergedActiveRooms.forEach(room => {
        if (room && room.id && !sortedRooms.find(r => r.id === room.id)) {
          unsortedRooms.push(room);
        }
      });

      // 合并权重排序的房间和未排序的房间，并应用置顶状态
      const finalRooms = [...sortedRooms, ...sortRoomsByTop(unsortedRooms)];
      return mergePinState(finalRooms);
    } catch (error) {
      logService.error('权重排序失败，使用默认排序', { error });
      // 发生错误时使用原有排序逻辑
      return sortRoomsByTop(mergePinState(mergedActiveRooms));
    }
  };

  // 2. 在排序與渲染前，合併 isPin 狀態
  const mergePinState = (rooms: RoomVO[]): RoomVO[] => {
    const pinnedIds = getPinnedRoomIds();
    return rooms.map(room => ({ ...room, isPin: pinnedIds.includes(room.id ?? '') }));
  };

  // 動態生成 SwipeAction 操作
  const getSwipeActions = (room: RoomVO) => {
    const pinnedIds = getPinnedRoomIds();
    if (room.isPin) {
      // 置頂房間只能右滑取消置頂
      return {
        leftActions: [],
        rightActions: [
          {
            key: 'unpin',
            text: t('取消置頂'),
            color: 'light',
            onClick: async () => {
              try {
                const newIds = pinnedIds.filter(id => id !== room.id);
                setPinnedRoomIds(newIds);
                Toast.show({ content: t('已取消置頂'), position: 'bottom' });
                // 根據當前頁簽刷新對應的DB數據
                if (activeFilter === 'robot') {
                  dispatch(fetchRobotRoomsFromDB(0));
                } else if (activeFilter === 'active') {
                  dispatch(fetchActiveRoomsFromDB(0));
                } else if (activeFilter === 'processed') {
                  dispatch(fetchProcessedRoomsFromDB(0));
                }
              } catch (error) {
                logService.error('取消置頂失敗', { error, room });
                Toast.show({ content: t('取消置頂失敗'), position: 'bottom' });
              }
            },
          },
        ],
      };
    } else {
      // 未置頂房間只能左滑置頂
      return {
        leftActions: [
          {
            key: 'pin',
            text: t('置頂'),
            color: 'primary',
            onClick: async () => {
              try {
                const newIds = [...pinnedIds, room.id ?? ''].filter(Boolean);
                setPinnedRoomIds(Array.from(new Set(newIds)));
                Toast.show({ content: t('已置頂'), position: 'bottom' });
                // 根據當前頁簽刷新對應的DB數據
                if (activeFilter === 'robot') {
                  dispatch(fetchRobotRoomsFromDB(0));
                } else if (activeFilter === 'active') {
                  dispatch(fetchActiveRoomsFromDB(0));
                } else if (activeFilter === 'processed') {
                  dispatch(fetchProcessedRoomsFromDB(0));
                }
              } catch (error) {
                logService.error('置頂失敗', { error, room });
                Toast.show({ content: t('置頂失敗'), position: 'bottom' });
              }
            },
          },
        ],
        rightActions: [],
      };
    }
  };

  // robotRooms、activeRooms、processedRooms 排序
  const sortedRobotRooms = sortRoomsByTop(mergePinState(robotRooms));
  const sortedActiveRooms = getSortedActiveRooms(); // 使用权重排序
  const sortedProcessedRooms = sortRoomsByTop(mergePinState(processedRooms));

  // 使用 useState 存储消息发送者信息
  const [senderInfo, setSenderInfo] = useState<Record<string, { avatarId: string | null, name: string }>>({});

  // 获取发送者信息的函数
  const fetchSenderInfo = useCallback(async (senderId: string) => {
    try {
      // 尝试作为用户查询
      if(senderId){
        const user = await getUserById(senderId);
        if (user) {
          const userInfo = {
            avatarId: user.avatarId || null,
            name: user.name || user.account || '未知用戶'
          };
          setSenderInfo(prev => {
            // 如果已有此发送者的信息，不再重复设置
            if (prev[senderId]) {
              return prev;
            }
            return {
              ...prev,
              [senderId]: userInfo
            };
          });
          return;
        }
  
        // 尝试作为联络人查询
        const contact = await contactService.getContactById(senderId);
        if (contact) {
          const contactInfo = {
            avatarId: contact.avatarId || null,
            name: contact.name || '未知聯絡人'
          };
          setSenderInfo(prev => {
            // 如果已有此发送者的信息，不再重复设置
            if (prev[senderId]) {
              return prev;
            }
            return {
              ...prev,
              [senderId]: contactInfo
            };
          });
          return;
        }
  
        // 如果都找不到，记录一个空值避免重复查询
        setSenderInfo(prev => {
          // 如果已有此发送者的信息，不再重复设置
          if (prev[senderId]) {
            return prev;
          }
          return {
            ...prev,
            [senderId]: { avatarId: null, name: 'Aile小助手' }
          };
        });
      }
      
    } catch (error) {
      logService.error('獲取發送者信息失敗', { error: error as Error, senderId });
    }
  }, []); // 移除 senderInfo 依赖，避免循环依赖

  // 收集并获取所有房间最后一条消息的发送者ID
  useEffect(() => {
    const allRooms = [...robotRooms, ...activeRooms, ...processedRooms, ...basicRooms];
    if (allRooms.length === 0) return;

    const allSenderIds = new Set<string>();
    
    allRooms.forEach(room => {
      if (!room.lastMessage) return;
      
      let messageObj: any = room.lastMessage;
      
      // 如果是字符串，尝试解析为JSON
      if (typeof room.lastMessage === 'string') {
        try {
          messageObj = JSON.parse(room.lastMessage);
        } catch (e) {
          return;
        }
      }
      
      // 如果解析成功且含有senderId，添加到集合
      if(messageObj.sourceType === "System"){
        const content = messageObj.content;
        if(content){
          const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;
          if(parsedContent.event !== SystemMessageEventCode.SessionStart){
            if(parsedContent.event === SystemMessageEventCode.RoomMemberAdd){
              allSenderIds.add(parsedContent.content.memberId);
              parsedContent.content.memberIds.forEach((id: string) => {
                allSenderIds.add(id);
              });
            }else{
              allSenderIds.add(parsedContent.content.agentId);
            }
          }
        }
      }else if (messageObj && messageObj.senderId) {
        allSenderIds.add(messageObj.senderId);
      }
    });
    
    // 对未缓存的发送者ID获取信息
    allSenderIds.forEach(senderId => {
      if (!senderInfo[senderId]) {
        fetchSenderInfo(senderId);
      }
    });
  }, [robotRooms, activeRooms, processedRooms, basicRooms, fetchSenderInfo, senderInfo]);

  if (showLoading || (initLoading && isEmpty)) {
    return <MessageTabSkeleton />;
  }

  return (
    <div className="roomlist-tab-container">
      {initLoading && isEmpty ? (
        <MessageTabSkeleton />
      ) : isEmpty ? (
        <div className="roomlist-tab-empty-state">
          <ErrorBlock status='empty' description='' title={t('尚無任何訊息')} />
        </div>
      ) : (
        <>
          <div className="filter-tabs">
            {filters.map(filter => (
              <div 
                key={filter.key}
                className={`filter-tab ${activeFilter === filter.key ? 'active' : ''}`}
                onClick={() => setActiveFilter(filter.key)}
              >
                <span className="filter-text">{filter.title}</span>
                {filter.hasBadge && <div className="dot-badge" />}
                {activeFilter === filter.key && <div className="active-line" />}
              </div>
            ))}
          </div>
          {activeFilter === 'robot' && (
            sortedRobotRooms.length === 0 ? (
              <div className="empty-list-container">
                <ErrorBlock status='empty' description='' title={t('尚無任何訊息')} />
              </div>
            ) : (
              <div className="list-container chat-list pb-14" onScroll={e => handleScroll('robot', e)}>
                <List className="chat-list-inner">
                  {sortedRobotRooms.map(room => {
                    const { leftActions, rightActions } = getSwipeActions(room);
                    return (
                      <SwipeAction
                        key={room.id}
                        leftActions={leftActions}
                        rightActions={rightActions}
                      >
                        <List.Item
                          arrowIcon={false}
                          className={`chat-item${room.isPin ? ' pinned-item' : ''}`}
                          prefix={renderChatAvatar(room)}
                          description={
                            <div className="chat-message">
                              {/* AI/Draft 標籤與圖示可根據 room 字段判斷 */}
                              {room.isAIDraft && <span className="ai-tag">AI</span>}
                              {(room.isDraft || room.isAIDraft) && <img src={editIcon} alt="edit" className="edit-icon" />}
                              <span className="message-text">
                                {getLastMessageSummary(
                                  (() => {
                                    return room.lastMessage;
                                  })(),
                                  senderInfo // 传入senderInfo
                                )}
                              </span>
                            </div>
                          }
                          extra={
                            <div className="chat-right">
                              <span className="chat-time">
                                {formatChatTime(room.updateTime)}
                              </span>
                              {renderBadge(room.unreadCount)}
                            </div>
                          }
                          onClick={() => handleChatItemClick(room)}
                        >
                          <div className="chat-title-row">
                            <span className="chat-title">{getRoomDisplayName(room)}</span>
                            {/* {room.isMute && <img src={muteIcon} alt="mute" className="mute-icon" />} */}
                            {/* chat-title-row 內 mute icon 旁加上置頂圖標 */}
                            {room.isPin && <img src={pinIcon} alt="pinned" className="pin-icon" />}
                          </div>
                        </List.Item>
                      </SwipeAction>
                    );
                  })}
                </List>
                {robotLoading && <div className="loading">{t('加載中...')}</div>}
              </div>
            )
          )}
          {activeFilter === 'active' && (
            sortedActiveRooms.length === 0 ? (
              <div className="empty-list-container">
                <ErrorBlock status='empty' description='' title={t('尚無任何訊息')} />
              </div>
            ) : (
              <div className="list-container chat-list pb-14" onScroll={e => handleScroll('active', e)}>
                <List className="chat-list-inner">
                  {sortedActiveRooms.map(room => {
                    const { leftActions, rightActions } = getSwipeActions(room);
                    return (
                      <SwipeAction
                        key={room.id}
                        leftActions={leftActions}
                        rightActions={rightActions}
                      >
                        <List.Item
                          arrowIcon={false}
                          className={`chat-item${room.isPin ? ' pinned-item' : ''}`}
                          prefix={renderChatAvatar(room)}
                          description={
                            <div className="chat-message">
                              {/* AI/Draft 標籤與圖示可根據 room 字段判斷 */}
                              {room.isAIDraft && <span className="ai-tag">AI</span>}
                              {(room.isDraft || room.isAIDraft) && <img src={editIcon} alt="edit" className="edit-icon" />}
                              <span className="message-text">
                                {getLastMessageSummary(
                                  (() => {
                                    return room.lastMessage;
                                  })(),
                                  senderInfo // 传入senderInfo
                                )}
                              </span>
                            </div>
                          }
                          extra={
                            <div className="chat-right">
                              <span className="chat-time">
                                {formatChatTime(room.updateTime)}
                              </span>
                              {renderBadge(room.unreadCount)}
                            </div>
                          }
                          onClick={() => handleChatItemClick(room)}
                        >
                          <div className="chat-title-row">
                            <span className="chat-title">{getRoomDisplayName(room)}</span>
                            {/* {room.isMute && <img src={muteIcon} alt="mute" className="mute-icon" />} */}
                            {/* active 分頁 chat-title-row 內加上置頂圖標 */}
                            {room.isPin && <img src={pinIcon} alt="pinned" className="pin-icon" />}
                          </div>
                        </List.Item>
                      </SwipeAction>
                    );
                  })}
                </List>
                {activeLoading && <div className="loading">{t('加載中...')}</div>}
              </div>
            )
          )}
          {activeFilter === 'processed' && (
            sortedProcessedRooms.length === 0 ? (
              <div className="empty-list-container">
                <ErrorBlock status='empty' description='' title={t('尚無任何訊息')} />
              </div>
            ) : (
              <div className="list-container chat-list pb-14" onScroll={e => handleScroll('processed', e)}>
                <List className="chat-list-inner">
                  {sortedProcessedRooms.map(room => {
                    const { leftActions, rightActions } = getSwipeActions(room);
                    return (
                      <SwipeAction
                        key={room.id}
                        leftActions={leftActions}
                        rightActions={rightActions}
                      >
                        <List.Item
                          arrowIcon={false}
                          className={`chat-item${room.isPin ? ' pinned-item' : ''}`}
                          prefix={renderChatAvatar(room)}
                          description={
                            <div className="chat-message">
                              {/* AI/Draft 標籤與圖示可根據 room 字段判斷 */}
                              {room.isAIDraft && <span className="ai-tag">AI</span>}
                              {(room.isDraft || room.isAIDraft) && <img src={editIcon} alt="edit" className="edit-icon" />}
                              <span className="message-text">
                                {getLastMessageSummary(
                                  (() => {
                                    return room.lastMessage;
                                  })(),
                                  senderInfo // 传入senderInfo
                                )}
                              </span>
                            </div>
                          }
                          extra={
                            <div className="chat-right">
                              <span className="chat-time">
                                {formatChatTime(room.updateTime)}
                              </span>
                              {renderBadge(room.unreadCount)}
                            </div>
                          }
                          onClick={() => handleChatItemClick(room)}
                        >
                          <div className="chat-title-row">
                            <span className="chat-title">{getRoomDisplayName(room)}</span>
                            {/* {room.isMute && <img src={muteIcon} alt="mute" className="mute-icon" />} */}
                            {/* processed 分頁 chat-title-row 內加上置頂圖標 */}
                            {room.isPin && <img src={pinIcon} alt="pinned" className="pin-icon" />}
                          </div>
                        </List.Item>
                      </SwipeAction>
                    );
                  })}
                </List>
                {processedLoading && <div className="loading">{t('加載中...')}</div>}
              </div>
            )
          )}
        </>
      )}
    </div>
  );
};

export default RoomListTab; 