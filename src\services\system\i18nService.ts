/**
 * @file i18nService.ts
 * @description React 国际化服务，提供多语言支持和语言切换功能
 * <AUTHOR> Team
 */

import i18n from 'i18next';
import { ConstantUtil } from '../../utils/constantUtil';
import { logService } from './logService';
import { setLocalStorage, getLocalStorage } from '../../utils/storage';

/**
 * 支持的語言類型
 */
export type SupportedLanguage = 'zh-TW' | 'zh-CN' | 'en-US' | 'ja-JP';

/**
 * 支持的語言配置
 */
export const supportedLanguages: Record<SupportedLanguage, { name: string; flag: string }> = {
  'zh-TW': { name: '繁體中文', flag: '🇹🇼' },
  'zh-CN': { name: '简体中文', flag: '🇨🇳' },
  'en-US': { name: 'English', flag: '🇺🇸' },
  'ja-JP': { name: '日本語', flag: '🇯🇵' }
};

/**
 * 语言变更事件回调类型
 */
export type LanguageChangeCallback = (language: SupportedLanguage) => void;

/**
 * i18n 服务类
 * 
 * 提供国际化功能，包括：
 * - 语言切换
 * - 本地存储管理
 * - 语言变更事件通知
 * - 翻译文本获取
 * 
 * @example
 * ```typescript
 * // 获取翻译文本
 * const text = i18nService.t('auth.login');
 * 
 * // 切换语言
 * i18nService.changeLanguage('en-US');
 * 
 * // 监听语言变更
 * i18nService.onLanguageChange((lang) => {
 *   console.log('Language changed to:', lang);
 * });
 * ```
 */
export class I18nService {
  private static instance: I18nService;
  private languageChangeListeners: Set<LanguageChangeCallback> = new Set();
  private currentLanguage: SupportedLanguage = 'zh-TW';

  private constructor() {
    this.initializeLanguage();
    this.setupLanguageChangeHandler();
  }

  /**
   * 获取服务单例实例
   */
  public static getInstance(): I18nService {
    if (!I18nService.instance) {
      I18nService.instance = new I18nService();
    }
    return I18nService.instance;
  }

  /**
   * 初始化语言设置
   * 从本地存储加载用户设置的语言，如果没有则使用默认语言
   */
  private initializeLanguage(): void {
    try {
      const savedLanguage = getLocalStorage<string>(ConstantUtil.THEME_STORAGE_KEY.replace('theme', 'language'), '');
      
      if (savedLanguage && this.isValidLanguage(savedLanguage)) {
        this.currentLanguage = savedLanguage as SupportedLanguage;
        i18n.changeLanguage(savedLanguage);
      } else {
        // 尝试从浏览器检测语言
        const detectedLanguage = this.detectBrowserLanguage();
        this.currentLanguage = detectedLanguage;
        i18n.changeLanguage(detectedLanguage);
      }
      
      logService.info('I18n service initialized', { language: this.currentLanguage });
    } catch (error) {
      logService.error('Failed to initialize i18n service', error);
      // 使用默认语言
      this.currentLanguage = 'zh-TW';
      i18n.changeLanguage('zh-TW');
    }
  }

  /**
   * 设置语言变更事件处理器
   */
  private setupLanguageChangeHandler(): void {
    i18n.on('languageChanged', (lng: string) => {
      if (this.isValidLanguage(lng)) {
        this.currentLanguage = lng as SupportedLanguage;
        this.notifyLanguageChange(this.currentLanguage);
        this.saveLanguageToStorage(this.currentLanguage);
      }
    });
  }

  /**
   * 检测浏览器语言
   */
  private detectBrowserLanguage(): SupportedLanguage {
    const browserLang = navigator.language || (navigator as any).userLanguage;
    
    // 优先匹配完整的语言代码
    if (this.isValidLanguage(browserLang)) {
      return browserLang as SupportedLanguage;
    }
    
    // 尝试匹配语言前缀
    const langPrefix = browserLang.split('-')[0];
    const matchedLang = Object.keys(supportedLanguages).find(lang => 
      lang.startsWith(langPrefix)
    );
    
    return (matchedLang as SupportedLanguage) || 'zh-TW';
  }

  /**
   * 验证语言代码是否有效
   */
  private isValidLanguage(language: string): boolean {
    return Object.keys(supportedLanguages).includes(language);
  }

  /**
   * 保存语言设置到本地存储
   */
  private saveLanguageToStorage(language: SupportedLanguage): void {
    try {
      const storageKey = ConstantUtil.THEME_STORAGE_KEY.replace('theme', 'language');
      setLocalStorage(storageKey, language);
    } catch (error) {
      logService.error('Failed to save language to storage', error);
      // 存储失败不应影响语言切换功能
    }
  }

  /**
   * 通知所有监听器语言已变更
   */
  private notifyLanguageChange(language: SupportedLanguage): void {
    this.languageChangeListeners.forEach(callback => {
      try {
        callback(language);
      } catch (error) {
        logService.error('Language change callback error', error);
      }
    });
  }

  /**
   * 切换语言
   * @param language 目标语言代码
   * @returns Promise<void>
   */
  public async changeLanguage(language: SupportedLanguage): Promise<void> {
    try {
      if (!this.isValidLanguage(language)) {
        throw new Error(`Unsupported language: ${language}`);
      }

      await i18n.changeLanguage(language);
      logService.info('Language changed successfully', { 
        from: this.currentLanguage, 
        to: language 
      });
    } catch (error) {
      logService.error('Failed to change language', error);
      throw error;
    }
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  /**
   * 获取当前语言的显示名称
   */
  public getCurrentLanguageName(): string {
    return supportedLanguages[this.currentLanguage].name;
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): Record<SupportedLanguage, { name: string; flag: string }> {
    return supportedLanguages;
  }

  /**
   * 获取翻译文本
   * @param key 翻译键名，支持嵌套路径如 'auth.login'
   * @param options 插值选项
   * @returns 翻译后的文本
   */
  public t(key: string, options?: any): string {
    try {
      const result = i18n.t(key, options);
      return typeof result === 'string' ? result : key;
    } catch (error) {
      logService.error('Translation error', { key, error });
      return key; // 返回原始键名作为备用
    }
  }

  /**
   * 检查翻译键是否存在
   * @param key 翻译键名
   * @returns 是否存在
   */
  public exists(key: string): boolean {
    return i18n.exists(key);
  }

  /**
   * 添加语言变更监听器
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  public onLanguageChange(callback: LanguageChangeCallback): () => void {
    this.languageChangeListeners.add(callback);
    
    // 返回取消监听的函数
    return () => {
      this.languageChangeListeners.delete(callback);
    };
  }

  /**
   * 获取适合后端API的语言代码
   * 将前端的语言代码转换为后端期望的格式
   */
  public getApiLanguageCode(): string {
    switch (this.currentLanguage) {
      case 'en-US':
        return 'en-us';
      case 'zh-TW':
        return 'zh-tw';
      case 'zh-CN':
        return 'zh-cn';
      default:
        return 'zh-tw';
    }
  }

  /**
   * 从缓存加载语言设置
   * 兼容旧版本的方法名
   */
  public loadFromCache(): void {
    this.initializeLanguage();
  }

  /**
   * 切换语言（兼容旧版本的方法名）
   * @param language 语言代码
   */
  public switchLanguage(language: string): void {
    if (this.isValidLanguage(language)) {
      this.changeLanguage(language as SupportedLanguage);
    }
  }

  /**
   * 获取语言代码（兼容旧版本的方法名）
   */
  public getLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 获取后端语言代码（兼容旧版本的方法名）
   */
  public getCELanguage(): string {
    return this.getApiLanguageCode();
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    this.languageChangeListeners.clear();
    i18n.off('languageChanged');
  }
}

// 导出单例实例
export const i18nService = I18nService.getInstance();

// 默认导出
export default i18nService; 