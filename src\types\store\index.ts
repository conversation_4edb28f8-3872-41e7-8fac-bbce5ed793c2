/**
 * Redux store 類型定義
 * 避免循環依賴，將 store 相關類型獨立出來
 */

import { ThunkAction, Action } from '@reduxjs/toolkit';

// 重新導出 store.ts 中的類型，避免循環依賴
// 這些類型的實際定義在 store.ts 中

// 這裡我們需要重新導出實際的類型
// 但為了避免循環依賴，我們使用類型導入
export type { RootState, AppDispatch } from '../../app/store';

// Thunk Action 類型
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  any, // 使用 any 避免循環依賴，實際使用時會被正確推斷
  unknown,
  Action<string>
>;
