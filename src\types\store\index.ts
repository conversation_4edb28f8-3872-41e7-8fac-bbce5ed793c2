/**
 * Redux状态相关类型定义
 */

// 临时类型定义，避免构建错误
interface User {
  id: string;
  name: string;
  email?: string;
}

interface Message {
  id: string;
  content: string;
  sendTime: string;
  senderId: string;
}

interface Room {
  id: string;
  name: string;
  type: string;
}

// 基础状态接口
export interface BaseState {
  loading: boolean;
  error: string | null;
  lastUpdated?: number;
}

// 分页状态接口
export interface PaginatedState<T = any> extends BaseState {
  items: T[];
  currentPage: number;
  pageSize: number;
  total: number;
  hasNextPage: boolean;
}

// 认证状态
export interface AuthState extends BaseState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  expiresAt: number | null;
}

// 消息状态
export interface MessageState extends BaseState {
  rooms: Record<string, RoomMessageState>;
}

// 房间消息状态
export interface RoomMessageState extends PaginatedState {
  roomId: string;
  messages: Message[];
  lastSequence: number;
  unreadCount: number;
}

// 房间状态
export interface RoomState extends BaseState {
  activeRooms: Room[];
  pinnedRooms: Room[];
  currentRoomId: string | null;
  unreadCounts: Record<string, number>;
}

// 应用状态
export interface AppState extends BaseState {
  theme: 'light' | 'dark';
  language: string;
  platform: 'web' | 'ios' | 'android';
  isOnline: boolean;
  notifications: Notification[];
}

// 根状态类型
export interface RootState {
  auth: AuthState;
  message: MessageState;
  room: RoomState;
  app: AppState;
}

// Action类型
export interface BaseAction {
  type: string;
  payload?: any;
  meta?: any;
  error?: boolean;
}

// Thunk Action类型
export type ThunkAction<R = void> = (
  dispatch: any,
  getState: () => RootState
) => R;
