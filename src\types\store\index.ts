/**
 * Redux store 類型定義
 * 避免循環依賴，將 store 相關類型獨立出來
 */

import { ThunkAction, Action } from '@reduxjs/toolkit';

// 這些類型將在 store.ts 中被重新導出
// 避免 slice 文件直接導入 store.ts 造成循環依賴

// Store 相關類型的佔位符，實際類型將在 store.ts 中定義
export interface RootState {
  // 這個接口將在 store.ts 中被重新定義
  [key: string]: any;
}

export interface AppDispatch {
  // 這個接口將在 store.ts 中被重新定義
  [key: string]: any;
}

// Thunk Action 類型
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
