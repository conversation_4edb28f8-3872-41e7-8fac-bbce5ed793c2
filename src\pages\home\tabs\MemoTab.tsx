import { ErrorBlock } from 'antd-mobile';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface MemoTabProps {
  onScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

const MemoTab: React.FC<MemoTabProps> = ({ onScroll }) => {
  const { t } = useTranslation();

  return (
    <div className="tab-content" onScroll={onScroll}>
      <ErrorBlock status='empty' title={t('尚無任何備忘錄')} description="" />
    </div>
  );
};

export default MemoTab; 