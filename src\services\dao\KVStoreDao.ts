import { logService } from '../system/logService';
import aileDBService from '../db/aileDBService';
import type { KVStore } from '../db/initSql';
import { getRequiredTenantId } from '@/utils/tenantUtil';

/**
 * KVStore 數據訪問對象
 * 提供 KVStore 表相關的數據庫操作
 */
class KVStoreDao {
  /**
   * 確保 KVStore 表存在
   */
  public async ensureKVStoreTableExists(): Promise<void> {
    try {
      // 檢查表是否存在
      const tableExists = await aileDBService.get<{ count: number }>(
        `SELECT count(*) as count FROM sqlite_master WHERE type='table' AND name='KVStore'`
      );

      if (!tableExists || tableExists.count === 0) {
        // 創建 KVStore 表
        await aileDBService.run(`
          CREATE TABLE IF NOT EXISTS KVStore (
            key TEXT PRIMARY KEY NOT NULL,
            value TEXT,
            createTime INTEGER,
            updateTime INTEGER
          )
        `);
        logService.info('成功創建 KVStore 表');
      }
    } catch (error) {
      logService.error('確保 KVStore 表存在時發生錯誤', { error: error as Error });
      throw error;
    }
  }

  private getTenantKey(key: string): string {
    const tenantId = getRequiredTenantId();
    return `${tenantId}::${key}`;
  }

  /**
   * 獲取 KV 值
   * @param key 鍵名
   * @returns 值或 null
   */
  public async getValue(key: string): Promise<string | null> {
    try {
      // 確保表存在
      await this.ensureKVStoreTableExists();
      const tenantKey = this.getTenantKey(key);

      // 查詢值
      const result = await aileDBService.get<{ value: string }>(
        `SELECT value FROM KVStore WHERE key = ?`,
        [tenantKey]
      );

      return result ? result.value : null;
    } catch (error) {
      logService.error('獲取 KV 值失敗', { error: error as Error, key });
      return null;
    }
  }

  /**
   * 保存 KV 值
   * @param key 鍵名
   * @param value 值
   * @returns 是否保存成功
   */
  public async saveValue(key: string, value: string): Promise<boolean> {
    try {
      // 確保表存在
      await this.ensureKVStoreTableExists();

      const now = Date.now();
      const tenantKey = this.getTenantKey(key);

      // 檢查是否已存在
      const existing = await this.getValue(key);

      if (existing !== null) {
        // 更新
        await aileDBService.run(
          `UPDATE KVStore SET value = ?, updateTime = ? WHERE key = ?`,
          [value, now, tenantKey]
        );
      } else {
        // 插入
        await aileDBService.run(
          `INSERT INTO KVStore (key, value, createTime, updateTime) VALUES (?, ?, ?, ?)`,
          [tenantKey, value, now, now]
        );
      }

      return true;
    } catch (error) {
      logService.error('保存 KV 值失敗', { error: error as Error, key });
      return false;
    }
  }

  /**
   * 刪除 KV 值
   * @param key 鍵名
   * @returns 是否刪除成功
   */
  public async deleteValue(key: string): Promise<boolean> {
    try {
      // 確保表存在
      await this.ensureKVStoreTableExists();
      const tenantKey = this.getTenantKey(key);

      await aileDBService.run(`DELETE FROM KVStore WHERE key = ?`, [tenantKey]);
      return true;
    } catch (error) {
      logService.error('刪除 KV 值失敗', { error: error as Error, key });
      return false;
    }
  }

  /**
   * 獲取所有 KV 值
   * @returns KV 值列表
   */
  public async getAllValues(): Promise<KVStore[]> {
    try {
      // 確保表存在
      await this.ensureKVStoreTableExists();
      const tenantId = getRequiredTenantId();

      const results = await aileDBService.all<KVStore>(
        `SELECT * FROM KVStore WHERE key LIKE ? ORDER BY key ASC`,
        [`${tenantId}::%`]
      );
      return results;
    } catch (error) {
      logService.error('獲取所有 KV 值失敗', { error: error as Error });
      return [];
    }
  }

  /**
   * 根據前綴獲取 KV 值
   * @param prefix 鍵名前綴
   * @returns 匹配前綴的 KV 值列表
   */
  public async getValuesByPrefix(prefix: string): Promise<KVStore[]> {
    try {
      // 確保表存在
      await this.ensureKVStoreTableExists();
      const tenantId = getRequiredTenantId();

      const results = await aileDBService.all<KVStore>(
        `SELECT * FROM KVStore WHERE key LIKE ? ORDER BY key ASC`,
        [`${tenantId}::${prefix}%`]
      );
      return results;
    } catch (error) {
      logService.error('獲取前綴匹配的 KV 值失敗', { error: error as Error, prefix });
      return [];
    }
  }
}

// 導出單例實例
export default new KVStoreDao(); 