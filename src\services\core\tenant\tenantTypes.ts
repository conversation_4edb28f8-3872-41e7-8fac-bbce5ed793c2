/**
 * 租户相关类型定义
 */

/**
 * 服務號信息
 */
export interface ServiceNumberInfo {
  id?: string;
  createTime?: number;
  updateTime?: number;
  name?: string;
  type?: string;
  code?: string;
  description?: string;
  openType?: string;
  status?: string;
  avatarId?: string;
  tenantId?: string;
  memberRoomId?: string;
}

/**
 * 租户与用户的关系
 */
export interface RelationTenantVO {
  accountId?: string;
  avatarId?: string;
  code?: string;
  description?: string;
  id: string;
  industry?: string;
  industrySub?: string;
  isLastTenant?: boolean;
  manageServiceNumberId?: string;
  name?: string;
  officialServiceNumberId?: string;
  openId?: string;
  relationId?: string;
  scale?: string;
  shortName?: string;
  type?: string;
  unReadCount?: number;
  officialServiceNumberInfo?: ServiceNumberInfo;
  manageServiceNumberInfo?: ServiceNumberInfo;
  [property: string]: any;
}

/**
 * 跨租户成员
 */
export interface TransTenantMember {
  accountId?: string;
  name?: string;
  status?: string;
  [property: string]: any;
}

/**
 * 跨租户信息
 */
export interface TransTenantVO {
  tenantId?: string;
  transMembers?: TransTenantMember[];
  [property: string]: any;
}

/**
 * CommonResultTenantModel
 */
export interface Response {
    code?: string;
    data?: TenantModel;
    msg?: string;
    status?: number;
    success?: boolean;
    timeCost?: number;
    [property: string]: any;
}

/**
 * TenantModel
 */
export interface TenantModel {
    accountId?: string;
    address?: string;
    avatarId?: string;
    certificateFailReason?: string;
    certificateFileId?: string;
    certificateStatus?: CertificateStatus;
    city?: string;
    code?: string;
    /** 創建時間，創建時間 */
    createTime?: number;
    description?: string;
    endTime?: number;
    id?: string;
    industry?: string;
    industrySub?: string;
    name?: string;
    phone?: string;
    representativeNumber?: string;
    scale?: string;
    shortName?: string;
    startTime?: number;
    switchTime?: number;
    type?: Type;
    unifiedNumber?: string;
    /** 更新時間，更新時間 */
    updateTime?: number;
    upgrade?: boolean;
    website?: string;
    [property: string]: any;
}

export enum CertificateStatus {
    Audit = "Audit",
    Audited = "Audited",
    Back = "Back",
}

export enum Type {
    Common = "Common",
    Official = "Official",
    Person = "Person",
    Public = "Public",
    Service = "Service",
}

 