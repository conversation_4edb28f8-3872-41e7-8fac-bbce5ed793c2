// pushService.ts
import { Capacitor } from '@capacitor/core';
import { ActionPerformed, PushNotifications, PushNotificationSchema, Token } from '@capacitor/push-notifications';
import { EventEmitter } from '../utils/EventEmitter';
import { Socket, io } from 'socket.io-client';
// @ts-ignore
import _ from 'lodash';
import { LogInState } from '../types/aile.enum';
import { ConstantUtil } from '../utils/constantUtil';
import logService from './system/logService';
import { envConfig } from '@/config/env';
import deviceService from './platform/deviceService';
import { Subscription } from 'rxjs';
import stateService from './stateService';
import httpService from './system/httpService';



// Types
enum RoomAction {
  add = 'add',
  update = 'update',
  delete = 'delete',
  lastMessageUpdate = 'lastMessageUpdate',
  topStatusChanged = 'topStatusChanged',
  muteStatusChanged = 'muteStatusChanged',
  notificationAction = 'notificationAction',
}

interface IAileResponse {
  success: boolean;
  data: any;
  message?: string;
}

class PushService {
  private eventEmitter = new EventEmitter();
  private pushToken: string = '';
  private key: string = '';
  private socket?: Socket;
  private subscriptions: Subscription[] = [];
  
  
  public initServices() {
    logService.info('PushService initialized');

    // 檢查是否啟用推送通知
    const pushEnabled = (ConstantUtil as any).USE_PUSH;
    logService.info(`[PUSH] Push notifications enabled: ${pushEnabled}`);

    if (pushEnabled && !this.isInIMBrowser()) {
      logService.info('[PUSH] Initializing push notifications');
      this.setupPushNotifications();
    } else {
      if (!pushEnabled) {
        logService.info('[PUSH] Push notifications disabled in configuration');
      } else {
        logService.info('[PUSH] Push notifications disabled - running in IM browser environment');
      }
    }

    // 自動初始化監聽狀態變化（Socket連接，不依賴推送通知）
    this.setupStateListeners();
  }
  
  /**
   * 設置對stateService的監聽
   * @private
   */
  private setupStateListeners(): void {
    // 清理舊訂閱
    this.cleanupSubscriptions();
    
    // 使用 Observable 方式訂閱登入狀態變化
    if (stateService.LoginChanged) {
      const loginSub = stateService.LoginChanged.subscribe({
        next: async (state: { status: LogInState, user: any }) => {
          if (state.status === LogInState.LoggedIn && state.user) {
            logService.info('[PUSH] User logged in, initializing socket connection', { userId: state.user.accountId });
            try {
              // 總是建立Socket連接（用於即時通訊）
              this.connect('', state.user.onlineId);

              // 只有在推送通知啟用時才獲取推送token
              const pushEnabled = (ConstantUtil as any).USE_PUSH;
              if (pushEnabled && !this.isInIMBrowser()) {
                logService.info('[PUSH] Getting push token for notifications');
                const result = await this.getToken();
                if (result.result) {
                  this.pushLog('Login and get token:' + result.pushToken);
                  this.updateToken(result.pushToken!, state.user.onlineId);
                } else {
                  this.pushLog('Login and get token failed');
                }
              } else {
                logService.info('[PUSH] Skipping push token - notifications disabled');
              }
            } catch (exp) {
              this.pushLog('Login connection failed', exp);
            }
          } else if (state.status === LogInState.LoggedOut) {
            logService.info('[PUSH] User logged out, disconnecting');
            this.disconnect();
          }
        },
        error: (err: Error) => {
          logService.error('[PUSH] Error in login state subscription', err);
        }
      });
      
      this.subscriptions.push(loginSub);
      logService.info('成功訂閱登入狀態變化 Observable');
    } else {
      // 降級到事件模式
      logService.warn('stateService.LoginChanged Observable 不存在，降級到事件模式');
      
      stateService.on('loginUserChanged', async (user: any) => {
        if (user) {
          logService.info('[PUSH] User logged in (event), initializing push notifications', { userId: user.accountId });
          try {
            this.connect('', user.onlineId);
            const result = await this.getToken();
            if (result.result) {
              this.pushLog('Login and get token:' + result.pushToken);
              this.updateToken(result.pushToken!, user.onlineId);
            } else {
              this.pushLog('Login and get token failed');
            }
          } catch (exp) {
            this.pushLog('Login and get token failed', exp);
          }
        }
      });
      
      stateService.on('logoutUserChanged', () => {
        logService.info('[PUSH] User logged out (event), disconnecting');
          this.disconnect();
      });
    }
  }
  
  
  // 清理訂閱
  private cleanupSubscriptions(): void {
    this.subscriptions.forEach(sub => {
      if (sub && !sub.closed) {
        sub.unsubscribe();
      }
    });
    this.subscriptions = [];
  }

  // 釋放資源
  public dispose(): void {
    this.cleanupSubscriptions();
    this.disconnect();
  }

  // Event handlers
  public on(event: string, listener: (...args: any[]) => void) {
    this.eventEmitter.on(event, listener);
    return () => this.eventEmitter.off(event, listener);
  }

  public emit(event: string, ...args: any[]) {
    this.eventEmitter.emit(event, ...args);
  }
  
  private pushLog(...msg: any[]) {
    // Only log if push logging is enabled in ConstantUtil
    logService.info('[PUSH] ', msg);
  }

  /**
   * Checks if the app is running in an IM client's built-in browser
   */
  private isInIMBrowser(): boolean {
    if (Capacitor.getPlatform() !== 'web') {
      return false; // Not affected in native environments
    }

    const userAgent = navigator.userAgent.toLowerCase();
    // Check for LINE built-in browser
    if (userAgent.indexOf('line') !== -1) {
      return true;
    }
    // Check for Facebook built-in browser
    if (userAgent.indexOf('fbav') !== -1 || userAgent.indexOf('fban') !== -1) {
      return true;
    }
    // Check for WeChat built-in browser
    if (userAgent.indexOf('micromessenger') !== -1) {
      return true;
    }
    // Check for Instagram built-in browser
    if (userAgent.indexOf('instagram') !== -1) {
      return true;
    }
    // Check for Twitter/X built-in browser
    if (userAgent.indexOf('twitter') !== -1) {
      return true;
    }
    // Other IM browser checks can be added here

    return false;
  }
  
  private setupPushNotifications() {
    if (Capacitor.getPlatform() === 'web') {
      // Setup web push notifications
      if (navigator && navigator.serviceWorker) {
        try {
          navigator.serviceWorker.addEventListener('message', (event: any) => {
            try {
              const data = JSON.parse(event.data);
              const roomId = data.roomId;
              this.pushLog(' web push message room id is : ', { roomId });
              // Process message here if needed
            } catch (e) {
              this.pushLog('message err:', event);
            }
          });
        } catch (error) {
          // Log error but don't interrupt application flow
          logService.error('[PUSH] Error setting up service worker listener:', error);
        }
      } else {
        logService.warn('[PUSH] Service worker API not available in this browser environment');
      }
    } else {
      // Setup native app push notifications
      window.addEventListener('appLaunchedByNotification', (event: any) => {
        this.pushLog('App launched by notification:', event, typeof event);

        const { roomId } = event;
        if (roomId) {
          logService.debug(`[PUSH]Opening chat room with ID: ${roomId}`);
          // Navigate to chat room page
          stateService.notifyRoomChanged({
            action: RoomAction.notificationAction,
            roomId: roomId,
            message: null,
          });
        } else {
          this.pushLog('Room ID not found in notification data');
        }
      });

      // Native platform notifications
      try {
        // Firebase Messaging listeners for notification received
        if (Capacitor.isPluginAvailable('FirebaseMessaging')) {
          const FirebaseMessaging = (window as any).FirebaseMessaging;
          
          FirebaseMessaging.addListener('notificationReceived', (event: any) => {
            this.pushLog('notificationReceived: ', { event });
            if (event?.notification.data) {
              try {
                // Parse data to object
                const org_data = (event.notification.data as any)?.data;
                const data = JSON.parse(org_data);
                const roomId = data.roomId;
                this.pushLog(' app push message room id is : ', { roomId });
              } catch (error) {
                console.error('[PUSH]Failed to parse notification data:', error);
              }
            } else {
              // Handle original notification content if no data field
              // const title = event.notification.title; // Unused
              // const body = event.notification.body; // Unused
            }
          });

          FirebaseMessaging.addListener('notificationActionPerformed', (event: any) => {
            this.pushLog('notificationActionPerformed: ', { event });
            if (event?.notification?.data) {
              try {
                const roomId = (event.notification.data as any)?.roomId;
                if (roomId) {
                  logService.debug(`[PUSH]Opening chat room with ID: ${roomId}`);
                  // Navigate to chat room page
                  this.emit('roomChanged', {
                    action: RoomAction.notificationAction,
                    roomId: roomId,
                    message: null,
                  });
                } else {
                  this.pushLog('Room ID not found in notification data');
                }
              } catch (error) {
                this.pushLog('Failed to parse notification data:', error);
              }
            }
          });
        }
      } catch (error) {
        logService.error('[PUSH] Error setting up Firebase Messaging listeners:', error);
      }
    }
  }
  
  private async registerNotifications() {
    let permStatus = await PushNotifications.checkPermissions();
    logService.debug(`registerNotifications ${permStatus.receive}`);

    if (permStatus.receive === 'prompt') {
      permStatus = await PushNotifications.requestPermissions();
    }

    if (permStatus.receive !== 'granted') {
      throw new Error('User denied permissions!');
    }

    await PushNotifications.register();
    logService.debug(`registerNotifications finished`);
  }

  public async registerPushNotifications(): Promise<{
    result: boolean;
    pushToken?: string;
    error?: any;
  }> {
    logService.debug(`registerPushNotifications ${Capacitor.getPlatform()}`);
    return new Promise(async (resolve) => {
      // Get push notification token
      PushNotifications.addListener('registration', (token: Token) => {
        this.pushLog('Push registration success, token: ' + token.value + ' key:' + this.key);
        this.pushToken = token.value;
        resolve({ result: true, pushToken: token.value });
      });

      // Registration error
      PushNotifications.addListener('registrationError', (error: any) => {
        this.pushLog('Error on registration: ' + JSON.stringify(error));
        resolve({ result: false, error: error });
      });

      // When push notification arrives
      PushNotifications.addListener('pushNotificationReceived', (notification: PushNotificationSchema) => {
        this.pushLog('Push received: ' + JSON.stringify(notification));
      });

      // When user clicks on push notification
      PushNotifications.addListener('pushNotificationActionPerformed', (notification: ActionPerformed) => {
        this.pushLog('Push action performed: ' + JSON.stringify(notification));
        // For iOS
        if (notification.notification?.data) {
          try {
            // Parse notification data
            const data = JSON.parse((notification.notification.data as any)?.data || '{}');

            // Get chat room ID from notification data
            const roomId = data?.roomId;
            if (roomId) {
              logService.debug(`[PUSH]Opening chat room with ID: ${roomId}`);
              // Navigate to chat room page
              stateService.notifyRoomChanged({
                action: RoomAction.notificationAction,
                roomId: roomId,
                message: null,
              });
            } else {
              this.pushLog('Room ID not found in notification data');
            }
          } catch (error) {
            this.pushLog('Failed to parse notification data:', error);
          }
        }
      });
      
      // Request push notification permission
      try {
        await this.registerNotifications();
      } catch (error) {
        resolve({ result: false, error });
      }
    });
  }

  public async registerPWANotifications(): Promise<{
    result: boolean;
    pushToken?: string;
    error?: any;
  }> {
    try {
      if (!Capacitor.isPluginAvailable('FirebaseMessaging')) {
        return { result: false, error: new Error('FirebaseMessaging not available') };
      }
      
      const FirebaseMessaging = (window as any).FirebaseMessaging;
      
      let permStatus = await FirebaseMessaging.checkPermissions();
      if (permStatus.receive === 'prompt') {
        permStatus = await FirebaseMessaging.requestPermissions();
      }
      if (permStatus.receive !== 'granted') {
        throw new Error('User denied permissions!');
      }

      // Use a config object that can be extended with serviceWorkerRegistration
      const options: any = {
        vapidKey: ((ConstantUtil as any).GlogalConfig?.vapidKey || ''),
      };
      
      FirebaseMessaging.addListener('tokenReceived', (_token: any) => {
        // Handle token received
      });

      // Ensure navigator.serviceWorker exists
      if (!navigator || !navigator.serviceWorker) {
        logService.warn('[PUSH] Service worker API not available in this browser environment');
        return { result: false, error: new Error('Service Worker API not available') };
      }

      try {
        options.serviceWorkerRegistration = await navigator.serviceWorker.register('firebase-messaging-sw.js');
        const { token } = await FirebaseMessaging.getToken(options);
        this.pushToken = token;

        return { result: true, pushToken: this.pushToken };
      } catch (swError) {
        logService.error('[PUSH] Error registering service worker:', swError);
        return { result: false, error: swError };
      }
    } catch (error) {
      return { result: false, error: error };
    }
  }

  public async getToken() {
    // 檢查推送通知是否啟用
    const pushEnabled = (ConstantUtil as any).USE_PUSH;
    if (!pushEnabled) {
      logService.info('[PUSH] Push notifications disabled in configuration');
      return { result: false, error: new Error('Push notifications disabled') };
    }

    if (Capacitor.getPlatform() === 'web') {
      // Skip FirebaseMessagingWeb plugin registration in web environment
      logService.info('[PUSH] Skipping Firebase Messaging registration in web environment');
      return { result: false, error: new Error('Firebase Messaging disabled in web environment') };
    } else {
      const result = await this.registerPushNotifications();
      return result;
    }
  }

  private buildBase(): { options: any; _header_: any } {
    const headers = {
      satoken: stateService.loginUser?.()?.tokenId ?? '',
    };
    const options = { headers };

    const sendHeader = {
      // Add any additional headers here if needed
    };
    
    return {
      options: options,
      _header_: sendHeader,
    };
  }

  public async updateToken(aptoken: string, key: string) {
    if (!httpService) {
      logService.error('[PUSH] HTTP service not initialized');
      return { result: false, error: 'HTTP service not initialized' };
    }
    
    // Get the update token API path or use a default
    const tokenEndpoint = (ConstantUtil as any).UPDADATE_TOKEN || '/api/push/update-token';
    const apiUrl = envConfig.API_BASE_URL + tokenEndpoint;
    
    const args = _.assign(
      {},
      {
        bundleId: stateService.deviceData?.()?.bundleId,
        osType: stateService.deviceData?.()?.osType,
        key: key,
        token: aptoken,
      }
    );

    const options = this.buildBase();

    this.pushLog('updateToken args', args);
    
    try {
      const response = httpService.post(apiUrl, args, options.options);
      
      // Check if DecryptResponse exists on ConstantUtil and use it if available
      const decryptResponseFn = (ConstantUtil as any).DecryptResponse;
      const decryptedResponse = typeof decryptResponseFn === 'function' ? 
        decryptResponseFn(response) : response;
        
      const aileRes = decryptedResponse as IAileResponse;
      this.pushLog('updateToken result', aileRes);
      
      if (aileRes.success) {
        return { result: true, response: aileRes.data };
      } else {
        logService.error('updateToken Failed:', aileRes);
        return { result: false, response: decryptedResponse };
      }
    } catch (error) {
      logService.error('updateToken error:', error);
      return { result: false, error };
    }
  }

  public connect(apToken: string, accountId: string) {
    // if (!this.settingsService?.app) {
    //   logService.error('[PUSH] Settings service not initialized');
    //   return;
    // }

    const deviceData = deviceService.getDeviceDataHeader();
    this.key = accountId;
    // 根據平台調整傳輸方式
    const platform = Capacitor.getPlatform();
    const transports = platform === 'web' ? ['websocket', 'polling'] : ['websocket'];

    const args = {
      query: {
        args: JSON.stringify({
          key: accountId,
          appToken: apToken,
          enableAck: true,
        }),
        deviceData: encodeURIComponent(JSON.stringify(deviceData)),
      },
      path: envConfig.SOCKET_PATH,
      transports: transports,
      forceNew: true,
      reconnectionDelay: 2000,
      reconnectionDelayMax: 5000,
      // APP端額外配置
      ...(platform !== 'web' && {
        upgrade: true,
        rememberUpgrade: true,
        timeout: 20000,
      })
    };



    if (this.socket) {
      this.socket.removeAllListeners();
      this.socket.disconnect();
    }

    this.socket = io(envConfig.SOCKET_URL, args);

    this.pushLog('Init SocketIO', envConfig.SOCKET_URL, args);
    
    this.setupSocketListeners();
  }
  
  private setupSocketListeners() {
    if (!this.socket) return;
    
    this.socket.on('message', (data: any, callback: any) => {
      try {
        if (data === 'onConnect back') {
          return;
        }
        if (typeof data === 'string' && data.startsWith('{')) {
          const json = JSON.parse(data);
          this.pushLog('SocketIO message(str):' + json.name + ' ' + JSON.stringify(json));
          this.processMessage(json.name, json.code, json.event, json.sequence, json.content, json?.tenantId, callback);
        } else {
          this.pushLog('SocketIO message(obj):' + JSON.stringify(data));
          this.processMessage(data.name, data.code, data.event, data.sequence, data.content, data?.tenantId, callback);
        }
      } catch (e) {
        logService.error('SocketIO message error:' + data);
        return;
      }
    });

    this.socket.io.on('reconnect', () => {
      logService.warn('New_Aile SocketIO reconnected... ');
    });

    this.socket.on('connect', () => {
      this.pushLog('New_Aile SocketIO ConnectStatus :' + this.socket?.connected + ' ' + this.socket?.id);
    });
    
    this.socket.on('connect_error', (reason: any) => {
      this.pushLog('New_Aile SocketIO ConnectStatus(connect_error) :', reason);
    });
    
    this.socket.on('disconnect', (reason: any) => {
      this.pushLog('New_Aile  SocketIO ConnectStatus(dis) :' + this.socket?.connected + ' ' + this.socket?.id + ' ' + reason);
    });

    this.socket.io.on('error', (e: any) => {
      this.pushLog('New_Aile  SocketIO ConnectStatus :' + ' ' + this.socket?.id + this.socket?.connected + ' Connect Error' + e);
    });

    this.socket.io.on('close', (e: any) => {
      this.pushLog('SocketIO ConnectStatus->close->' + e);
    });


  }

  public async processMessage(
    name: string, 
    code: string, 
    event: string, 
    sequence: number, 
    content: any, 
    tenantId: string, 
    callback: any
  ) {
    try {
      if (callback) {
        callback({
          name: name,
          action: 'received',
          ack: true,
        });
      }
      
      // Emit event to notify about message
      stateService.notifyProcessMessageEvent({
        name,
        code,
        event,
        sequence,
        content,
        tenantId,
        callback,
        isOff: false,
      });
      
      // Check for force offline
      if (code === 'SocketIO' && event === 'ForceOffline') {
        this.emit('loginChanged', LogInState.ForceOffline, null);
      }
    } catch (e) {
      console.log('error', e);
    }
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
  
  public getPushToken(): string {
    return this.pushToken;
  }
}

// Create a singleton instance
const pushService = new PushService();
export default pushService;