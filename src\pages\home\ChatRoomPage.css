/* 导入基础样式 */
@import './chat/ChatRoom.css';
@import './chat/ChatRoomIcons.css';

/* 特定样式覆盖 */
.chat-room-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #E4F4FD;
}

/* Navbar Styles */
.chat-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
}

.chat-navbar-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 6px 16px 6px 0;
}

.chat-back-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
}

.chat-room-title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4em;
  color: #333333;
}

.chat-navbar-right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.chat-navbar-icon {
  width: 24px;
  height: 24px;
}

.chat-navbar-menu {
  position: relative;
}

.chat-badge-content {
  position: absolute;
  top: -5px;
  right: -5px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 15px;
  height: auto;
  padding: 1px 4px;
  background-color: #FF3141;
  border-radius: 100px;
}

.chat-badge-content span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 9px;
  line-height: 1.4em;
  color: #FFFFFF;
  text-align: center;
}

/* Chat Content */
.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-bottom: 16px;
}

.chat-date-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  width: 100%;
}

.chat-date-header span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4em;
  color: #666666;
}

/* Message Styles */
.chat-message-row {
  display: flex;
  gap: 8px;
  width: 100%;
}

.chat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 44px;
  overflow: hidden;
}

.chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-message-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.chat-message-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 8px;
}

.chat-message-bubble {
  padding: 8px 12px;
  border-radius: 8px;
  max-width: 240px;
}

.chat-message-bubble span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
}

/* User Message Styles */
.user-message-row {
  justify-content: flex-end;
}

.user-message-container {
  align-items: flex-end;
}

.user-message-wrapper {
  flex-direction: row;
}

.user-message {
  background-color: #386591;
  border-radius: 8px 0 8px 8px;
}

.user-message span {
  color: #FFFFFF;
}

/* Chat Input */
.chat-input-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
}

.chat-input-toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #FFFFFF;
}

.chat-toolbar-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
}

.chat-toolbar-icon img {
  width: 24px;
  height: 24px;
}

.chat-input {
  flex: 1;
  padding: 8px 12px;
  background-color: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #333333;
  min-height: 36px;
  max-height: 120px;
  resize: none;
}

.chat-input .adm-text-area {
  border: none;
  background: transparent;
  padding: 0;
}

.chat-input .adm-text-area-element {
  min-height: 20px !important;
  line-height: 20px;
  font-size: 14px;
  padding: 0;
  border: none;
  background: transparent;
  resize: none;
}

.chat-input::placeholder {
  color: #999999;
  opacity: 0.7;
}

/* Bot Message Styles */
.bot-message {
  background-color: #FFFFFF;
  border-radius: 0 8px 8px 8px;
}

.bot-message span {
  color: #333333;
}

/* Bot Logo Styles */
.chat-bot-logo {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 32px;
  top: 24px;
}

.chat-bot-logo img {
  width: 100%;
  height: 100%;
}

/* 页面错误状态样式 */
.page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #E4F4FD;
  color: #ff4d4f;
  font-size: 14px;
  gap: 16px;
  padding: 20px;
  text-align: center;
}

.page-error button {
  background-color: #1677FF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.page-error button:hover {
  background-color: #0958d9;
}