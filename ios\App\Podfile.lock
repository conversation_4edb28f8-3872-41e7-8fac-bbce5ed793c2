PODS:
  - AileCapacitorLineLogin (1.3.10):
    - Capacitor
    - LineSDKSwift (~> 5.8.0)
  - Capacitor (7.4.2):
    - CapacitorCordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorCamera (7.0.1):
    - Capacitor
  - CapacitorCommunityMedia (8.0.1):
    - Capacitor
    - SDWebImage
  - CapacitorCommunitySqlite (7.0.1):
    - Capacitor
    - SQLCipher
    - ZIPFoundation
  - CapacitorCordova (7.4.2)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorFilesystem (7.1.2):
    - Capacitor
    - IONFilesystemLib (~> 1.0)
  - CapacitorMlkitBarcodeScanning (7.2.1):
    - Capacitor
    - GoogleMLKit/BarcodeScanning (= 7.0.0)
  - CapacitorPushNotifications (7.0.1):
    - Capacitor
  - CapacitorShare (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - IONFilesystemLib (1.0.0)
  - LineSDKSwift (5.8.2):
    - LineSDKSwift/Core (= 5.8.2)
  - LineSDKSwift/Core (5.8.2)
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SQLCipher (4.9.0):
    - SQLCipher/standard (= 4.9.0)
  - SQLCipher/common (4.9.0)
  - SQLCipher/standard (4.9.0):
    - SQLCipher/common
  - ZIPFoundation (0.9.19)

DEPENDENCIES:
  - AileCapacitorLineLogin (from `../../node_modules/aile-capacitor-line-login`)
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCommunityMedia (from `../../node_modules/@capacitor-community/media`)"
  - "CapacitorCommunitySqlite (from `../../node_modules/@capacitor-community/sqlite`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorMlkitBarcodeScanning (from `../../node_modules/@capacitor-mlkit/barcode-scanning`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - "CapacitorShare (from `../../node_modules/@capacitor/share`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

SPEC REPOS:
  trunk:
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - IONFilesystemLib
    - LineSDKSwift
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SQLCipher
    - ZIPFoundation

EXTERNAL SOURCES:
  AileCapacitorLineLogin:
    :path: "../../node_modules/aile-capacitor-line-login"
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCommunityMedia:
    :path: "../../node_modules/@capacitor-community/media"
  CapacitorCommunitySqlite:
    :path: "../../node_modules/@capacitor-community/sqlite"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorMlkitBarcodeScanning:
    :path: "../../node_modules/@capacitor-mlkit/barcode-scanning"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapacitorShare:
    :path: "../../node_modules/@capacitor/share"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  AileCapacitorLineLogin: 05b0af0a36b7fb4d3529e968c7da7e412b861b16
  Capacitor: 9d9e481b79ffaeacaf7a85d6a11adec32bd33b59
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorCamera: 6e73f1fc6c629a672658705a02409b60854bc0f1
  CapacitorCommunityMedia: d1538b56a3303298e7cf45a18b11e624fbcbdc8e
  CapacitorCommunitySqlite: 8b2c6bab33e3519280811d481f8bd0fa90343e1b
  CapacitorCordova: 5e58d04631bc5094894ac106e2bf1da18a9e6151
  CapacitorDevice: c6f6d587dd310527f8a48bf09c4e7b4a4cf14329
  CapacitorFilesystem: 337eeaf16a9fa5a44d2329df411523fa3a9a6ee9
  CapacitorMlkitBarcodeScanning: 147c172f6e423618b156e050f715cfb963680ef4
  CapacitorPushNotifications: 6a2794788c583dd89215f1805ca4bced1b13dbdf
  CapacitorShare: e573823f511f260f598d0423c33b1e3d7bbe5fd1
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  IONFilesystemLib: ceacae793975039530458eabab0c495c70515a0d
  LineSDKSwift: 52bce568988f61f4a529b0ee629525a0cb85b9fe
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SQLCipher: 31878d8ebd27e5c96db0b7cb695c96e9f8ad77da
  ZIPFoundation: b8c29ea7ae353b309bc810586181fd073cb3312c

PODFILE CHECKSUM: 830dc68c3b851333ecee846178ce0ea53960db93

COCOAPODS: 1.16.2
