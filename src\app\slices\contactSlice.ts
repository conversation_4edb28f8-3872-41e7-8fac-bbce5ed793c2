import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { contactService } from '../../services/core/tenant';
import { logService } from '../../services/system/logService';
import type { Contact } from '../../services/db/initSql';
import snService from '../../services/core/tenant/snService';
import type { RootState } from '../../types/store';
import { getCurrentTenantId } from '@/utils/tenantUtil';

// 聯繫人狀態接口
interface ContactState {
  // 主數據（按租戶分類）
  entitiesByTenant: Record<string, Record<string, Contact>>;  // tenantId -> contactId -> Contact
  idsByTenant: Record<string, string[]>;  // tenantId -> contactIds[]
  // 當前狀態
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  // 分頁信息
  pageInfo: {
    lastRefreshTime: number;
  };
  // 頭像緩存狀態
  avatars: Record<string, {
    avatarId?: string;
    lastUpdated: number;
    status: 'idle' | 'loading' | 'succeeded' | 'failed';
  }>;
}

const initialState: ContactState = {
  entitiesByTenant: {},
  idsByTenant: {},
  status: 'idle',
  error: null,
  pageInfo: {
    lastRefreshTime: 0
  },
  avatars: {}
};

// 從本地DB加載聯繫人
export const fetchContactsFromDB = createAsyncThunk(
  'contacts/fetchFromDB',
  async (params: { tenantId: string }, { rejectWithValue }) => {
    try {
      const contacts = await contactService.getContactList({
        tenantId: params.tenantId
      });
      // 返回包含租戶ID和聯繫人列表的數據
      return { tenantId: params.tenantId, contacts };
    } catch (error) {
      logService.error('從DB加載聯繫人失敗', { error, tenantId: params.tenantId });
      return rejectWithValue((error as Error).message || '加載失敗');
    }
  }
);

// 從API同步聯繫人
export const syncContactsFromAPI = createAsyncThunk(
  'contacts/syncFromAPI',
  async (_, {  rejectWithValue, getState }) => {
    try {
      // 獲取當前租戶ID - 从Redux状态获取而非localStorage
      const state = getState() as RootState;
      const tenantId = state.tenant.currentTenantId;
      if (!tenantId) {
        return rejectWithValue('無法獲取當前租戶ID');
      }

      // 獲取商務號ID
      const bossServiceNumberId = await snService.getBossServiceNumberId();
      if (!bossServiceNumberId) {
        return rejectWithValue('無法獲取商務號ID');
      }

      // 異步同步聯繫人數據
      let totalSaved = 0;

      await contactService.syncContactsToDb( bossServiceNumberId);

      return { totalSaved, serviceNumberId: bossServiceNumberId, tenantId };
    } catch (error) {
      logService.error('同步聯繫人失敗', { error });
      return rejectWithValue((error as Error).message || '同步失敗');
    }
  }
);

const contactSlice = createSlice({
  name: 'contacts',
  initialState,
  reducers: {
    // 添加多個聯繫人（按租戶）- 支持增量更新和去重
    addContacts: (state, action: PayloadAction<{ tenantId: string, contacts: Contact[], replace?: boolean }>) => {
      const { tenantId, contacts, replace = false } = action.payload;

      // 確保租戶對應的數據結構已初始化
      if (!state.entitiesByTenant[tenantId]) {
        state.entitiesByTenant[tenantId] = {};
      }

      if (!state.idsByTenant[tenantId]) {
        state.idsByTenant[tenantId] = [];
      }

      // 如果是替换模式，清空现有数据
      if (replace) {
        state.entitiesByTenant[tenantId] = {};
        state.idsByTenant[tenantId] = [];
      }

      // 添加聯繫人數據，确保去重和数据更新
      contacts.forEach(contact => {
        const existingContact = state.entitiesByTenant[tenantId][contact.id];

        // 更新或添加聯繫人實體（总是使用最新数据）
        state.entitiesByTenant[tenantId][contact.id] = {
          ...existingContact, // 保留现有数据
          ...contact, // 用新数据覆盖
          // 确保关键字段不为空
          id: contact.id,
          name: contact.name || existingContact?.name || '',
          tenantId: contact.tenantId || tenantId
        };

        // 如果ID列表中不存在，則添加到列表末尾
        if (!state.idsByTenant[tenantId].includes(contact.id)) {
          state.idsByTenant[tenantId].push(contact.id);
        }

        // 更新或初始化頭像狀態
        if (contact.avatarId) {
          state.avatars[contact.id] = {
            ...state.avatars[contact.id], // 保留现有状态
            avatarId: contact.avatarId,
            lastUpdated: Date.now(),
            status: state.avatars[contact.id]?.status || 'idle'
          };
        }
      });

      // 按名称排序（可选，提升用户体验）
      state.idsByTenant[tenantId].sort((a, b) => {
        const contactA = state.entitiesByTenant[tenantId][a];
        const contactB = state.entitiesByTenant[tenantId][b];
        return (contactA?.name || '').localeCompare(contactB?.name || '');
      });
    },
    
    // 更新聯繫人頭像
    updateContactAvatar: (state, action: PayloadAction<{
      contactId: string;
      avatarId?: string;
      status?: 'idle' | 'loading' | 'succeeded' | 'failed';
    }>) => {
      const { contactId, avatarId, status } = action.payload;
      
      // 獲取當前租戶ID
      const tenantId = getCurrentTenantId();
      if (!tenantId || !state.entitiesByTenant[tenantId]) {
        return;
      }
      
      // 更新實體中的頭像ID
      if (state.entitiesByTenant[tenantId][contactId]) {
        state.entitiesByTenant[tenantId][contactId].avatarId = avatarId;
      }
      
      // 更新頭像緩存狀態
      state.avatars[contactId] = {
        avatarId,
        lastUpdated: Date.now(),
        status: status || 'idle'
      };
    },
    
    // 重置特定租戶的聯繫人數據
    resetContactsByTenant: (state, action: PayloadAction<{ tenantId: string }>) => {
      const { tenantId } = action.payload;
      
      // 清空指定租戶的數據
      if (state.entitiesByTenant[tenantId]) {
        state.entitiesByTenant[tenantId] = {};
      }
      
      if (state.idsByTenant[tenantId]) {
        state.idsByTenant[tenantId] = [];
      }
      
      state.status = 'idle';
      state.error = null;
      // 不清空頭像緩存，因為它可以跨租戶共享
    },
    
    // 重置所有聯繫人數據（用於登出等場景）
    resetAllContacts: (state) => {
      state.entitiesByTenant = {};
      state.idsByTenant = {};
      state.status = 'idle';
      state.error = null;
      state.pageInfo.lastRefreshTime = 0;
      // 保留頭像緩存狀態
    }
  },
  extraReducers: (builder) => {
    builder
      // 處理從DB加載聯繫人
      .addCase(fetchContactsFromDB.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchContactsFromDB.fulfilled, (state, action) => {
        state.status = 'succeeded';

        const { tenantId, contacts } = action.payload;

        // 使用 addContacts reducer 来处理数据，确保去重和排序
        // 从数据库加载时使用replace模式，确保数据一致性
        contactSlice.caseReducers.addContacts(state, {
          type: 'contacts/addContacts',
          payload: { tenantId, contacts, replace: true }
        });
      })
      .addCase(fetchContactsFromDB.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string || '加載聯繫人失敗';
      })
      
      // 處理從API同步聯繫人
      .addCase(syncContactsFromAPI.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(syncContactsFromAPI.fulfilled, (state) => {
        state.status = 'succeeded';
        state.pageInfo.lastRefreshTime = Date.now();
      })
      .addCase(syncContactsFromAPI.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string || '同步聯繫人失敗';
      });
  }
});

// 選擇器：獲取當前租戶的聯繫人實體
export const selectCurrentTenantContactEntities = (state: RootState) => {
  const tenantId = state.tenant.currentTenantId;
  return tenantId && state.contacts.entitiesByTenant[tenantId]
    ? state.contacts.entitiesByTenant[tenantId]
    : {};
};

// 選擇器：獲取當前租戶的聯繫人ID列表
export const selectCurrentTenantContactIds = (state: RootState) => {
  const tenantId = state.tenant.currentTenantId;
  return tenantId && state.contacts.idsByTenant[tenantId]
    ? state.contacts.idsByTenant[tenantId]
    : [];
};

export const { 
  addContacts, 
  updateContactAvatar, 
  resetContactsByTenant, 
  resetAllContacts 
} = contactSlice.actions;

export default contactSlice.reducer; 