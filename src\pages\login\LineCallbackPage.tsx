import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch } from '../../app/hooks';
import { loginWithLINE } from '../../app/slices/authSlice';

import { logService } from '../../services/system/logService';
import { ConstantUtil } from '../../utils/constantUtil';
import { Toast } from 'antd-mobile';
import { deviceService } from '../../services/platform';
import { ROUTE_LOGIN, ROUTE_HOME } from '../../config/app/routes';

/**
 * LINE 登錄回調頁面
 * 處理從 LINE 登錄返回的回調
 */
const LineCallbackPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        logService.info('LINE回調頁面載入', { pathname: location.pathname, search: location.search });
        
        // 當使用通用 OAuth2 包時，此頁面通常只在 Web 版本中需要處理回調
        // 原生應用會由 Capacitor 插件直接處理
        if (!deviceService.isWeb()) {
          logService.info('非Web環境，無需處理回調');
          navigate(ROUTE_LOGIN);
          return;
        }
        
        // 從URL中獲取參數
        const params = new URLSearchParams(location.search);
        const code = params.get('code');
        const state = params.get('state');
        
        if (!code) {
          logService.error('LINE回調缺少必要參數', { code });
          Toast.show({
            icon: 'fail',
            content: 'LINE登錄失敗，請重試',
            duration: 3000,
          });
          navigate(ROUTE_LOGIN);
          return;
        }
        
        logService.info('處理LINE回調', { code: code?.substring(0, 5) + '...', state });
        
        // 此時我們已從 URL 參數獲取了授權碼，但對於 Web 版本，
        // 我們依然需要後端來交換訪問令牌
        // 注意：在原生應用中，通用 OAuth2 包已經處理了這一步驟
        
        // 使用後端 API 交換訪問令牌
        // 模擬處理為示例
        // 實際項目中應該調用後端 API
        setTimeout(() => {
          // 模擬後端返回的訪問令牌
          const mockAccessToken = "mock_access_token_" + Date.now();
          
          // 使用 Redux 動作處理登錄流程
          dispatch(loginWithLINE({
            loginType: ConstantUtil.LOGIN_TYPE_LINE,
            thirdChannel: 'web',
            useNonMobile: true,
            scopeId: mockAccessToken
          }));
          
          // 跳轉到首頁
          navigate(ROUTE_HOME);
        }, 1000);
      } catch (error) {
        logService.error('處理LINE回調出錯', error as Error);
        Toast.show({
          icon: 'fail',
          content: 'LINE登錄失敗，請重試',
          duration: 3000,
        });
        navigate(ROUTE_LOGIN);
      }
    };

    handleCallback();
  }, [location, navigate, dispatch]);

  return (
    <div className="line-callback-page">
      <div className="line-callback-loading">
        <p>LINE 登錄中，請稍候...</p>
      </div>
    </div>
  );
};

export default LineCallbackPage; 