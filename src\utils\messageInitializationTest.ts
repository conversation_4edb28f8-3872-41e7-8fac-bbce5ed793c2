/**
 * 消息初始化逻辑测试工具
 * 用于验证 initializeRoomMessages 的优化效果
 */

import { messageService } from '../services/core/chat/messageService';
import { ConstantUtil } from './constantUtil';

interface TestScenario {
  name: string;
  description: string;
  roomId: string;
  lastSequence: number;
  expectedDbMessages: number;
  expectedApiCalls: number;
}

/**
 * 测试场景定义
 */
const testScenarios: TestScenario[] = [
  {
    name: '场景1：数据完整',
    description: 'lastSequence=20，DB中有20条消息(1-20)，无需API调用',
    roomId: 'test-room-1',
    lastSequence: 20,
    expectedDbMessages: 20,
    expectedApiCalls: 0
  },
  {
    name: '场景2：数据缺失',
    description: 'lastSequence=16，DB中只有8条消息(1-8)，需要API获取9-16',
    roomId: 'test-room-2',
    lastSequence: 16,
    expectedDbMessages: 8,
    expectedApiCalls: 1
  },
  {
    name: '场景3：完全缺失',
    description: 'lastSequence=20，DB中无数据，需要API获取所有消息',
    roomId: 'test-room-3',
    lastSequence: 20,
    expectedDbMessages: 0,
    expectedApiCalls: 1
  },
  {
    name: '场景4：大量缺失',
    description: 'lastSequence=100，DB中有10条消息(1-10)，需要分批API获取11-100',
    roomId: 'test-room-4',
    lastSequence: 100,
    expectedDbMessages: 10,
    expectedApiCalls: 5 // (100-10)/20 = 4.5，向上取整为5
  }
];

/**
 * 模拟数据库查询结果
 */
async function simulateDbQuery(roomId: string, expectedCount: number) {
  const messages = [];
  for (let i = 1; i <= expectedCount; i++) {
    messages.push({
      id: `msg-${i}`,
      roomId,
      sequence: i,
      content: `Message ${i}`,
      sendTime: Date.now() - (expectedCount - i) * 1000,
      senderId: 'user-1'
    });
  }
  return messages;
}

/**
 * 分析数据完整性
 */
function analyzeDataIntegrity(
  lastSequence: number, 
  dbMessages: any[]
): {
  isComplete: boolean;
  missingCount: number;
  missingRange: { start: number; end: number } | null;
  recommendedAction: string;
} {
  if (dbMessages.length === 0) {
    return {
      isComplete: false,
      missingCount: lastSequence,
      missingRange: { start: 1, end: lastSequence },
      recommendedAction: `从API获取所有消息 (1-${lastSequence})`
    };
  }

  const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0);
  const dbMaxSequence = Math.max(...sequences);

  const isComplete = dbMaxSequence >= lastSequence;
  const missingCount = Math.max(0, lastSequence - dbMaxSequence);

  let recommendedAction = '';
  let missingRange = null;

  if (isComplete) {
    recommendedAction = '数据完整，无需API调用';
  } else {
    missingRange = { start: dbMaxSequence + 1, end: lastSequence };
    const batchCount = Math.ceil(missingCount / ConstantUtil.DEFAULT_PAGE_SIZE);
    recommendedAction = `从API分批获取缺失消息，共${batchCount}批`;
  }

  return {
    isComplete,
    missingCount,
    missingRange,
    recommendedAction
  };
}

/**
 * 运行单个测试场景
 */
async function runTestScenario(scenario: TestScenario): Promise<void> {
  console.log(`\n🧪 ${scenario.name}`);
  console.log(`📝 ${scenario.description}`);
  console.log('─'.repeat(60));

  try {
    // 模拟数据库查询
    const dbMessages = await simulateDbQuery(scenario.roomId, scenario.expectedDbMessages);
    
    console.log('📊 当前状态:');
    console.log(`   roomId: ${scenario.roomId}`);
    console.log(`   lastSequence: ${scenario.lastSequence}`);
    console.log(`   DB消息数: ${dbMessages.length}`);
    
    if (dbMessages.length > 0) {
      const sequences = dbMessages.map(msg => msg.sequence || 0);
      console.log(`   DB序列号范围: ${Math.min(...sequences)} - ${Math.max(...sequences)}`);
    }

    // 分析数据完整性
    const analysis = analyzeDataIntegrity(scenario.lastSequence, dbMessages);
    
    console.log('\n🔍 完整性分析:');
    console.log(`   数据完整: ${analysis.isComplete ? '✅' : '❌'}`);
    console.log(`   缺失消息数: ${analysis.missingCount}`);
    if (analysis.missingRange) {
      console.log(`   缺失范围: ${analysis.missingRange.start} - ${analysis.missingRange.end}`);
    }
    console.log(`   建议操作: ${analysis.recommendedAction}`);

    // 验证预期结果
    const expectedApiCalls = analysis.missingCount > 0 ? 
      Math.ceil(analysis.missingCount / ConstantUtil.DEFAULT_PAGE_SIZE) : 0;
    
    console.log('\n✅ 验证结果:');
    console.log(`   预期API调用: ${scenario.expectedApiCalls}`);
    console.log(`   实际API调用: ${expectedApiCalls}`);
    console.log(`   结果匹配: ${expectedApiCalls === scenario.expectedApiCalls ? '✅' : '❌'}`);

    if (expectedApiCalls !== scenario.expectedApiCalls) {
      console.log(`   ⚠️  预期与实际不符！`);
    }

  } catch (error) {
    console.error(`❌ 测试场景失败:`, error);
  }
}

/**
 * 运行所有测试场景
 */
export async function runMessageInitializationTests(): Promise<void> {
  console.log('🚀 开始消息初始化逻辑测试');
  console.log('=' .repeat(80));

  for (const scenario of testScenarios) {
    await runTestScenario(scenario);
  }

  console.log('\n' + '='.repeat(80));
  console.log('🏁 所有测试场景完成');
  
  // 输出优化建议
  console.log('\n💡 优化建议:');
  console.log('1. 初始化时先查询DB，不使用sequence过滤');
  console.log('2. 比较lastSequence与DB最大序列号，检测数据缺失');
  console.log('3. 分批获取缺失数据，避免单次API调用过大');
  console.log('4. 使用延迟执行，避免API调用过于频繁');
  console.log('5. 详细记录日志，便于问题排查');
}

/**
 * 测试特定房间的数据完整性
 */
export async function testRoomDataIntegrity(roomId: string, lastSequence: number): Promise<void> {
  console.log(`\n🔍 测试房间数据完整性: ${roomId}`);
  console.log('─'.repeat(50));

  try {
    // 实际查询数据库
    const dbMessages = await messageService.getMessagesFromDB(
      roomId,
      0,
      ConstantUtil.DEFAULT_PAGE_SIZE * 2,
      undefined,
      'desc'
    );

    const analysis = analyzeDataIntegrity(lastSequence, dbMessages);
    
    console.log('📊 实际数据状态:');
    console.log(`   lastSequence: ${lastSequence}`);
    console.log(`   DB消息数: ${dbMessages.length}`);
    
    if (dbMessages.length > 0) {
      const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0);
      const dbMaxSequence = Math.max(...sequences);
      const dbMinSequence = Math.min(...sequences);
      console.log(`   DB序列号范围: ${dbMinSequence} - ${dbMaxSequence}`);
    }

    console.log('\n🔍 完整性分析:');
    console.log(`   数据完整: ${analysis.isComplete ? '✅' : '❌'}`);
    console.log(`   缺失消息数: ${analysis.missingCount}`);
    if (analysis.missingRange) {
      console.log(`   缺失范围: ${analysis.missingRange.start} - ${analysis.missingRange.end}`);
    }
    console.log(`   建议操作: ${analysis.recommendedAction}`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}
