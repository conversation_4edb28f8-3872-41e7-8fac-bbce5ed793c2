import aileDBService, { closeAileDB, initAileDB } from './aileDBService';
import { getLocalStorage } from '../../utils/storage';


jest.mock('../../utils/storage', () => ({
  getLocalStorage: jest.fn(),
  setLocalStorage: jest.fn()
}));

jest.mock('./sqliteService', () => ({
  __esModule: true,
  default: {
    getPlatform: jest.fn().mockReturnValue('web'),
    initWebStore: jest.fn().mockResolvedValue(undefined),
    openDatabase: jest.fn().mockResolvedValue({
      open: jest.fn().mockResolvedValue(true),
      isDBOpen: jest.fn().mockResolvedValue({ result: true }),
      query: jest.fn().mockResolvedValue({ columns: [], values: [] }),
      run: jest.fn().mockResolvedValue({ changes: { changes: 1 } }),
      close: jest.fn().mockResolvedValue(undefined)
    }),
    closeDatabase: jest.fn().mockResolvedValue(undefined),
    isConnection: jest.fn().mockResolvedValue(true),
    saveToStore: jest.fn().mockResolvedValue(undefined),
    saveToLocalDisk: jest.fn().mockResolvedValue(undefined)
  }
}));

describe('aileDBService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (getLocalStorage as jest.Mock).mockReturnValue('test-account-id');
  });

  it('應有 init 方法', () => {
    expect(typeof aileDBService.init).toBe('function');
  });

  it('應有 initForAccount 方法', () => {
    expect(typeof aileDBService.initForAccount).toBe('function');
  });

  it('應有 exec 方法', () => {
    expect(typeof aileDBService.exec).toBe('function');
  });

  it('應有 get 方法', () => {
    expect(typeof aileDBService.get).toBe('function');
  });

  it('應有 all 方法', () => {
    expect(typeof aileDBService.all).toBe('function');
  });

  it('應有 run 方法', () => {
    expect(typeof aileDBService.run).toBe('function');
  });

  it('應有 close 方法', () => {
    expect(typeof aileDBService.close).toBe('function');
  });

  it('應有 isInitialized 方法', () => {
    expect(typeof aileDBService.isInitialized).toBe('function');
  });

  it('應有 closeAileDB 函數', () => {
    expect(typeof closeAileDB).toBe('function');
  });

  it('應有 initAileDB 函數', () => {
    expect(typeof initAileDB).toBe('function');
  });

  it('initAileDB 應該調用 initForAccount', async () => {
    const spy = jest.spyOn(aileDBService, 'initForAccount').mockResolvedValue(true);
    await initAileDB('test-id');
    expect(spy).toHaveBeenCalledWith('test-id');
    spy.mockRestore();
  });

  // 可擴展：測試初始化、查詢等功能
}); 