import { logService } from '../system/logService';
import aileDBService from '../db/aileDBService';
import type { Tenant } from '../db/initSql';


/**
 * 租戶數據訪問對象
 * 提供租戶表相關的數據庫操作
 */
class TenantDao {
  /**
   * 獲取關聯租戶列表
   * @param params 查詢參數
   * @returns 關聯租戶列表
   */
  public async getTenantList(params: {
    pageIndex?: number;
    pageSize?: number;
    key?: string;
    refreshTime?: number;
    accountId?: string;
    channel?: string;
  }): Promise<Array<Tenant & { relationId?: string; openId?: string }>> {
    const {
      pageIndex = 0,
      pageSize = 20,
      key,
      refreshTime = 0,
      accountId,
      channel
    } = params;

    try {
      // 構建 SQL 查詢
      let sql = `SELECT * FROM Tenant WHERE 1=1`;
      const sqlParams: any[] = [];

      // 處理查詢條件
      if (key) {
        sql += ` AND (name LIKE ? OR shortName LIKE ? OR code LIKE ?)`;
        const likeKey = `%${key}%`;
        sqlParams.push(likeKey, likeKey, likeKey);
      }

      if (refreshTime > 0) {
        sql += ` AND updateTime > ?`;
        sqlParams.push(refreshTime);
      }

      if (accountId) {
        sql += ` AND accountId = ?`;
        sqlParams.push(accountId);
      }

      if (channel) {
        sql += ` AND channel = ?`;
        sqlParams.push(channel);
      }

      // 排序與分頁
      sql += ` ORDER BY name ASC LIMIT ? OFFSET ?`;
      sqlParams.push(pageSize, pageIndex * pageSize);

      // 執行查詢
      const tenants = await aileDBService.all<Tenant & { relationId?: string; openId?: string }>(sql, sqlParams);
      return tenants;
    } catch (error) {
      logService.error('查詢租戶列表失敗', { error: error as Error, params });
      return [];
    }
  }

  /**
   * 獲取租戶詳情
   * @param id 租戶ID
   * @returns 租戶詳情
   */
  public async getTenantById(id: string): Promise<Tenant | null> {
    if (!id) {
      logService.warn('嘗試使用空ID查詢租戶');
      return null;
    }
    
    try {
      // 使用更簡單的查詢語句，避免複雜語法可能導致的錯誤
      const tenant = await aileDBService.get<Tenant>(
        `SELECT * FROM Tenant WHERE id = ?`,
        [id]
      );
      
      // 檢查是否為空對象 (沒有任何屬性)
      if (tenant && Object.keys(tenant).length === 0) {
        logService.warn(`查詢到ID為 ${id} 的租戶，但返回空對象`);
        return null; // 返回 null 而不是空對象
      }
      
      // 檢查是否有必要的屬性
      if (tenant && (!tenant.id || tenant.id !== id)) {
        logService.warn(`查詢到的租戶數據不完整或ID不匹配`, { 
          expected: id, 
          actual: tenant.id,
          data: tenant 
        });
        return null;
      }
      
      if (!tenant) {
        logService.warn(`未找到ID為 ${id} 的租戶`);
      } else {
        logService.info(`成功獲取租戶詳情: ${tenant.name}`, { 
          id: tenant.id,
          officialServiceNumberId: tenant.officialServiceNumberId || '無' 
        });
      }
      
      return tenant;
    } catch (error) {
      logService.error('獲取租戶詳情失敗', { error: error as Error, id });
      return null;
    }
  }

  /**
   * 檢查租戶是否存在於數據庫中
   * @param id 租戶ID
   * @returns 是否存在
   */
  public async hasTenant(id: string): Promise<boolean> {
    try {
      const result = await aileDBService.get<{ count: number }>(
        `SELECT COUNT(*) as count FROM Tenant WHERE id = ?`,
        [id]
      );
      return result ? result.count > 0 : false;
    } catch (error) {
      logService.error('檢查租戶是否存在失敗', { error: error as Error, id });
      return false;
    }
  }

  /**
   * 保存租戶信息 (Upsert: 存在則更新，不存在則插入)
   * @param tenant 租戶信息
   * @returns 是否保存成功
   */
  public async saveTenant(tenant: Tenant): Promise<boolean> {
    try {
      if (!tenant || !tenant.id) {
        logService.warn('無效的租戶數據，缺少ID', { tenant });
        return false;
      }

      const keys = Object.keys(tenant);
      const placeholders = keys.map(() => '?').join(',');
      const values = Object.values(tenant);

      const sql = `INSERT OR REPLACE INTO Tenant (${keys.join(',')}) VALUES (${placeholders})`;

      await aileDBService.run(sql, values);
      logService.info('租戶資料已保存 (Upsert)', { tenantId: tenant.id });
      
      return true;
    } catch (error) {
      logService.error('保存租戶信息失敗', { error: error as Error, tenant });
      return false;
    }
  }

  /**
   * 批量保存租戶信息 (使用 Upsert)
   * @param tenants 租戶信息列表
   * @returns 保存成功的租戶數量
   */
  public async saveTenants(tenants: Tenant[]): Promise<number> {
    if (!tenants || !Array.isArray(tenants) || tenants.length === 0) {
      return 0;
    }

    const validTenants = tenants.filter(t => t && t.id);
    if (validTenants.length === 0) {
      logService.warn('沒有有效的租戶信息需要保存');
      return 0;
    }
    
    try {
      // 移除手動事務管理，改為每個操作都在隱式事務中執行
      
      let successCount = 0;
      for (const tenant of validTenants) {
        const keys = Object.keys(tenant);
        const placeholders = `(${keys.map(() => '?').join(',')})`;
        const sql = `INSERT OR REPLACE INTO Tenant (${keys.join(',')}) VALUES ${placeholders}`;
        const values = keys.map(key => tenant[key as keyof Tenant]);
        
        await aileDBService.run(sql, values);
        successCount++;
      }

      logService.info(`批量保存租戶完成，共處理 ${successCount} 條記錄`, { total: validTenants.length });

      // 持久化數據，增加重試邏輯
      if (successCount > 0) {
        try {
          await this.saveToStoreWithRetry();
        } catch (saveError) {
          logService.error('持久化租戶數據失敗，但數據已保存到內存數據庫', { 
            error: saveError as Error,
            successCount 
          });
          // 不拋出異常，因為數據已寫入，下次應用啟動時會重建
        }
      }

      return successCount;

    } catch (error) {
      logService.error('批量保存租戶信息失敗', { error: error as Error, count: validTenants.length });
      return 0;
    }
  }
  
  /**
   * 带重试逻辑的持久化数据保存
   * @private
   */
  private async saveToStoreWithRetry(retries = 2): Promise<void> {
    let lastError;
    
    for (let i = 0; i <= retries; i++) {
      try {
        if (i > 0) {
          logService.info(`嘗試第${i}次重新保存數據庫到持久存儲`);
          // 重試前短暫延遲
          await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        await aileDBService.saveToStore();
        logService.info('批量保存租戶後數據已持久化');
        return; // 成功保存，退出函數
      } catch (error) {
        lastError = error;
        logService.warn(`持久化數據失敗，嘗試重試 (${i}/${retries})`, { error });
      }
    }
    
    // 所有重試都失敗
    throw lastError;
  }

  /**
   * 刪除租戶
   * @param id 租戶ID
   * @returns 是否刪除成功
   */
  public async deleteTenant(id: string): Promise<boolean> {
    try {
      await aileDBService.run(`DELETE FROM Tenant WHERE id = ?`, [id]);
      return true;
    } catch (error) {
      logService.error('刪除租戶失敗', { error: error as Error, id });
      return false;
    }
  }
}

// 導出單例實例
export default new TenantDao(); 