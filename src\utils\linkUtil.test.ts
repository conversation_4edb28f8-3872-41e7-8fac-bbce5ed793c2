import { parseTextWithLinks, hasLinks, extractUrls } from './linkUtil';

describe('linkUtil', () => {
  describe('parseTextWithLinks', () => {
    it('should handle plain text without URLs', () => {
      const result = parseTextWithLinks('Hello world');
      expect(result).toEqual([
        { text: 'Hello world', isUrl: false }
      ]);
    });

    it('should handle text with HTTP URL', () => {
      const result = parseTextWithLinks('Visit https://example.com for more info');
      expect(result).toEqual([
        { text: 'Visit ', isUrl: false },
        { text: 'https://example.com', isUrl: true, href: 'https://example.com' },
        { text: ' for more info', isUrl: false }
      ]);
    });

    it('should handle text with HTTPS URL', () => {
      const result = parseTextWithLinks('Check out https://www.google.com');
      expect(result).toEqual([
        { text: 'Check out ', isUrl: false },
        { text: 'https://www.google.com', isUrl: true, href: 'https://www.google.com' }
      ]);
    });

    it('should handle text with www URL and add https prefix', () => {
      const result = parseTextWithLinks('Go to www.example.com');
      expect(result).toEqual([
        { text: 'Go to ', isUrl: false },
        { text: 'www.example.com', isUrl: true, href: 'https://www.example.com' }
      ]);
    });

    it('should handle text with FTP URL', () => {
      const result = parseTextWithLinks('Download from ftp://files.example.com');
      expect(result).toEqual([
        { text: 'Download from ', isUrl: false },
        { text: 'ftp://files.example.com', isUrl: true, href: 'ftp://files.example.com' }
      ]);
    });

    it('should handle multiple URLs in one text', () => {
      const result = parseTextWithLinks('Visit https://example.com and www.google.com');
      expect(result).toEqual([
        { text: 'Visit ', isUrl: false },
        { text: 'https://example.com', isUrl: true, href: 'https://example.com' },
        { text: ' and ', isUrl: false },
        { text: 'www.google.com', isUrl: true, href: 'https://www.google.com' }
      ]);
    });

    it('should handle empty string', () => {
      const result = parseTextWithLinks('');
      expect(result).toEqual([
        { text: '', isUrl: false }
      ]);
    });

    it('should handle URL at the beginning', () => {
      const result = parseTextWithLinks('https://example.com is a great site');
      expect(result).toEqual([
        { text: 'https://example.com', isUrl: true, href: 'https://example.com' },
        { text: ' is a great site', isUrl: false }
      ]);
    });

    it('should handle URL at the end', () => {
      const result = parseTextWithLinks('Check this out: https://example.com');
      expect(result).toEqual([
        { text: 'Check this out: ', isUrl: false },
        { text: 'https://example.com', isUrl: true, href: 'https://example.com' }
      ]);
    });
  });

  describe('hasLinks', () => {
    it('should return false for plain text', () => {
      expect(hasLinks('Hello world')).toBe(false);
    });

    it('should return true for text with HTTP URL', () => {
      expect(hasLinks('Visit https://example.com')).toBe(true);
    });

    it('should return true for text with www URL', () => {
      expect(hasLinks('Go to www.example.com')).toBe(true);
    });

    it('should return true for text with FTP URL', () => {
      expect(hasLinks('Download ftp://files.example.com')).toBe(true);
    });
  });

  describe('extractUrls', () => {
    it('should return empty array for plain text', () => {
      expect(extractUrls('Hello world')).toEqual([]);
    });

    it('should extract single URL', () => {
      expect(extractUrls('Visit https://example.com')).toEqual(['https://example.com']);
    });

    it('should extract multiple URLs', () => {
      expect(extractUrls('Visit https://example.com and www.google.com')).toEqual([
        'https://example.com',
        'www.google.com'
      ]);
    });

    it('should extract different types of URLs', () => {
      expect(extractUrls('HTTP: https://example.com, FTP: ftp://files.com, WWW: www.test.com')).toEqual([
        'https://example.com',
        'ftp://files.com',
        'www.test.com'
      ]);
    });
  });
});
