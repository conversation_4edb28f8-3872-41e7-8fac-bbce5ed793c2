---
description: 
globs: 
alwaysApply: true
---
# 代码风格规范

## TypeScript 规范
- 使用严格模式
- 避免使用 any
- 使用类型推断
- 使用接口定义数据结构
- 使用类型别名定义联合类型

## React 规范
- 使用函数组件
- 使用 Hooks
- 使用 TypeScript 类型
- 使用 Props 类型定义
- 使用 React.memo 优化

## 命名规范
- 组件使用 PascalCase
- 函数使用 camelCase
- 常量使用 UPPER_SNAKE_CASE
- 类型使用 PascalCase
- 接口使用 I 前缀

## 文件组织
- 一个文件一个组件
- 使用 index.ts 导出
- 相关文件放在同一目录
- 使用有意义的文件名

## 注释规范
- 使用 JSDoc 注释
- 注释复杂逻辑
- 注释公共 API
- 保持注释更新

## 样式规范
- 使用 Tailwind CSS
- 避免内联样式
- 使用 CSS 变量
- 遵循移动端适配

## 代码格式化
- 使用 Prettier
- 使用 ESLint
- 使用 EditorConfig
- 使用 Git Hooks

## 导入规范
- 使用绝对路径
- 按类型分组
- 按字母顺序排序
- 避免循环依赖

## 错误处理
- 使用 try-catch
- 使用错误边界
- 统一错误处理
- 提供错误信息
- 所有异常处理（如 try-catch、错误边界、Promise.catch）必须调用 logService 记录异常信息
- 日志内容应包含错误堆栈、上下文信息、用户标识等关键信息
- error/fatal 级别错误由 logService 自动上报 Sentry 或远程服务

## 性能优化
- 使用 useMemo
- 使用 useCallback
- 避免不必要的渲染
- 优化资源加载

## 日志规范
- 每个核心代码文件需自动引入 logService
- 在关键操作、异常、重要状态变更处记录日志
- 日志分级（info/debug/warn/error）合理使用
- 日志内容应包含关键信息，便于排查

## 测试规范
- 每个核心逻辑需有单元测试覆盖
- 新增或修改功能时自动生成对应测试类
- 所有测试文件统一以 .test.ts 或 .test.tsx 结尾，命名与被测文件保持一致
- **测试文件与源文件放在同级目录**，不使用 __tests__ 目录
- 使用 Jest + React Testing Library
- 每个测试文件独立管理所需的 mock，不依赖全局 setupTests
- 测试覆盖常规流程、异常分支和边界情况
- 保持测试代码与业务代码同步更新

## 本地存储封装规范
- 所有 localStorage 操作必须通过 src/utils/storage.ts 提供的 setLocalStorage、getLocalStorage、removeLocalStorage、clearLocalStorage 封装函数进行，不得直接调用原生 localStorage API（如 localStorage.getItem/setItem/removeItem/clear）。
- localStorage key 必须统一在 ConstantUtil 中定义，禁止硬编码。
- 业务代码、服务、组件等均需遵循此规范。





