import { test, expect } from '@playwright/test';

test('登录页面应该正确渲染所有元素', async ({ page }) => {
  await page.goto('/');
  
  // 检查标题和描述文本
  await expect(page.locator('text=集中溝通窗口，讓客服應對更即時')).toBeVisible();
  
  // 检查 logo 是否显示
  const logo = page.locator('img[alt="Aile Logo"]');
  await expect(logo).toBeVisible();
  
  // 检查主图像是否显示
  const mainImage = page.locator('img[alt="主要图像"]');
  await expect(mainImage).toBeVisible();
  
  // 检查按钮是否显示
  await expect(page.locator('text=透過 LINE 繼續')).toBeVisible();
  await expect(page.locator('text=使用其他方式繼續')).toBeVisible();
  
  // LINE 图标是否显示
  const lineIcon = page.locator('img[alt="LINE"]');
  await expect(lineIcon).toBeVisible();
});

test('按钮样式应该符合设计稿', async ({ page }) => {
  await page.goto('/');
  
  // 检查 LINE 按钮样式
  const lineButton = page.locator('button:has-text("透過 LINE 繼續")');
  await expect(lineButton).toHaveCSS('background-color', 'rgb(57, 205, 0)');
  
  // 检查其他方式按钮样式
  const otherButton = page.locator('button:has-text("使用其他方式繼續")');
  await expect(otherButton).toHaveCSS('color', 'rgb(153, 153, 153)');
}); 

// 假設 AvatarImage 組件已在某頁面（如 /login）中使用

test('頭像圖片組件 E2E：下載與快取顯示', async ({ page }) => {
  await page.goto('/login');
  // 假設有 data-testid="avatar-image"
  await expect(page.getByTestId('avatar-loading')).toBeVisible();
  await expect(page.getByTestId('avatar-image')).toBeVisible();
  // 模擬刷新後快取命中
  await page.reload();
  await expect(page.getByTestId('avatar-image')).toBeVisible();
});

test('頭像圖片組件 E2E：下載失敗顯示錯誤', async ({ page }) => {
  await page.goto('/login?avatarError=1'); // 假設 query 觸發失敗
  await expect(page.getByTestId('avatar-error')).toBeVisible();
}); 