// src/apolloClient.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// 從環境變數獲取 BFF 的 GraphQL 端點
// 這裡會讀取 .env 中設定的 VITE_BFF_GRAPHQL_URL
const GRAPHQL_BFF_URL = "https://2f365eba9ba4.ngrok-free.app/graphql";

const httpLink = createHttpLink({
  uri: GRAPHQL_BFF_URL, // <-- Apollo Client 會向這個 URL 發送所有 GraphQL 請求
});

const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('authToken');
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    },
  };
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
});

export default client;