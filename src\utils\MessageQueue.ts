import { logService } from '../services/system/logService';

/**
 * 消息隊列項目介面
 */
export interface QueueItem<T = any> {
  id: string;
  data: T;
  timestamp: number;
  attempts: number;
  maxAttempts: number;
  processed: boolean;
}

/**
 * 消息處理器函數類型
 */
export type MessageProcessor<T = any> = (data: T) => Promise<boolean>;

/**
 * 消息隊列選項介面
 */
export interface MessageQueueOptions {
  maxAttempts?: number;
  processInterval?: number;
  maxQueueSize?: number;
  retryDelay?: number;
  concurrency?: number;
}

/**
 * 消息隊列類
 * 用於異步處理消息，支持重試和並發控制
 */
export class MessageQueue<T = any> {
  private queue: QueueItem<T>[] = [];
  // private processing: boolean = false; // Unused property
  private processingCount: number = 0;
  private interval: NodeJS.Timeout | null = null;
  private maxAttempts: number;
  private processInterval: number;
  private maxQueueSize: number;
  private retryDelay: number;
  private concurrency: number;
  private processor: MessageProcessor<T>;

  /**
   * 建立消息隊列
   * @param processor 消息處理器函數
   * @param options 隊列選項
   */
  constructor(processor: MessageProcessor<T>, options: MessageQueueOptions = {}) {
    this.processor = processor;
    this.maxAttempts = options.maxAttempts || 3;
    this.processInterval = options.processInterval || 200;
    this.maxQueueSize = options.maxQueueSize || 1000;
    this.retryDelay = options.retryDelay || 1000;
    this.concurrency = options.concurrency || 3;
    
    // 開始處理隊列
    this.startProcessing();
  }

  /**
   * 添加消息到隊列
   * @param id 消息ID
   * @param data 消息數據
   * @returns 是否成功添加
   */
  public enqueue(id: string, data: T): boolean {
    // 檢查隊列大小限制
    if (this.queue.length >= this.maxQueueSize) {
      logService.warn('消息隊列已滿，無法添加更多消息', { queueSize: this.queue.length });
      return false;
    }
    
    // 檢查是否已存在相同ID的消息
    const existingIndex = this.queue.findIndex(item => item.id === id);
    if (existingIndex >= 0) {
      // 更新已有消息
      this.queue[existingIndex].data = data;
      this.queue[existingIndex].timestamp = Date.now();
      this.queue[existingIndex].processed = false;
      return true;
    }
    
    // 添加新消息
    this.queue.push({
      id,
      data,
      timestamp: Date.now(),
      attempts: 0,
      maxAttempts: this.maxAttempts,
      processed: false
    });
    
    logService.debug('消息已添加到隊列', { id, queueSize: this.queue.length });
    return true;
  }

  /**
   * 啟動隊列處理
   */
  private startProcessing(): void {
    if (this.interval) {
      return;
    }
    
    this.interval = setInterval(() => {
      this.processQueue();
    }, this.processInterval);
    
    logService.debug('消息隊列處理已啟動');
  }

  /**
   * 停止隊列處理
   */
  public stopProcessing(): void {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      logService.debug('消息隊列處理已停止');
    }
  }

  /**
   * 處理隊列中的消息
   */
  private async processQueue(): Promise<void> {
    // 檢查是否有正在處理的消息達到並發上限
    if (this.processingCount >= this.concurrency) {
      return;
    }
    
    // 獲取未處理的消息
    const unprocessedItems = this.queue.filter(item => !item.processed);
    if (unprocessedItems.length === 0) {
      return;
    }
    
    // 按照最大並發數處理消息
    const itemsToProcess = unprocessedItems.slice(0, this.concurrency - this.processingCount);
    for (const item of itemsToProcess) {
      // 標記為正在處理
      item.processed = true;
      this.processingCount++;
      
      // 異步處理消息
      this.processItem(item).finally(() => {
        this.processingCount--;
      });
    }
  }

  /**
   * 處理單個消息項
   * @param item 消息項
   */
  private async processItem(item: QueueItem<T>): Promise<void> {
    try {
      item.attempts++;
      
      // 調用處理器處理消息
      const success = await this.processor(item.data);
      
      if (success) {
        // 成功處理，從隊列中移除
        const index = this.queue.findIndex(i => i.id === item.id);
        if (index >= 0) {
          this.queue.splice(index, 1);
          logService.debug('消息處理成功並已從隊列中移除', { id: item.id });
        }
      } else if (item.attempts < item.maxAttempts) {
        // 處理失敗但未達到最大嘗試次數，安排重試
        item.processed = false;
        logService.warn('消息處理失敗，將進行重試', { 
          id: item.id, 
          attempts: item.attempts,
          maxAttempts: item.maxAttempts 
        });
        
        // 延遲一段時間再重試
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      } else {
        // 達到最大重試次數，從隊列中移除
        const index = this.queue.findIndex(i => i.id === item.id);
        if (index >= 0) {
          this.queue.splice(index, 1);
          logService.error('消息處理失敗且已達到最大重試次數', { 
            id: item.id, 
            attempts: item.attempts 
          });
        }
      }
    } catch (error) {
      // 發生異常
      logService.error('處理消息時發生異常', error as Error);
      
      // 如果未達到最大嘗試次數，標記為未處理以便重試
      if (item.attempts < item.maxAttempts) {
        item.processed = false;
      } else {
        // 達到最大重試次數，從隊列中移除
        const index = this.queue.findIndex(i => i.id === item.id);
        if (index >= 0) {
          this.queue.splice(index, 1);
        }
      }
    }
  }

  /**
   * 獲取隊列長度
   */
  public size(): number {
    return this.queue.length;
  }

  /**
   * 清空隊列
   */
  public clear(): void {
    this.queue = [];
    logService.info('消息隊列已清空');
  }

  /**
   * 銷毀隊列
   */
  public destroy(): void {
    this.stopProcessing();
    this.clear();
    logService.info('消息隊列已銷毀');
  }
} 