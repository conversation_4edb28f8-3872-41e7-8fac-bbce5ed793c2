/**
 * 日志服务 - AileApp（现代 React/TypeScript 风格）
 * 支持日志级别、本地持久化、Sentry 上报、远程上报
 */

import * as Sentry from '@sentry/react';
import { ConstantUtil } from '../../utils/constantUtil';
import { getLocalStorage } from '../../utils/storage';
import { envConfig } from '../../config/env';

// 日志级别定义
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL',
}

// 日志条目接口
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  source?: string;
  userId?: string;
  sessionId?: string;
}

// 日志服务配置
interface LogServiceConfig {
  minLevel: LogLevel;
  maxLogEntries: number;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  applicationName: string;
}

/**
 * AileApp 日志服务（单例）
 */
class LogService {
  private static instance: LogService;
  private config: LogServiceConfig;
  private logBuffer: LogEntry[] = [];
  private sessionId: string;
  private userId?: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.config = {
      minLevel: LogLevel.INFO,
      maxLogEntries: 1000,
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      applicationName: ConstantUtil.APP_NAME,
    };
    this.loadLogsFromStorage();
  }

  /**
   * 获取日志服务单例
   */
  public static getInstance(): LogService {
    if (!LogService.instance) {
      LogService.instance = new LogService();
    }
    return LogService.instance;
  }

  /**
   * 配置日志服务
   */
  public configure(config: Partial<LogServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 设置用户标识
   */
  public setUserId(userId: string): void {
    this.userId = userId;
    this.info('用户ID已设置', { userId });
  }

  /**
   * 获取应用名称
   */
  public getApplicationName(): string {
    return this.config.applicationName;
  }

  /**
   * 获取格式化的当前日期时间
   */
  private getNowFormatDate(): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }

  // ================= 日志记录方法 =================
  /**
   * 记录调试日志
   */
  public debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  /**
   * 记录信息日志
   */
  public info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  /**
   * 记录警告日志
   */
  public warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  /**
   * 记录错误日志
   */
  public error(message: string, data?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, data, source);
  }

  /**
   * 记录严重错误日志
   */
  public fatal(message: string, data?: any, source?: string): void {
    this.log(LogLevel.FATAL, message, data, source);
  }

  // ================= 日志管理方法 =================
  /**
   * 获取所有日志
   */
  public getLogs(): LogEntry[] {
    return [...this.logBuffer];
  }

  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logBuffer = [];
    if (this.config.enableStorage) {
      try {
        localStorage.removeItem(ConstantUtil.LOGS_STORAGE_KEY);
      } catch (error) {
        console.error('清空日志存储失败', error);
      }
    }
  }

  /**
   * 手动触发日志上报
   */
  public flushLogs(): Promise<boolean> {
    if (!this.config.enableRemote || !this.config.remoteEndpoint) {
      return Promise.resolve(false);
    }

    return this.sendLogsToServer(this.logBuffer);
  }

  // ================= 内部核心方法 =================
  /**
   * 记录日志的内部方法
   */
  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    // 检查日志级别
    if (!this.shouldLog(level)) {
      return;
    }

    // 自动获取业务调用堆栈来源
    let resolvedSource = source;
    if (!resolvedSource) {
      const err = new Error();
      if (err.stack) {
        const stackLines = err.stack.split('\n');
        // 跳过前两行（Error本身和logService内部调用），取第一个非logService的调用
        const businessLine = stackLines.find(line =>
          line.includes('.ts') && !line.includes('logService')
        );
        if (businessLine) {
          resolvedSource = businessLine.trim();
        }
      }
    }

    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      source: resolvedSource,
      userId: this.userId,
      sessionId: this.sessionId,
    };

    // 控制台输出时也显示来源
    if (this.config.enableConsole) {
      this.writeToConsole({ ...logEntry, source: resolvedSource });
    }

    // 添加到缓冲区
    this.logBuffer.push(logEntry);
    if (this.logBuffer.length > this.config.maxLogEntries) {
      this.logBuffer.shift();
    }

    // 本地存储
    if (this.config.enableStorage) {
      this.saveLogsToStorage();
    }

    // Sentry 上报（仅错误和严重错误）
    if (level === LogLevel.ERROR || level === LogLevel.FATAL) {
      if (data instanceof Error) {
        Sentry.captureException(data);
      } else {
        Sentry.captureMessage(
          `[${this.config.applicationName}][${level}] ${message} ${data ? JSON.stringify(data) : ''}`,
          level === LogLevel.ERROR ? 'error' : 'fatal'
        );
      }
    }

    // 远程上报（如有自定义接口）
    if (this.config.enableRemote && this.config.remoteEndpoint) {
      if (level === LogLevel.ERROR || level === LogLevel.FATAL) {
        this.sendLogsToServer([logEntry]);
      }
    }
  }

  /**
   * 判断是否应该记录该级别的日志
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL];
    return levels.indexOf(level) >= levels.indexOf(this.config.minLevel);
  }

  /**
   * 控制台输出日志
   */
  private writeToConsole(logEntry: LogEntry): void {
    const formattedTime = this.getNowFormatDate();
    const prefix = `[${this.config.applicationName}:${logEntry.level}][${formattedTime}]`;

    switch (logEntry.level) {
      case LogLevel.DEBUG:
        if (envConfig.DEBUG !== false) { // 僅當 DEBUG 為 true 或未設置時才輸出
          console.log(`%c${prefix} ${logEntry.message}`, 'color:green', logEntry.data || '');
        }
        break;
      case LogLevel.INFO:
        console.info(prefix, logEntry.message, logEntry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(prefix, logEntry.message, logEntry.data || '');
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, logEntry.message, logEntry.data || '');
        break;
    }
  }

  /**
   * 保存日志到本地存储
   */
  private saveLogsToStorage(): void {
    try {
      // setLocalStorage(ConstantUtil.LOGS_STORAGE_KEY, this.logBuffer);
    } catch (error) {
      console.error('保存日志到本地存储失败', error);
    }
  }

  /**
   * 从本地存储加载日志
   */
  private loadLogsFromStorage(): void {
    try {
      this.logBuffer = getLocalStorage<LogEntry[]>(ConstantUtil.LOGS_STORAGE_KEY, []);
    } catch (error) {
      console.error('从本地存储加载日志失败', error);
    }
  }

  /**
   * 发送日志到远程服务器
   */
  private sendLogsToServer(logs: LogEntry[]): Promise<boolean> {
    if (!this.config.remoteEndpoint || logs.length === 0) {
      return Promise.resolve(false);
    }

    return fetch(this.config.remoteEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        application: this.config.applicationName,
        sessionId: this.sessionId,
        timestamp: this.getNowFormatDate(),
        logs,
      }),
    })
      .then(response => response.ok)
      .catch(error => {
        console.error('发送日志到服务器失败', error);
        return false;
      });
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// 导出日志服务单例
export const logService = LogService.getInstance();

// 默认导出
export default logService; 