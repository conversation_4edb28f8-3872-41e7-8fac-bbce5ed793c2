import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import { 
  restoreAuthToken, 
  setAccount, 
  logout,
  type AccountResponse 
} from '@/app/slices/authSlice';
import { applyTenantToken } from '@/app/slices/tenantSlice';
import { authService } from '@/services/core/auth';
import { logService } from '@/services/system/logService';

export interface UseAuthInitializationOptions {
  /** 是否自动初始化认证状态 */
  autoInit?: boolean;
  /** 认证初始化完成回调 */
  onAuthInitComplete?: (isAuthenticated: boolean) => void;
}

export interface AuthInitializationState {
  /** 是否正在初始化认证 */
  isInitializing: boolean;
  /** 认证初始化是否完成 */
  isInitialized: boolean;
  /** 用户是否已认证 */
  isAuthenticated: boolean;
  /** 初始化错误 */
  error: Error | null;
}

/**
 * 认证初始化 Hook
 * 处理用户认证状态的初始化和恢复
 */
export function useAuthInitialization(options: UseAuthInitializationOptions = {}) {
  const { autoInit = true, onAuthInitComplete } = options;
  
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const authState = useAppSelector(state => state.auth);
  
  const [state, setState] = useState<AuthInitializationState>({
    isInitializing: false,
    isInitialized: false,
    isAuthenticated: false,
    error: null
  });

  /**
   * 初始化认证状态
   */
  const initializeAuth = async (): Promise<boolean> => {
    if (state.isInitializing) {
      logService.warn('认证正在初始化中，跳过重复调用');
      return state.isAuthenticated;
    }

    setState(prev => ({
      ...prev,
      isInitializing: true,
      error: null
    }));

    try {
      logService.info('开始认证状态初始化');

      // 从本地存储获取认证状态
      const authInitState = authService.getAuthState();
      
      if (!authInitState.isAuthenticated) {
        logService.info('用户未认证，跳过认证初始化');
        setState(prev => ({
          ...prev,
          isInitializing: false,
          isInitialized: true,
          isAuthenticated: false
        }));
        
        onAuthInitComplete?.(false);
        return false;
      }

      // 恢复认证状态到 Redux
      if (authInitState.authToken) {
        dispatch(restoreAuthToken(authInitState.authToken));
      }
      
      if (authInitState.account) {
        dispatch(setAccount(authInitState.account));
      }

      // 验证令牌有效性
      const isTokenValid = await authService.checkAccountToken();
      
      if (!isTokenValid) {
        logService.warn('令牌无效，清除认证状态');
        
        // 清除无效的认证状态
        authService.clearAuth();
        dispatch(logout());
        
        setState(prev => ({
          ...prev,
          isInitializing: false,
          isInitialized: true,
          isAuthenticated: false,
          error: new Error('令牌无效')
        }));
        
        onAuthInitComplete?.(false);
        return false;
      }

      setState(prev => ({
        ...prev,
        isInitializing: false,
        isInitialized: true,
        isAuthenticated: true
      }));

      logService.info('认证状态初始化完成');
      onAuthInitComplete?.(true);
      return true;

    } catch (error) {
      const authError = error as Error;
      logService.error('认证状态初始化失败', { error: authError });

      setState(prev => ({
        ...prev,
        isInitializing: false,
        isInitialized: true,
        isAuthenticated: false,
        error: authError
      }));

      onAuthInitComplete?.(false);
      return false;
    }
  };

  /**
   * 处理登录成功
   */
  const handleLoginSuccess = async (
    accountData: AccountResponse,
    shouldNavigate: boolean = true
  ): Promise<void> => {
    try {
      logService.info('处理登录成功', { accountId: accountData.accountId });

      // 更新 Redux 状态
      dispatch(restoreAuthToken(accountData.tokenId || null));
      dispatch(setAccount(accountData));

      // 调用 authService 处理登录成功逻辑
      const result = await authService.handleLoginSuccess(accountData, navigate);
      
      if (result.shouldNavigate && shouldNavigate) {
        if (result.route) {
          navigate(result.route, { replace: true });
        }
        
        // 如果有租户列表，应用租户令牌
        if (result.tenantList && result.tenantList.length > 0) {
          const firstTenant = result.tenantList[0];
          if (firstTenant.tenantId) {
            try {
              await dispatch(applyTenantToken(firstTenant.tenantId)).unwrap();
              logService.info('已应用租户令牌', { tenantId: firstTenant.tenantId });
            } catch (tenantError) {
              logService.error('应用租户令牌失败', { error: tenantError });
            }
          }
        }
      }

      // 更新认证状态
      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        error: null
      }));

    } catch (error) {
      logService.error('处理登录成功失败', { error: error as Error });
      throw error;
    }
  };

  /**
   * 处理登出
   */
  const handleLogout = async (): Promise<void> => {
    try {
      logService.info('处理用户登出');

      // 调用 authService 登出
      await authService.logout();
      
      // 更新 Redux 状态
      dispatch(logout());

      // 更新本地状态
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        error: null
      }));

      logService.info('用户登出完成');

    } catch (error) {
      logService.error('用户登出失败', { error: error as Error });
      throw error;
    }
  };

  /**
   * 重置认证状态
   */
  const reset = () => {
    setState({
      isInitializing: false,
      isInitialized: false,
      isAuthenticated: false,
      error: null
    });
  };

  // 自动初始化
  useEffect(() => {
    if (autoInit && !state.isInitialized && !state.isInitializing) {
      initializeAuth();
    }
  }, [autoInit]);

  // 监听 Redux 认证状态变化
  useEffect(() => {
    setState(prev => ({
      ...prev,
      isAuthenticated: authState.isAuthenticated
    }));
  }, [authState.isAuthenticated]);

  return {
    ...state,
    initializeAuth,
    handleLoginSuccess,
    handleLogout,
    reset
  };
}

export default useAuthInitialization;
