/**
 * 主题上下文
 * 提供全局的主题管理
 */

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { THEME_COLORS, THEME_FONTS, THEME_SPACING } from '../config/app/theme';

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto';

// 主题状态类型
interface ThemeState {
  mode: ThemeMode;
  colors: typeof THEME_COLORS;
  fonts: typeof THEME_FONTS;
  spacing: typeof THEME_SPACING;
  isDark: boolean;
}

// 上下文类型
interface ThemeContextType extends ThemeState {
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

// 创建上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider组件Props
interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeMode;
}

// 获取系统主题
function getSystemTheme(): 'light' | 'dark' {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
}

// 计算实际主题
function getActualTheme(mode: ThemeMode): 'light' | 'dark' {
  if (mode === 'auto') {
    return getSystemTheme();
  }
  return mode;
}

// Provider组件
export function ThemeProvider({ children, defaultTheme = 'light' }: ThemeProviderProps) {
  const [mode, setMode] = useState<ThemeMode>(defaultTheme);
  const [isDark, setIsDark] = useState(false);

  // 设置主题
  const setTheme = (newMode: ThemeMode) => {
    setMode(newMode);
    localStorage.setItem('theme', newMode);
  };

  // 切换主题
  const toggleTheme = () => {
    const newMode = isDark ? 'light' : 'dark';
    setTheme(newMode);
  };

  // 监听系统主题变化
  useEffect(() => {
    if (mode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        setIsDark(getActualTheme(mode) === 'dark');
      };
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [mode]);

  // 更新isDark状态
  useEffect(() => {
    setIsDark(getActualTheme(mode) === 'dark');
  }, [mode]);

  // 初始化主题
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as ThemeMode;
    if (savedTheme) {
      setMode(savedTheme);
    }
  }, []);

  // 应用主题到document
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
    document.documentElement.style.colorScheme = isDark ? 'dark' : 'light';
  }, [isDark]);

  const value: ThemeContextType = {
    mode,
    colors: THEME_COLORS,
    fonts: THEME_FONTS,
    spacing: THEME_SPACING,
    isDark,
    setTheme,
    toggleTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

// Hook
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
