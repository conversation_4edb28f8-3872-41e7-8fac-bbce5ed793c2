import React from 'react';
import { useTranslation } from 'react-i18next';
import './SidebarPlaceholder.css';
import addIcon from '../../assets/icons/sidebar/add-icon.svg';

interface SidebarPlaceholderProps {
  /** 用户名 */
  userName?: string;
  /** 用户头像ID */
  avatarId?: string | null;
}

/**
 * 侧边栏占位内容组件
 * 在团队数据加载时显示基本的用户信息和操作按钮
 */
export const SidebarPlaceholder: React.FC<SidebarPlaceholderProps> = ({ 
  userName = '',
}) => {
  const { t } = useTranslation();

  return (
    <div className="sidebar-placeholder">
      {/* 用户信息 */}
      <div className="sidebar-placeholder-user">
        <div className="sidebar-placeholder-avatar">
          {userName ? userName.charAt(0).toUpperCase() : 'U'}
        </div>
        <div className="sidebar-placeholder-username">{userName || t('用戶')}</div>
      </div>
      
      {/* 团队标题 */}
      <div className="sidebar-placeholder-title">{t('團隊')}</div>
      
      {/* 基本操作 */}
      <div className="sidebar-placeholder-teams">
        {/* 加入团队按钮 */}
        <div className="sidebar-placeholder-team-item">
          <div className="sidebar-placeholder-team-content">
            <div className="sidebar-placeholder-team-prefix">
              <img src={addIcon} alt="add" className="sidebar-placeholder-add-icon" />
            </div>
            <div className="sidebar-placeholder-team-main">
              <div className="sidebar-placeholder-team-text">{t('加入其他團隊')}</div>
            </div>
          </div>
        </div>
        
        {/* 加载提示 */}
        <div className="sidebar-placeholder-loading">
          <div className="sidebar-placeholder-loading-text">{t('正在加載團隊列表...')}</div>
        </div>
      </div>
    </div>
  );
};

export default SidebarPlaceholder;
