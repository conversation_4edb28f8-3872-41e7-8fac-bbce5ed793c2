import React, { useRef, useEffect, useState, KeyboardEvent, useCallback } from 'react';
import { TextArea } from 'antd-mobile';
import { TextAreaRef } from 'antd-mobile/es/components/text-area';
import './ChatInput.css';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend?: () => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  maxRows?: number;
  autoFocus?: boolean;
  onHeightChange?: (height: number) => void; // 高度變化回調
}

const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSend,
  placeholder = 'Aa',
  disabled = false,
  className = '',
  maxRows = 4,
  autoFocus = false,
  onHeightChange
}) => {
  const textareaRef = useRef<TextAreaRef>(null);
  const [currentHeight, setCurrentHeight] = useState(44); // 默認高度

  // 自動焦點處理
  useEffect(() => {
    if (autoFocus && textareaRef.current?.nativeElement) {
      textareaRef.current.nativeElement.focus();
    }
  }, [autoFocus]);

  // 監聽高度變化
  const observeHeight = useCallback(() => {
    if (textareaRef.current?.nativeElement) {
      const element = textareaRef.current.nativeElement;
      const newHeight = element.scrollHeight;

      if (newHeight !== currentHeight) {
        setCurrentHeight(newHeight);
        onHeightChange?.(newHeight);
      }
    }
  }, [currentHeight, onHeightChange]);

  // 監聽內容變化，調整高度
  useEffect(() => {
    observeHeight();
  }, [value, observeHeight]);

  // 處理鍵盤事件
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: 換行，讓默認行為處理
        return;
      } else {
        // Enter: 發送消息
        e.preventDefault();
        if (value.trim() && onSend) {
          onSend();
        }
      }
    }
  };

  // 處理輸入變化
  const handleChange = (val: string) => {
    onChange(val);
  };

  // 處理焦點事件（簡化版）
  const handleFocus = () => {
    // 可以在這裡添加焦點處理邏輯
  };

  const handleBlur = () => {
    // 可以在這裡添加失焦處理邏輯
  };

  // 獲取焦點的方法（供外部調用）
  const focus = useCallback(() => {
    if (textareaRef.current?.nativeElement) {
      textareaRef.current.nativeElement.focus();
    }
  }, []);

  // 清空內容的方法（供外部調用）
  const clear = useCallback(() => {
    onChange('');
  }, [onChange]);

  // 失去焦點的方法（供外部調用）
  const blur = useCallback(() => {
    if (textareaRef.current?.nativeElement) {
      textareaRef.current.nativeElement.blur();
    }
  }, []);

  // 暴露方法給父組件
  React.useImperativeHandle(textareaRef, () => ({
    focus,
    clear,
    blur,
    nativeElement: textareaRef.current?.nativeElement || null
  }));

  return (
    <div>
    <TextArea
      ref={textareaRef}
      className={`chat-input ${className}`}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onFocus={handleFocus}
      onBlur={handleBlur}
      disabled={disabled}
      autoSize={{
        minRows: 1,
        maxRows: maxRows
      }}
      showCount={false}
    />
    </div>
  );
};

export default ChatInput;
