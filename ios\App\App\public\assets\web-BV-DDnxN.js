import{W as n,L as i,C as r,a as s,B as o}from"./index-BEWO_Yza.js";class h extends n{constructor(){super(...arguments),this._isSupported="BarcodeDetector"in window,this.errorVideoElementMissing="videoElement must be provided.",this.eventBarcodesScanned="barcodesScanned"}async startScan(e){if(!this._isSupported)throw this.createUnavailableException();if(!e?.videoElement)throw new Error(this.errorVideoElementMissing);this.videoElement=e.videoElement,this.stream=await navigator.mediaDevices.getUserMedia({video:{facingMode:{ideal:e?.lensFacing===i.Front?"user":"environment"}},audio:!1}),e.videoElement.srcObject=this.stream,await e.videoElement.play();const t=new BarcodeDetector;this.intervalId=window.setInterval(async()=>{if(!e.videoElement)return;const a=await t.detect(e.videoElement);a.length!==0&&this.handleScannedBarcodes(a)},500)}async stopScan(){if(!this._isSupported)throw this.createUnavailableException();this.intervalId&&(clearInterval(this.intervalId),this.intervalId=void 0),this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=void 0),this.videoElement&&(this.videoElement.srcObject=null,this.videoElement=void 0)}async readBarcodesFromImage(e){throw this.createUnavailableException()}async scan(){throw this.createUnavailableException()}async isSupported(){return{supported:this._isSupported}}async enableTorch(){throw this.createUnavailableException()}async disableTorch(){throw this.createUnavailableException()}async toggleTorch(){throw this.createUnavailableException()}async isTorchEnabled(){throw this.createUnavailableException}async isTorchAvailable(){throw this.createUnavailableException()}async setZoomRatio(e){throw this.createUnavailableException()}async getZoomRatio(){throw this.createUnavailableException()}async getMinZoomRatio(){throw this.createUnavailableException()}async getMaxZoomRatio(){throw this.createUnavailableException()}async openSettings(){throw this.createUnavailableException()}async isGoogleBarcodeScannerModuleAvailable(){throw this.createUnavailableException()}async installGoogleBarcodeScannerModule(){throw this.createUnavailableException()}async checkPermissions(){try{return{camera:(await navigator.permissions.query({name:"camera"})).state}}catch{return{camera:"prompt"}}}async requestPermissions(){try{return(await navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(t=>t.stop()),{camera:"granted"}}catch{return{camera:"denied"}}}createUnavailableException(){return new r("This plugin method is not available on this platform.",s.Unavailable)}handleScannedBarcodes(e){const t={barcodes:e.map(a=>({cornerPoints:[[a.cornerPoints[0].x,a.cornerPoints[0].y],[a.cornerPoints[1].x,a.cornerPoints[1].y],[a.cornerPoints[2].x,a.cornerPoints[2].y],[a.cornerPoints[3].x,a.cornerPoints[3].y]],displayValue:a.rawValue,rawValue:a.rawValue,format:a.format.toUpperCase(),valueType:o.Unknown}))};this.notifyListeners(this.eventBarcodesScanned,t)}}export{h as BarcodeScannerWeb};
