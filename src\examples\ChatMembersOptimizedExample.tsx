import React, { useState } from 'react';
import { Button, Card, Space, List, Tag, Toast } from 'antd-mobile';
import {
  syncChatMemberAndStore,
  getChatMembersFromDB
} from '@/services/core/chat/roomService';
import type { ChatMemberVO } from '@/services/core/chat/roomService.types';
import { roomDao } from '@/services/dao';

/**
 * 聊天室成員優化功能演示組件
 * 展示智能缓存和按需同步功能
 */
export const ChatMembersOptimizedExample: React.FC = () => {
  const [members, setMembers] = useState<ChatMemberVO[]>([]);
  const [loading, setLoading] = useState(false);
  const [roomId] = useState('demo-room-optimized');

  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // 模擬聊天室成員數據
  const mockMembers: ChatMemberVO[] = [
    {
      id: 'member-opt-1',
      memberId: 'user-opt-1',
      roomId,
      type: 'user',
      status: 'active',
      privilege: 'admin',
      mute: false,
      top: true,
      createTime: Date.now(),
      updateTime: Date.now(),
      tenantId: 'demo-tenant'
    },
    {
      id: 'member-opt-2',
      memberId: 'user-opt-2',
      roomId,
      type: 'user',
      status: 'active',
      privilege: 'member',
      mute: false,
      top: false,
      createTime: Date.now(),
      updateTime: Date.now(),
      tenantId: 'demo-tenant'
    }
  ];

  // 直接同步聊天室成員
  const handleDirectSync = async () => {
    setLoading(true);
    addLog('開始直接同步聊天室成員...');

    try {
      const result = await syncChatMemberAndStore({ roomId });

      addLog(`同步完成: ${result.success ? '成功' : '失敗'}`);
      addLog(`成員數量: ${result.data?.items?.length || 0}`);

      // 刷新顯示的成員列表
      const currentMembers = await getChatMembersFromDB(roomId);
      setMembers(currentMembers);

      Toast.show({
        content: `同步${result.success ? '成功' : '失敗'}`,
        position: 'bottom'
      });
    } catch (error) {
      addLog(`同步失敗: ${error}`);
      Toast.show({
        content: '同步失敗',
        position: 'bottom'
      });
    } finally {
      setLoading(false);
    }
  };





  // 添加模擬數據
  const handleAddMockData = async () => {
    setLoading(true);
    addLog('添加模擬成員數據...');
    
    try {
      await roomDao.upsertChatMembers(mockMembers);
      
      // 更新同步時間戳
      await roomDao.updateChatMemberSyncTimestamp(roomId, Date.now());
      
      const currentMembers = await getChatMembersFromDB(roomId);
      setMembers(currentMembers);
      
      addLog(`成功添加 ${mockMembers.length} 個成員`);
      Toast.show({
        content: '模擬數據添加成功',
        position: 'bottom'
      });
    } catch (error) {
      addLog(`添加失敗: ${error}`);
      Toast.show({
        content: '添加失敗',
        position: 'bottom'
      });
    } finally {
      setLoading(false);
    }
  };

  // 清除數據
  const handleClearData = async () => {
    setLoading(true);
    addLog('清除所有數據...');
    
    try {
      // 這裡應該有清除數據的方法，暫時用查詢來模擬
      setMembers([]);
      addLog('數據已清除');
      Toast.show({
        content: '數據已清除',
        position: 'bottom'
      });
    } catch (error) {
      addLog(`清除失敗: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 清空日誌
  const handleClearLogs = () => {
    setLogs([]);
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card title="聊天室成員優化功能演示" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <strong>房間ID:</strong> {roomId}
          </div>
          

        </Space>
      </Card>

      <Card title="操作按鈕" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            block
            color="primary"
            onClick={handleDirectSync}
            loading={loading}
          >
            直接同步成員
          </Button>
          
          <Button 
            block 
            fill="outline" 
            onClick={handleAddMockData}
            loading={loading}
          >
            添加模擬數據
          </Button>
          
          <Button 
            block 
            fill="outline" 
            color="danger" 
            onClick={handleClearData}
            loading={loading}
          >
            清除數據
          </Button>
        </Space>
      </Card>

      <Card title={`成員列表 (${members.length})`} style={{ marginBottom: '16px' }}>
        {members.length > 0 ? (
          <List>
            {members.map((member) => (
              <List.Item
                key={member.id}
                extra={
                  <Space>
                    <Tag color={member.privilege === 'admin' ? 'danger' : 'primary'}>
                      {member.privilege}
                    </Tag>
                    {member.top && <Tag color="success">置頂</Tag>}
                    {member.mute && <Tag color="warning">靜音</Tag>}
                  </Space>
                }
              >
                <div>
                  <div><strong>{member.memberId}</strong></div>
                  <div style={{ fontSize: '12px', color: '#999' }}>
                    {member.type} • {member.status}
                  </div>
                </div>
              </List.Item>
            ))}
          </List>
        ) : (
          <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
            暫無成員數據
          </div>
        )}
      </Card>

      <Card 
        title="操作日誌" 
        extra={
          <Button size="mini" fill="outline" onClick={handleClearLogs}>
            清空
          </Button>
        }
      >
        <div style={{ 
          maxHeight: '200px', 
          overflowY: 'auto', 
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <div key={index} style={{ marginBottom: '4px' }}>
                {log}
              </div>
            ))
          ) : (
            <div style={{ color: '#999' }}>暫無日誌</div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default ChatMembersOptimizedExample;
