var jt=i=>{throw TypeError(i)},Ft=(i,l,h)=>l.has(i)||jt("Cannot "+h),ja=(i,l,h)=>(Ft(i,l,"read from private field"),h?h.call(i):l.get(i)),Fa=(i,l,h)=>l.has(i)?jt("Cannot add the same private member more than once"):l instanceof WeakSet?l.add(i):l.set(i,h),Ra=(i,l,h,f)=>(Ft(i,l,"write to private field"),l.set(i,h),h);const Mr=[["Aztec","M"],["Codabar","L"],["Code39","L"],["Code93","L"],["Code128","L"],["DataBar","L"],["DataBarExpanded","L"],["DataMatrix","M"],["EAN-8","L"],["EAN-13","L"],["ITF","L"],["MaxiCode","M"],["PDF417","M"],["QRCode","M"],["UPC-A","L"],["UPC-E","L"],["MicroQRCode","M"],["rMQRCode","M"],["DXFilmEdge","L"],["DataBarLimited","L"]],jr=Mr.map(([i])=>i),Ba=jr.filter((i,l)=>Mr[l][1]==="L"),ka=jr.filter((i,l)=>Mr[l][1]==="M");function Yr(i){switch(i){case"Linear-Codes":return Ba.reduce((l,h)=>l|Yr(h),0);case"Matrix-Codes":return ka.reduce((l,h)=>l|Yr(h),0);case"Any":return(1<<Mr.length)-1;case"None":return 0;default:return 1<<jr.indexOf(i)}}function La(i){if(i===0)return"None";const l=31-Math.clz32(i);return jr[l]}function Wa(i){return i.reduce((l,h)=>l|Yr(h),0)}const Ua=["LocalAverage","GlobalHistogram","FixedThreshold","BoolCast"];function Va(i){return Ua.indexOf(i)}const It=["Unknown","ASCII","ISO8859_1","ISO8859_2","ISO8859_3","ISO8859_4","ISO8859_5","ISO8859_6","ISO8859_7","ISO8859_8","ISO8859_9","ISO8859_10","ISO8859_11","ISO8859_13","ISO8859_14","ISO8859_15","ISO8859_16","Cp437","Cp1250","Cp1251","Cp1252","Cp1256","Shift_JIS","Big5","GB2312","GB18030","EUC_JP","EUC_KR","UTF16BE","UTF16BE","UTF8","UTF16LE","UTF32BE","UTF32LE","BINARY"];function Ha(i){return i==="UnicodeBig"?It.indexOf("UTF16BE"):It.indexOf(i)}const za=["Text","Binary","Mixed","GS1","ISO15434","UnknownECI"];function Na(i){return za[i]}const Ga=["Ignore","Read","Require"];function qa(i){return Ga.indexOf(i)}const Qa=["Plain","ECI","HRI","Hex","Escaped"];function Xa(i){return Qa.indexOf(i)}const Dr={formats:[],tryHarder:!0,tryRotate:!0,tryInvert:!0,tryDownscale:!0,tryDenoise:!1,binarizer:"LocalAverage",isPure:!1,downscaleFactor:3,downscaleThreshold:500,minLineCount:2,maxNumberOfSymbols:255,tryCode39ExtendedMode:!0,returnErrors:!1,eanAddOnSymbol:"Ignore",textMode:"HRI",characterSet:"Unknown"};function Dt(i){return{...i,formats:Wa(i.formats),binarizer:Va(i.binarizer),eanAddOnSymbol:qa(i.eanAddOnSymbol),textMode:Xa(i.textMode),characterSet:Ha(i.characterSet)}}function Ja(i){return{...i,format:La(i.format),contentType:Na(i.contentType),eccLevel:i.ecLevel}}const Ya={locateFile:(i,l)=>{const h=i.match(/_(.+?)\.wasm$/);return h?`https://fastly.jsdelivr.net/npm/zxing-wasm@2.2.0/dist/${h[1]}/${i}`:l+i}},Jr=new WeakMap;function Ka(i,l){return Object.is(i,l)||Object.keys(i).length===Object.keys(l).length&&Object.keys(i).every(h=>Object.hasOwn(l,h)&&i[h]===l[h])}function Rt(i,{overrides:l,equalityFn:h=Ka,fireImmediately:f=!1}={}){var O;const[x,D]=(O=Jr.get(i))!=null?O:[Ya],F=l??x;let E;if(f){if(D&&(E=h(x,F)))return D;const M=i({...F});return Jr.set(i,[F,M]),M}(E??h(x,F))||Jr.set(i,[F])}async function Za(i,l,h=Dr){const f={...Dr,...h},O=await Rt(i,{fireImmediately:!0});let x,D;if("width"in l&&"height"in l&&"data"in l){const{data:E,data:{byteLength:M},width:N,height:q}=l;D=O._malloc(M),O.HEAPU8.set(E,D),x=O.readBarcodesFromPixmap(D,N,q,Dt(f))}else{let E,M;if("buffer"in l)[E,M]=[l.byteLength,l];else if("byteLength"in l)[E,M]=[l.byteLength,new Uint8Array(l)];else if("size"in l)[E,M]=[l.size,new Uint8Array(await l.arrayBuffer())];else throw new TypeError("Invalid input type");D=O._malloc(E),O.HEAPU8.set(M,D),x=O.readBarcodesFromImage(D,E,Dt(f))}O._free(D);const F=[];for(let E=0;E<x.size();++E)F.push(Ja(x.get(E)));return F}({...Dr,formats:[...Dr.formats]});var Bt=async function(i={}){var l,h,f=i,O,x,D=new Promise((t,r)=>{O=t,x=r}),F=typeof window=="object",E=typeof Bun<"u",M=typeof WorkerGlobalScope<"u";typeof process=="object"&&!((h=process.versions)===null||h===void 0)&&h.node&&process.type!="renderer";var N="./this.program",q,Q="";function ir(t){return f.locateFile?f.locateFile(t,Q):Q+t}var sr,tr;if(F||M||E){try{Q=new URL(".",q).href}catch{}M&&(tr=t=>{var r=new XMLHttpRequest;return r.open("GET",t,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),sr=async t=>{var r=await fetch(t,{credentials:"same-origin"});if(r.ok)return r.arrayBuffer();throw new Error(r.status+" : "+r.url)}}var pr=console.log.bind(console),G=console.error.bind(console),ur,vr,Zr=!1,X,k,yr,er,nr,T,rt,tt;function et(){var t=vr.buffer;X=new Int8Array(t),yr=new Int16Array(t),f.HEAPU8=k=new Uint8Array(t),er=new Uint16Array(t),nr=new Int32Array(t),T=new Uint32Array(t),rt=new Float32Array(t),tt=new Float64Array(t)}function qt(){if(f.preRun)for(typeof f.preRun=="function"&&(f.preRun=[f.preRun]);f.preRun.length;)ie(f.preRun.shift());nt(ot)}function Qt(){P.ya()}function Xt(){if(f.postRun)for(typeof f.postRun=="function"&&(f.postRun=[f.postRun]);f.postRun.length;)oe(f.postRun.shift());nt(at)}var J=0,cr=null;function Jt(t){var r;J++,(r=f.monitorRunDependencies)===null||r===void 0||r.call(f,J)}function Yt(t){var r;if(J--,(r=f.monitorRunDependencies)===null||r===void 0||r.call(f,J),J==0&&cr){var e=cr;cr=null,e()}}function Fr(t){var r;(r=f.onAbort)===null||r===void 0||r.call(f,t),t="Aborted("+t+")",G(t),Zr=!0,t+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(t);throw x(e),e}var mr;function Kt(){return ir("zxing_reader.wasm")}function Zt(t){if(t==mr&&ur)return new Uint8Array(ur);if(tr)return tr(t);throw"both async and sync fetching of the wasm failed"}async function re(t){if(!ur)try{var r=await sr(t);return new Uint8Array(r)}catch{}return Zt(t)}async function te(t,r){try{var e=await re(t),n=await WebAssembly.instantiate(e,r);return n}catch(a){G(`failed to asynchronously prepare wasm: ${a}`),Fr(a)}}async function ee(t,r,e){if(!t&&typeof WebAssembly.instantiateStreaming=="function")try{var n=fetch(r,{credentials:"same-origin"}),a=await WebAssembly.instantiateStreaming(n,e);return a}catch(o){G(`wasm streaming compile failed: ${o}`),G("falling back to ArrayBuffer instantiation")}return te(r,e)}function ne(){return{a:Wn}}async function ae(){function t(o,u){return P=o.exports,vr=P.xa,et(),vt=P.Ba,Yt(),P}Jt();function r(o){return t(o.instance)}var e=ne();if(f.instantiateWasm)return new Promise((o,u)=>{f.instantiateWasm(e,(s,c)=>{o(t(s))})});mr!=null||(mr=Kt());try{var n=await ee(ur,mr,e),a=r(n);return a}catch(o){return x(o),Promise.reject(o)}}var nt=t=>{for(;t.length>0;)t.shift()(f)},at=[],oe=t=>at.push(t),ot=[],ie=t=>ot.push(t),m=t=>zn(t),g=()=>Nn(),gr=[],wr=0,se=t=>{var r=new Rr(t);return r.get_caught()||(r.set_caught(!0),wr--),r.set_rethrown(!1),gr.push(r),qn(t),Vn(t)},U=0,ue=()=>{y(0,0);var t=gr.pop();Gn(t.excPtr),U=0};class Rr{constructor(r){this.excPtr=r,this.ptr=r-24}set_type(r){T[this.ptr+4>>2]=r}get_type(){return T[this.ptr+4>>2]}set_destructor(r){T[this.ptr+8>>2]=r}get_destructor(){return T[this.ptr+8>>2]}set_caught(r){r=r?1:0,X[this.ptr+12]=r}get_caught(){return X[this.ptr+12]!=0}set_rethrown(r){r=r?1:0,X[this.ptr+13]=r}get_rethrown(){return X[this.ptr+13]!=0}init(r,e){this.set_adjusted_ptr(0),this.set_type(r),this.set_destructor(e)}set_adjusted_ptr(r){T[this.ptr+16>>2]=r}get_adjusted_ptr(){return T[this.ptr+16>>2]}}var $r=t=>Hn(t),Br=t=>{var r=U;if(!r)return $r(0),0;var e=new Rr(r);e.set_adjusted_ptr(r);var n=e.get_type();if(!n)return $r(0),r;for(var a of t){if(a===0||a===n)break;var o=e.ptr+16;if(Qn(a,n,o))return $r(a),r}return $r(n),r},ce=()=>Br([]),le=t=>Br([t]),fe=(t,r)=>Br([t,r]),de=()=>{var t=gr.pop();t||Fr("no exception to throw");var r=t.excPtr;throw t.get_rethrown()||(gr.push(t),t.set_rethrown(!0),t.set_caught(!1),wr++),U=r,U},he=(t,r,e)=>{var n=new Rr(t);throw n.init(r,e),U=t,wr++,U},pe=()=>wr,ve=t=>{throw U||(U=t),U},ye=()=>Fr(""),br={},kr=t=>{for(;t.length;){var r=t.pop(),e=t.pop();e(r)}};function lr(t){return this.fromWireType(T[t>>2])}var ar={},Y={},Cr={},me=class extends Error{constructor(t){super(t),this.name="InternalError"}},Tr=t=>{throw new me(t)},K=(t,r,e)=>{t.forEach(s=>Cr[s]=r);function n(s){var c=e(s);c.length!==t.length&&Tr("Mismatched type converter count");for(var d=0;d<t.length;++d)W(t[d],c[d])}var a=new Array(r.length),o=[],u=0;r.forEach((s,c)=>{Y.hasOwnProperty(s)?a[c]=Y[s]:(o.push(s),ar.hasOwnProperty(s)||(ar[s]=[]),ar[s].push(()=>{a[c]=Y[s],++u,u===o.length&&n(a)}))}),o.length===0&&n(a)},ge=t=>{var r=br[t];delete br[t];var e=r.rawConstructor,n=r.rawDestructor,a=r.fields,o=a.map(u=>u.getterReturnType).concat(a.map(u=>u.setterArgumentType));K([t],o,u=>{var s={};return a.forEach((c,d)=>{var p=c.fieldName,v=u[d],$=u[d].optional,b=c.getter,_=c.getterContext,A=u[d+a.length],S=c.setter,I=c.setterContext;s[p]={read:z=>v.fromWireType(b(_,z)),write:(z,R)=>{var B=[];S(I,z,A.toWireType(B,R)),kr(B)},optional:$}}),[{name:r.name,fromWireType:c=>{var d={};for(var p in s)d[p]=s[p].read(c);return n(c),d},toWireType:(c,d)=>{for(var p in s)if(!(p in d)&&!s[p].optional)throw new TypeError(`Missing field: "${p}"`);var v=e();for(p in s)s[p].write(v,d[p]);return c!==null&&c.push(n,v),v},argPackAdvance:V,readValueFromPointer:lr,destructorFunction:n}]})},we=(t,r,e,n,a)=>{},$e=()=>{for(var t=new Array(256),r=0;r<256;++r)t[r]=String.fromCharCode(r);it=t},it,j=t=>{for(var r="",e=t;k[e];)r+=it[k[e++]];return r},fr=class extends Error{constructor(t){super(t),this.name="BindingError"}},C=t=>{throw new fr(t)};function be(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};var n=r.name;if(t||C(`type "${n}" must have a positive integer typeid pointer`),Y.hasOwnProperty(t)){if(e.ignoreDuplicateRegistrations)return;C(`Cannot register type '${n}' twice`)}if(Y[t]=r,delete Cr[t],ar.hasOwnProperty(t)){var a=ar[t];delete ar[t],a.forEach(o=>o())}}function W(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return be(t,r,e)}var V=8,Ce=(t,r,e,n)=>{r=j(r),W(t,{name:r,fromWireType:function(a){return!!a},toWireType:function(a,o){return o?e:n},argPackAdvance:V,readValueFromPointer:function(a){return this.fromWireType(k[a])},destructorFunction:null})},Te=t=>({count:t.count,deleteScheduled:t.deleteScheduled,preservePointerOnDelete:t.preservePointerOnDelete,ptr:t.ptr,ptrType:t.ptrType,smartPtr:t.smartPtr,smartPtrType:t.smartPtrType}),Lr=t=>{function r(e){return e.$$.ptrType.registeredClass.name}C(r(t)+" instance already deleted")},Wr=!1,st=t=>{},Pe=t=>{t.smartPtr?t.smartPtrType.rawDestructor(t.smartPtr):t.ptrType.registeredClass.rawDestructor(t.ptr)},ut=t=>{t.count.value-=1;var r=t.count.value===0;r&&Pe(t)},dr=t=>typeof FinalizationRegistry>"u"?(dr=r=>r,t):(Wr=new FinalizationRegistry(r=>{ut(r.$$)}),dr=r=>{var e=r.$$,n=!!e.smartPtr;if(n){var a={$$:e};Wr.register(r,a,r)}return r},st=r=>Wr.unregister(r),dr(t)),_e=()=>{let t=Pr.prototype;Object.assign(t,{isAliasOf(e){if(!(this instanceof Pr)||!(e instanceof Pr))return!1;var n=this.$$.ptrType.registeredClass,a=this.$$.ptr;e.$$=e.$$;for(var o=e.$$.ptrType.registeredClass,u=e.$$.ptr;n.baseClass;)a=n.upcast(a),n=n.baseClass;for(;o.baseClass;)u=o.upcast(u),o=o.baseClass;return n===o&&a===u},clone(){if(this.$$.ptr||Lr(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=dr(Object.create(Object.getPrototypeOf(this),{$$:{value:Te(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e},delete(){this.$$.ptr||Lr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&C("Object already scheduled for deletion"),st(this),ut(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||Lr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&C("Object already scheduled for deletion"),this.$$.deleteScheduled=!0,this}});const r=Symbol.dispose;r&&(t[r]=t.delete)};function Pr(){}var Ur=(t,r)=>Object.defineProperty(r,"name",{value:t}),ct={},lt=(t,r,e)=>{if(t[r].overloadTable===void 0){var n=t[r];t[r]=function(){for(var a=arguments.length,o=new Array(a),u=0;u<a;u++)o[u]=arguments[u];return t[r].overloadTable.hasOwnProperty(o.length)||C(`Function '${e}' called with an invalid number of arguments (${o.length}) - expects one of (${t[r].overloadTable})!`),t[r].overloadTable[o.length].apply(this,o)},t[r].overloadTable=[],t[r].overloadTable[n.argCount]=n}},ft=(t,r,e)=>{f.hasOwnProperty(t)?((e===void 0||f[t].overloadTable!==void 0&&f[t].overloadTable[e]!==void 0)&&C(`Cannot register public name '${t}' twice`),lt(f,t,t),f[t].overloadTable.hasOwnProperty(e)&&C(`Cannot register multiple overloads of a function with the same number of arguments (${e})!`),f[t].overloadTable[e]=r):(f[t]=r,f[t].argCount=e)},Ee=48,Oe=57,xe=t=>{t=t.replace(/[^a-zA-Z0-9_]/g,"$");var r=t.charCodeAt(0);return r>=Ee&&r<=Oe?`_${t}`:t};function Se(t,r,e,n,a,o,u,s){this.name=t,this.constructor=r,this.instancePrototype=e,this.rawDestructor=n,this.baseClass=a,this.getActualType=o,this.upcast=u,this.downcast=s,this.pureVirtualFunctions=[]}var Vr=(t,r,e)=>{for(;r!==e;)r.upcast||C(`Expected null or instance of ${e.name}, got an instance of ${r.name}`),t=r.upcast(t),r=r.baseClass;return t},Hr=t=>{if(t===null)return"null";var r=typeof t;return r==="object"||r==="array"||r==="function"?t.toString():""+t};function Ae(t,r){if(r===null)return this.isReference&&C(`null is not a valid ${this.name}`),0;r.$$||C(`Cannot pass "${Hr(r)}" as a ${this.name}`),r.$$.ptr||C(`Cannot pass deleted object as a pointer of type ${this.name}`);var e=r.$$.ptrType.registeredClass,n=Vr(r.$$.ptr,e,this.registeredClass);return n}function Ie(t,r){var e;if(r===null)return this.isReference&&C(`null is not a valid ${this.name}`),this.isSmartPointer?(e=this.rawConstructor(),t!==null&&t.push(this.rawDestructor,e),e):0;(!r||!r.$$)&&C(`Cannot pass "${Hr(r)}" as a ${this.name}`),r.$$.ptr||C(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&r.$$.ptrType.isConst&&C(`Cannot convert argument of type ${r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name} to parameter type ${this.name}`);var n=r.$$.ptrType.registeredClass;if(e=Vr(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(r.$$.smartPtr===void 0&&C("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?e=r.$$.smartPtr:C(`Cannot convert argument of type ${r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:e=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)e=r.$$.smartPtr;else{var a=r.clone();e=this.rawShare(e,H.toHandle(()=>a.delete())),t!==null&&t.push(this.rawDestructor,e)}break;default:C("Unsupporting sharing policy")}return e}function De(t,r){if(r===null)return this.isReference&&C(`null is not a valid ${this.name}`),0;r.$$||C(`Cannot pass "${Hr(r)}" as a ${this.name}`),r.$$.ptr||C(`Cannot pass deleted object as a pointer of type ${this.name}`),r.$$.ptrType.isConst&&C(`Cannot convert argument of type ${r.$$.ptrType.name} to parameter type ${this.name}`);var e=r.$$.ptrType.registeredClass,n=Vr(r.$$.ptr,e,this.registeredClass);return n}var dt=(t,r,e)=>{if(r===e)return t;if(e.baseClass===void 0)return null;var n=dt(t,r,e.baseClass);return n===null?null:e.downcast(n)},Me={},je=(t,r)=>{for(r===void 0&&C("ptr should not be undefined");t.baseClass;)r=t.upcast(r),t=t.baseClass;return r},Fe=(t,r)=>(r=je(t,r),Me[r]),_r=(t,r)=>{(!r.ptrType||!r.ptr)&&Tr("makeClassHandle requires ptr and ptrType");var e=!!r.smartPtrType,n=!!r.smartPtr;return e!==n&&Tr("Both smartPtrType and smartPtr must be specified"),r.count={value:1},dr(Object.create(t,{$$:{value:r,writable:!0}}))};function Re(t){var r=this.getPointee(t);if(!r)return this.destructor(t),null;var e=Fe(this.registeredClass,r);if(e!==void 0){if(e.$$.count.value===0)return e.$$.ptr=r,e.$$.smartPtr=t,e.clone();var n=e.clone();return this.destructor(t),n}function a(){return this.isSmartPointer?_r(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:t}):_r(this.registeredClass.instancePrototype,{ptrType:this,ptr:t})}var o=this.registeredClass.getActualType(r),u=ct[o];if(!u)return a.call(this);var s;this.isConst?s=u.constPointerType:s=u.pointerType;var c=dt(r,this.registeredClass,s.registeredClass);return c===null?a.call(this):this.isSmartPointer?_r(s.registeredClass.instancePrototype,{ptrType:s,ptr:c,smartPtrType:this,smartPtr:t}):_r(s.registeredClass.instancePrototype,{ptrType:s,ptr:c})}var Be=()=>{Object.assign(Er.prototype,{getPointee(t){return this.rawGetPointee&&(t=this.rawGetPointee(t)),t},destructor(t){var r;(r=this.rawDestructor)===null||r===void 0||r.call(this,t)},argPackAdvance:V,readValueFromPointer:lr,fromWireType:Re})};function Er(t,r,e,n,a,o,u,s,c,d,p){this.name=t,this.registeredClass=r,this.isReference=e,this.isConst=n,this.isSmartPointer=a,this.pointeeType=o,this.sharingPolicy=u,this.rawGetPointee=s,this.rawConstructor=c,this.rawShare=d,this.rawDestructor=p,!a&&r.baseClass===void 0?n?(this.toWireType=Ae,this.destructorFunction=null):(this.toWireType=De,this.destructorFunction=null):this.toWireType=Ie}var ht=(t,r,e)=>{f.hasOwnProperty(t)||Tr("Replacing nonexistent public symbol"),f[t].overloadTable!==void 0&&e!==void 0?f[t].overloadTable[e]=r:(f[t]=r,f[t].argCount=e)},pt=[],vt,w=t=>{var r=pt[t];return r||(pt[t]=r=vt.get(t)),r},ke=function(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];if(t.includes("j"))return dynCallLegacy(t,r,e);var n=w(r),a=n(...e);return a},Le=function(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return function(){for(var n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return ke(t,r,a,e)}},L=function(t,r){t=j(t);function e(){if(t.includes("j"))return Le(t,r);var a=w(r);return a}var n=e();return typeof n!="function"&&C(`unknown function pointer with signature ${t}: ${r}`),n};class We extends Error{}var yt=t=>{var r=Un(t),e=j(r);return rr(r),e},Or=(t,r)=>{var e=[],n={};function a(o){if(!n[o]&&!Y[o]){if(Cr[o]){Cr[o].forEach(a);return}e.push(o),n[o]=!0}}throw r.forEach(a),new We(`${t}: `+e.map(yt).join([", "]))},Ue=(t,r,e,n,a,o,u,s,c,d,p,v,$)=>{p=j(p),o=L(a,o),s&&(s=L(u,s)),d&&(d=L(c,d)),$=L(v,$);var b=xe(p);ft(b,function(){Or(`Cannot construct ${p} due to unbound types`,[n])}),K([t,r,e],n?[n]:[],_=>{_=_[0];var A,S;n?(A=_.registeredClass,S=A.instancePrototype):S=Pr.prototype;var I=Ur(p,function(){if(Object.getPrototypeOf(this)!==z)throw new fr(`Use 'new' to construct ${p}`);if(R.constructor_body===void 0)throw new fr(`${p} has no accessible constructor`);for(var St=arguments.length,Sr=new Array(St),Ar=0;Ar<St;Ar++)Sr[Ar]=arguments[Ar];var At=R.constructor_body[Sr.length];if(At===void 0)throw new fr(`Tried to invoke ctor of ${p} with invalid number of parameters (${Sr.length}) - expected (${Object.keys(R.constructor_body).toString()}) parameters instead!`);return At.apply(this,Sr)}),z=Object.create(S,{constructor:{value:I}});I.prototype=z;var R=new Se(p,I,z,$,A,o,s,d);if(R.baseClass){var B,xr;(xr=(B=R.baseClass).__derivedClasses)!==null&&xr!==void 0||(B.__derivedClasses=[]),R.baseClass.__derivedClasses.push(R)}var Ma=new Er(p,R,!0,!1,!1),Ot=new Er(p+"*",R,!1,!1,!1),xt=new Er(p+" const*",R,!1,!0,!1);return ct[t]={pointerType:Ot,constPointerType:xt},ht(b,I),[Ma,Ot,xt]})},zr=(t,r)=>{for(var e=[],n=0;n<t;n++)e.push(T[r+n*4>>2]);return e};function Ve(t){for(var r=1;r<t.length;++r)if(t[r]!==null&&t[r].destructorFunction===void 0)return!0;return!1}function Nr(t,r,e,n,a,o){var u=r.length;u<2&&C("argTypes array size mismatch! Must at least get return value and 'this' types!");var s=r[1]!==null&&e!==null,c=Ve(r),d=r[0].name!=="void",p=u-2,v=new Array(p),$=[],b=[],_=function(){b.length=0;var A;$.length=s?2:1,$[0]=a,s&&(A=r[1].toWireType(b,this),$[1]=A);for(var S=0;S<p;++S)v[S]=r[S+2].toWireType(b,S<0||arguments.length<=S?void 0:arguments[S]),$.push(v[S]);var I=n(...$);function z(R){if(c)kr(b);else for(var B=s?1:2;B<r.length;B++){var xr=B===1?A:v[B-2];r[B].destructorFunction!==null&&r[B].destructorFunction(xr)}if(d)return r[0].fromWireType(R)}return z(I)};return Ur(t,_)}var He=(t,r,e,n,a,o)=>{var u=zr(r,e);a=L(n,a),K([],[t],s=>{s=s[0];var c=`constructor ${s.name}`;if(s.registeredClass.constructor_body===void 0&&(s.registeredClass.constructor_body=[]),s.registeredClass.constructor_body[r-1]!==void 0)throw new fr(`Cannot register multiple constructors with identical number of parameters (${r-1}) for class '${s.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return s.registeredClass.constructor_body[r-1]=()=>{Or(`Cannot construct ${s.name} due to unbound types`,u)},K([],u,d=>(d.splice(1,0,null),s.registeredClass.constructor_body[r-1]=Nr(c,d,null,a,o),[])),[]})},mt=t=>{t=t.trim();const r=t.indexOf("(");return r===-1?t:t.slice(0,r)},ze=(t,r,e,n,a,o,u,s,c,d)=>{var p=zr(e,n);r=j(r),r=mt(r),o=L(a,o),K([],[t],v=>{v=v[0];var $=`${v.name}.${r}`;r.startsWith("@@")&&(r=Symbol[r.substring(2)]),s&&v.registeredClass.pureVirtualFunctions.push(r);function b(){Or(`Cannot call ${$} due to unbound types`,p)}var _=v.registeredClass.instancePrototype,A=_[r];return A===void 0||A.overloadTable===void 0&&A.className!==v.name&&A.argCount===e-2?(b.argCount=e-2,b.className=v.name,_[r]=b):(lt(_,r,$),_[r].overloadTable[e-2]=b),K([],p,S=>{var I=Nr($,S,v,o,u);return _[r].overloadTable===void 0?(I.argCount=e-2,_[r]=I):_[r].overloadTable[e-2]=I,[]}),[]})},gt=[],Z=[0,1,,1,null,1,!0,1,!1,1],Gr=t=>{t>9&&--Z[t+1]===0&&(Z[t]=void 0,gt.push(t))},H={toValue:t=>(t||C(`Cannot use deleted val. handle = ${t}`),Z[t]),toHandle:t=>{switch(t){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:{const r=gt.pop()||Z.length;return Z[r]=t,Z[r+1]=1,r}}}},wt={name:"emscripten::val",fromWireType:t=>{var r=H.toValue(t);return Gr(t),r},toWireType:(t,r)=>H.toHandle(r),argPackAdvance:V,readValueFromPointer:lr,destructorFunction:null},Ne=t=>W(t,wt),Ge=(t,r)=>{switch(r){case 4:return function(e){return this.fromWireType(rt[e>>2])};case 8:return function(e){return this.fromWireType(tt[e>>3])};default:throw new TypeError(`invalid float width (${r}): ${t}`)}},qe=(t,r,e)=>{r=j(r),W(t,{name:r,fromWireType:n=>n,toWireType:(n,a)=>a,argPackAdvance:V,readValueFromPointer:Ge(r,e),destructorFunction:null})},Qe=(t,r,e,n,a,o,u,s)=>{var c=zr(r,e);t=j(t),t=mt(t),a=L(n,a),ft(t,function(){Or(`Cannot call ${t} due to unbound types`,c)},r-1),K([],c,d=>{var p=[d[0],null].concat(d.slice(1));return ht(t,Nr(t,p,null,a,o),r-1),[]})},Xe=(t,r,e)=>{switch(r){case 1:return e?n=>X[n]:n=>k[n];case 2:return e?n=>yr[n>>1]:n=>er[n>>1];case 4:return e?n=>nr[n>>2]:n=>T[n>>2];default:throw new TypeError(`invalid integer width (${r}): ${t}`)}},Je=(t,r,e,n,a)=>{r=j(r);const o=n===0;let u=c=>c;if(o){var s=32-8*e;u=c=>c<<s>>>s,a=u(a)}W(t,{name:r,fromWireType:u,toWireType:(c,d)=>d,argPackAdvance:V,readValueFromPointer:Xe(r,e,n!==0),destructorFunction:null})},Ye=(t,r,e)=>{var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],a=n[r];function o(u){var s=T[u>>2],c=T[u+4>>2];return new a(X.buffer,c,s)}e=j(e),W(t,{name:e,fromWireType:o,argPackAdvance:V,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},Ke=Object.assign({optional:!0},wt),Ze=(t,r)=>{W(t,Ke)},rn=(t,r,e,n)=>{if(!(n>0))return 0;for(var a=e,o=e+n-1,u=0;u<t.length;++u){var s=t.charCodeAt(u);if(s>=55296&&s<=57343){var c=t.charCodeAt(++u);s=65536+((s&1023)<<10)|c&1023}if(s<=127){if(e>=o)break;r[e++]=s}else if(s<=2047){if(e+1>=o)break;r[e++]=192|s>>6,r[e++]=128|s&63}else if(s<=65535){if(e+2>=o)break;r[e++]=224|s>>12,r[e++]=128|s>>6&63,r[e++]=128|s&63}else{if(e+3>=o)break;r[e++]=240|s>>18,r[e++]=128|s>>12&63,r[e++]=128|s>>6&63,r[e++]=128|s&63}}return r[e]=0,e-a},or=(t,r,e)=>rn(t,k,r,e),$t=t=>{for(var r=0,e=0;e<t.length;++e){var n=t.charCodeAt(e);n<=127?r++:n<=2047?r+=2:n>=55296&&n<=57343?(r+=4,++e):r+=3}return r},bt=typeof TextDecoder<"u"?new TextDecoder:void 0,Ct=function(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:NaN;for(var n=r+e,a=r;t[a]&&!(a>=n);)++a;if(a-r>16&&t.buffer&&bt)return bt.decode(t.subarray(r,a));for(var o="";r<a;){var u=t[r++];if(!(u&128)){o+=String.fromCharCode(u);continue}var s=t[r++]&63;if((u&224)==192){o+=String.fromCharCode((u&31)<<6|s);continue}var c=t[r++]&63;if((u&240)==224?u=(u&15)<<12|s<<6|c:u=(u&7)<<18|s<<12|c<<6|t[r++]&63,u<65536)o+=String.fromCharCode(u);else{var d=u-65536;o+=String.fromCharCode(55296|d>>10,56320|d&1023)}}return o},tn=(t,r)=>t?Ct(k,t,r):"",en=(t,r)=>{r=j(r),W(t,{name:r,fromWireType(e){for(var n=T[e>>2],a=e+4,o,s,u=a,s=0;s<=n;++s){var c=a+s;if(s==n||k[c]==0){var d=c-u,p=tn(u,d);o===void 0?o=p:(o+="\0",o+=p),u=c+1}}return rr(e),o},toWireType(e,n){n instanceof ArrayBuffer&&(n=new Uint8Array(n));var a,o=typeof n=="string";o||ArrayBuffer.isView(n)&&n.BYTES_PER_ELEMENT==1||C("Cannot pass non-string to std::string"),o?a=$t(n):a=n.length;var u=Et(4+a+1),s=u+4;return T[u>>2]=a,o?or(n,s,a+1):k.set(n,s),e!==null&&e.push(rr,u),u},argPackAdvance:V,readValueFromPointer:lr,destructorFunction(e){rr(e)}})},Tt=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0,nn=(t,r)=>{for(var e=t>>1,n=e+r/2,a=e;!(a>=n)&&er[a];)++a;if(a-e>16&&Tt)return Tt.decode(er.subarray(e,a));for(var o="",u=e;!(u>=n);++u){var s=er[u];if(s==0)break;o+=String.fromCharCode(s)}return o},an=(t,r,e)=>{if(e!=null||(e=2147483647),e<2)return 0;e-=2;for(var n=r,a=e<t.length*2?e/2:t.length,o=0;o<a;++o){var u=t.charCodeAt(o);yr[r>>1]=u,r+=2}return yr[r>>1]=0,r-n},on=t=>t.length*2,sn=(t,r)=>{for(var e=0,n="";!(e>=r/4);){var a=nr[t+e*4>>2];if(a==0)break;if(++e,a>=65536){var o=a-65536;n+=String.fromCharCode(55296|o>>10,56320|o&1023)}else n+=String.fromCharCode(a)}return n},un=(t,r,e)=>{if(e!=null||(e=2147483647),e<4)return 0;for(var n=r,a=n+e-4,o=0;o<t.length;++o){var u=t.charCodeAt(o);if(u>=55296&&u<=57343){var s=t.charCodeAt(++o);u=65536+((u&1023)<<10)|s&1023}if(nr[r>>2]=u,r+=4,r+4>a)break}return nr[r>>2]=0,r-n},cn=t=>{for(var r=0,e=0;e<t.length;++e){var n=t.charCodeAt(e);n>=55296&&n<=57343&&++e,r+=4}return r},ln=(t,r,e)=>{e=j(e);var n,a,o,u;r===2?(n=nn,a=an,u=on,o=s=>er[s>>1]):r===4&&(n=sn,a=un,u=cn,o=s=>T[s>>2]),W(t,{name:e,fromWireType:s=>{for(var c=T[s>>2],d,p=s+4,v=0;v<=c;++v){var $=s+4+v*r;if(v==c||o($)==0){var b=$-p,_=n(p,b);d===void 0?d=_:(d+="\0",d+=_),p=$+r}}return rr(s),d},toWireType:(s,c)=>{typeof c!="string"&&C(`Cannot pass non-string to C++ string type ${e}`);var d=u(c),p=Et(4+d+r);return T[p>>2]=d/r,a(c,p+4,d+r),s!==null&&s.push(rr,p),p},argPackAdvance:V,readValueFromPointer:lr,destructorFunction(s){rr(s)}})},fn=(t,r,e,n,a,o)=>{br[t]={name:j(r),rawConstructor:L(e,n),rawDestructor:L(a,o),fields:[]}},dn=(t,r,e,n,a,o,u,s,c,d)=>{br[t].fields.push({fieldName:j(r),getterReturnType:e,getter:L(n,a),getterContext:o,setterArgumentType:u,setter:L(s,c),setterContext:d})},hn=(t,r)=>{r=j(r),W(t,{isVoid:!0,name:r,argPackAdvance:0,fromWireType:()=>{},toWireType:(e,n)=>{}})},qr=[],pn=(t,r,e,n)=>(t=qr[t],r=H.toValue(r),t(null,r,e,n)),vn={},yn=t=>{var r=vn[t];return r===void 0?j(t):r},Pt=()=>{if(typeof globalThis=="object")return globalThis;function t(r){r.$$$embind_global$$$=r;var e=typeof $$$embind_global$$$=="object"&&r.$$$embind_global$$$==r;return e||delete r.$$$embind_global$$$,e}if(typeof $$$embind_global$$$=="object"||(typeof global=="object"&&t(global)?$$$embind_global$$$=global:typeof self=="object"&&t(self)&&($$$embind_global$$$=self),typeof $$$embind_global$$$=="object"))return $$$embind_global$$$;throw Error("unable to get global object.")},mn=t=>t===0?H.toHandle(Pt()):(t=yn(t),H.toHandle(Pt()[t])),gn=t=>{var r=qr.length;return qr.push(t),r},_t=(t,r)=>{var e=Y[t];return e===void 0&&C(`${r} has unknown type ${yt(t)}`),e},wn=(t,r)=>{for(var e=new Array(t),n=0;n<t;++n)e[n]=_t(T[r+n*4>>2],`parameter ${n}`);return e},$n=(t,r,e)=>{var n=[],a=t.toWireType(n,e);return n.length&&(T[r>>2]=H.toHandle(n)),a},bn=Reflect.construct,Cn=(t,r,e)=>{var n=wn(t,r),a=n.shift();t--;var o=new Array(t),u=(c,d,p,v)=>{for(var $=0,b=0;b<t;++b)o[b]=n[b].readValueFromPointer(v+$),$+=n[b].argPackAdvance;var _=e===1?bn(d,o):d.apply(c,o);return $n(a,p,_)},s=`methodCaller<(${n.map(c=>c.name).join(", ")}) => ${a.name}>`;return gn(Ur(s,u))},Tn=t=>{t>9&&(Z[t+1]+=1)},Pn=t=>{var r=H.toValue(t);kr(r),Gr(t)},_n=(t,r)=>{t=_t(t,"_emval_take_value");var e=t.readValueFromPointer(r);return H.toHandle(e)},En=(t,r,e,n)=>{var a=new Date().getFullYear(),o=new Date(a,0,1),u=new Date(a,6,1),s=o.getTimezoneOffset(),c=u.getTimezoneOffset(),d=Math.max(s,c);T[t>>2]=d*60,nr[r>>2]=+(s!=c);var p=b=>{var _=b>=0?"-":"+",A=Math.abs(b),S=String(Math.floor(A/60)).padStart(2,"0"),I=String(A%60).padStart(2,"0");return`UTC${_}${S}${I}`},v=p(s),$=p(c);c<s?(or(v,e,17),or($,n,17)):(or(v,n,17),or($,e,17))},On=()=>2147483648,xn=(t,r)=>Math.ceil(t/r)*r,Sn=t=>{var r=vr.buffer,e=(t-r.byteLength+65535)/65536|0;try{return vr.grow(e),et(),1}catch{}},An=t=>{var r=k.length;t>>>=0;var e=On();if(t>e)return!1;for(var n=1;n<=4;n*=2){var a=r*(1+.2/n);a=Math.min(a,t+100663296);var o=Math.min(e,xn(Math.max(t,a),65536)),u=Sn(o);if(u)return!0}return!1},Qr={},In=()=>N||"./this.program",hr=()=>{if(!hr.strings){var t=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",r={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:t,_:In()};for(var e in Qr)Qr[e]===void 0?delete r[e]:r[e]=Qr[e];var n=[];for(var e in r)n.push(`${e}=${r[e]}`);hr.strings=n}return hr.strings},Dn=(t,r)=>{var e=0,n=0;for(var a of hr()){var o=r+e;T[t+n>>2]=o,e+=or(a,o,1/0)+1,n+=4}return 0},Mn=(t,r)=>{var e=hr();T[t>>2]=e.length;var n=0;for(var a of e)n+=$t(a)+1;return T[r>>2]=n,0},jn=t=>52;function Fn(t,r,e,n,a){return 70}var Rn=[null,[],[]],Bn=(t,r)=>{var e=Rn[t];r===0||r===10?((t===1?pr:G)(Ct(e)),e.length=0):e.push(r)},kn=(t,r,e,n)=>{for(var a=0,o=0;o<e;o++){var u=T[r>>2],s=T[r+4>>2];r+=8;for(var c=0;c<s;c++)Bn(t,k[u+c]);a+=s}return T[n>>2]=a,0},Ln=t=>t;$e(),_e(),Be(),f.noExitRuntime&&f.noExitRuntime,f.print&&(pr=f.print),f.printErr&&(G=f.printErr),f.wasmBinary&&(ur=f.wasmBinary),f.arguments&&f.arguments,f.thisProgram&&(N=f.thisProgram);var Wn={s:se,w:ue,a:ce,j:le,m:fe,P:de,p:he,ga:pe,d:ve,ba:ye,ua:ge,aa:we,pa:Ce,sa:Ue,ra:He,H:ze,na:Ne,V:qe,W:Qe,x:Je,t:Ye,ta:Ze,oa:en,Q:ln,I:fn,va:dn,qa:hn,da:pn,wa:Gr,D:mn,ma:Cn,X:Tn,Y:Pn,U:_n,ca:En,ha:An,ea:Dn,fa:Mn,ia:jn,_:Fn,S:kn,K:$a,C:Ca,M:Zn,R:xa,q:ya,b:ea,E:wa,ka:Pa,c:na,ja:_a,h:Kn,i:sa,r:fa,O:ga,v:ha,F:va,L:ma,z:Ta,J:Sa,$:Aa,Z:Ia,k:aa,f:Yn,e:ta,g:ra,N:Oa,l:ia,la:ba,o:da,B:ua,u:pa,T:la,A:Ea,n:oa,G:ca,y:Ln},P=await ae();P.ya;var Un=P.za,rr=f._free=P.Aa,Et=f._malloc=P.Ca,Vn=P.Da,y=P.Ea,Hn=P.Fa,zn=P.Ga,Nn=P.Ha,Gn=P.Ia,qn=P.Ja,Qn=P.Ka;f.dynCall_viijii=P.La;var Xn=f.dynCall_iiijj=P.Ma;f.dynCall_jiji=P.Na;var Jn=f.dynCall_jiiii=P.Oa;f.dynCall_iiiiij=P.Pa,f.dynCall_iiiiijj=P.Qa,f.dynCall_iiiiiijj=P.Ra;function Yn(t,r){var e=g();try{w(t)(r)}catch(n){if(m(e),n!==n+0)throw n;y(1,0)}}function Kn(t,r,e,n){var a=g();try{return w(t)(r,e,n)}catch(o){if(m(a),o!==o+0)throw o;y(1,0)}}function Zn(t,r,e,n,a){var o=g();try{return w(t)(r,e,n,a)}catch(u){if(m(o),u!==u+0)throw u;y(1,0)}}function ra(t,r,e,n){var a=g();try{w(t)(r,e,n)}catch(o){if(m(a),o!==o+0)throw o;y(1,0)}}function ta(t,r,e){var n=g();try{w(t)(r,e)}catch(a){if(m(n),a!==a+0)throw a;y(1,0)}}function ea(t,r){var e=g();try{return w(t)(r)}catch(n){if(m(e),n!==n+0)throw n;y(1,0)}}function na(t,r,e){var n=g();try{return w(t)(r,e)}catch(a){if(m(n),a!==a+0)throw a;y(1,0)}}function aa(t){var r=g();try{w(t)()}catch(e){if(m(r),e!==e+0)throw e;y(1,0)}}function oa(t,r,e,n,a,o,u,s,c,d,p){var v=g();try{w(t)(r,e,n,a,o,u,s,c,d,p)}catch($){if(m(v),$!==$+0)throw $;y(1,0)}}function ia(t,r,e,n,a){var o=g();try{w(t)(r,e,n,a)}catch(u){if(m(o),u!==u+0)throw u;y(1,0)}}function sa(t,r,e,n,a){var o=g();try{return w(t)(r,e,n,a)}catch(u){if(m(o),u!==u+0)throw u;y(1,0)}}function ua(t,r,e,n,a,o,u){var s=g();try{w(t)(r,e,n,a,o,u)}catch(c){if(m(s),c!==c+0)throw c;y(1,0)}}function ca(t,r,e,n,a,o,u,s,c,d,p,v,$,b,_,A){var S=g();try{w(t)(r,e,n,a,o,u,s,c,d,p,v,$,b,_,A)}catch(I){if(m(S),I!==I+0)throw I;y(1,0)}}function la(t,r,e,n,a,o,u,s,c){var d=g();try{w(t)(r,e,n,a,o,u,s,c)}catch(p){if(m(d),p!==p+0)throw p;y(1,0)}}function fa(t,r,e,n,a,o){var u=g();try{return w(t)(r,e,n,a,o)}catch(s){if(m(u),s!==s+0)throw s;y(1,0)}}function da(t,r,e,n,a,o){var u=g();try{w(t)(r,e,n,a,o)}catch(s){if(m(u),s!==s+0)throw s;y(1,0)}}function ha(t,r,e,n,a,o,u){var s=g();try{return w(t)(r,e,n,a,o,u)}catch(c){if(m(s),c!==c+0)throw c;y(1,0)}}function pa(t,r,e,n,a,o,u,s){var c=g();try{w(t)(r,e,n,a,o,u,s)}catch(d){if(m(c),d!==d+0)throw d;y(1,0)}}function va(t,r,e,n,a,o,u,s){var c=g();try{return w(t)(r,e,n,a,o,u,s)}catch(d){if(m(c),d!==d+0)throw d;y(1,0)}}function ya(t){var r=g();try{return w(t)()}catch(e){if(m(r),e!==e+0)throw e;y(1,0)}}function ma(t,r,e,n,a,o,u,s,c){var d=g();try{return w(t)(r,e,n,a,o,u,s,c)}catch(p){if(m(d),p!==p+0)throw p;y(1,0)}}function ga(t,r,e,n,a,o,u){var s=g();try{return w(t)(r,e,n,a,o,u)}catch(c){if(m(s),c!==c+0)throw c;y(1,0)}}function wa(t,r,e,n){var a=g();try{return w(t)(r,e,n)}catch(o){if(m(a),o!==o+0)throw o;y(1,0)}}function $a(t,r,e,n){var a=g();try{return w(t)(r,e,n)}catch(o){if(m(a),o!==o+0)throw o;y(1,0)}}function ba(t,r,e,n,a,o,u,s){var c=g();try{w(t)(r,e,n,a,o,u,s)}catch(d){if(m(c),d!==d+0)throw d;y(1,0)}}function Ca(t,r,e,n,a,o){var u=g();try{return w(t)(r,e,n,a,o)}catch(s){if(m(u),s!==s+0)throw s;y(1,0)}}function Ta(t,r,e,n,a,o,u,s,c,d){var p=g();try{return w(t)(r,e,n,a,o,u,s,c,d)}catch(v){if(m(p),v!==v+0)throw v;y(1,0)}}function Pa(t,r,e){var n=g();try{return w(t)(r,e)}catch(a){if(m(n),a!==a+0)throw a;y(1,0)}}function _a(t,r,e,n,a){var o=g();try{return w(t)(r,e,n,a)}catch(u){if(m(o),u!==u+0)throw u;y(1,0)}}function Ea(t,r,e,n,a,o,u,s,c,d){var p=g();try{w(t)(r,e,n,a,o,u,s,c,d)}catch(v){if(m(p),v!==v+0)throw v;y(1,0)}}function Oa(t,r,e,n,a,o,u){var s=g();try{w(t)(r,e,n,a,o,u)}catch(c){if(m(s),c!==c+0)throw c;y(1,0)}}function xa(t,r,e,n){var a=g();try{return w(t)(r,e,n)}catch(o){if(m(a),o!==o+0)throw o;y(1,0)}}function Sa(t,r,e,n,a,o,u,s,c,d,p,v){var $=g();try{return w(t)(r,e,n,a,o,u,s,c,d,p,v)}catch(b){if(m($),b!==b+0)throw b;y(1,0)}}function Aa(t,r,e,n,a,o,u){var s=g();try{return Xn(t,r,e,n,a,o,u)}catch(c){if(m(s),c!==c+0)throw c;y(1,0)}}function Ia(t,r,e,n,a){var o=g();try{return Jn(t,r,e,n,a)}catch(u){if(m(o),u!==u+0)throw u;y(1,0)}}function Xr(){if(J>0){cr=Xr;return}if(qt(),J>0){cr=Xr;return}function t(){var r;f.calledRun=!0,!Zr&&(Qt(),O(f),(r=f.onRuntimeInitialized)===null||r===void 0||r.call(f),Xt())}f.setStatus?(f.setStatus("Running..."),setTimeout(()=>{setTimeout(()=>f.setStatus(""),1),t()},1)):t()}function Da(){if(f.preInit)for(typeof f.preInit=="function"&&(f.preInit=[f.preInit]);f.preInit.length>0;)f.preInit.shift()()}return Da(),Xr(),l=D,l};function ro(i){return Rt(Bt,i)}async function to(i,l){return Za(Bt,i,l)}const kt=[["aztec","Aztec"],["code_128","Code128"],["code_39","Code39"],["code_93","Code93"],["codabar","Codabar"],["databar","DataBar"],["databar_expanded","DataBarExpanded"],["databar_limited","DataBarLimited"],["data_matrix","DataMatrix"],["dx_film_edge","DXFilmEdge"],["ean_13","EAN-13"],["ean_8","EAN-8"],["itf","ITF"],["maxi_code","MaxiCode"],["micro_qr_code","MicroQRCode"],["pdf417","PDF417"],["qr_code","QRCode"],["rm_qr_code","rMQRCode"],["upc_a","UPC-A"],["upc_e","UPC-E"],["linear_codes","Linear-Codes"],["matrix_codes","Matrix-Codes"],["any","Any"]],eo=[...kt,["unknown"]].map(i=>i[0]),Kr=new Map(kt);function no(i){for(const[l,h]of Kr)if(i===h)return l;return"unknown"}function ao(i){if(Lt(i))return{width:i.naturalWidth,height:i.naturalHeight};if(Wt(i))return{width:i.width.baseVal.value,height:i.height.baseVal.value};if(Ut(i))return{width:i.videoWidth,height:i.videoHeight};if(Ht(i))return{width:i.width,height:i.height};if(Nt(i))return{width:i.displayWidth,height:i.displayHeight};if(Vt(i))return{width:i.width,height:i.height};if(zt(i))return{width:i.width,height:i.height};throw new TypeError("The provided value is not of type '(Blob or HTMLCanvasElement or HTMLImageElement or HTMLVideoElement or ImageBitmap or ImageData or OffscreenCanvas or SVGImageElement or VideoFrame)'.")}function Lt(i){var l,h;try{return i instanceof((h=(l=i?.ownerDocument)==null?void 0:l.defaultView)==null?void 0:h.HTMLImageElement)}catch{return!1}}function Wt(i){var l,h;try{return i instanceof((h=(l=i?.ownerDocument)==null?void 0:l.defaultView)==null?void 0:h.SVGImageElement)}catch{return!1}}function Ut(i){var l,h;try{return i instanceof((h=(l=i?.ownerDocument)==null?void 0:l.defaultView)==null?void 0:h.HTMLVideoElement)}catch{return!1}}function Vt(i){var l,h;try{return i instanceof((h=(l=i?.ownerDocument)==null?void 0:l.defaultView)==null?void 0:h.HTMLCanvasElement)}catch{return!1}}function Ht(i){try{return i instanceof ImageBitmap||Object.prototype.toString.call(i)==="[object ImageBitmap]"}catch{return!1}}function zt(i){try{return i instanceof OffscreenCanvas||Object.prototype.toString.call(i)==="[object OffscreenCanvas]"}catch{return!1}}function Nt(i){try{return i instanceof VideoFrame||Object.prototype.toString.call(i)==="[object VideoFrame]"}catch{return!1}}function oo(i){try{return i instanceof Blob||Object.prototype.toString.call(i)==="[object Blob]"}catch{return!1}}function io(i){try{return i instanceof ImageData||Object.prototype.toString.call(i)==="[object ImageData]"}catch{return!1}}function so(i,l){try{const h=new OffscreenCanvas(i,l);if(h.getContext("2d")instanceof OffscreenCanvasRenderingContext2D)return h;throw void 0}catch{const h=document.createElement("canvas");return h.width=i,h.height=l,h}}async function Gt(i){if(Lt(i)&&!await fo(i))throw new DOMException("Failed to load or decode HTMLImageElement.","InvalidStateError");if(Wt(i)&&!await ho(i))throw new DOMException("Failed to load or decode SVGImageElement.","InvalidStateError");if(Nt(i)&&po(i))throw new DOMException("VideoFrame is closed.","InvalidStateError");if(Ut(i)&&(i.readyState===0||i.readyState===1))throw new DOMException("Invalid element or state.","InvalidStateError");if(Ht(i)&&yo(i))throw new DOMException("The image source is detached.","InvalidStateError");const{width:l,height:h}=ao(i);if(l===0||h===0)return null;const f=so(l,h).getContext("2d");f.drawImage(i,0,0);try{return f.getImageData(0,0,l,h)}catch{throw new DOMException("Source would taint origin.","SecurityError")}}async function uo(i){let l;try{l=await createImageBitmap(i)}catch{try{if(globalThis.Image){l=new Image;let h="";try{h=URL.createObjectURL(i),l.src=h,await l.decode()}finally{URL.revokeObjectURL(h)}}else return i}catch{throw new DOMException("Failed to load or decode Blob.","InvalidStateError")}}return await Gt(l)}function co(i){const{width:l,height:h}=i;if(l===0||h===0)return null;const f=i.getContext("2d");try{return f.getImageData(0,0,l,h)}catch{throw new DOMException("Source would taint origin.","SecurityError")}}async function lo(i){if(oo(i))return await uo(i);if(io(i)){if(vo(i))throw new DOMException("The image data has been detached.","InvalidStateError");return i}return Vt(i)||zt(i)?co(i):await Gt(i)}async function fo(i){try{return await i.decode(),!0}catch{return!1}}async function ho(i){var l;try{return await((l=i.decode)==null?void 0:l.call(i)),!0}catch{return!1}}function po(i){return i.format===null}function vo(i){return i.data.buffer.byteLength===0}function yo(i){return i.width===0&&i.height===0}function Mt(i,l){return mo(i)?new DOMException(`${l}: ${i.message}`,i.name):go(i)?new i.constructor(`${l}: ${i.message}`):new Error(`${l}: ${i}`)}function mo(i){return i instanceof DOMException||Object.prototype.toString.call(i)==="[object DOMException]"}function go(i){return i instanceof Error||Object.prototype.toString.call(i)==="[object Error]"}var Ir;class wo{constructor(l={}){Fa(this,Ir);var h;try{const f=(h=l?.formats)==null?void 0:h.filter(O=>O!=="unknown");if(f?.length===0)throw new TypeError("Hint option provided, but is empty.");for(const O of f??[])if(!Kr.has(O))throw new TypeError(`Failed to read the 'formats' property from 'BarcodeDetectorOptions': The provided value '${O}' is not a valid enum value of type BarcodeFormat.`);Ra(this,Ir,f??[]),ro({fireImmediately:!0}).catch(()=>{})}catch(f){throw Mt(f,"Failed to construct 'BarcodeDetector'")}}static async getSupportedFormats(){return eo.filter(l=>l!=="unknown")}async detect(l){try{const h=await lo(l);if(h===null)return[];let f;const O={tryCode39ExtendedMode:!1,textMode:"Plain",formats:ja(this,Ir).map(x=>Kr.get(x))};try{f=await to(h,O)}catch(x){throw console.error(x),new DOMException("Barcode detection service unavailable.","NotSupportedError")}return f.map(x=>{const{topLeft:{x:D,y:F},topRight:{x:E,y:M},bottomLeft:{x:N,y:q},bottomRight:{x:Q,y:ir}}=x.position,sr=Math.min(D,E,N,Q),tr=Math.min(F,M,q,ir),pr=Math.max(D,E,N,Q),G=Math.max(F,M,q,ir);return{boundingBox:new DOMRectReadOnly(sr,tr,pr-sr,G-tr),rawValue:x.text,format:no(x.format),cornerPoints:[{x:D,y:F},{x:E,y:M},{x:Q,y:ir},{x:N,y:q}]}})}catch(h){throw Mt(h,"Failed to execute 'detect' on 'BarcodeDetector'")}}}Ir=new WeakMap;globalThis.BarcodeDetector!=null||(globalThis.BarcodeDetector=wo);export{wo as BarcodeDetector,ro as prepareZXingModule};
