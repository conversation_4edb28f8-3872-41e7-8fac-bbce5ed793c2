# 测试目录

本目录包含项目的所有测试文件，按源码结构组织。

## 目录结构

```
__tests__/
├── components/        # 组件测试
├── pages/            # 页面测试
├── services/         # 服务测试
├── utils/            # 工具函数测试
├── hooks/            # 自定义Hook测试
├── setup.ts          # 测试环境设置
├── utils/            # 测试工具函数
│   └── test-utils.tsx
└── README.md         # 本文件
```

## 测试规范

### 文件命名
- 测试文件以 `.test.ts` 或 `.test.tsx` 结尾
- 测试文件名与源文件名对应
- 例如：`src/services/auth/authService.ts` → `__tests__/services/auth/authService.test.ts`

### 测试结构
- 使用 `describe` 分组相关测试
- 使用 `it` 或 `test` 描述具体测试用例
- 测试描述应该清晰说明测试的功能

### 测试工具
- 使用 `__tests__/utils/test-utils.tsx` 中的工具函数
- 使用 `createMockUser`、`createMockRoom` 等创建测试数据
- 使用自定义的 `render` 函数渲染组件

## 运行测试

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- authService.test.ts

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 监听模式运行测试
npm test -- --watch
```

## 迁移说明

所有测试文件已从 `src/` 目录迁移到此目录，保持相同的目录结构以便于维护。
