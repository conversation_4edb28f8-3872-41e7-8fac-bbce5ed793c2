
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import ChatRoomPage from './ChatRoomPage';

// Mock the navigate function
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

describe('ChatRoomPage', () => {
  it('renders correctly', () => {
    render(
      <BrowserRouter>
        <ChatRoomPage />
      </BrowserRouter>
    );
    
    // Check for main elements
    expect(screen.getByText('我的聊天室')).toBeInTheDocument();
    expect(screen.getByText('04/07')).toBeInTheDocument();
    expect(screen.getByText('Aiwow訊息Aiwow訊息Aiwow訊息Aiwow訊息Aiwow訊息')).toBeInTheDocument();
    expect(screen.getByText('Aile訊息')).toBeInTheDocument();
    expect(screen.getByText('Aile訊息Aile訊息Aile訊息Aile')).toBeInTheDocument();
    
    // Check for timestamps
    expect(screen.getAllByText('13:41')).toHaveLength(1);
    expect(screen.getAllByText('13:45')).toHaveLength(2);
    
    // Check for input
    expect(screen.getByPlaceholderText('Aa')).toBeInTheDocument();
    
    // Check for icons
    const icons = document.querySelectorAll('img');
    expect(icons.length).toBeGreaterThan(5);
  });
}); 