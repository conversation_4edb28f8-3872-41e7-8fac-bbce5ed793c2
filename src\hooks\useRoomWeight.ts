import { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '@/app/store';
import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from '@/types/aile.enum';
import { RoomWeightDto, WeightCalculationResult } from '@/types/room.types';
import {
  calculateRoomWeight,
  recalculateRoomWeight,
  calculateRoomWeights,
  updateRoomP1Weight,
  updateRoomP2Weight,
  updateRoomP3Weight,
  updateRoomActiveTime,
  removeRoomWeight,
  removeRoomWeights,
  clearAllWeights,
  resortRooms,
} from '@/app/slices/roomWeightSlice';
import { RoomWeightScoreUtil } from '@/utils/roomWeightScoreUtil';

/**
 * 聊天室权重管理Hook
 */
export const useRoomWeight = () => {
  const dispatch = useDispatch<AppDispatch>();
  
  // 从Redux状态中获取权重相关数据
  const {
    roomWeights,
    sortedRoomIds,
    isCalculating,
    lastUpdated,
  } = useSelector((state: RootState) => state.roomWeight);

  /**
   * 计算单个聊天室权重
   */
  const calculateWeight = useCallback((roomId: string, roomWeightDto: RoomWeightDto) => {
    dispatch(calculateRoomWeight(roomId, roomWeightDto));
  }, [dispatch]);

  /**
   * 重新计算单个聊天室权重
   */
  const recalculateWeight = useCallback((roomId: string, updatedRoomWeightDto: RoomWeightDto) => {
    dispatch(recalculateRoomWeight(roomId, updatedRoomWeightDto));
  }, [dispatch]);

  /**
   * 批量计算聊天室权重
   */
  const calculateWeights = useCallback((roomWeights: Record<string, RoomWeightDto>) => {
    dispatch(calculateRoomWeights(roomWeights));
  }, [dispatch]);

  /**
   * 更新聊天室P1权重（最高优先级）
   */
  const updateP1Weight = useCallback((roomId: string, p1: P1WeightEnum) => {
    dispatch(updateRoomP1Weight(roomId, p1));
  }, [dispatch]);

  /**
   * 更新聊天室P2权重（次要优先级）
   */
  const updateP2Weight = useCallback((roomId: string, p2: P2WeightEnum) => {
    dispatch(updateRoomP2Weight(roomId, p2));
  }, [dispatch]);

  /**
   * 更新聊天室P3权重（标签优先级）
   */
  const updateP3Weight = useCallback((roomId: string, p3: P3WeightEnum[]) => {
    dispatch(updateRoomP3Weight(roomId, p3));
  }, [dispatch]);

  /**
   * 更新聊天室活跃时间
   */
  const updateActiveTime = useCallback((roomId: string, timestamp?: number) => {
    dispatch(updateRoomActiveTime(roomId, timestamp));
  }, [dispatch]);

  /**
   * 移除单个聊天室权重
   */
  const removeWeight = useCallback((roomId: string) => {
    dispatch(removeRoomWeight(roomId));
  }, [dispatch]);

  /**
   * 批量移除聊天室权重
   */
  const removeWeights = useCallback((roomIds: string[]) => {
    dispatch(removeRoomWeights(roomIds));
  }, [dispatch]);

  /**
   * 清空所有权重
   */
  const clearWeights = useCallback(() => {
    dispatch(clearAllWeights());
  }, [dispatch]);

  /**
   * 重新排序聊天室
   */
  const resortRoomList = useCallback(() => {
    dispatch(resortRooms());
  }, [dispatch]);

  /**
   * 获取指定聊天室的权重信息
   */
  const getRoomWeight = useCallback((roomId: string): WeightCalculationResult | undefined => {
    return roomWeights[roomId];
  }, [roomWeights]);

  /**
   * 获取指定聊天室的Score
   */
  const getRoomScore = useCallback((roomId: string): number | undefined => {
    return roomWeights[roomId]?.score;
  }, [roomWeights]);

  /**
   * 检查聊天室是否有权重信息
   */
  const hasRoomWeight = useCallback((roomId: string): boolean => {
    return roomId in roomWeights;
  }, [roomWeights]);

  /**
   * 获取排序后的聊天室权重列表
   */
  const getSortedRoomWeights = useCallback((): Array<{ roomId: string; weight: WeightCalculationResult }> => {
    return sortedRoomIds.map(roomId => ({
      roomId,
      weight: roomWeights[roomId],
    }));
  }, [sortedRoomIds, roomWeights]);

  /**
   * 比较两个聊天室的权重
   */
  const compareRoomWeights = useCallback((roomId1: string, roomId2: string): number => {
    const score1 = roomWeights[roomId1]?.score ?? 0;
    const score2 = roomWeights[roomId2]?.score ?? 0;
    return RoomWeightScoreUtil.compareScores(score1, score2);
  }, [roomWeights]);

  /**
   * 解析Score为各个组成部分
   */
  const parseScore = useCallback((score: number) => {
    return RoomWeightScoreUtil.parseScore(score);
  }, []);

  /**
   * 将P3位掩码解析为标签列表
   */
  const parseP3ToList = useCallback((p3Mask: number): P3WeightEnum[] => {
    return RoomWeightScoreUtil.parseP3ToList(p3Mask);
  }, []);

  /**
   * 格式化P3标签列表为位掩码
   */
  const formatP3 = useCallback((p3List: P3WeightEnum[]): number => {
    return RoomWeightScoreUtil.formatP3(p3List);
  }, []);

  /**
   * 获取当前时间的P4值
   */
  const getCurrentP4 = useCallback((): number => {
    return RoomWeightScoreUtil.getCurrentP4();
  }, []);

  /**
   * 快速设置聊天室为置顶
   */
  const setRoomTop = useCallback((roomId: string, isTop: boolean) => {
    const currentWeight = roomWeights[roomId];
    if (!currentWeight) return;

    const currentP3List = RoomWeightScoreUtil.parseP3ToList(currentWeight.p3);
    const newP3List = isTop 
      ? [...currentP3List.filter(tag => tag !== P3WeightEnum.Top), P3WeightEnum.Top]
      : currentP3List.filter(tag => tag !== P3WeightEnum.Top);
    
    updateP3Weight(roomId, newP3List.length > 0 ? newP3List : [P3WeightEnum.Default]);
  }, [roomWeights, updateP3Weight]);

  /**
   * 快速设置聊天室为收藏
   */
  const setRoomFavorite = useCallback((roomId: string, isFavorite: boolean) => {
    const currentWeight = roomWeights[roomId];
    if (!currentWeight) return;

    const currentP3List = RoomWeightScoreUtil.parseP3ToList(currentWeight.p3);
    const newP3List = isFavorite 
      ? [...currentP3List.filter(tag => tag !== P3WeightEnum.Favorite), P3WeightEnum.Favorite]
      : currentP3List.filter(tag => tag !== P3WeightEnum.Favorite);
    
    updateP3Weight(roomId, newP3List.length > 0 ? newP3List : [P3WeightEnum.Default]);
  }, [roomWeights, updateP3Weight]);

  /**
   * 快速设置聊天室未读状态
   */
  const setRoomUnread = useCallback((roomId: string, isUnread: boolean) => {
    updateP2Weight(roomId, isUnread ? P2WeightEnum.Unread : P2WeightEnum.Default);
  }, [updateP2Weight]);

  return {
    // 状态
    roomWeights,
    sortedRoomIds,
    isCalculating,
    lastUpdated,
    
    // 基础操作
    calculateWeight,
    recalculateWeight,
    calculateWeights,
    removeWeight,
    removeWeights,
    clearWeights,
    resortRoomList,
    
    // 权重更新
    updateP1Weight,
    updateP2Weight,
    updateP3Weight,
    updateActiveTime,
    
    // 查询操作
    getRoomWeight,
    getRoomScore,
    hasRoomWeight,
    getSortedRoomWeights,
    compareRoomWeights,
    
    // 工具方法
    parseScore,
    parseP3ToList,
    formatP3,
    getCurrentP4,
    
    // 快捷操作
    setRoomTop,
    setRoomFavorite,
    setRoomUnread,
  };
};
