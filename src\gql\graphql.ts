/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  JSON: { input: any; output: any; }
  /** The `Upload` scalar type represents a file upload. */
  Upload: { input: any; output: any; }
};

export type AccountProfileData = {
  __typename?: 'AccountProfileData';
  accountId?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  mobile?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
};

export type AccountProfileResponse = {
  __typename?: 'AccountProfileResponse';
  code: Scalars['String']['output'];
  data?: Maybe<AccountProfileData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export type AvatarBase64Response = {
  __typename?: 'AvatarBase64Response';
  code: Scalars['String']['output'];
  data?: Maybe<Scalars['String']['output']>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type BasePaginationInput = {
  direction: SortDirection;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy: Scalars['String']['input'];
  pageIndex: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  refreshTime?: InputMaybe<Scalars['String']['input']>;
};

export type BasePaginationResponse = {
  __typename?: 'BasePaginationResponse';
  count: Scalars['Int']['output'];
  hasNextPage: Scalars['Boolean']['output'];
  items: Array<Scalars['JSON']['output']>;
  refreshTime: Scalars['Int']['output'];
};

export type BetweenMessageRequest = {
  maxSequence: Scalars['Int']['input'];
  minSequence: Scalars['Int']['input'];
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  roomId: Scalars['String']['input'];
};

export enum BloodType {
  A = 'A',
  Ab = 'AB',
  B = 'B',
  O = 'O'
}

export enum Channel {
  Admin = 'Admin',
  Ai = 'Ai',
  Aile = 'Aile',
  AileLite = 'AileLite',
  Aiwow = 'Aiwow',
  Anonymous = 'Anonymous',
  Cbm = 'Cbm',
  Facebook = 'Facebook',
  Google = 'Google',
  Instagram = 'Instagram',
  Internal = 'Internal',
  Line = 'Line',
  Webchat = 'Webchat'
}

export type CheckLoginOtpResponse = {
  __typename?: 'CheckLoginOtpResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type CreateInvitationRequest = {
  serviceNumberId?: InputMaybe<Scalars['String']['input']>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  type?: InvitationType;
};

export type CreateInvitationResponse = {
  __typename?: 'CreateInvitationResponse';
  code: Scalars['String']['output'];
  data?: Maybe<InvitationData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type CreatePersonalTenantInput = {
  file?: InputMaybe<Scalars['Upload']['input']>;
  name: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreatePersonalTenantResponse = {
  __typename?: 'CreatePersonalTenantResponse';
  code: Scalars['String']['output'];
  data?: Maybe<TenantInfo>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export type DeviceCheckRequestInput = {
  onceToken: Scalars['String']['input'];
};

export type EmployeeProfile = {
  __typename?: 'EmployeeProfile';
  accountId: Scalars['String']['output'];
  age?: Maybe<Scalars['Int']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  birthday?: Maybe<Scalars['Float']['output']>;
  channel: Channel;
  createTime: Scalars['Float']['output'];
  gender?: Maybe<Gender>;
  homePagePicId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isBindAile?: Maybe<Scalars['Boolean']['output']>;
  isCollectInfo?: Maybe<Scalars['Boolean']['output']>;
  isJoinAile?: Maybe<Scalars['Boolean']['output']>;
  joinType: JoinType;
  mood?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  openId?: Maybe<Scalars['String']['output']>;
  personRoomId: Scalars['String']['output'];
  status: Status;
  tenantId: Scalars['String']['output'];
  updateTime: Scalars['Float']['output'];
};

export type EmployeeProfileResponse = {
  __typename?: 'EmployeeProfileResponse';
  code: Scalars['String']['output'];
  data?: Maybe<EmployeeProfile>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export enum Gender {
  Female = 'Female',
  Male = 'Male',
  Other = 'Other',
  Unknown = 'Unknown'
}

export type GetAvatarBase64Input = {
  avatarId: Scalars['String']['input'];
  size: Scalars['String']['input'];
};

export type InvitationData = {
  __typename?: 'InvitationData';
  code?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
  type?: Maybe<InvitationType>;
};

export enum InvitationType {
  JoinServiceNumber = 'JoinServiceNumber',
  JoinTenant = 'JoinTenant'
}

export enum JoinType {
  Guarantor = 'Guarantor',
  Invitation = 'Invitation'
}

export enum Language {
  EnUs = 'EN_US',
  ZhCn = 'ZH_CN',
  ZhTw = 'ZH_TW'
}

export type LoginData = {
  __typename?: 'LoginData';
  accountId: Scalars['String']['output'];
  accountType: Scalars['String']['output'];
  countryCode?: Maybe<Scalars['String']['output']>;
  currentEmployeeInfo?: Maybe<EmployeeProfile>;
  currentTenantInfo?: Maybe<TenantInfo>;
  isInitial: Scalars['Boolean']['output'];
  isMute?: Maybe<Scalars['Boolean']['output']>;
  lastTenantId?: Maybe<Scalars['String']['output']>;
  loginStatus?: Maybe<Scalars['String']['output']>;
  loginType: Scalars['String']['output'];
  mobile?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onlineId?: Maybe<Scalars['String']['output']>;
  personRoomId: Scalars['String']['output'];
  systemAccountId: Scalars['String']['output'];
  systemRoomId?: Maybe<Scalars['String']['output']>;
  tenantRelations?: Maybe<Array<TenantRelation>>;
  tokenId: Scalars['String']['output'];
};

export type LoginRequestInput = {
  accountId?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  checkCode?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  loginType: Scalars['String']['input'];
  mobile?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onceToken?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  scopeId?: InputMaybe<Scalars['String']['input']>;
  thirdChannel?: InputMaybe<Channel>;
  useNonMobile?: InputMaybe<Scalars['Boolean']['input']>;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<LoginData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type LoginWithOtpRequest = {
  checkCode: Scalars['String']['input'];
  onceToken: Scalars['String']['input'];
};

export type LoginWithThirdRequest = {
  scopeId: Scalars['String']['input'];
  thirdChannel: Channel;
};

export enum MaritalStatus {
  Divorced = 'Divorced',
  Married = 'Married',
  Single = 'Single',
  Widowed = 'Widowed'
}

export type Message = {
  __typename?: 'Message';
  accountId: Scalars['String']['output'];
  appointChannel?: Maybe<Scalars['String']['output']>;
  body: Scalars['String']['output'];
  channel: Scalars['String']['output'];
  channelMessageId?: Maybe<Scalars['String']['output']>;
  content: Scalars['String']['output'];
  excludeMemberIds?: Maybe<Scalars['String']['output']>;
  flag?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  nearMessageId?: Maybe<Scalars['String']['output']>;
  osType?: Maybe<Scalars['String']['output']>;
  recipientAccountId?: Maybe<Scalars['String']['output']>;
  recipientId: Scalars['String']['output'];
  roomId: Scalars['String']['output'];
  sendTime: Scalars['Float']['output'];
  senderId: Scalars['String']['output'];
  senderName?: Maybe<Scalars['String']['output']>;
  sequence: Scalars['Int']['output'];
  sessionId?: Maybe<Scalars['String']['output']>;
  sourceType: Scalars['String']['output'];
  tag?: Maybe<Scalars['String']['output']>;
  tenantId: Scalars['String']['output'];
  themeId?: Maybe<Scalars['String']['output']>;
  time: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type MessageListData = {
  __typename?: 'MessageListData';
  count: Scalars['Int']['output'];
  hasNextPage: Scalars['Boolean']['output'];
  items: Array<Maybe<Message>>;
  lastSequence: Scalars['Int']['output'];
};

export type MessageListResponse = {
  __typename?: 'MessageListResponse';
  code: Scalars['String']['output'];
  data?: Maybe<MessageListData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type MessageRequest = {
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  roomId: Scalars['String']['input'];
  sequence: Scalars['Int']['input'];
};

export type Mutation = {
  __typename?: 'Mutation';
  _empty?: Maybe<Scalars['String']['output']>;
  checkLoginOtp: CheckLoginOtpResponse;
  createPersonalTenant: CreatePersonalTenantResponse;
  deviceCheck: LoginResponse;
  login: LoginResponse;
  loginWithOtp: LoginResponse;
  loginWithThird: LoginResponse;
  requestLoginOtp: OtpResponse;
  roomItem: RoomItemResponse;
  switchTenant: SwitchTenantResponse;
  updateAccountProfile: AccountProfileResponse;
};


export type MutationCheckLoginOtpArgs = {
  request?: InputMaybe<CheckLoginOtpRequestInput>;
};


export type MutationCreatePersonalTenantArgs = {
  request: CreatePersonalTenantInput;
};


export type MutationDeviceCheckArgs = {
  request?: InputMaybe<DeviceCheckRequestInput>;
};


export type MutationLoginArgs = {
  request?: InputMaybe<LoginRequestInput>;
};


export type MutationLoginWithOtpArgs = {
  request: LoginWithOtpRequest;
};


export type MutationLoginWithThirdArgs = {
  request: LoginWithThirdRequest;
};


export type MutationRequestLoginOtpArgs = {
  request?: InputMaybe<OtpInput>;
};


export type MutationRoomItemArgs = {
  request: RoomItemRequest;
};


export type MutationSwitchTenantArgs = {
  tenantId: Scalars['String']['input'];
};


export type MutationUpdateAccountProfileArgs = {
  input: UpdateAccountProfileInput;
};

export type OptionalPaginationInput = {
  direction?: InputMaybe<SortDirection>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageIndex?: InputMaybe<Scalars['Int']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime?: InputMaybe<Scalars['String']['input']>;
};

export type OtpData = {
  __typename?: 'OtpData';
  onceToken: Scalars['String']['output'];
  validSecond: Scalars['Int']['output'];
};

export type OtpInput = {
  countryCode: Scalars['String']['input'];
  mobile: Scalars['String']['input'];
};

export type OtpResponse = {
  __typename?: 'OtpResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<OtpData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type Query = {
  __typename?: 'Query';
  _empty?: Maybe<Scalars['String']['output']>;
  createInvitation: CreateInvitationResponse;
  employeeProfile: EmployeeProfileResponse;
  getAvatarBase64: AvatarBase64Response;
  getBetweenSquenceMessages: MessageListResponse;
  getLessThanSquenceMessages: MessageListResponse;
  getMoreThanSquenceMessages: MessageListResponse;
  tenantContactDetail: TenantContactDetailResponse;
  tenantContactList: TenantContactListResponse;
  tenantRelations: TenantRelationsResponse;
  verifyInvitationCode: VerifyInvitationCodeResponse;
};


export type QueryCreateInvitationArgs = {
  request: CreateInvitationRequest;
};


export type QueryGetAvatarBase64Args = {
  request: GetAvatarBase64Input;
};


export type QueryGetBetweenSquenceMessagesArgs = {
  request?: InputMaybe<BetweenMessageRequest>;
};


export type QueryGetLessThanSquenceMessagesArgs = {
  request?: InputMaybe<MessageRequest>;
};


export type QueryGetMoreThanSquenceMessagesArgs = {
  request?: InputMaybe<MessageRequest>;
};


export type QueryTenantContactDetailArgs = {
  request: TenantContactDetailRequest;
};


export type QueryTenantContactListArgs = {
  request: TenantContactListRequest;
};


export type QueryTenantRelationsArgs = {
  request?: InputMaybe<TenantRelationsRequest>;
};


export type QueryVerifyInvitationCodeArgs = {
  request: VerifyInvitationCodeRequest;
};

export type RoomData = {
  __typename?: 'RoomData';
  accountId?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  businessDescription?: Maybe<Scalars['String']['output']>;
  businessId?: Maybe<Scalars['String']['output']>;
  businessName?: Maybe<Scalars['String']['output']>;
  businessStatus?: Maybe<Scalars['String']['output']>;
  homePagePicId?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isCustomName?: Maybe<Scalars['Boolean']['output']>;
  isExternal?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ownerId?: Maybe<Scalars['String']['output']>;
  serviceNumberId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type RoomItemRequest = {
  roomId: Scalars['String']['input'];
};

export type RoomItemResponse = {
  __typename?: 'RoomItemResponse';
  code: Scalars['String']['output'];
  data?: Maybe<RoomData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type SendMessageRequest = {
  content: Scalars['String']['input'];
  messageType: Scalars['String']['input'];
  roomId: Scalars['String']['input'];
};

export type SendMessageResponse = {
  __typename?: 'SendMessageResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Message>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type ServiceNumberSimpleData = {
  __typename?: 'ServiceNumberSimpleData';
  accountId: Scalars['String']['output'];
  avatarId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  ownerId: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export enum SortDirection {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum Status {
  Audit = 'Audit',
  Delete = 'Delete',
  Disable = 'Disable',
  Enable = 'Enable',
  New = 'New'
}

export type Subscription = {
  __typename?: 'Subscription';
  message?: Maybe<Message>;
};

export type SwitchTenantResponse = {
  __typename?: 'SwitchTenantResponse';
  code: Scalars['String']['output'];
  data?: Maybe<TenantInfo>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type TenantContact = {
  __typename?: 'TenantContact';
  accountId?: Maybe<Scalars['String']['output']>;
  age?: Maybe<Scalars['Int']['output']>;
  alias?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  birthday?: Maybe<Scalars['String']['output']>;
  bloodType?: Maybe<BloodType>;
  businessCardId?: Maybe<Scalars['String']['output']>;
  channel?: Maybe<Channel>;
  company?: Maybe<Scalars['String']['output']>;
  companyAddress?: Maybe<Scalars['String']['output']>;
  companyDepartment?: Maybe<Scalars['String']['output']>;
  companyDuty?: Maybe<Scalars['String']['output']>;
  companyEmail?: Maybe<Scalars['String']['output']>;
  companyPhone?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['Float']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Gender>;
  id?: Maybe<Scalars['String']['output']>;
  interests?: Maybe<Scalars['String']['output']>;
  isAileCompany?: Maybe<Scalars['Boolean']['output']>;
  isBindAile?: Maybe<Scalars['Boolean']['output']>;
  isCollectInfo?: Maybe<Scalars['Boolean']['output']>;
  isJoinAile?: Maybe<Scalars['Boolean']['output']>;
  languages?: Maybe<Array<Language>>;
  maritalStatus?: Maybe<MaritalStatus>;
  name?: Maybe<Scalars['String']['output']>;
  openId?: Maybe<Scalars['String']['output']>;
  parentAddressBookId?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Status>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
};

export type TenantContactDetailRequest = {
  id: Scalars['String']['input'];
};

export type TenantContactDetailResponse = {
  __typename?: 'TenantContactDetailResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<TenantContact>;
  msg?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type TenantContactListRequest = {
  direction?: InputMaybe<Scalars['String']['input']>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime: Scalars['Float']['input'];
  serviceNumberId?: InputMaybe<Scalars['String']['input']>;
};

export type TenantContactListResponse = {
  __typename?: 'TenantContactListResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<TenantContacts>;
  msg?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type TenantContacts = {
  __typename?: 'TenantContacts';
  count?: Maybe<Scalars['Int']['output']>;
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  items?: Maybe<Array<TenantContact>>;
  refreshTime?: Maybe<Scalars['Float']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type TenantInfo = {
  __typename?: 'TenantInfo';
  accountId: Scalars['String']['output'];
  address?: Maybe<Scalars['String']['output']>;
  avatarId: Scalars['String']['output'];
  certificateFailReason?: Maybe<Scalars['String']['output']>;
  certificateFileId?: Maybe<Scalars['String']['output']>;
  certificateStatus?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  createTime: Scalars['Float']['output'];
  description?: Maybe<Scalars['String']['output']>;
  employeeInfo?: Maybe<EmployeeProfile>;
  endTime?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  industrySub?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  representativeNumber?: Maybe<Scalars['String']['output']>;
  scale?: Maybe<TenantScale>;
  shortName: Scalars['String']['output'];
  startTime?: Maybe<Scalars['Float']['output']>;
  switchTime: Scalars['Float']['output'];
  type: TenantType;
  unifiedNumber?: Maybe<Scalars['String']['output']>;
  updateTime: Scalars['Float']['output'];
  upgrade?: Maybe<Scalars['String']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type TenantRelation = {
  __typename?: 'TenantRelation';
  accountId: Scalars['String']['output'];
  avatarId: Scalars['String']['output'];
  bossServiceNumber: ServiceNumberSimpleData;
  code: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  industrySub?: Maybe<Scalars['String']['output']>;
  isLastTenant?: Maybe<Scalars['Boolean']['output']>;
  joinTime: Scalars['Float']['output'];
  manageServiceNumber: ServiceNumberSimpleData;
  manageServiceNumberId: Scalars['String']['output'];
  name: Scalars['String']['output'];
  officialServiceNumber: ServiceNumberSimpleData;
  officialServiceNumberId: Scalars['String']['output'];
  openId: Scalars['String']['output'];
  relationId: Scalars['String']['output'];
  scale?: Maybe<TenantScale>;
  shortName: Scalars['String']['output'];
  type: TenantType;
  unReadCount: Scalars['Int']['output'];
};

export type TenantRelationsRequest = {
  accountId?: InputMaybe<Scalars['String']['input']>;
  channel?: InputMaybe<Channel>;
  direction?: InputMaybe<SortDirection>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageIndex?: InputMaybe<Scalars['Int']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime?: InputMaybe<Scalars['String']['input']>;
};

export type TenantRelationsResponse = {
  __typename?: 'TenantRelationsResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Array<TenantRelation>>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export enum TenantScale {
  Enterprise = 'Enterprise',
  Middle = 'Middle',
  Small = 'Small'
}

export enum TenantType {
  Common = 'Common',
  Official = 'Official',
  Person = 'Person',
  Public = 'Public',
  Service = 'Service'
}

export type UpdateAccountProfileInput = {
  file?: InputMaybe<Scalars['Upload']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type VerifyInvitationCodeRequest = {
  code: Scalars['String']['input'];
};

export type VerifyInvitationCodeResponse = {
  __typename?: 'VerifyInvitationCodeResponse';
  code: Scalars['String']['output'];
  data: Scalars['Boolean']['output'];
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type CheckLoginOtpRequestInput = {
  checkCode: Scalars['String']['input'];
  onceToken: Scalars['String']['input'];
};

export type RequestLoginOtpMutationVariables = Exact<{
  requestLoginOtpRequest2?: InputMaybe<OtpInput>;
}>;


export type RequestLoginOtpMutation = { __typename?: 'Mutation', requestLoginOtp: { __typename?: 'OtpResponse', code?: string | null, status: number, msg: string, success: boolean, timeCost?: number | null, data?: { __typename?: 'OtpData', onceToken: string, validSecond: number } | null } };


export const RequestLoginOtpDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RequestLoginOtp"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"requestLoginOtpRequest2"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"OtpInput"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"requestLoginOtp"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"request"},"value":{"kind":"Variable","name":{"kind":"Name","value":"requestLoginOtpRequest2"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"onceToken"}},{"kind":"Field","name":{"kind":"Name","value":"validSecond"}}]}},{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"msg"}},{"kind":"Field","name":{"kind":"Name","value":"success"}},{"kind":"Field","name":{"kind":"Name","value":"timeCost"}}]}}]}}]} as unknown as DocumentNode<RequestLoginOtpMutation, RequestLoginOtpMutationVariables>;