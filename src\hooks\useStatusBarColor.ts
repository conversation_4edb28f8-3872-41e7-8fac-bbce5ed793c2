/**
 * 状态栏颜色管理Hook
 * 自动根据页面内容调整状态栏颜色
 */

import { useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { statusBarService } from '../services/platform';
import { PageColorUtils } from '../config/app/pageColors';
import { useAppSelector } from '../app/hooks';

interface UseStatusBarColorOptions {
  /**
   * 自定义颜色，优先级最高
   */
  customColor?: string;
  
  /**
   * 是否自动根据路径设置颜色
   */
  autoDetect?: boolean;
  
  /**
   * 是否自动检测文字颜色样式
   */
  autoDetectStyle?: boolean;
  
  /**
   * 延迟设置时间（毫秒）
   */
  delay?: number;
  
  /**
   * 是否在组件卸载时重置颜色
   */
  resetOnUnmount?: boolean;
}

export const useStatusBarColor = (options: UseStatusBarColorOptions = {}) => {
  const {
    customColor,
    autoDetect = true,
    autoDetectStyle = true,
    delay = 0,
    resetOnUnmount = false
  } = options;
  
  const location = useLocation();
  const authState = useAppSelector(state => state.auth);
  const isAdmin = authState.account?.type === 'owner' || false;
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentColorRef = useRef<string>('');
  
  /**
   * 设置状态栏颜色
   */
  const setStatusBarColor = useCallback(async (color: string) => {
    if (!color || currentColorRef.current === color) {
      return;
    }
    
    try {
      const normalizedColor = PageColorUtils.normalizeColor(color);
      await statusBarService.setPageBackgroundColor(normalizedColor, autoDetectStyle);
      currentColorRef.current = normalizedColor;
      
      console.log(`StatusBar: Color updated to ${normalizedColor} for path ${location.pathname}`);
    } catch (error) {
      console.error('Failed to set status bar color:', error);
    }
  }, [autoDetectStyle, location.pathname]);
  
  /**
   * 获取当前页面应该使用的颜色
   */
  const getCurrentPageColor = useCallback(() => {
    // 优先使用自定义颜色
    if (customColor) {
      return customColor;
    }
    
    // 自动检测页面颜色
    if (autoDetect) {
      return PageColorUtils.getColorByPath(location.pathname, isAdmin);
    }
    
    // 默认颜色
    return '#FFFFFF';
  }, [customColor, autoDetect, location.pathname, isAdmin]);
  
  /**
   * 更新状态栏颜色
   */
  const updateStatusBarColor = useCallback(() => {
    const targetColor = getCurrentPageColor();
    
    if (delay > 0) {
      // 清除之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // 延迟设置
      timeoutRef.current = setTimeout(() => {
        setStatusBarColor(targetColor);
      }, delay);
    } else {
      // 立即设置
      setStatusBarColor(targetColor);
    }
  }, [getCurrentPageColor, setStatusBarColor, delay]);
  
  /**
   * 手动设置颜色
   */
  const setColor = useCallback((color: string) => {
    setStatusBarColor(color);
  }, [setStatusBarColor]);
  
  /**
   * 重置为默认颜色
   */
  const resetColor = useCallback(() => {
    setStatusBarColor('#FFFFFF');
  }, [setStatusBarColor]);
  
  /**
   * 获取当前颜色
   */
  const getCurrentColor = useCallback(() => {
    return currentColorRef.current || statusBarService.getCurrentBackgroundColor();
  }, []);
  
  // 监听路径变化和用户身份变化
  useEffect(() => {
    updateStatusBarColor();
  }, [updateStatusBarColor]);
  
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 清除定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // 重置颜色（如果需要）
      if (resetOnUnmount) {
        statusBarService.resetToDefault();
      }
    };
  }, [resetOnUnmount]);
  
  return {
    /**
     * 手动设置状态栏颜色
     */
    setColor,
    
    /**
     * 重置为默认颜色
     */
    resetColor,
    
    /**
     * 获取当前颜色
     */
    getCurrentColor,
    
    /**
     * 更新状态栏颜色（重新计算）
     */
    updateColor: updateStatusBarColor,
    
    /**
     * 当前计算出的页面颜色
     */
    pageColor: getCurrentPageColor(),
    
    /**
     * 状态栏服务是否支持
     */
    isSupported: statusBarService.isSupported(),
  };
};

/**
 * 简化版Hook，只设置固定颜色
 */
export const useFixedStatusBarColor = (color: string, autoDetectStyle: boolean = true) => {
  return useStatusBarColor({
    customColor: color,
    autoDetect: false,
    autoDetectStyle,
    resetOnUnmount: true
  });
};

/**
 * 自动检测Hook，根据路径自动设置颜色
 */
export const useAutoStatusBarColor = (delay: number = 100) => {
  return useStatusBarColor({
    autoDetect: true,
    autoDetectStyle: true,
    delay,
    resetOnUnmount: false
  });
};
