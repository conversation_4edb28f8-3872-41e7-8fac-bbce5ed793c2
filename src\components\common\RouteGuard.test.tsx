import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import RouteGuard from './RouteGuard';
import authReducer from '../../app/slices/authSlice';
import tenantReducer from '../../app/slices/tenantSlice';

import { aileDBService } from '../../services/db/aileDBService';
import {
  ROUTE_LOGIN,
  ROUTE_HOME,
  ROUTE_USER_SIGNUP,
  ROUTE_BUSINESS_REGISTER
} from '../../config/app/routes';

// Mock dependencies
jest.mock('../../services/system/logService', () => ({
  logService: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('../../utils/storage', () => ({
  getLocalStorage: jest.fn(),
}));

jest.mock('../../services/db/aileDBService', () => ({
  __esModule: true,
  default: {
    isInitialized: jest.fn(),
  },
}));

const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock store setup helper
const createMockStore = (initialState: any) => {
  return configureStore({
    reducer: {
      auth: authReducer,
      tenant: tenantReducer,
    } as any,
    preloadedState: initialState,
  });
};

const TestComponent = () => <div>Protected Content</div>;

describe('RouteGuard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('應允許訪問公共路由', () => {
    const store = createMockStore({
      auth: {
        isAuthenticated: false,
        account: null,
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: null, currentTenantInfo: null },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_LOGIN]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('應重定向未認證用戶到登錄頁', () => {
    const store = createMockStore({
      auth: {
        isAuthenticated: false,
        account: null,
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: null, currentTenantInfo: null },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_HOME]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_LOGIN, { replace: true });
  });

  it('應重定向新用戶到用戶註冊頁', () => {
    const store = createMockStore({
      auth: {
        isAuthenticated: true,
        account: { accountId: 'test-user', isInitial: true },
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: null, currentTenantInfo: null },
    });

    (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_HOME]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_USER_SIGNUP, { replace: true });
  });

  it('應重定向數據庫未初始化的用戶到登錄頁', () => {
    const store = createMockStore({
      auth: {
        isAuthenticated: true,
        account: { accountId: 'test-user', isInitial: false },
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: 'tenant-1', currentTenantInfo: null },
    });

    (aileDBService.isInitialized as jest.Mock).mockReturnValue(false);

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_HOME]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_LOGIN, { replace: true });
  });

  it('應重定向沒有租戶的用戶到商家註冊頁', () => {
    const { getLocalStorage } = require('../../utils/storage');
    getLocalStorage.mockReturnValue(null);
    
    const store = createMockStore({
      auth: {
        isAuthenticated: true,
        account: { accountId: 'test-user', isInitial: false },
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: null, currentTenantInfo: null },
    });

    (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_HOME]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_BUSINESS_REGISTER, { replace: true });
  });

  it('應允許通過所有檢查的用戶訪問受保護路由', () => {
    const store = createMockStore({
      auth: {
        isAuthenticated: true,
        account: { accountId: 'test-user', isInitial: false },
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: 'tenant-1', currentTenantInfo: null },
    });

    (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_HOME]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('應從localStorage獲取租戶ID', () => {
    const { getLocalStorage } = require('../../utils/storage');
    getLocalStorage.mockReturnValue('tenant-from-storage');
    
    const store = createMockStore({
      auth: {
        isAuthenticated: true,
        account: { accountId: 'test-user', isInitial: false },
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false
      },
      tenant: { currentTenantId: null, currentTenantInfo: null },
    });

    (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[ROUTE_HOME]}>
          <RouteGuard>
            <TestComponent />
          </RouteGuard>
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(mockNavigate).not.toHaveBeenCalled();
  });
}); 