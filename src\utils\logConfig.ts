/**
 * 日誌配置工具 - AileApp
 * 提供應用日誌配置和控制功能
 */

import { LogLevel, logService } from '../services/system/logService';
import { ConstantUtil } from './constantUtil';

/**
 * 配置日誌服務
 * @param enableDebug 是否啟用調試日誌
 */
export const configureLogService = (enableDebug: boolean = false): void => {
  logService.configure({
    minLevel: enableDebug ? LogLevel.DEBUG : LogLevel.INFO,
    maxLogEntries: 1000,
    enableConsole: true,
    enableStorage: true,
    enableRemote: process.env.NODE_ENV === 'production',
    applicationName: ConstantUtil.APP_NAME,
  });
};

/**
 * 啟用調試日誌
 * 將日誌級別設置為 DEBUG，並在控制台輸出相關訊息
 */
export const enableDebugLogs = (): void => {
  configureLogService(true);
  logService.debug('調試日誌已啟用', { timestamp: new Date().toISOString() });
  
  // 在控制台顯示使用方法
  console.log('%c[AileApp] 調試日誌已啟用', 'font-weight: bold; color:rgb(122, 168, 76);');
  console.log('%c可用命令:', 'color: #729FCF;');
  console.log('%c- logService.debug(message, data)', 'color:rgb(122, 168, 76);');
  console.log('%c- logService.info(message, data)', 'color: #729FCF;');
  console.log('%c- logService.warn(message, data)', 'color: #EDD400;');
  console.log('%c- logService.error(message, data)', 'color: #EF2929;');
  console.log('%c- logService.getLogs()', 'color: #AD7FA8;');
  console.log('%c- logService.clearLogs()', 'color: #AD7FA8;');
};

/**
 * 禁用調試日誌
 * 將日誌級別設置為 INFO
 */
export const disableDebugLogs = (): void => {
  configureLogService(false);
  logService.info('調試日誌已禁用');
};

/**
 * 初始化日誌配置
 * 根據環境和參數決定是否啟用調試日誌
 */
export const initLogConfig = (): void => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const urlParams = new URLSearchParams(window.location.search);
  const debugParam = urlParams.get('debug');
  
  const enableDebug = isDevelopment || debugParam === 'true';
  
  configureLogService(enableDebug);
  
  if (enableDebug) {
    logService.debug('調試日誌已自動啟用', { 
      env: process.env.NODE_ENV,
      debugParam
    });
  }
  
  // 將啟用/禁用方法掛載到全局對象，方便調試
  (window as any).enableAileDebug = enableDebugLogs;
  (window as any).disableAileDebug = disableDebugLogs;
  (window as any).logService = logService;
}; 