/* 任务面板容器 */
.tasks-panel {
  width: 100%;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  position: relative;
  z-index: 10;
}

/* 当任务展开时移除底部圆角 */
.tasks-panel.expanded {
  border-radius: 8px 8px 0 0;
}

/* 任务标题栏 */
.tasks-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
}

.tasks-icon-container {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tasks-icon {
  width: 12.92px;
  height: 16.25px;
}

.tasks-title {
  flex: 1;
  font-family: 'SF Pro', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 1.4em;
  padding: 12px 0;
  margin-left: 8px;
}

.tasks-arrow {
  width: 16px;
  height: 13px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-left: 4px;
}

.tasks-arrow img {
  width: 11.08px;
  height: 6.54px;
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.tasks-arrow.expanded img {
  transform: rotate(0deg);
}

/* 任务列表覆盖层 */
.tasks-list-overlay {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 20;
  margin-top: -1px; /* 消除可能的缝隙 */
}

/* 任务列表 */
.tasks-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 任务项 */
.task-item {
  align-items: center;
  padding-left: 12px;
  padding-right: 8px;
  cursor: pointer;
}

.task-content {
  display: flex;
  flex: 1;
  align-items: center;
  border-bottom: 1px solid #EEEEEE;
}

.view-more .task-content {
  border-bottom: none;
}

.task-title {
  flex: 1;
  font-family: 'SF Pro', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4em;
  padding: 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-due-time {
  font-family: 'SF Pro', sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.4em;
  text-align: right;
  padding-left: 12px;
} 