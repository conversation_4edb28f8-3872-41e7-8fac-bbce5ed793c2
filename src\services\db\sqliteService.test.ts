import sqliteService from './sqliteService';

describe('sqliteService', () => {
  it('應有 getPlatform 方法', () => {
    expect(typeof sqliteService.getPlatform).toBe('function');
  });
  
  it('應有 initWebStore 方法', () => {
    expect(typeof sqliteService.initWebStore).toBe('function');
  });
  
  it('應有 openDatabase 方法', () => {
    expect(typeof sqliteService.openDatabase).toBe('function');
  });
  
  it('應有 closeDatabase 方法', () => {
    expect(typeof sqliteService.closeDatabase).toBe('function');
  });
  
  it('應有 isConnection 方法', () => {
    expect(typeof sqliteService.isConnection).toBe('function');
  });
  
  it('應有 saveToStore 方法', () => {
    expect(typeof sqliteService.saveToStore).toBe('function');
  });
  
  it('應有 saveToLocalDisk 方法', () => {
    expect(typeof sqliteService.saveToLocalDisk).toBe('function');
  });
  
  it('應有 addUpgradeStatement 方法', () => {
    expect(typeof sqliteService.addUpgradeStatement).toBe('function');
  });
  
  // 可擴展：mock DB 行為、異常分支
}); 