[{"pkg": "@capacitor-community/media", "classpath": "com.getcapacitor.community.media.MediaPlugin"}, {"pkg": "@capacitor-community/sqlite", "classpath": "com.getcapacitor.community.database.sqlite.CapacitorSQLitePlugin"}, {"pkg": "@capacitor-mlkit/barcode-scanning", "classpath": "io.capawesome.capacitorjs.plugins.mlkit.barcodescanning.BarcodeScannerPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/push-notifications", "classpath": "com.capacitorjs.plugins.pushnotifications.PushNotificationsPlugin"}, {"pkg": "@capacitor/share", "classpath": "com.capacitorjs.plugins.share.SharePlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "aile-capacitor-line-login", "classpath": "com.aile.plugins.linelogin.LineLoginPlugin"}]