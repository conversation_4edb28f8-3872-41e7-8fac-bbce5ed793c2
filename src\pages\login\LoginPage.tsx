import * as React from 'react';
import { useEffect, useState } from 'react';
import { Button, Toast, ActionSheet } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../app/hooks';
import { clearError, loginWithLINE, restoreAuthToken, setAccount } from '../../app/slices/authSlice';
import { logService } from '../../services/system/logService';
import authService from '../../services/core/auth/authService';
import { ConstantUtil } from '../../utils/constantUtil';
import { setLocalStorage, getLocalStorage } from '../../utils/storage';

import { closeAileDB } from '../../services/db/aileDBService';
import mainImage from '../../assets/images/main_image.png';
import lineIcon from '../../assets/images/line_icon.png';
import logo from '../../assets/images/logo.svg';
import './LoginPage.css';
import { useFixedStatusBarColor } from '../../hooks/useStatusBarColor';
import { PAGE_COLORS } from '../../config/app/pageColors';
import { ROUTE_OTP_LOGIN } from '../../config/app/routes';

/**
 * 登录页面组件
 * 提供LINE登录和其他登录选项
 */
const LoginPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { 
    loginError,
  } = useAppSelector((state) => state.auth);

  // 新增局部状态来控制按钮的加载状态
  const [isButtonLoading, setButtonLoading] = React.useState(false);
  
  // 環境切換相關狀態
  const [showEnvSelector, setShowEnvSelector] = useState(false);
  const [pressTimer, setPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [currentEnv, setCurrentEnv] = useState<string>(() => {
    // 優先從 localStorage 讀取設置的環境
    return getLocalStorage<string>(ConstantUtil.PREFERRED_ENV_KEY, import.meta.env.MODE || 'development');
  });
  
  // 長按觸發時間（毫秒）
  const LONG_PRESS_DURATION = 1500;
  
  // 環境選項
  const envActions = [
    { text: '開發環境 (development)', key: 'development' },
    { text: '本地環境 (dev-local)', key: 'dev-local' },
    { text: '測試環境 (staging)', key: 'staging' },
    { text: '生產環境 (production)', key: 'production' },
  ];

  // 设置登录页面状态栏颜色
  useFixedStatusBarColor(PAGE_COLORS.AUTH.LOGIN);

  useEffect(() => {
    // 關閉數據庫連接
    closeAileDB()
      .then(() => {
        logService.info('登錄頁：已關閉數據庫連接，避免數據跨會話串聯');
      })
      .catch((error: Error) => {
        logService.error('登錄頁：關閉數據庫連接失敗', { error });
      });
  }, []);

  useEffect(() => {
    if (loginError) {
      Toast.show({
        icon: 'fail',
        content: loginError,
        duration: 3000,
      });
    }
  }, [loginError]);

  const handleLineLogin = async () => {
    try {
      dispatch(clearError());
      setButtonLoading(true); // 设置按钮为加载状态
      logService.info('用戶點擊 LINE 登錄按鈕');
      
      const result = await authService.lineLogin();
      logService.info('LINE 登錄成功', result.toString());
      
      // 使用 loginWithLINE action 並等待結果
      const loginResult = await dispatch(loginWithLINE({
        loginType: ConstantUtil.LOGIN_TYPE_LINE,
        thirdChannel: result.channel,
        useNonMobile: true,
        scopeId: result.id,
      })).unwrap();
      
      // 如果登錄成功，使用 authService 的成功處理方法
      if (loginResult && loginResult.data) {
        // 先執行 Redux 操作
        dispatch(restoreAuthToken(loginResult.data.tokenId ?? null));
        dispatch(setAccount(loginResult.data));

        // 然後處理登錄成功邏輯
        const result = await authService.handleLoginSuccess(loginResult.data, navigate);

        // 如果有租戶信息，處理租戶相關的 Redux 操作
        if (result.tenantList) {
          // 這裡可以添加租戶相關的 Redux 操作
          // 例如：dispatch(setTenantList(result.tenantList));
        }
      }

    } catch (error) {
      logService.error('LINE 登錄啟動失敗', error as Error);
      Toast.show({
        icon: 'fail',
        content: '啟動 LINE 登錄失敗，請稍後重試',
        duration: 3000,
      });
    } finally {
      setButtonLoading(false); // 无论成功或失败，都要重置按钮状态
    }
  };

  const handleOtherLogin = () => {
    logService.info('用戶選擇使用其他方式登錄');
    // 添加state参数，指示这是从登录页面直接跳转的，不需要再次验证token
    navigate(ROUTE_OTP_LOGIN, { 
      state: { fromLoginPage: true, skipTokenCheck: true } 
    });
  };

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isButtonLoading) {
        // 页面重新可见且按钮仍在加载状态，可能是从 LINE App 返回但没有完成登录
        setTimeout(() => {
          if (isButtonLoading) {
            logService.info('從 LINE App 返回但未完成登錄');
            dispatch(clearError());
          }
        }, 1000); // 给一点时间让回调处理
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isButtonLoading, dispatch]);
  
  // 處理長按開始
  const handlePressStart = () => {
    // 清除任何現有的計時器
    if (pressTimer) clearTimeout(pressTimer);
    
    // 設置新的計時器
    const timer = setTimeout(() => {
      setShowEnvSelector(true);
      logService.debug('環境選擇器觸發', { currentEnv });
    }, LONG_PRESS_DURATION);
    
    setPressTimer(timer);
  };
  
  // 處理長按結束
  const handlePressEnd = () => {
    // 清除計時器
    if (pressTimer) {
      clearTimeout(pressTimer);
      setPressTimer(null);
    }
  };
  
  // 切換環境
  const switchEnvironment = (env: string) => {
    try {
      if (env === currentEnv) {
        Toast.show({
          content: `當前已是${env}環境`,
          duration: 1500,
        });
        return;
      }
      
      // 儲存使用者選擇的環境
      setLocalStorage(ConstantUtil.PREFERRED_ENV_KEY, env);
      
      logService.info('使用者切換環境', { from: currentEnv, to: env });
      
      // 更新當前環境狀態
      setCurrentEnv(env);
      
      Toast.show({
        content: `切換到${env}環境，正在重新載入...`,
        duration: 1500,
      });
      
      // 重新載入頁面以應用新環境設置
      setTimeout(() => {
        window.location.reload();
      }, 1800);
    } catch (error) {
      logService.error('切換環境失敗', error as Error);
      Toast.show({
        icon: 'fail',
        content: '切換環境失敗',
        duration: 1500,
      });
    }
  };

  return (
    <div className="login-page">
      {/* 左上角長按區域 */}
      <div 
        className="env-selector-trigger"
        onTouchStart={handlePressStart}
        onTouchEnd={handlePressEnd}
        onTouchCancel={handlePressEnd}
        onMouseDown={handlePressStart}
        onMouseUp={handlePressEnd}
        onMouseLeave={handlePressEnd}
      />
      
      {/* 環境選擇ActionSheet */}
      <ActionSheet
        visible={showEnvSelector}
        actions={envActions.map(item => ({
          text: item.text,
          key: item.key,
          onClick: () => {
            switchEnvironment(item.key);
            setShowEnvSelector(false);
          }
        }))}
        extra={`當前環境: ${currentEnv}`}
        onClose={() => setShowEnvSelector(false)}
        closeOnAction={true}
        cancelText="取消"
      />
      
      {/* 主要内容区域 */}
      <div className="login-page__content">
        <div className="login-page__header">
          {/* Logo */}
          <div className="login-page__logo">
            <img src={logo} alt="Aile Logo" />
          </div>
          
          {/* 标语 */}
          <p className="login-page__tagline">
            集中溝通窗口，讓客服應對更即時
          </p>
        </div>
        
        {/* 主图 */}
        <div className="login-page__main-image">
          <img src={mainImage} alt="Aile Platform" />
        </div>
      </div>
      
      {/* 按钮区域 */}
      <div className="login-page__buttons">
        {/* LINE登录按钮 */}
        <button 
          onClick={handleLineLogin}
          disabled={isButtonLoading}
          className={`login-page__line-button ${isButtonLoading ? 'login-page__line-button--loading' : ''}`}
        >
          <img src={lineIcon} alt="LINE" className="login-page__line-icon" />
          <span className="login-page__line-text">
            {isButtonLoading ? '正在啟動 LINE 登錄...' : '通過 LINE 繼續'}
          </span>
        </button>
        
        {/* 其他方式登录按钮 */}
        <Button
          block
          onClick={handleOtherLogin}
          className="login-page__other-button"
          disabled={isButtonLoading}
        >
          <span className="login-page__other-text">
            使用其他方式繼續
          </span>
        </Button>
      </div>
    </div>
  );
};

export default LoginPage;
