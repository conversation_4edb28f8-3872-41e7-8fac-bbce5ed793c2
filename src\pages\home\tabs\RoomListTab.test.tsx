
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import RoomListTab from './RoomListTab';
import { 
  ROUTE_CHAT_ROOM, 
  ROUTE_SYSTEM_CHAT_ROOM, 
  generateRoute
} from '../../../config/app/routes';

// Mock the i18n functionality
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        '機器人(99)': '機器人(99)',
        '服務中(20)': '服務中(20)',
        '已處理': '已處理',
        '團隊聊天室': '團隊聊天室',
        '今天下午2點有個會議喔！': '今天下午2點有個會議喔！',
        '我想請問這個商品': '我想請問這個商品',
        '我的聊天室': '我的聊天室',
        '訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息': '訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息訊息',
        '系統聊天室': '系統聊天室',
        '請問什麼時候會到貨？': '請問什麼時候會到貨？',
        '親愛的本月壽星拉拉拉拉拉拉拉拉拉拉拉拉拉拉...': '親愛的本月壽星拉拉拉拉拉拉拉拉拉拉拉拉拉拉...',
        '謝謝～': '謝謝～',
        '行銷部門': '行銷部門',
        '下週一會議討論新活動事項': '下週一會議討論新活動事項',
        '已收到您的訂單，會盡快處理': '已收到您的訂單，會盡快處理',
        '請問產品有什麼特點？': '請問產品有什麼特點？',
        '謝謝您的建議，我會考慮的': '謝謝您的建議，我會考慮的',
        '下次見面詳談合作細節': '下次見面詳談合作細節',
        '好的，我會準時參加': '好的，我會準時參加',
        '系統通知': '系統通知',
        '您有新的系統更新，請查看': '您有新的系統更新，請查看'
      };
      return translations[key] || key;
    }
  })
}));

// Mock navigate function
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('RoomListTab', () => {
  const mockOnScroll = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    render(
      <BrowserRouter>
        <RoomListTab onScroll={mockOnScroll} />
      </BrowserRouter>
    );
    
    // Check if filter tabs are rendered
    expect(screen.getByText('機器人(99)')).toBeInTheDocument();
    expect(screen.getByText('服務中(20)')).toBeInTheDocument();
    expect(screen.getByText('已處理')).toBeInTheDocument();
    
    // Check if chat items are rendered
    expect(screen.getByText('團隊聊天室')).toBeInTheDocument();
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('我的聊天室')).toBeInTheDocument();
  });

  it('navigates to chat room when "我的聊天室" is clicked', () => {
    render(
      <BrowserRouter>
        <RoomListTab onScroll={mockOnScroll} />
      </BrowserRouter>
    );
    
    // Find and click on the "我的聊天室" item
    fireEvent.click(screen.getByText('我的聊天室'));
    
    // Verify that navigation was called with the correct route
    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_CHAT_ROOM);
  });

  it('navigates to system chat room when "系統聊天室" is clicked', () => {
    render(
      <BrowserRouter>
        <RoomListTab onScroll={mockOnScroll} />
      </BrowserRouter>
    );
    
    // Find and click on the "系統聊天室" item
    fireEvent.click(screen.getByText('系統聊天室'));
    
    // Verify that navigation was called with the correct route
    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_SYSTEM_CHAT_ROOM);
  });

  it('navigates to customer chat room when a customer chat item is clicked', () => {
    render(
      <BrowserRouter>
        <RoomListTab onScroll={mockOnScroll} />
      </BrowserRouter>
    );
    
    // Find and click on a customer chat item
    fireEvent.click(screen.getByText('Alice'));
    
    // Verify that navigation was called with the correct route including the customer ID
    expect(mockNavigate).toHaveBeenCalledWith(generateRoute.customerRoom('2'));
  });

  it('applies active filter when filter tab is clicked', () => {
    render(
      <BrowserRouter>
        <RoomListTab onScroll={mockOnScroll} />
      </BrowserRouter>
    );
    
    // Default active filter should be 'active'
    const activeFilter = document.querySelector('.filter-tab.active');
    expect(activeFilter).toHaveTextContent('服務中(20)');
    
    // Click on "機器人" filter
    fireEvent.click(screen.getByText('機器人(99)'));
    
    // "機器人" filter should now be active
    const newActiveFilter = document.querySelector('.filter-tab.active');
    expect(newActiveFilter).toHaveTextContent('機器人(99)');
  });
}); 