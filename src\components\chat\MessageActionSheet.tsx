import React from 'react';
import { ActionSheet } from 'antd-mobile';
import './MessageActionSheet.css';

interface MessageActionSheetProps {
  visible: boolean;
  onClose: () => void;
  onRetry: () => void;
  onDelete: () => void;
}

/**
 * 消息操作菜单组件
 * 用于失败消息的重传和删除操作
 */
const MessageActionSheet: React.FC<MessageActionSheetProps> = ({
  visible,
  onClose,
  onRetry,
  onDelete
}) => {
  const actions = [
    {
      text: '重传',
      key: 'retry',
      style: { color: '#1677ff' },
      onClick: () => {
        onRetry();
        onClose();
      }
    },
    {
      text: '删除',
      key: 'delete',
      style: { color: '#ff4d4f' },
      onClick: () => {
        onDelete();
        onClose();
      }
    }
  ];

  return (
    <ActionSheet
      visible={visible}
      actions={actions}
      onClose={onClose}
      cancelText="取消"
      className="message-action-sheet"
    />
  );
};

export default MessageActionSheet;
