/**
 * 用於處理 Redux Store 相關工具函數，避免循環依賴
 */

// 此方法會在 app 初始化後被設置
let getStoreInstance: () => any = () => {
  throw new Error('Store not initialized yet');
};

/**
 * 設置 store 實例獲取函數
 * 在 store.ts 初始化後調用
 */
export const setStoreGetter = (getter: () => any) => {
  getStoreInstance = getter;
};

/**
 * 獲取 store 實例
 * 在需要使用 store 的地方調用
 */
export const getStore = () => getStoreInstance();

/**
 * 安全地 dispatch action
 * 在任何需要 dispatch action 的地方使用此方法，而非直接調用 store.dispatch
 */
export const safeDispatch = (action: any) => {
  try {
    const store = getStore();
    if (store && typeof store.dispatch === 'function') {
      return store.dispatch(action);
    }
  } catch (error) {
    console.error('Failed to dispatch action:', error);
  }
  return null;
}; 