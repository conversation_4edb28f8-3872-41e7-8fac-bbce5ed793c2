
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import App from './App';
import { store } from './app/store';
import './styles/global.css';
import { JeepSqlite } from 'jeep-sqlite/dist/components/jeep-sqlite';
import { Capacitor } from '@capacitor/core';


customElements.define('jeep-sqlite', JeepSqlite);
const platform = Capacitor.getPlatform();

// 渲染应用主体
const rootRender = () => {
ReactDOM.createRoot(document.getElementById('root')!).render(
  // 移除 StrictMode，避免組件在開發模式下被渲染兩次
  <Provider store={store}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </Provider>
);
}


if (platform !== "web") {
  rootRender();
} else {
  // Web platform initialization
  window.addEventListener('DOMContentLoaded', async () => {
    try {
      // Create jeep-sqlite element
      const jeepEl = document.createElement("jeep-sqlite");
      document.body.appendChild(jeepEl);

      // Wait for custom element to be defined
      await customElements.whenDefined('jeep-sqlite');

      // Render the app
      rootRender();
    } catch (err) {
      console.error(`Error initializing jeep-sqlite: ${err}`);
      throw new Error(`Error initializing jeep-sqlite: ${err}`);
    }
  });
}
 


// // 在开发环境下初始化Stagewise工具栏
// if (process.env.NODE_ENV === 'development') {
//   // 动态导入Stagewise工具栏以减少生产环境的打包体积
//   import('./components/StagewiseToolbar').then(({ default: StagewiseToolbar }) => {
//     // 创建工具栏容器
//     const toolbarRoot = document.createElement('div');
//     toolbarRoot.id = 'stagewise-toolbar-root';
//     document.body.appendChild(toolbarRoot);

//     // 渲染工具栏
//     ReactDOM.createRoot(toolbarRoot).render(
//       <React.StrictMode>
//         <StagewiseToolbar />
//       </React.StrictMode>
//     );
//   }).catch(err => {
//     console.error('Failed to load Stagewise Toolbar:', err);
//   });
// } 