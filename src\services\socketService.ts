// @ts-ignore
import _ from 'lodash';
import { EventEmitter } from '../utils/EventEmitter';
import { LogInState } from '../types/aile.enum';
import { ConnectionStatus } from '../types/socket.types';
import logService from './system/logService';
import stateService, { MessageAction, RoomAction } from './stateService';
import { roomDao, messageDao } from './dao';
import { fetchRoomItem } from './core/chat/roomService';
import { MessageData } from './core/chat/messageService';
// 避免循环依赖，使用延迟获取 store
// import { store } from '../app/store';
import { updateRoom } from '../app/slices/roomSlice';

// 延迟获取 store 以避免循环依赖
function getStore() {
  // 使用全局变量获取 store，这在 store.ts 中已经设置
  return (window as any).__STORE__;
}


// Types
enum SocketMessageAction {
  add = 'add',
  update = 'update',
  delete = 'delete',
}

enum MemberChangedAction {
  Received = 'received',
  Readed = 'readed',
}

enum RoomType {
  accountPerson = 'accountPerson',
  team = 'team',
  system = 'system',
  customer = 'customer',
}

enum UnReadType {
  CommonUnRead = 'common',
  ServiceUnRead = 'service',
  SystemUnRead = 'system',
  AccountUnRead = 'account',
  RoomUnRead = 'room',
}

export enum SocketSessionEvent {
  AgentActive = 'AgentActive',
  RobotActive = 'RobotActive',
  RobotStop = 'RobotStop',
  AgentStop = 'AgentStop',
  TransferStart = 'TransferStart',
  TransferCancel = 'TransferCancel',
  TransferComplete = 'TransferComplete',
  DistributeActive = 'DistributeActive',
  Timeout = 'Timeout',
}

enum MessageType {
  NOTIFICATION = 'NOTIFICATION',
  MESSAGE = 'MESSAGE',
}

interface Message {
  id: string;
  roomId: string;
  messageId: string;
  _from?: string;
  from?: string;
  channel?: string;
  content: any;
  tenantCode?: string;
  tenantId?: string;
  sendTime?: Date;
  sentAt: Date;
  sequence: number;
  isUser?: boolean;
  [key: string]: any;
}

interface Room {
  id: string;
  name: string;
  lastSequence?: number;
  lastMessage?: Message;
  markUnreadNum?: number;
  roomType: RoomType;
  isMute: boolean;
  isTop: boolean;
  unReadNum?: number;
  memberIds?: string[];
  tenantId?: string;
  [key: string]: any;
}

// 進程消息事件介面
interface ProcessMessageEvent {
  name: string;
  code: string;
  event: string;
  sequence: number;
  content: any;
  tenantId: string;
  callback: any;
  isOff: any;
}

// Service class
class SocketService {
  private eventEmitter = new EventEmitter();
  private queue: { 
    name: string; 
    code: string; 
    event: string; 
    sequence: number; 
    content: any; 
    tenantId: string; 
    callback: any; 
    isOff: any 
  }[] = [];
  private isProcessing = false;
  private roomDbService: any = null; // Will be initialized from outside
  private aileLocalService: any = null; // Will be initialized from outside
  private aileOnlineService: any = null; // Will be initialized from outside
  // private capacitorService: any = null; // Will be initialized from outside - Unused

  private openRoomIds: Set<string> = new Set();
  
  // 状态服务订阅移除函数
  private stateUnsubscriber: (() => void) | null = null;
  
  constructor() {
    // Setup interval to process queue if needed
    setInterval(() => {
      if (!this.isProcessing && this.queue.length > 0) {
        this.processQueue();
      }
    }, 5000);
    
    // 订阅 stateService 的 processMessageEvent 事件
    this.stateUnsubscriber = stateService.on('processMessageEvent', (event: ProcessMessageEvent) => {
      logService.debug('Received processMessageEvent from stateService:', { event });
      this.processMessageEvent(event);
    });
    
    logService.info('SocketService initialized and subscribed to stateService.processMessageEvent');
  }

  // Initialize services that would have been injected in Angular
  public initServices(
    roomDbService: any,
    aileLocalService: any, 
    aileOnlineService: any,
  ) {
    this.roomDbService = roomDbService;
    this.aileLocalService = aileLocalService;
    this.aileOnlineService = aileOnlineService;
    // this.capacitorService = capacitorService; // Unused
    
    logService.debug('SocketService dependencies initialized');
  }
  
  // Methods to track which rooms are currently open
  public setRoomOpen(roomId: string) {
    this.openRoomIds.add(roomId);
  }
  
  public setRoomClosed(roomId: string) {
    this.openRoomIds.delete(roomId);
  }
  
  public isRoomOpen(roomId: string): boolean {
    return this.openRoomIds.has(roomId);
  }

  // Event handlers
  public on(event: string, listener: (...args: any[]) => void) {
    this.eventEmitter.on(event, listener);
    return () => this.eventEmitter.off(event, listener);
  }

  public emit(event: string, ...args: any[]) {
    this.eventEmitter.emit(event, ...args);
  }

  // Process incoming socket.io messages
  public processMessageEvent(event: ProcessMessageEvent) {
    logService.debug('Process SocketIO data : ', { event });
    
    // 確認收到消息（如果有回調函數）
    if (event.callback && typeof event.callback === 'function') {
      try {
        logService.debug('已確認接收進程消息事件', { 
          name: event.name, 
          code: event.code, 
          event: event.event 
        });
        event.callback({
          name: event.name,
          action: 'received',
          ack: true
        });
      } catch (error) {
        logService.warn('執行回調時出錯', error as Error);
      }
    }
    
    // 根據事件類型進行特殊處理
    if (event.code === 'SocketIO' && event.event === 'ForceOffline') {
      // 處理強制下線事件
      this.emit('loginChanged', LogInState.ForceOffline, null);
      return;
    }
    
    // 將事件添加到處理隊列
    this.queue.push(event);
    this.processQueue();
  }

  private async processQueue() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    
    while (this.queue.length > 0) {
      const event = this.queue.shift()!;
      try {
        if (event.name === 'Aile.Message') {
          let code: string = event.name + '_' + event.code + '_' + event.event;
          code = code.replace(/\./g, '_');
          if (typeof (this as any)[code] === 'function') {
            try {
              (this as any)[code].call(this, [event.content, event.tenantId, event.callback, event.isOff]);
            } catch (e) {
              logService.info(`Process MessageEvent ${code} but catch error: ${e}`);
            }
          } else {
            logService.info(`Process MessageEvent ${code} but is not exist function.`);
          }
        } else {
          let code: string = event.name + '_' + event.code;
          code = code.replace(/\./g, '_');
          if (typeof (this as any)[code] === 'function') {
            try {
              (this as any)[code].call(this, [event.content, event.tenantId, event.event, event.callback, event.isOff]);
            } catch (e) {
              logService.info(`Process MessageEvent ${code} but catch error: ${e}`);
            }
          } else {
            logService.info(`Process MessageEvent ${code} but is not exist function.`);
          }
        }
      } catch (error) {
        console.error('Error processing message:', error);
      }
    }

    this.isProcessing = false;
  }

  // Message handlers
  async Aile_Message_Message_Create(args: any[]) {
    const content = args[0];
    const callback = args[2];
    
    if (callback) {
      callback({ name: 'Aile.Message', ack: true, action: 'received' });
    }
    
    const message = content.message;
    
    let messageItem = {} as MessageData;
    messageItem = _.assign({}, message);
    messageItem.replyToId = message.replyToId ?? '';
    messageItem.replyContent = message.replyContent ?? '';
    messageItem.updateTime = message.updateTime ?? 0;
    // 補齊 MessageData 需要的屬性
    messageItem.senderId = message.senderId ?? message.from ?? '';
    messageItem.senderName = message.senderName ?? '';
    messageItem.type = message.type ?? '';
    messageItem.id = message.id ?? '';
    messageItem.roomId = message.roomId ?? '';
    messageItem.sendTime = message.sendTime ?? new Date();
    messageItem.content = message.content ?? '';
    messageItem.status = message.status ?? '';
    messageItem.metadata = message.metadata ?? {};
    messageItem.sequence = message.sequence ?? 0;
    
    // Store message locally
    await messageDao.saveMessagesToDb([messageItem]);
    
    // // Get local room
    let room = await roomDao.getRoomById(message.roomId);
    
    if (room) {
      if ((room.lastSequence ?? 0) < message.sequence) {
        room.lastSequence = message.sequence;
        room.lastMessage = message.content;
        if ((room.markUnreadNum ?? 0) > 0) {
          room.markUnreadNum = 0;
        }
        await roomDao.upsertRooms([room]);
        
        // 使用新的 updateRoom action 更新 Redux store
        if (room.id) {  // 確保 room.id 不為 undefined
          const store = getStore();
          if (store) {
            store.dispatch(updateRoom({
              roomId: room.id,
              room: room,
              lastMessage: messageItem
            }));
          }
        }
        
        stateService.notifyRoomChanged({
          action: RoomAction.lastMessageUpdate,
          roomId: message.roomId,
          room: room,
          message: messageItem,
          tenantId: message.tenantId,
        });
      }
    } else {
      const roomFetch = await fetchRoomItem({ roomId: message.roomId });
      if (roomFetch?.data) {
        room = roomFetch.data;
        
        // 使用新的 updateRoom action 更新 Redux store
        if (room && room.id) {  // 確保 room 和 room.id 都不為 undefined
          const store = getStore();
          if (store) {
            store.dispatch(updateRoom({
              roomId: room.id,
              room: room,
              lastMessage: messageItem
            }));
          }
        }
        
        stateService.notifyRoomChanged({
          action: RoomAction.add,
          roomId: message.roomId,
          room: room,
          message: messageItem,
          tenantId: message.tenantId,
        });
      }
    }
    
    if (room) {
      if (content.unread?.roomUnreadCount !== room.unReadNum) {
        room.unReadNum = content.unread?.roomUnreadCount;
        await this.roomDbService?.upsertRoom(room);
        
        if (room.roomType === RoomType.accountPerson) {
          stateService.notifyUnReadChanged({
            type: UnReadType.AccountUnRead,
            count: content.unread?.roomUnreadCount,
            roomId: room.id,
          });
        } else {
          stateService.notifyUnReadChanged({
            type: UnReadType.RoomUnRead,
            count: content.unread?.roomUnreadCount,
            roomId: room.id,
          });
        }
      }
      
      // Play notification sound if needed
      // if (!room.isMute && messageItem.isUser === false) {
      //   if (!this.isRoomOpen(room.id ?? '')) {
      //     this.capacitorService?.playNotificationSound();
      //   }
      // }
    }

    const loginUserId = stateService.loginUser()?.id;
    const loginAccount = stateService.loginAccount();
    if (messageItem.senderId === loginUserId || 
      (messageItem.roomId === loginAccount?.personRoomId && messageItem.senderId === loginAccount?.accountId) || 
      (messageItem.roomId === loginAccount?.systemRoomId && messageItem.senderId === loginAccount?.accountId)
    ) {
      logService.info('該消息為當前用戶發送，已發送');
      return;
    }

    stateService.notifyMessageChanged({
      action: MessageAction.add,
      roomId: message.roomId,
      messageId: message.messageId,
      message: messageItem,
      tenantId: message.tenantCode,
    });
    
    const unreadObj = content.unread;
    if (unreadObj) {
      stateService.notifyUnReadChanged({
        type: UnReadType.CommonUnRead,
        count: unreadObj.commonUnreadCount,
      });
      
      stateService.notifyUnReadChanged({
        type: UnReadType.ServiceUnRead,
        count: unreadObj.serviceUnreadCount,
      });
      
      stateService.notifyUnReadChanged({
        type: UnReadType.SystemUnRead,
        count: unreadObj.systemUnReadCount,
      });
    }
  }

  Aile_Notice_Account(args: any[]) {
    const content = args[0];
    const tenantId = args[1];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    const isOff = args[4];
    logService.debug('Aile_Notice_Account data : ', { content, event, tenantId, isOff });
  }

  Aile_Notice_Tenant(args: any[]) {
    const content = args[0];
    const tenantId = args[1];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    const isOff = args[4];
    logService.debug('Aile_Notice_Tenant data : ', { content, event, tenantId, isOff });
  }

  async Aile_Notice_Room(args: any[]) {
    const content = args[0];
    const tenantId = args[1];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    const isOff = args[4];
    logService.debug('Aile_Notice_Room data : ', { content, event, tenantId, isOff });

    switch (event) {
      case 'Top': {
        // Handle room top status
        const roomId = content.roomId;
        const isTop = content.top ?? false;

        const room = await this.aileLocalService?.getRoomById(roomId);
        
        if (room != null) {
          room.isTop = isTop;
          await this.roomDbService?.updateRoomTopStatus(roomId, isTop);
          this.emit('roomChanged', {
            action: RoomAction.topStatusChanged,
            room: room,
            roomId: roomId,
            message: null,
          });
        }
        break;
      }
      case 'Mute': {
        // Handle room mute status
        const roomId = content.roomId;
        const isMute = content.mute ?? false;

        const room = await this.aileLocalService?.getRoomById(roomId);
        
        if (room != null) {
          room.isMute = isMute;
          await this.roomDbService?.updateRoomMuteStatus(roomId, isMute);
          this.emit('roomChanged', {
            action: RoomAction.muteStatusChanged,
            room: room,
            roomId: roomId,
            message: null,
          });
        }
        break;
      }
      default: {
        break;
      }
    }
  }

  Aile_Notice_ServiceNumber(args: any[]) {
    const content = args[0];
    const tenantId = args[1];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    const isOff = args[4];
    logService.debug('Aile_Notice_ServiceNumber data : ', { content, event, tenantId, isOff });
  }

  Aile_Notice_ServiceMember(args: any[]) {
    const content = args[0];
    const tenantId = args[1];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    const isOff = args[4];
    logService.debug('Aile_Notice_ServiceMember data : ', { content, event, tenantId, isOff });
  }

  async Aile_Notice_Message(args: any[]) {
    const content = args[0];

    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    switch (event) {
      case 'Received': {
        // Handle message received status
        const memberId = content.memberId;
        const roomId = content.roomId;
        const lastReceivedSequence = content.lastReceivedSequence;
        
        stateService.notifyMemberChanged({
          action: MemberChangedAction.Received,
          roomId: roomId,
          memberId: memberId,
          sequence: lastReceivedSequence,
        });
        break;
      }
      case 'Read': {
        // Handle message read status
        const memberId = content.memberId;
        const roomId = content.roomId;
        const lastReadSequence = content.lastReadSequence;
        
        stateService.notifyMemberChanged({
          action: MemberChangedAction.Readed,
          roomId: roomId,
          memberId: memberId,
          sequence: lastReadSequence,
        });
        break;
      }
      default: {
        break;
      }
    }
  }

  async Aile_Notice_RoomMember(args: any[]) {
    const content = args[0];

    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    switch (event) {
      case 'Add': {
        // Handle room member add
        const roomId = content.roomId;
        const roomFetch = await this.aileOnlineService?.fetchRoomItem(roomId);
        
        if (roomFetch?.result) {
          const room = await this.aileLocalService?.getRoomById(roomId);
          stateService.notifyRoomChanged({
            action: RoomAction.add,
            roomId: roomId,
            room: room,
            message: null,
            tenantId: roomFetch.result.tenantCode,
          });
        }
        break;
      }
      default: {
        break;
      }
    }
  }

  async Aile_Notice_Session(args: any[]) {
    const content = args[0];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    } 
    logService.debug('Aile_Notice_Session data : ', { content, event });

    let room = await roomDao.getRoomById(content.roomId);
    if (room) {
      const roomFetch = await fetchRoomItem({ roomId: content.roomId });
      if (roomFetch?.data) {
        room = roomFetch.data;
      }
    }

    if (room) {
      switch (event) {
        case SocketSessionEvent.AgentActive:
        case SocketSessionEvent.DistributeActive:
        case SocketSessionEvent.AgentStop:
        case SocketSessionEvent.Timeout: 
        case SocketSessionEvent.TransferStart:
        case SocketSessionEvent.TransferCancel:
        case SocketSessionEvent.TransferComplete:{
          if(content.agentId) room.agentId = content.agentId;
          if(content.serviceTime) room.serviceTime = content.serviceTime;
          if(content.status)  room.sessionStatus = content.status;
          if(content.reason) room.transferReason = content.reason;
          if(content.acceptorId) room.agentId = content.acceptorId;
          if(event === SocketSessionEvent.TransferComplete){
            room.isTransfer = true;
          }
          await roomDao.upsertRooms([room]);
          if (room && room.id) {  // 確保 room 和 room.id 都不為 undefined
            const store = getStore();
            if (store) {
              store.dispatch(updateRoom({
                roomId: room.id,
                room: room
              }));
            }
          }
          stateService.notifyRoomChanged({
            action: RoomAction.update,
            roomId: content.roomId,
            room: room,
            message: null,
            tenantId: room.tenantId,
          });
          break;
        }
        default: {
          break;
        }
      }

    }


  }
  async Notice_SocketIO(args: any[]) {
    const content = args[0];
    const tenantId = args[1];
    const event = args[2];
    const callback = args[3];
    
    if (callback) {
      callback({ name: 'Aile.Notice', ack: true, action: 'received' });
    }
    
    const isOff = args[4];
    logService.debug('Notice_SocketIO data : ', { content, event, tenantId, isOff });
    
    if (event == 'ForceOffline') {
      this.emit('loginChanged', LogInState.ForceOffline, null);
    }
  }

  /**
   * 清理資源
   */
  public dispose(): void {
    // 取消訂閱 stateService
    if (this.stateUnsubscriber) {
      this.stateUnsubscriber();
      this.stateUnsubscriber = null;
    }
    
    // 清空監聽器
    this.eventEmitter.removeAllListeners();
    
    logService.info('SocketService resources disposed');
  }

  // 添加缺少的方法以修復編譯錯誤
  public connectWithAuth(authInfo: any): void {
    logService.debug('connectWithAuth called', { authInfo });
    // TODO: 實現實際的連接邏輯
  }

  public getStatus(): ConnectionStatus {
    // TODO: 返回實際的連接狀態
    return ConnectionStatus.CONNECTED;
  }

  public isConnected(): boolean {
    // TODO: 返回實際的連接狀態
    return true;
  }

  public send(messageType: any, data: any): void {
    logService.debug('send called', { messageType, data });
    // TODO: 實現實際的發送邏輯
  }

  public confirmPush(pushId: string, status: string): void {
    logService.debug('confirmPush called', { pushId, status });
    // TODO: 實現實際的推送確認邏輯
  }
}

export {
  SocketService,
  SocketMessageAction,
  RoomAction,
  MemberChangedAction,
  RoomType,
  UnReadType,
  MessageType
};

export type { Message, Room, ProcessMessageEvent };

// Create a singleton instance
const socketService = new SocketService();
export default socketService;
