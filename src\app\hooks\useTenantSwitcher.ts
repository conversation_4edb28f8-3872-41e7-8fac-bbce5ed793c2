import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../hooks';
import { setCurrentTenantId, setCurrentTenantInfo } from '../slices/tenantSlice';
import { resetContactsByTenant } from '../slices/contactSlice';
import stateService from '../../services/stateService';
import { logService } from '../../services/system/logService';
import { setLocalStorage } from '../../utils/storage';
import { ConstantUtil } from '../../utils/constantUtil';
import type { Tenant } from '../../services/db/initSql';

/**
 * 處理租戶切換的 Hook，確保數據在 Redux 和 stateService 之間同步
 */
export const useTenantSwitcher = () => {
  const dispatch = useAppDispatch();
  const currentTenantId = useAppSelector((state) => state.tenant.currentTenantId);

  /**
   * 切換當前租戶
   * @param tenantId 租戶ID
   * @param tenantInfo 可選的租戶詳細信息
   */
  const switchTenant = useCallback((tenantId: string, tenantInfo?: Tenant | null) => {
    if (!tenantId) {
      logService.warn('嘗試切換到無效的租戶ID');
      return false;
    }

    try {
      // 0. 如果切换到相同租户，直接返回
      if (currentTenantId === tenantId) {
        logService.debug('切換到相同租戶，跳過處理', { tenantId });
        return true;
      }

      // 1. 清空当前租户的联系人数据（如果存在当前租户）
      if (currentTenantId) {
        logService.debug('清空當前租戶聯繫人數據', { currentTenantId });
        dispatch(resetContactsByTenant({ tenantId: currentTenantId }));
      }

      // 2. 更新 Redux 状态到新租户
      dispatch(setCurrentTenantId(tenantId));

      // 3. 更新 stateService
      stateService.setTenantId(tenantId);

      // 4. 保存到 localStorage (redundant with Redux, but just to be safe)
      setLocalStorage(ConstantUtil.CURRENT_TENANT_ID_KEY, tenantId);

      // 5. 如果提供了租戶詳細信息，也更新它
      if (tenantInfo) {
        dispatch(setCurrentTenantInfo(tenantInfo));

        // 確保租戶代碼也被設置
        if (tenantInfo.code) {
          stateService.setTenantCode(tenantInfo.code);
        }
      }

      logService.info('成功切換租戶', {
        from: currentTenantId,
        to: tenantId
      });
      return true;
    } catch (error) {
      logService.error('租戶切換失敗', { tenantId, error });
      return false;
    }
  }, [dispatch, currentTenantId]);
  
  return {
    switchTenant
  };
};

export default useTenantSwitcher; 