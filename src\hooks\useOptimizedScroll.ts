import { useRef, useCallback, useEffect, useState } from 'react';
import { logService } from '@/services/system/logService';

export interface ScrollState {
  scrollTop: number;
  scrollHeight: number;
  clientHeight: number;
  isScrolling: boolean;
  isNearTop: boolean;
  isNearBottom: boolean;
}

export interface UseOptimizedScrollOptions {
  loadMoreThreshold?: number; // 觸發載入更多的閾值（像素）
  loadMoreThresholdPercent?: number; // 觸發載入更多的閾值（百分比）
  debounceMs?: number; // 防抖延遲
  onLoadMore?: () => void;
  onScroll?: (state: ScrollState) => void;
  maintainScrollPosition?: boolean; // 是否保持滾動位置
  autoScrollToBottom?: boolean; // 是否自動滾動到底部
}

/**
 * 優化的滾動Hook
 * 提供高性能的滾動處理和位置保持功能
 */
export const useOptimizedScroll = (options: UseOptimizedScrollOptions = {}) => {
  const {
    loadMoreThreshold = 100,
    loadMoreThresholdPercent = 0.1, // 10%
    debounceMs = 16, // 60fps
    onLoadMore,
    onScroll,
    maintainScrollPosition = false,
    autoScrollToBottom = false
  } = options;

  const scrollElementRef = useRef<HTMLDivElement>(null);
  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollTop: 0,
    scrollHeight: 0,
    clientHeight: 0,
    isScrolling: false,
    isNearTop: false,
    isNearBottom: false
  });

  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTopRef = useRef(0);
  const isLoadingMoreRef = useRef(false);
  const previousScrollHeightRef = useRef(0);

  // 計算滾動狀態
  const calculateScrollState = useCallback((element: HTMLDivElement): ScrollState => {
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    // 修复顶部触发逻辑：确保在最顶部时能够触发
    const isAtTop = scrollTop <= 2; // 允许2px的误差，确保在最顶部时触发
    const isWithinThreshold = scrollTop <= loadMoreThreshold;
    const isWithinPercentage = scrollHeight > 0 && (scrollTop / scrollHeight) <= loadMoreThresholdPercent;

    // 优先级：最顶部 > 距离阈值 > 百分比阈值
    const isNearTop = isAtTop || isWithinThreshold || isWithinPercentage;

    // 调试日志（仅在接近顶部时输出）
    if (scrollTop <= 50) {
      logService.debug('📊 滚动状态检测', {
        scrollTop,
        scrollHeight,
        clientHeight,
        isAtTop,
        isWithinThreshold,
        isWithinPercentage,
        isNearTop,
        loadMoreThreshold,
        loadMoreThresholdPercent,
        scrollPercentage: scrollHeight > 0 ? (scrollTop / scrollHeight).toFixed(4) : '0'
      });
    }
    const isNearBottom = (scrollHeight - scrollTop - clientHeight) <= 50;

    return {
      scrollTop,
      scrollHeight,
      clientHeight,
      isScrolling: true,
      isNearTop,
      isNearBottom
    };
  }, [loadMoreThreshold, loadMoreThresholdPercent]);

  // 防抖的滾動處理函數
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const newScrollState = calculateScrollState(element);
    
    setScrollState(newScrollState);
    
    // 觸發載入更多
    if (newScrollState.isNearTop && onLoadMore && !isLoadingMoreRef.current) {
      isLoadingMoreRef.current = true;

      onLoadMore();

      // 重置載入標記
      setTimeout(() => {
        isLoadingMoreRef.current = false;
      }, 1000);
    }

    // 觸發外部滾動回調
    if (onScroll) {
      onScroll(newScrollState);
    }

    // 清除之前的超時
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 設置滾動結束標記
    scrollTimeoutRef.current = setTimeout(() => {
      setScrollState(prev => ({ ...prev, isScrolling: false }));
    }, debounceMs * 3);

    lastScrollTopRef.current = newScrollState.scrollTop;
  }, [calculateScrollState, onLoadMore, onScroll, debounceMs]);

  // 滾動到底部
  const scrollToBottom = useCallback((smooth = true) => {
    if (scrollElementRef.current) {
      const element = scrollElementRef.current;
      if (smooth) {
        element.scrollTo({
          top: element.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        element.scrollTop = element.scrollHeight;
      }
    }
  }, []);

  // 立即滾動到底部（無動畫）
  const scrollToBottomImmediate = useCallback(() => {
    scrollToBottom(false);
  }, [scrollToBottom]);

  // 保持滾動位置（當內容在頂部添加時）
  const maintainPosition = useCallback(() => {
    if (!maintainScrollPosition || !scrollElementRef.current) return;

    const element = scrollElementRef.current;
    const currentScrollHeight = element.scrollHeight;
    const previousScrollHeight = previousScrollHeightRef.current;

    if (previousScrollHeight > 0 && currentScrollHeight > previousScrollHeight) {
      const heightDiff = currentScrollHeight - previousScrollHeight;
      const currentScrollTop = element.scrollTop;

      // 只有當用戶不在底部時才保持位置（避免在底部時也調整位置）
      const isAtBottom = (currentScrollHeight - currentScrollTop - element.clientHeight) <= 50;

      if (!isAtBottom) {
        const newScrollTop = currentScrollTop + heightDiff;

        // 使用 requestAnimationFrame 確保 DOM 更新完成
        requestAnimationFrame(() => {
          if (element && element.scrollTop === currentScrollTop) {
            element.scrollTop = newScrollTop;
            lastScrollTopRef.current = newScrollTop;

            logService.debug('保持滾動位置', {
              heightDiff,
              newScrollTop,
              currentScrollTop,
              previousScrollHeight,
              currentScrollHeight,
              isAtBottom
            });
          }
        });
      } else {
        // 如果在底部，更新記錄的滾動位置
        lastScrollTopRef.current = currentScrollTop;
      }
    }

    previousScrollHeightRef.current = currentScrollHeight;
  }, [maintainScrollPosition]);

  // 監聽內容變化以保持滾動位置
  useEffect(() => {
    if (maintainScrollPosition && scrollElementRef.current) {
      const element = scrollElementRef.current;
      let timeoutId: NodeJS.Timeout;

      // 使用 MutationObserver 監聽 DOM 變化
      const observer = new MutationObserver((mutations) => {
        // 檢查是否有實際的內容變化
        const hasContentChange = mutations.some(mutation =>
          mutation.type === 'childList' &&
          (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)
        );

        if (hasContentChange) {
          // 防抖處理，避免頻繁調整
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => {
            maintainPosition();
          }, 10);
        }
      });

      observer.observe(element, {
        childList: true,
        subtree: true,
        attributes: false
      });

      return () => {
        observer.disconnect();
        clearTimeout(timeoutId);
      };
    }
  }, [maintainScrollPosition, maintainPosition]);

  // 自動滾動到底部
  useEffect(() => {
    if (autoScrollToBottom && !scrollState.isScrolling && scrollElementRef.current) {
      // 延遲執行以確保內容已渲染
      setTimeout(() => {
        scrollToBottomImmediate();
      }, 50);
    }
  }, [autoScrollToBottom, scrollState.isScrolling, scrollToBottomImmediate]);

  // 處理視窗大小變化（鍵盤彈出/收起）
  useEffect(() => {
    const handleResize = () => {
      if (autoScrollToBottom) {
        scrollToBottomImmediate();
      }
    };

    window.addEventListener('resize', handleResize);
    
    // 移動端特定事件處理
    if ('visualViewport' in window) {
      window.visualViewport?.addEventListener('resize', handleResize);
      window.visualViewport?.addEventListener('scroll', handleResize);
    }
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if ('visualViewport' in window) {
        window.visualViewport?.removeEventListener('resize', handleResize);
        window.visualViewport?.removeEventListener('scroll', handleResize);
      }
    };
  }, [autoScrollToBottom, scrollToBottomImmediate]);

  // 清理定時器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 獲取當前滾動位置百分比
  const getScrollPercentage = useCallback(() => {
    if (!scrollElementRef.current) return 0;
    
    const element = scrollElementRef.current;
    const { scrollTop, scrollHeight, clientHeight } = element;
    
    if (scrollHeight <= clientHeight) return 100;
    
    return (scrollTop / (scrollHeight - clientHeight)) * 100;
  }, []);

  // 滾動到指定百分比位置
  const scrollToPercentage = useCallback((percentage: number, smooth = true) => {
    if (!scrollElementRef.current) return;
    
    const element = scrollElementRef.current;
    const { scrollHeight, clientHeight } = element;
    const maxScrollTop = scrollHeight - clientHeight;
    const targetScrollTop = (percentage / 100) * maxScrollTop;
    
    if (smooth) {
      element.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    } else {
      element.scrollTop = targetScrollTop;
    }
  }, []);

  return {
    scrollElementRef,
    scrollState,
    handleScroll,
    scrollToBottom,
    scrollToBottomImmediate,
    maintainPosition,
    getScrollPercentage,
    scrollToPercentage
  };
};
