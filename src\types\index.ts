/**
 * 类型定义统一入口
 * 按功能模块重新组织的类型导出
 */

// API相关类型
export * from './api';

// 组件相关类型
export * from './components';

// 服务相关类型
export * from './services';

// 状态管理相关类型
export * from './store';

// 通用类型
export * from './common';

// 外部库类型
// export * from './external';

// 为了保持向后兼容性，重新导出原有类型
// 这些可以在迁移完成后逐步移除
export * from './aile.enum';
export * from './chat.types';
export * from './message.types';
export * from './notification.types';
export * from './room.types';
export * from './socket.types';
