import { useState, useCallback } from 'react';
import { tenantService } from '../services';
import type { Tenant } from '../services/db/initSql';

/**
 * 租戶關聯列表請求參數類型
 */
export interface RelationTenantListParams {
  pageIndex?: number;
  pageSize?: number;
  key?: string;
  refreshTime?: number;
  accountId?: string;
  channel?: string;
}

/**
 * 租戶關聯列表響應類型
 */
export interface RelationTenantListResponse {
  status: number;
  success: boolean;
  data: Array<Tenant & { relationId?: string; openId?: string }>;
  msg: string;
  code: string;
  timeCost?: number;
}

/**
 * 租戶 API Hook
 * 提供租戶相關 API 的調用方法
 */
export function useTenantApi() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * 獲取關聯租戶列表
   */
  const getRelationTenantList = useCallback(async (params: RelationTenantListParams): Promise<RelationTenantListResponse> => {
    setLoading(true);
    setError(null);

    try {
      const response = await tenantService.getTenantRelationList(params);
      return response;
    } catch (err) {
      const error = err as Error;
      setError(error);
      return {
        status: -1,
        success: false,
        data: [],
        msg: error.message || '獲取關聯租戶列表失敗',
        code: '-1'
      };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 獲取租戶詳情
   */
  const getTenantById = useCallback(async (id: string): Promise<Tenant | null> => {
    setLoading(true);
    setError(null);

    try {
      const tenant = await tenantService.getTenantById(id);
      return tenant;
    } catch (err) {
      const error = err as Error;
      setError(error);
      return null;
    } finally {
      setLoading(false);
    }
    }, []);

  /**
   * 保存租戶信息
   */
  const saveTenant = useCallback(async (tenant: Tenant): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const success = await tenantService.saveTenant(tenant);
      return success;
    } catch (err) {
      const error = err as Error;
      setError(error);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 刪除租戶
   */
  const deleteTenant = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const success = await tenantService.deleteTenant(id);
      return success;
    } catch (err) {
      const error = err as Error;
      setError(error);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    getRelationTenantList,
    getTenantById,
    saveTenant,
    deleteTenant
  };
} 