import { ConstantUtil } from '@/utils/constantUtil';
import aileDBService from '../db/aileDBService';
import { getLocalStorage } from '@/utils/storage';
import { logService } from '../system/logService';
import type { IServiceNumberInfo } from '@/services/core/tenant/snService';

class ServiceNumberDao {
  /**
   * 插入或更新 ServiceNumber 資料
   * @param info 商務號資料
   * @returns 是否成功
   */
  public async upsertServiceNumber(info: IServiceNumberInfo): Promise<boolean> {
    try {
      // DAO 層自動補 tenantId
      const tenantId = (info as any).tenantId || getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      if (!tenantId) {
        logService.warn('ServiceNumberDao: 無法保存商務號，tenantId 為空', { info });
        return false;
      }
      const now = Date.now();
      const sql = `REPLACE INTO ServiceNumber (id, name, type, code, description, openType, status, avatarId, tenantId, memberRoomId, createTime, updateTime)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
      await aileDBService.exec(sql, [
        info.id,
        info.name || '',
        info.type || '',
        info.code || '',
        info.description || '',
        info.openType || '',
        info.status || '',
        info.avatarId || '',
        tenantId,
        info.memberRoomId || '',
        info.createTime || now,
        now
      ]);
      logService.info('ServiceNumberDao: 商務號資料已保存', { id: info.id, tenantId });
      return true;
    } catch (error) {
      logService.error('ServiceNumberDao: 保存商務號資料失敗', { error: error as Error, info });
      return false;
    }
  }

  /**
   * 根據ID獲取服務號資訊
   * @param id 服務號ID
   * @returns 服務號資訊
   */
  public async getServiceNumberById(id: string): Promise<IServiceNumberInfo | null> {
    try {
      const tenantId = getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      if (!tenantId) {
        logService.warn('ServiceNumberDao: 無法查詢服務號，tenantId 為空', { id });
        return null;
      }
      
      const serviceNumber = await aileDBService.get<IServiceNumberInfo>(
        `SELECT * FROM ServiceNumber WHERE id = ? AND tenantId = ?`,
        [id, tenantId]
      );
      
      if (!serviceNumber) {
        logService.debug('ServiceNumberDao: 未找到指定ID的服務號', { id, tenantId });
        return null;
      }
      
      return serviceNumber;
    } catch (error) {
      logService.error('ServiceNumberDao: 獲取服務號資訊失敗', { error: error as Error, id });
      return null;
    }
  }
}

const serviceNumberDao = new ServiceNumberDao();
export default serviceNumberDao;
