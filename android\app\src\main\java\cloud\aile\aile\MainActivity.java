package cloud.aile.aile;

import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.webkit.WebSettings;
import android.webkit.WebView;
import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 启用WebView调试，帮助排查空白屏幕问题
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
    }

    @Override
    public void onStart() {
        super.onStart();

        // Android 9+ 兼容性修复
        configureWebViewForAndroid9();
    }

    /**
     * 配置 WebView 以兼容 Android 9+
     */
    private void configureWebViewForAndroid9() {
        // 延迟配置 WebView，确保 Bridge 已经初始化
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (bridge != null) {
                WebView webView = bridge.getWebView();
                if (webView != null) {
                    WebSettings webSettings = webView.getSettings();

                    // Android 9+ 特定配置
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                        // 允许混合内容（HTTP 和 HTTPS）
                        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

                        // 启用文件访问
                        webSettings.setAllowFileAccess(true);
                        webSettings.setAllowFileAccessFromFileURLs(true);
                        webSettings.setAllowUniversalAccessFromFileURLs(true);

                        // 启用 DOM 存储
                        webSettings.setDomStorageEnabled(true);

                        // 启用数据库存储
                        webSettings.setDatabaseEnabled(true);

                        // 设置用户代理
                        String userAgent = webSettings.getUserAgentString();
                        webSettings.setUserAgentString(userAgent + " CapacitorAndroid");

                        // 启用 JavaScript
                        webSettings.setJavaScriptEnabled(true);

                        // 设置缓存模式
                        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);

                        android.util.Log.d("MainActivity", "Android 9+ WebView 配置完成");
                    }
                } else {
                    android.util.Log.w("MainActivity", "WebView 为 null，稍后重试");
                    // 如果 WebView 还没准备好，再次尝试
                    new Handler(Looper.getMainLooper()).postDelayed(this::configureWebViewForAndroid9, 500);
                }
            }
        }, 100);
    }
}
