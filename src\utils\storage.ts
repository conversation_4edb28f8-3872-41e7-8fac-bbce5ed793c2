// 本地存储工具函数

/**
 * 存储数据到 localStorage
 */
export const setLocalStorage = <T>(key: string, value: T): void => {
  try {
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error('Error saving to localStorage', error);
  }
};

/**
 * 从 localStorage 获取数据
 */
export const getLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const serializedValue = localStorage.getItem(key);
    if (serializedValue === null) {
      return defaultValue;
    }
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error('Error reading from localStorage', error);
    return defaultValue;
  }
};

/**
 * 从 localStorage 删除数据
 */
export const removeLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing from localStorage', error);
  }
};

/**
 * 清空 localStorage
 */
export const clearLocalStorage = (): void => {
  try {
    localStorage.clear();
  } catch (error) {
    console.error('Error clearing localStorage', error);
  }
};

import { ConstantUtil } from './constantUtil';

export function getPinnedRoomIds(): string[] {
  return getLocalStorage<string[]>(ConstantUtil.ROOM_PINNED_IDS_KEY, []);
}

export function setPinnedRoomIds(ids: string[]): void {
  setLocalStorage(ConstantUtil.ROOM_PINNED_IDS_KEY, ids);
} 