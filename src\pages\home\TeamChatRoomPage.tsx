import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import ChatRoom from './chat/ChatRoom';
import { ChatRoomType } from '../../types/chat.types';
import { fetchRoomItem } from '@/services/core/chat/roomService';
import { logService } from '@/services/system/logService';
import './TeamChatRoomPage.css';
import { useFixedStatusBarColor } from '../../hooks/useStatusBarColor';
import { PAGE_COLORS } from '../../config/app/pageColors';

const TeamChatRoomPage: React.FC = () => {
  const navigate = useNavigate();
  const { roomId } = useParams<{ roomId: string }>();
  const [roomInfo, setRoomInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 设置团队聊天室状态栏颜色
  useFixedStatusBarColor(PAGE_COLORS.CHAT.TEAM);

  const handleBack = () => {
    navigate(-1);
  };

  useEffect(() => {
    if (!roomId) return;
    setError(null);

    logService.info('開始獲取團隊聊天室資訊', { roomId });

    fetchRoomItem({ roomId })
      .then((res) => {
        const roomData = res?.data || null;
        logService.info('團隊聊天室資訊獲取成功', {
          roomId,
          hasData: !!roomData,
          lastSequence: roomData?.lastSequence
        });
        setRoomInfo(roomData);
      })
      .catch((err) => {
        logService.error('房間資訊獲取失敗', { error: err, roomId });
        setError('房間資訊獲取失敗');
      });
  }, [roomId]);

  return (
    <div className="team-chat-room-page">
      {error ? (
        <div className="page-error">
          <span>{error}</span>
          <button onClick={() => window.location.reload()}>重新載入</button>
        </div>
      ) : (
        <ChatRoom
          type={ChatRoomType.TEAM}
          title={'團隊聊天室'}
          dateGroups={[]}
          roomId={roomId || ''}
          roomInfo={roomInfo}
          memberCount={roomInfo?.memberCount || 0}
          onBackClick={handleBack}
        />
      )}
    </div>
  );
};

export default TeamChatRoomPage; 