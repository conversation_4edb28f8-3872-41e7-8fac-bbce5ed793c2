import { logService } from '../system/logService';
import kvStoreService from '../system/kvStoreService';
import { RelationTenantVO } from '../core/tenant/tenantTypes';
import { getLocalStorage } from '../../utils/storage';
import { ConstantUtil } from '../../utils/constantUtil';

/**
 * 租戶緩存管理器
 * 提供租戶數據的智能緩存管理功能
 */
export class TenantCacheManager {
  private static instance: TenantCacheManager;
  
  // 緩存配置
  private readonly config = {
    /** 緩存過期時間（毫秒），默認 5 分鐘 */
    ttl: 5 * 60 * 1000,
    /** 強制刷新間隔（毫秒），默認 30 分鐘 */
    forceRefreshInterval: 30 * 60 * 1000,
    /** 緩存鍵前綴 */
    cacheKeyPrefix: 'tenant_relation_list',
    /** 統計信息鍵 */
    statsKey: 'tenant_cache_stats'
  };

  // 內存緩存
  private memoryCache: Map<string, TenantCacheData> = new Map();
  
  // 緩存統計
  private stats = {
    hitCount: 0,
    missCount: 0,
    apiCallCount: 0,
    lastResetTime: Date.now()
  };

  /**
   * 獲取單例實例
   */
  public static getInstance(): TenantCacheManager {
    if (!TenantCacheManager.instance) {
      TenantCacheManager.instance = new TenantCacheManager();
    }
    return TenantCacheManager.instance;
  }

  private constructor() {
    this.loadStats();
    logService.info('租戶緩存管理器初始化完成');
  }

  /**
   * 獲取緩存鍵
   */
  private getCacheKey(accountId?: string): string {
    const actualAccountId = accountId || getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
    return `${this.config.cacheKeyPrefix}_${actualAccountId || 'default'}`;
  }

  /**
   * 檢查緩存是否有效
   */
  private isCacheValid(cacheData: TenantCacheData): boolean {
    const now = Date.now();
    return (now - cacheData.timestamp) <= this.config.ttl;
  }

  /**
   * 檢查是否需要強制刷新
   */
  private shouldForceRefresh(cacheData: TenantCacheData): boolean {
    const now = Date.now();
    return (now - cacheData.timestamp) > this.config.forceRefreshInterval;
  }

  /**
   * 從內存緩存獲取數據
   */
  public getFromMemory(accountId?: string): RelationTenantVO[] | null {
    const cacheKey = this.getCacheKey(accountId);
    const cached = this.memoryCache.get(cacheKey);
    
    if (!cached) {
      return null;
    }

    if (this.isCacheValid(cached)) {
      this.stats.hitCount++;
      logService.debug('內存緩存命中', { 
        cacheKey, 
        count: cached.data.length,
        age: Date.now() - cached.timestamp
      });
      return cached.data;
    } else {
      // 緩存過期，清除
      this.memoryCache.delete(cacheKey);
      return null;
    }
  }

  /**
   * 從持久化存儲獲取數據
   */
  public async getFromStorage(accountId?: string): Promise<RelationTenantVO[] | null> {
    try {
      const cacheKey = this.getCacheKey(accountId);
      const cachedStr = await kvStoreService.getValue(cacheKey);
      
      if (!cachedStr) {
        this.stats.missCount++;
        return null;
      }

      const cached: TenantCacheData = JSON.parse(cachedStr);
      
      if (this.isCacheValid(cached)) {
        // 更新內存緩存
        this.memoryCache.set(cacheKey, cached);
        this.stats.hitCount++;
        
        logService.debug('持久化緩存命中', { 
          cacheKey, 
          count: cached.data.length,
          age: Date.now() - cached.timestamp
        });
        return cached.data;
      } else {
        // 緩存過期，清除
        await kvStoreService.deleteValue(cacheKey);
        this.stats.missCount++;
        return null;
      }
    } catch (error) {
      logService.error('從持久化存儲獲取租戶緩存失敗', { error });
      this.stats.missCount++;
      return null;
    }
  }

  /**
   * 保存數據到緩存
   */
  public async set(data: RelationTenantVO[], accountId?: string): Promise<void> {
    try {
      const now = Date.now();
      const cacheKey = this.getCacheKey(accountId);
      const cacheData: TenantCacheData = {
        data,
        timestamp: now,
        version: 1
      };

      // 保存到內存緩存
      this.memoryCache.set(cacheKey, cacheData);

      // 保存到持久化存儲
      await kvStoreService.setValue(cacheKey, JSON.stringify(cacheData));
      
      logService.debug('租戶數據已緩存', { 
        cacheKey,
        count: data.length, 
        timestamp: now 
      });
    } catch (error) {
      logService.error('保存租戶緩存失敗', { error });
    }
  }

  /**
   * 檢查緩存狀態
   */
  public async getCacheStatus(accountId?: string): Promise<{
    hasMemoryCache: boolean;
    hasStorageCache: boolean;
    isValid: boolean;
    shouldRefresh: boolean;
    age?: number;
    count?: number;
  }> {
    const cacheKey = this.getCacheKey(accountId);
    
    // 檢查內存緩存
    const memoryCache = this.memoryCache.get(cacheKey);
    const hasMemoryCache = !!memoryCache;
    
    // 檢查持久化緩存
    let storageCache: TenantCacheData | null = null;
    try {
      const cachedStr = await kvStoreService.getValue(cacheKey);
      if (cachedStr) {
        storageCache = JSON.parse(cachedStr);
      }
    } catch (error) {
      logService.error('檢查持久化緩存狀態失敗', { error });
    }
    
    const hasStorageCache = !!storageCache;
    const latestCache = memoryCache || storageCache;
    
    if (!latestCache) {
      return {
        hasMemoryCache: false,
        hasStorageCache: false,
        isValid: false,
        shouldRefresh: true
      };
    }

    const isValid = this.isCacheValid(latestCache);
    const shouldRefresh = this.shouldForceRefresh(latestCache);
    const age = Date.now() - latestCache.timestamp;

    return {
      hasMemoryCache,
      hasStorageCache,
      isValid,
      shouldRefresh,
      age,
      count: latestCache.data.length
    };
  }

  /**
   * 清除指定賬戶的緩存
   */
  public async clear(accountId?: string): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(accountId);
      
      // 清除內存緩存
      this.memoryCache.delete(cacheKey);
      
      // 清除持久化緩存
      await kvStoreService.deleteValue(cacheKey);
      
      logService.info('租戶緩存已清除', { cacheKey });
    } catch (error) {
      logService.error('清除租戶緩存失敗', { error });
    }
  }

  /**
   * 清除所有緩存
   */
  public async clearAll(): Promise<void> {
    try {
      // 清除內存緩存
      this.memoryCache.clear();
      
      // 清除持久化緩存（需要遍歷所有可能的鍵）
      // 這裡可以根據實際需求實現更精確的清除邏輯
      
      logService.info('所有租戶緩存已清除');
    } catch (error) {
      logService.error('清除所有租戶緩存失敗', { error });
    }
  }

  /**
   * 獲取緩存統計信息
   */
  public getStats(): typeof this.stats {
    return { ...this.stats };
  }

  /**
   * 重置統計信息
   */
  public resetStats(): void {
    this.stats = {
      hitCount: 0,
      missCount: 0,
      apiCallCount: 0,
      lastResetTime: Date.now()
    };
    this.saveStats();
  }

  /**
   * 記錄 API 調用
   */
  public recordApiCall(): void {
    this.stats.apiCallCount++;
    this.saveStats();
  }

  /**
   * 加載統計信息
   */
  private async loadStats(): Promise<void> {
    try {
      const statsStr = await kvStoreService.getValue(this.config.statsKey);
      if (statsStr) {
        this.stats = { ...this.stats, ...JSON.parse(statsStr) };
      }
    } catch (error) {
      logService.error('加載緩存統計信息失敗', { error });
    }
  }

  /**
   * 保存統計信息
   */
  private async saveStats(): Promise<void> {
    try {
      await kvStoreService.setValue(this.config.statsKey, JSON.stringify(this.stats));
    } catch (error) {
      logService.error('保存緩存統計信息失敗', { error });
    }
  }
}

/**
 * 租戶緩存數據結構
 */
export interface TenantCacheData {
  /** 租戶列表數據 */
  data: RelationTenantVO[];
  /** 緩存時間戳 */
  timestamp: number;
  /** 數據版本號 */
  version: number;
}

// 導出單例實例
export const tenantCacheManager = TenantCacheManager.getInstance();
export default tenantCacheManager;
