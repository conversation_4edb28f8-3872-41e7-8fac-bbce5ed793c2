// 核心业务服务统一导出

export * from './auth';

// 明确导出 tenant 模块，排除冲突的 Type
export {
  tenantService,
  contactService,
  attachmentService,
  userService
} from './tenant';

// 明确导出类型，避免冲突
export { Type as TenantType, CertificateStatus } from './tenant/tenantTypes';

// 明确导出 chat 模块
export { messageService } from './chat';
export * from './chat/roomService';
export { RoomType } from './chat/roomService.types';
