[0KRunning with gitlab-runner 17.7.0 (3153ccc6)[0;m
[0K  on OSXdeMac-mini.local t2_wm8Q2_, system ID: s_37cc550b00d4[0;m
[0K[36;1mResolving secrets[0;m[0;m
section_start:1753954537:prepare_executor
[0K[0K[36;1mPreparing the "shell" executor[0;m[0;m
[0KUsing Shell (bash) executor...[0;m
section_end:1753954537:prepare_executor
[0Ksection_start:1753954537:prepare_script
[0K[0K[36;1mPreparing environment[0;m[0;m
Running on Aile-GitLab-Runner-OSX-002.local...
section_end:1753954537:prepare_script
[0Ksection_start:1753954537:get_sources
[0K[0K[36;1mGetting source from Git repository[0;m[0;m
[32;1mFetching changes with git depth set to 20...[0;m
Reinitialized existing Git repository in /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/.git/
[32;1mChecking out 06baa5df as detached HEAD (ref is dev)...[0;m
Removing .bundle/
Removing dist/assets/index-ELp83gAs.js
Removing dist/assets/index-rAcyCrQ3.css
Removing dist/assets/index-rBBYHqgs.js
Removing dist/assets/web-6I8s8KNK.js
Removing dist/assets/web-BYxD7UEB.js
Removing dist/assets/web-B_TjSmCb.js
Removing dist/assets/web-CXWnLpjF.js
Removing dist/assets/web-D5rLx4X_.js
Removing dist/assets/web-DCjm9-5y.js
Removing dist/assets/web-DYR9ntCl.js
Removing dist/assets/web-tP-Cei8y.js
Removing fastlane/certificate.p12
Removing fastlane/profile.mobileprovision
Removing ios/App/App.xcodeproj/project.xcworkspace/
Removing ios/App/App.xcworkspace/xcshareddata/
Removing ios/App/App/capacitor.config.json
Removing ios/App/App/config.xml
Removing ios/App/App/public/
Removing ios/App/Pods/
Removing ios/build/
Removing ios/capacitor-cordova-ios-plugins/
Removing node_modules/
Removing vendor/
[32;1mSkipping Git submodules setup[0;m
section_end:1753954544:get_sources
[0Ksection_start:1753954544:restore_cache
[0K[0K[36;1mRestoring cache[0;m[0;m
[32;1mChecking cache for dev-ios-1-non_protected...[0;m
Runtime platform                                  [0;m  arch[0;m=arm64 os[0;m=darwin pid[0;m=91240 revision[0;m=3153ccc6 version[0;m=17.7.0
[0;33mWARNING: file does not exist                      [0;m 
[0;33mFailed to extract cache[0;m
section_end:1753954544:restore_cache
[0Ksection_start:1753954544:step_script
[0K[0K[36;1mExecuting "step_script" stage of the job script[0;m[0;m
[32;1m$ export LANG=en_US.UTF-8[0;m
[32;1m$ export LC_ALL=en_US.UTF-8[0;m
[32;1m$ export DEVELOPER_TEAM_ID=${DEVELOPER_TEAM_ID}[0;m
[32;1m$ export IOS_CERTIFICATE_BASE64=${IOS_CERTIFICATE_BASE64}[0;m
[32;1m$ export IOS_CERTIFICATE_PASSWORD=${IOS_CERTIFICATE_PASSWORD}[0;m
[32;1m$ export PROVISIONING_PROFILE_BASE64=${PROVISIONING_PROFILE_BASE64}[0;m
[32;1m$ export APPLE_ID=${APPLE_ID}[0;m
[32;1m$ export APP_IDENTIFIER="cloud.aile.aile"[0;m
[32;1m$ bundle config set path 'vendor/bundle'[0;m
[32;1m$ bundle check || bundle install --jobs 4[0;m
The following gems are missing
 * fastlane (2.228.0)
 * cocoapods (1.16.2)
 * fastlane-plugin-firebase_app_distribution (0.10.1)
 * xcpretty (0.4.1)
 * xcode-install (2.8.1)
 * fastlane-plugin-google_cloud_storage_rebooted (1.0.5)
 * fastlane-plugin-versioning (0.7.1)
 * CFPropertyList (3.0.7)
 * addressable (2.8.7)
 * artifactory (3.0.17)
 * aws-sdk-s3 (1.190.0)
 * babosa (1.0.4)
 * colored (1.2)
 * commander (4.6.0)
 * dotenv (2.8.1)
 * emoji_regex (3.2.3)
 * excon (0.112.0)
 * faraday (1.10.4)
 * faraday-cookie_jar (0.0.7)
 * faraday_middleware (1.2.1)
 * fastimage (2.4.0)
 * fastlane-sirp (1.0.0)
 * gh_inspector (1.1.3)
 * google-apis-androidpublisher_v3 (0.54.0)
 * google-apis-playcustomapp_v1 (0.13.0)
 * google-cloud-env (1.6.0)
 * google-cloud-storage (1.47.0)
 * highline (2.0.3)
 * http-cookie (1.0.8)
 * json (2.12.2)
 * jwt (2.10.1)
 * mini_magick (4.13.2)
 * multipart-post (2.4.1)
 * naturally (2.3.0)
 * optparse (0.6.0)
 * plist (3.7.2)
 * rubyzip (2.4.1)
 * security (0.1.5)
 * simctl (1.6.10)
 * terminal-notifier (2.0.0)
 * terminal-table (3.0.2)
 * tty-screen (0.8.2)
 * tty-spinner (0.9.3)
 * word_wrap (1.0.0)
 * xcodeproj (1.27.0)
 * xcpretty-travis-formatter (1.0.1)
 * claide (1.1.0)
 * cocoapods-core (1.16.2)
 * cocoapods-deintegrate (1.0.5)
 * cocoapods-downloader (2.1)
 * cocoapods-plugins (1.0.0)
 * cocoapods-search (1.0.1)
 * cocoapods-trunk (1.6.0)
 * cocoapods-try (1.2.0)
 * colored2 (3.1.2)
 * escape (0.0.4)
 * fourflusher (2.3.1)
 * molinillo (0.8.0)
 * nap (1.1.0)
 * ruby-macho (2.5.1)
 * google-apis-firebaseappdistribution_v1 (0.3.0)
 * google-apis-firebaseappdistribution_v1alpha (0.2.0)
 * rouge (3.28.0)
 * base64 (0.3.0)
 * nkf (0.2.0)
 * rexml (3.4.1)
 * public_suffix (4.0.7)
 * aws-sdk-core (3.226.0)
 * aws-sdk-kms (1.105.0)
 * aws-sigv4 (1.12.1)
 * faraday-em_http (1.0.0)
 * faraday-em_synchrony (1.0.1)
 * faraday-excon (1.1.0)
 * faraday-httpclient (1.0.1)
 * faraday-multipart (1.1.1)
 * faraday-net_http (1.0.2)
 * faraday-net_http_persistent (1.2.0)
 * faraday-patron (1.0.0)
 * faraday-rack (1.0.0)
 * faraday-retry (1.0.3)
 * sysrandom (1.0.5)
 * google-apis-core (0.11.3)
 * digest-crc (0.7.0)
 * google-apis-iamcredentials_v1 (0.17.0)
 * google-apis-storage_v1 (0.31.0)
 * google-cloud-core (1.8.0)
 * googleauth (1.8.1)
 * mini_mime (1.1.5)
 * domain_name (0.6.20240107)
 * unicode-display_width (2.6.0)
 * tty-cursor (0.7.1)
 * atomos (0.1.3)
 * nanaimo (0.4.0)
 * activesupport (*******)
 * algoliasearch (1.27.5)
 * concurrent-ruby (1.3.5)
 * fuzzy_match (2.0.4)
 * netrc (0.11.0)
 * typhoeus (1.4.1)
 * aws-eventstream (1.4.0)
 * aws-partitions (1.1118.0)
 * jmespath (1.6.2)
 * logger (1.7.0)
 * httpclient (2.9.0)
 * representable (3.2.0)
 * retriable (3.1.2)
 * rake (13.3.0)
 * google-cloud-errors (1.5.0)
 * multi_json (1.15.0)
 * os (1.1.4)
 * signet (0.20.0)
 * i18n (1.14.7)
 * minitest (5.25.5)
 * tzinfo (1.2.11)
 * ethon (0.16.0)
 * mutex_m (0.3.0)
 * declarative (0.0.20)
 * trailblazer-option (0.1.2)
 * uber (0.1.0)
 * thread_safe (0.3.6)
 * ffi (1.17.2)
Install missing gems with `bundle install`
Bundler 2.5.22 is running, but your lockfile was generated with 2.6.9. Installing Bundler 2.6.9 and restarting using that version.
Fetching gem metadata from https://rubygems.org/.
Fetching bundler 2.6.9
Installing bundler 2.6.9
Fetching gem metadata from https://rubygems.org/.......
Fetching rake 13.3.0
Installing rake 13.3.0
Fetching base64 0.3.0
Fetching nkf 0.2.0
Fetching rexml 3.4.1
Fetching concurrent-ruby 1.3.5
Installing base64 0.3.0
Fetching minitest 5.25.5
Installing nkf 0.2.0 with native extensions
Installing rexml 3.4.1
Fetching thread_safe 0.3.6
Installing concurrent-ruby 1.3.5
Installing minitest 5.25.5
Fetching public_suffix 4.0.7
Fetching mutex_m 0.3.0
Installing thread_safe 0.3.6
Fetching json 2.12.2
Installing public_suffix 4.0.7
Fetching artifactory 3.0.17
Installing mutex_m 0.3.0
Fetching atomos 0.1.3
Installing json 2.12.2 with native extensions
Installing artifactory 3.0.17
Fetching aws-eventstream 1.4.0
Installing atomos 0.1.3
Fetching aws-partitions 1.1118.0
Installing aws-eventstream 1.4.0
Fetching jmespath 1.6.2
Installing aws-partitions 1.1118.0
Fetching logger 1.7.0
Installing jmespath 1.6.2
Fetching babosa 1.0.4
Installing logger 1.7.0
Fetching claide 1.1.0
Installing babosa 1.0.4
Fetching fuzzy_match 2.0.4
Installing claide 1.1.0
Fetching nap 1.1.0
Installing fuzzy_match 2.0.4
Fetching netrc 0.11.0
Installing nap 1.1.0
Fetching ffi 1.17.2 (arm64-darwin)
Installing netrc 0.11.0
Fetching cocoapods-deintegrate 1.0.5
Fetching cocoapods-downloader 2.1
Installing ffi 1.17.2 (arm64-darwin)
Fetching cocoapods-search 1.0.1
Installing cocoapods-deintegrate 1.0.5
Fetching cocoapods-try 1.2.0
Installing cocoapods-downloader 2.1
Fetching colored2 3.1.2
Installing cocoapods-search 1.0.1
Fetching escape 0.0.4
Installing cocoapods-try 1.2.0
Fetching fourflusher 2.3.1
Installing colored2 3.1.2
Fetching gh_inspector 1.1.3
Installing escape 0.0.4
Fetching molinillo 0.8.0
Installing fourflusher 2.3.1
Fetching ruby-macho 2.5.1
Installing gh_inspector 1.1.3
Fetching nanaimo 0.4.0
Installing molinillo 0.8.0
Fetching colored 1.2
Installing ruby-macho 2.5.1
Fetching highline 2.0.3
Installing nanaimo 0.4.0
Fetching declarative 0.0.20
Installing colored 1.2
Fetching digest-crc 0.7.0
Installing highline 2.0.3
Fetching domain_name 0.6.20240107
Installing declarative 0.0.20
Fetching dotenv 2.8.1
Installing digest-crc 0.7.0 with native extensions
Installing domain_name 0.6.20240107
Fetching emoji_regex 3.2.3
Installing dotenv 2.8.1
Fetching excon 0.112.0
Installing emoji_regex 3.2.3
Fetching faraday-em_http 1.0.0
Installing excon 0.112.0
Fetching faraday-em_synchrony 1.0.1
Installing faraday-em_http 1.0.0
Fetching faraday-excon 1.1.0
Installing faraday-em_synchrony 1.0.1
Fetching faraday-httpclient 1.0.1
Installing faraday-excon 1.1.0
Fetching multipart-post 2.4.1
Installing faraday-httpclient 1.0.1
Fetching faraday-net_http 1.0.2
Installing multipart-post 2.4.1
Fetching faraday-net_http_persistent 1.2.0
Installing faraday-net_http 1.0.2
Fetching faraday-patron 1.0.0
Installing faraday-net_http_persistent 1.2.0
Fetching faraday-rack 1.0.0
Installing faraday-patron 1.0.0
Fetching faraday-retry 1.0.3
Installing faraday-rack 1.0.0
Fetching ruby2_keywords 0.0.5
Installing faraday-retry 1.0.3
Fetching fastimage 2.4.0
Installing ruby2_keywords 0.0.5
Fetching sysrandom 1.0.5
Installing fastimage 2.4.0
Fetching multi_json 1.15.0
Installing sysrandom 1.0.5 with native extensions
Installing multi_json 1.15.0
Fetching os 1.1.4
Installing os 1.1.4
Fetching mini_mime 1.1.5
Installing mini_mime 1.1.5
Fetching trailblazer-option 0.1.2
Installing trailblazer-option 0.1.2
Fetching uber 0.1.0
Installing uber 0.1.0
Fetching retriable 3.1.2
Fetching google-cloud-errors 1.5.0
Installing retriable 3.1.2
Installing google-cloud-errors 1.5.0
Fetching mini_magick 4.13.2
Fetching naturally 2.3.0
Installing mini_magick 4.13.2
Fetching optparse 0.6.0
Fetching plist 3.7.2
Installing naturally 2.3.0
Fetching rubyzip 2.4.1
Installing optparse 0.6.0
Fetching security 0.1.5
Installing plist 3.7.2
Fetching terminal-notifier 2.0.0
Installing rubyzip 2.4.1
Fetching unicode-display_width 2.6.0
Installing security 0.1.5
Fetching tty-screen 0.8.2
Installing terminal-notifier 2.0.0
Fetching tty-cursor 0.7.1
Installing unicode-display_width 2.6.0
Fetching word_wrap 1.0.0
Installing tty-screen 0.8.2
Fetching rouge 3.28.0
Installing tty-cursor 0.7.1
Fetching fastlane-plugin-versioning 0.7.1
Installing word_wrap 1.0.0
Fetching jwt 2.10.1
Installing rouge 3.28.0
Installing fastlane-plugin-versioning 0.7.1
Fetching i18n 1.14.7
Installing jwt 2.10.1
Fetching tzinfo 1.2.11
Fetching addressable 2.8.7
Installing i18n 1.14.7
Fetching httpclient 2.9.0
Installing tzinfo 1.2.11
Fetching aws-sigv4 1.12.1
Installing addressable 2.8.7
Fetching cocoapods-plugins 1.0.0
Installing httpclient 2.9.0
Fetching cocoapods-trunk 1.6.0
Installing aws-sigv4 1.12.1
Fetching CFPropertyList 3.0.7
Installing cocoapods-plugins 1.0.0
Fetching ethon 0.16.0
Installing cocoapods-trunk 1.6.0
Fetching commander 4.6.0
Installing CFPropertyList 3.0.7
Fetching http-cookie 1.0.8
Installing ethon 0.16.0
Fetching faraday-multipart 1.1.1
Installing commander 4.6.0
Fetching representable 3.2.0
Installing http-cookie 1.0.8
Fetching fastlane-sirp 1.0.0
Installing faraday-multipart 1.1.1
Fetching terminal-table 3.0.2
Installing representable 3.2.0
Fetching tty-spinner 0.9.3
Installing fastlane-sirp 1.0.0
Fetching xcpretty 0.4.1
Installing terminal-table 3.0.2
Fetching activesupport *******
Installing tty-spinner 0.9.3
Fetching algoliasearch 1.27.5
Installing xcpretty 0.4.1
Fetching aws-sdk-core 3.226.0
Installing activesupport *******
Installing algoliasearch 1.27.5
Fetching xcodeproj 1.27.0
Fetching simctl 1.6.10
Installing aws-sdk-core 3.226.0
Installing xcodeproj 1.27.0
Fetching typhoeus 1.4.1
Installing simctl 1.6.10
Fetching faraday 1.10.4
Fetching xcpretty-travis-formatter 1.0.1
Installing typhoeus 1.4.1
Fetching aws-sdk-kms 1.105.0
Installing faraday 1.10.4
Fetching cocoapods-core 1.16.2
Installing xcpretty-travis-formatter 1.0.1
Fetching faraday-cookie_jar 0.0.7
Installing aws-sdk-kms 1.105.0
Fetching faraday_middleware 1.2.1
Installing cocoapods-core 1.16.2
Fetching signet 0.20.0
Installing faraday-cookie_jar 0.0.7
Fetching google-cloud-env 1.6.0
Installing faraday_middleware 1.2.1
Fetching aws-sdk-s3 1.190.0
Installing signet 0.20.0
Fetching cocoapods 1.16.2
Installing google-cloud-env 1.6.0
Fetching googleauth 1.8.1
Installing aws-sdk-s3 1.190.0
Fetching google-cloud-core 1.8.0
Installing cocoapods 1.16.2
Installing googleauth 1.8.1
Fetching google-apis-core 0.11.3
Installing google-cloud-core 1.8.0
Installing google-apis-core 0.11.3
Fetching google-apis-androidpublisher_v3 0.54.0
Fetching google-apis-playcustomapp_v1 0.13.0
Fetching google-apis-iamcredentials_v1 0.17.0
Installing google-apis-androidpublisher_v3 0.54.0
Fetching google-apis-storage_v1 0.31.0
Installing google-apis-playcustomapp_v1 0.13.0
Fetching google-apis-firebaseappdistribution_v1 0.3.0
Installing google-apis-iamcredentials_v1 0.17.0
Fetching google-apis-firebaseappdistribution_v1alpha 0.2.0
Installing google-apis-storage_v1 0.31.0
Installing google-apis-firebaseappdistribution_v1 0.3.0
Installing google-apis-firebaseappdistribution_v1alpha 0.2.0
Fetching fastlane-plugin-firebase_app_distribution 0.10.1
Installing fastlane-plugin-firebase_app_distribution 0.10.1
Fetching google-cloud-storage 1.47.0
Installing google-cloud-storage 1.47.0
Fetching fastlane 2.228.0
Fetching fastlane-plugin-google_cloud_storage_rebooted 1.0.5
Installing fastlane-plugin-google_cloud_storage_rebooted 1.0.5
Installing fastlane 2.228.0
Fetching xcode-install 2.8.1
Installing xcode-install 2.8.1
Bundle complete! 7 Gemfile dependencies, 123 gems now installed.
Bundled gems are installed into `./vendor/bundle`
Post-install message from algoliasearch:
A new major version is available for Algolia! Please now use the https://rubygems.org/gems/algolia gem to get the latest features.
Post-install message from rubyzip:
RubyZip 3.0 is coming!
**********************

The public API of some Rubyzip classes has been modernized to use named
parameters for optional arguments. Please check your usage of the
following classes:
  * `Zip::File`
  * `Zip::Entry`
  * `Zip::InputStream`
  * `Zip::OutputStream`
  * `Zip::DOSTime`

Run your test suite with the `RUBYZIP_V3_API_WARN` environment
variable set to see warnings about usage of the old API. This will
help you to identify any changes that you need to make to your code.
See https://github.com/rubyzip/rubyzip/wiki/Updating-to-version-3.x for
more information.

Please ensure that your Gemfiles and .gemspecs are suitably restrictive
to avoid an unexpected breakage when 3.0 is released (e.g. ~> 2.3.0).
See https://github.com/rubyzip/rubyzip for details. The Changelog also
lists other enhancements and bugfixes that have been implemented since
version 2.3.0.
[32;1m$ export SWIFT_OPTIMIZATION_LEVEL="-Onone"[0;m
[32;1m$ export FL_LOG_LEVEL="verbose"[0;m
[32;1m$ export GYM_CLEAN=true[0;m
[32;1m$ npm install --no-audit --no-fund --legacy-peer-deps[0;m
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated @babel/plugin-proposal-class-properties@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated @babel/plugin-proposal-object-rest-spread@7.20.7: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.
npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead

added 1050 packages in 3s
[32;1m$ npm run build[0;m

> new_aile@5.0.0 build
> tsc && vite build

[36mvite v7.0.0 [32mbuilding for production...[36m[39m
transforming...
[32m✓[39m 3757 modules transformed.
rendering chunks...
[33m[plugin vite:reporter] 
(!) /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/tenant/tenantService.ts is dynamically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/sidebar/QRScanner.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/sidebar/QRScanner.tsx but also statically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/app/slices/roomSlice.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/app/slices/tenantSlice.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/sidebar/AddMemberQRCodePage.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/auth/authService.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/tenant/index.ts, dynamic import will not move module into another chunk.
[39m
[33m[plugin vite:reporter] 
(!) /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/app/slices/tenantSlice.ts is dynamically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/tenant/tenantService.ts but also statically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/App.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/app/hooks/useTenantSwitcher.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/app/store.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/components/common/RouteGuard.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/hooks/useSidebarPreloader.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/hooks/useTenantRelations.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/home/<USER>/ChatRoom.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/home/<USER>/AccountTab.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/login/BusinessSignUpPage.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/sidebar/QRScanner.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/sidebar/Sidebar.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/auth/authService.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/syncInitService.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/system/initAppService.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/system/initAppService.ts, dynamic import will not move module into another chunk.
[39m
[33m[plugin vite:reporter] 
(!) /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor/app/dist/esm/index.js is dynamically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/App.tsx but also statically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/platform/swipeBackService.ts, dynamic import will not move module into another chunk.
[39m
[33m[plugin vite:reporter] 
(!) /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/app/store.ts is dynamically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/pages/sidebar/QRScanner.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/tenant/tenantService.ts but also statically imported by /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/App.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/main.tsx, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/socketService.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/services/syncInitService.ts, /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/src/utils/sidebarPerformanceTest.ts, dynamic import will not move module into another chunk.
[39m
computing gzip size...
[2mdist/[22m[32mindex.html                             [39m[1m[2m    0.86 kB[22m[1m[22m[2m │ gzip:     0.54 kB[22m
[2mdist/[22m[2massets/[22m[32micon-aiwow-GXohbiBF.svg         [39m[1m[2m    4.34 kB[22m[1m[22m[2m │ gzip:     3.27 kB[22m
[2mdist/[22m[2massets/[22m[32micon-im-ig-BwDNcG6a.svg         [39m[1m[2m    6.44 kB[22m[1m[22m[2m │ gzip:     2.10 kB[22m
[2mdist/[22m[2massets/[22m[32mprofile-header-bg-CdpAJfZB.jpg  [39m[1m[2m   18.70 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32mgrace-avatar-eWBu37UN.png       [39m[1m[2m   25.23 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32mjack-avatar-CYCxm8tV.png        [39m[1m[2m   31.67 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32muser-avatar-BxYWMwOA.png        [39m[1m[2m   33.53 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32malex-avatar-vGb2RPm0.png        [39m[1m[2m   34.50 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32mpeter-avatar-DXn_VRqh.png       [39m[1m[2m   40.32 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32mjames_avatar-DtQu6KEF.png       [39m[1m[2m   53.50 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32malice_avatar-CMnm7NUg.png       [39m[1m[2m   78.82 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32mjohn_avatar-BxIPEIKc.png        [39m[1m[2m  131.38 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[32mmain_image-Bv2yvUEq.png         [39m[1m[2m  558.53 kB[22m[1m[22m
[2mdist/[22m[2massets/[22m[35mindex-rAcyCrQ3.css              [39m[1m[2m  133.30 kB[22m[1m[22m[2m │ gzip:    22.40 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-BYxD7UEB.js                 [39m[1m[2m    0.36 kB[22m[1m[22m[2m │ gzip:     0.25 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-B_TjSmCb.js                 [39m[1m[2m    0.67 kB[22m[1m[22m[2m │ gzip:     0.33 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-DYR9ntCl.js                 [39m[1m[2m    0.73 kB[22m[1m[22m[2m │ gzip:     0.24 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-DCjm9-5y.js                 [39m[1m[2m    2.44 kB[22m[1m[22m[2m │ gzip:     1.09 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-D5rLx4X_.js                 [39m[1m[2m    2.96 kB[22m[1m[22m[2m │ gzip:     1.00 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-tP-Cei8y.js                 [39m[1m[2m    7.57 kB[22m[1m[22m[2m │ gzip:     2.36 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-6I8s8KNK.js                 [39m[1m[2m    8.69 kB[22m[1m[22m[2m │ gzip:     2.96 kB[22m
[2mdist/[22m[2massets/[22m[36mweb-CXWnLpjF.js                 [39m[1m[2m    9.63 kB[22m[1m[22m[2m │ gzip:     1.33 kB[22m
[2mdist/[22m[2massets/[22m[36mindex-ELp83gAs.js               [39m[1m[2m   41.13 kB[22m[1m[22m[2m │ gzip:    14.64 kB[22m
[2mdist/[22m[2massets/[22m[36mindex-rBBYHqgs.js               [39m[1m[33m7,897.97 kB[39m[22m[2m │ gzip: 2,607.90 kB[22m
[33m
(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.[39m
[32m✓ built in 4.96s[39m
[32;1m$ npx cap sync ios[0;m
[info] Using Gemfile: RubyGems bundle installed
✔ Copying web assets from dist to ios/App/App/public in 5.13ms
✔ Creating capacitor.config.json in ios/App/App in 268.21μs
✔ copy ios in 42.08ms
✔ Updating iOS plugins in 2.65ms
✔ Updating iOS native dependencies with bundle exec pod install in 3.04s
[info] Found 11 Capacitor plugins for ios:
       @capacitor-community/media@8.0.1
       @capacitor-community/sqlite@7.0.1
       @capacitor-mlkit/barcode-scanning@7.2.1
       @capacitor/app@7.0.1
       @capacitor/camera@7.0.1
       @capacitor/device@7.0.1
       @capacitor/filesystem@7.1.2
       @capacitor/push-notifications@7.0.1
       @capacitor/share@7.0.1
       @capacitor/status-bar@7.0.1
       aile-capacitor-line-login@1.3.10
✔ update ios in 3.05s
[info] Sync finished in 3.357s
[32;1m$ cd ios/App[0;m
[32;1m$ bundle exec pod deintegrate[0;m
/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/vendor/bundle/ruby/3.3.0/gems/activesupport-*******/lib/active_support/core_ext/array/conversions.rb:3: warning: bigdecimal was loaded from the standard library, but will no longer be part of the default gems starting from Ruby 3.4.0.
You can add bigdecimal to your Gemfile or gemspec to silence this warning.
Deintegrating `App.xcodeproj`
Deleted 1 'Check Pods Manifest.lock' build phases.
Deleted 1 'Embed Pods Frameworks' build phases.
- Pods_App.framework
- Pods-App.debug.xcconfig
- Pods-App.release.xcconfig
Deleted 1 empty `Pods` groups from project.
Deleted 1 empty `Frameworks` groups from project.
Removing `Pods` directory.

Project has been deintegrated. No traces of CocoaPods left in project.
Note: The workspace referencing the Pods project still remains.
[32;1m$ rm -rf Pods Podfile.lock ../build[0;m
[32;1m$ bundle exec pod cache clean --all[0;m
/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/vendor/bundle/ruby/3.3.0/gems/activesupport-*******/lib/active_support/core_ext/array/conversions.rb:3: warning: bigdecimal was loaded from the standard library, but will no longer be part of the default gems starting from Ruby 3.4.0.
You can add bigdecimal to your Gemfile or gemspec to silence this warning.
[32;1m$ echo "Re-installing pods with repo update..."[0;m
Re-installing pods with repo update...
[32;1m$ bundle exec pod install --repo-update[0;m
/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/vendor/bundle/ruby/3.3.0/gems/activesupport-*******/lib/active_support/core_ext/array/conversions.rb:3: warning: bigdecimal was loaded from the standard library, but will no longer be part of the default gems starting from Ruby 3.4.0.
You can add bigdecimal to your Gemfile or gemspec to silence this warning.
Updating local specs repositories
Analyzing dependencies
Downloading dependencies
Installing AileCapacitorLineLogin (1.3.10)
Installing Capacitor (7.4.2)
Installing CapacitorApp (7.0.1)
Installing CapacitorCamera (7.0.1)
Installing CapacitorCommunityMedia (8.0.1)
Installing CapacitorCommunitySqlite (7.0.1)
Installing CapacitorCordova (7.4.2)
Installing CapacitorDevice (7.0.1)
Installing CapacitorFilesystem (7.1.2)
Installing CapacitorMlkitBarcodeScanning (7.2.1)
Installing CapacitorPushNotifications (7.0.1)
Installing CapacitorShare (7.0.1)
Installing CapacitorStatusBar (7.0.1)
Installing GTMSessionFetcher (3.5.0)
Installing GoogleDataTransport (10.1.0)
Installing GoogleMLKit (7.0.0)
Installing GoogleToolboxForMac (4.2.1)
Installing GoogleUtilities (8.1.0)
Installing IONFilesystemLib (1.0.0)
Installing LineSDKSwift (5.8.2)
Installing MLImage (1.0.0-beta6)
Installing MLKitBarcodeScanning (6.0.0)
Installing MLKitCommon (12.0.0)
Installing MLKitVision (8.0.0)
Installing PromisesObjC (2.4.0)
Installing SDWebImage (5.21.1)
Installing SQLCipher (4.9.0)
Installing ZIPFoundation (0.9.19)
Installing nanopb (3.30910.0)
Generating Pods project
Integrating client project
Pod installation complete! There are 13 dependencies from the Podfile and 29 total pods installed.
[32;1m$ find . -name "*.sh" -path "*/Pods/*" -exec chmod +x {} \; || true[0;m
[32;1m$ sed -i '' 's/ENABLE_USER_SCRIPT_SANDBOXING = YES;/ENABLE_USER_SCRIPT_SANDBOXING = NO;/g' App.xcodeproj/project.pbxproj[0;m
[32;1m$ cd ../..[0;m
[32;1m$ bundle exec fastlane ios build_and_upload skip_pod_install:true[0;m
/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/vendor/bundle/ruby/3.3.0/gems/highline-2.0.3/lib/highline/import.rb:10: warning: abbrev was loaded from the standard library, but will no longer be part of the default gems starting from Ruby 3.4.0.
You can add abbrev to your Gemfile or gemspec to silence this warning.
+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
|                                                                                                                                                                                                                   [32mUsed plugins[0m                                                                                                                                                                                                                   |
+-----------------------------------------------+---------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Plugin                                        | Version | Action                                                                                                                                                                                                                                                                                                                                                                                 |
+-----------------------------------------------+---------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| fastlane-plugin-firebase_app_distribution     | 0.10.1  | firebase_app_distribution, firebase_app_distribution_add_testers, firebase_app_distribution_create_group, firebase_app_distribution_delete_group, firebase_app_distribution_get_latest_release, firebase_app_distribution_get_udids, firebase_app_distribution_remove_testers                                                                                                          |
| fastlane-plugin-google_cloud_storage_rebooted | 1.0.5   | google_cloud_storage_check_file, google_cloud_storage_download, google_cloud_storage_upload                                                                                                                                                                                                                                                                                            |
| fastlane-plugin-versioning                    | 0.7.1   | ci_build_number, get_app_store_version_number, get_build_number_from_plist, get_build_number_from_xcodeproj, get_info_plist_path, get_version_number_from_git_branch, get_version_number_from_plist, get_version_number_from_xcodeproj, increment_build_number_in_plist, increment_build_number_in_xcodeproj, increment_version_number_in_plist, increment_version_number_in_xcodeproj |
+-----------------------------------------------+---------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

[17:36:47]: [32m------------------------------[0m
[17:36:47]: [32m--- Step: default_platform ---[0m
[17:36:47]: [32m------------------------------[0m
[17:36:47]: [32mDriving the lane 'ios build_and_upload' 🚀[0m
[17:36:47]: 當前工作目錄: /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane
[17:36:47]: 證書文件創建成功
[17:36:47]: Provisioning Profile 創建成功
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [32mStep: mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/[0m
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [36m$ mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/[0m
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [32mStep: grep UUID -A1 -a /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane/profile.mobileprovision | grep -io "[0-9a-f\-]\{36\}"[0m
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [36m$ grep UUID -A1 -a /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane/profile.mobileprovision | grep -io "[0-9a-f\-]\{36\}"[0m
[17:36:47]: ▸ [35mb2a80967-76b9-450a-9ec6-c9084b19a84c[0m
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [32mStep: cp /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane/profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles/b2a80967-76b9-450a-9ec6-c9084b19a84c.mobileprovision[0m
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [36m$ cp /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane/profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles/b2a80967-76b9-450a-9ec6-c9084b19a84c.mobileprovision[0m
[17:36:47]: Provisioning Profile 已安裝: ~/Library/MobileDevice/Provisioning\ Profiles/b2a80967-76b9-450a-9ec6-c9084b19a84c.mobileprovision
[17:36:47]: [32m-----------------------------[0m
[17:36:47]: [32m--- Step: create_keychain ---[0m
[17:36:47]: [32m-----------------------------[0m
[17:36:47]: [33mFound keychain '~/Library/Keychains/build_keychain', creation skipped[0m
[17:36:47]: [33mIf creating a new Keychain DB is required please set the `require_create` option true to cause the action to fail[0m
[17:36:47]: [36m$ security list-keychains -d user[0m
[17:36:47]: ▸ [35m"/Users/<USER>/Library/Keychains/build_keychain-db"[0m
[17:36:47]: ▸ [35m"/Users/<USER>/Library/Keychains/login.keychain-db"[0m
[17:36:47]: [33mFound keychain '/Users/<USER>/Library/Keychains/build_keychain-db' in list-keychains, adding to search list skipped[0m
[17:36:47]: [32m--------------------------------[0m
[17:36:47]: [32m--- Step: import_certificate ---[0m
[17:36:47]: [32m--------------------------------[0m
[17:36:47]: Setting key partition list... (this can take a minute if there are a lot of keys installed)
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [32mStep: grep UUID -A1 -a /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane/profile.mobileprovision | grep -io "[0-9a-f\-]\{36\}"[0m
[17:36:47]: [32m--------------------------------------------------------------------[0m
[17:36:47]: [36m$ grep UUID -A1 -a /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/fastlane/profile.mobileprovision | grep -io "[0-9a-f\-]\{36\}"[0m
[17:36:47]: ▸ [35mb2a80967-76b9-450a-9ec6-c9084b19a84c[0m
[17:36:47]: [32m------------------------------------------[0m
[17:36:47]: [32m--- Step: update_code_signing_settings ---[0m
[17:36:47]: [32m------------------------------------------[0m

+--------------------------------------------------------------+
|              [32mSummary for code signing settings[0m               |
+-----------------------+--------------------------------------+
| use_automatic_signing | false                                |
| team_id               | XK59M655TD                           |
| code_sign_identity    | iPhone Distribution                  |
| profile_uuid          | b2a80967-76b9-450a-9ec6-c9084b19a84c |
| profile_name          | iOS Distribution Profile             |
| bundle_identifier     | cloud.aile.aile                      |
| path                  | ./ios/App/App.xcodeproj              |
+-----------------------+--------------------------------------+

[17:36:47]: Updating the Automatic Codesigning flag to disabled for the given project '/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/App.xcodeproj/project.pbxproj'
[17:36:47]: [33mSet Team id to: XK59M655TD for target: App for build configuration: Debug[0m
[17:36:47]: [33mSet Code Sign identity to: iPhone Distribution for target: App for build configuration: Debug[0m
[17:36:47]: [33mSet Provisioning Profile name to: iOS Distribution Profile for target: App for build configuration: Debug[0m
[17:36:47]: [33mSet Provisioning Profile UUID to: b2a80967-76b9-450a-9ec6-c9084b19a84c for target: App for build configuration: Debug[0m
[17:36:47]: [33mSet Bundle identifier to: cloud.aile.aile for target: App for build configuration: Debug[0m
[17:36:47]: [33mSet Team id to: XK59M655TD for target: App for build configuration: Release[0m
[17:36:47]: [33mSet Code Sign identity to: iPhone Distribution for target: App for build configuration: Release[0m
[17:36:47]: [33mSet Provisioning Profile name to: iOS Distribution Profile for target: App for build configuration: Release[0m
[17:36:47]: [33mSet Provisioning Profile UUID to: b2a80967-76b9-450a-9ec6-c9084b19a84c for target: App for build configuration: Release[0m
[17:36:47]: [33mSet Bundle identifier to: cloud.aile.aile for target: App for build configuration: Release[0m
[17:36:47]: [32mSuccessfully updated project settings to use Code Sign Style = 'Manual'[0m
[17:36:47]: [32mModified Targets:[0m
[17:36:47]: [32m	 * App[0m
[17:36:47]: [32mModified Build Configurations:[0m
[17:36:47]: [32m	 * Debug[0m
[17:36:47]: [32m	 * Release[0m
[17:36:47]: [32m-----------------[0m
[17:36:47]: [32m--- Step: gym ---[0m
[17:36:47]: [32m-----------------[0m
[17:36:47]: [33mSkipped Swift Package Manager dependencies resolution.[0m
[17:36:47]: [36m$ xcodebuild -showBuildSettings -workspace ./ios/App/App.xcworkspace -scheme App -configuration Release 2>&1[0m
[17:36:48]: Detected provisioning profile mapping: {:"cloud.aile.aile"=>"b2a80967-76b9-450a-9ec6-c9084b19a84c"}

+----------------------------------------------------------------------------------------------------------------------------------------------------------------+
|                                                                    [32mSummary for gym 2.228.0[0m                                                                     |
+-----------------------------------------------------+----------------------------------------------------------------------------------------------------------+
| workspace                                           | ./ios/App/App.xcworkspace                                                                                |
| scheme                                              | App                                                                                                      |
| configuration                                       | Release                                                                                                  |
| export_method                                       | app-store                                                                                                |
| export_options.method                               | app-store                                                                                                |
| export_options.teamID                               | XK59M655TD                                                                                               |
| export_options.provisioningProfiles.cloud.aile.aile | b2a80967-76b9-450a-9ec6-c9084b19a84c                                                                     |
| export_options.signingCertificate                   | Apple Distribution                                                                                       |
| export_options.signingStyle                         | manual                                                                                                   |
| export_options.compileBitcode                       | false                                                                                                    |
| export_options.stripSwiftSymbols                    | true                                                                                                     |
| clean                                               | true                                                                                                     |
| skip_package_dependencies_resolution                | true                                                                                                     |
| output_directory                                    | ./ios/build                                                                                              |
| output_name                                         | App                                                                                                      |
| xcargs                                              | SWIFT_OPTIMIZATION_LEVEL=-Onone ENABLE_BITCODE=NO EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE=NO SKIP_INSTALL=NO |
| buildlog_path                                       | ./ios/build/logs                                                                                         |
| silent                                              | false                                                                                                    |
| skip_package_ipa                                    | false                                                                                                    |
| skip_package_pkg                                    | false                                                                                                    |
| build_path                                          | /Users/<USER>/Library/Developer/Xcode/Archives/2025-07-31                                             |
| result_bundle                                       | false                                                                                                    |
| destination                                         | generic/platform=iOS                                                                                     |
| xcodebuild_formatter                                | xcpretty                                                                                                 |
| build_timing_summary                                | false                                                                                                    |
| skip_profile_detection                              | false                                                                                                    |
| xcodebuild_command                                  | xcodebuild                                                                                               |
| disable_package_automatic_updates                   | false                                                                                                    |
| use_system_scm                                      | false                                                                                                    |
| xcode_path                                          | /Applications/Xcode.app                                                                                  |
+-----------------------------------------------------+----------------------------------------------------------------------------------------------------------+

[17:36:48]: [36m$ set -o pipefail && xcodebuild -workspace ./ios/App/App.xcworkspace -scheme App -configuration Release -destination 'generic/platform=iOS' -archivePath /Users/<USER>/Library/Developer/Xcode/Archives/2025-07-31/App\ 2025-07-31\ 17.36.48.xcarchive SWIFT_OPTIMIZATION_LEVEL=-Onone ENABLE_BITCODE=NO EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE=NO SKIP_INSTALL=NO clean archive | tee /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/build/logs/App-App.log | xcpretty[0m
[17:36:49]: [35m[33m▸[0m [39;1mClean[0m Succeeded[0m
[17:36:51]: [35m[33m▸[0m [39;1mRunning script[0m '[CP] Copy XCFrameworks'[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m Pods-App-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m pb_encode.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m pb_decode.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m pb_common.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m pb.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m nanopb-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m ZIPFoundation-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m sqlite3.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SQLCipher-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIColor+SDHexString.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDmetamacros.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageTransitionInternal.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWeakProxy.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDInternalMacros.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageIOAnimatedCoderInternal.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageFramePool.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCachesManagerOperation.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageAssetManager.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDFileAttributeHelper.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDDisplayLink.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDDeviceHelper.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAsyncBlockOperation.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAssociatedObject.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m NSBezierPath+SDRoundedCorners.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIView+WebCacheState.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIView+WebCacheOperation.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIView+WebCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImageView+WebCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImageView+HighlightedWebCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+Transform.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+MultiFormat.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+Metadata.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+MemoryCacheCost.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+GIF.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+ForceDecode.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIImage+ExtendedCacheData.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m UIButton+WebCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageTransition.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImagePrefetcher.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageOptionsProcessor.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageOperation.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageManager.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageIndicator.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageError.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDownloaderResponseModifier.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDownloaderRequestModifier.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDownloaderOperation.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDownloaderDecryptor.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDownloaderConfig.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDownloader.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageDefine.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageCompat.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageCacheSerializer.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImageCacheKeyFilter.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImage.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDWebImage-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDMemoryCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageTransformer.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageLoadersManager.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageLoader.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageIOCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageIOAnimatedCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageHEICCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageGraphics.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageGIFCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageFrame.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCodersManager.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCoderHelper.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCachesManager.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCacheDefine.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCacheConfig.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageAWebPCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDImageAPNGCoder.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDGraphicsImageRenderer.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDDiskCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDCallbackQueue.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAnimatedImageView+WebCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAnimatedImageView.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAnimatedImageRep.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAnimatedImagePlayer.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m SDAnimatedImage.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m NSImage+Compatibility.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m NSData+ImageContentType.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m NSButton+WebCache.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromisePrivate.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m PromisesObjC-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromises.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromiseError.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Wrap.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Validate.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Timeout.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Then.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Testing.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Retry.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Reduce.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Recover.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Race.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Do.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Delay.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Catch.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Await.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Async.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Always.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+Any.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m FBLPromise+All.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m LineSDKSwift-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GoogleUtilities-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULUserDefaults.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULNetworkInfo.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULLoggerLevel.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULLogger.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULKeychainUtils.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULKeychainStorage.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GULAppEnvironmentUtil.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GoogleToolboxForMac-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMNSData+zlib.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMLogger.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMDefines.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GoogleDataTransport.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GoogleDataTransport-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCORTransport.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCORTargets.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCORProductData.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCOREventTransformer.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCOREventDataObject.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCOREvent.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCOREndpoints.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCORConsoleLogger.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GDTCORClock.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMSessionFetcherService.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMSessionUploadFetcher.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMSessionFetcherLogging.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMSessionFetcher.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m GTMSessionFetcher-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorStatusBar-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorShare-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorPushNotifications-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorMlkitBarcodeScanning-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m BarcodeScannerPlugin.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorFilesystem-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorDevice-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m NSDictionary+CordovaPreferences.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorCordova.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVWebViewProcessPoolFactory.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVViewController.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVURLProtocol.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVScreenOrientationDelegate.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVPluginResult.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVPluginManager.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVPlugin.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVPlugin+Resources.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVInvokedUrlCommand.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVConfigParser.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVCommandDelegateImpl.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDV.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVCommandDelegate.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CDVAvailability.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m AppDelegate.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorSQLitePlugin.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorCommunitySqlite-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m MediaPlugin.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorCommunityMedia-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorCamera-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CapacitorApp-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPPluginMethod.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m Capacitor.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPPluginCall.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPPlugin.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPInstanceDescriptor.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPInstanceConfiguration.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPBridgedPlugin.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPBridgedJSTypes.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m CAPBridgeViewController+CDVScreenOrientationDelegate.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m AileCapacitorLineLogin-umbrella.h[0m
[17:36:51]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-ZIPFoundation_Privacy-ZIPFoundation-Info.plist[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/ZIPFoundation_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:51]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-SQLCipher-SQLCipher-Info.plist[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/SQLCipher.bundle/PrivacyInfo.xcprivacy[0m
[17:36:51]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:51]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/LineSDK.bundle/Resource.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-nanopb_Privacy-nanopb-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-SDWebImage-SDWebImage-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/SDWebImage.bundle/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-GoogleToolboxForMac_Privacy-GoogleToolboxForMac-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/GoogleToolboxForMac_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-GoogleToolboxForMac_Logger_Privacy-GoogleToolboxForMac-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/GoogleToolboxForMac_Logger_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-GTMSessionFetcher_Core_Privacy-GTMSessionFetcher-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorCordova-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/Cordova.framework/PrivacyInfo.xcprivacy[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m ZIPFoundation_Privacy.bundle (in target 'ZIPFoundation-ZIPFoundation_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m nanopb_Privacy.bundle (in target 'nanopb-nanopb_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m nanopb-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/nanopb.framework/nanopb_Privacy.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m ZIPFoundation-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/ZIPFoundation.framework/ZIPFoundation_Privacy.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m SQLCipher.bundle (in target 'SQLCipher-SQLCipher' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m SDWebImage.bundle (in target 'SDWebImage-SDWebImage' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m GoogleUtilities_Privacy.bundle (in target 'GoogleUtilities-GoogleUtilities_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/SDWebImage.framework/SDWebImage.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m SDWebImage-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m GoogleUtilities-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m SQLCipher-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/SQLCipher.framework/SQLCipher.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m GoogleToolboxForMac_Privacy.bundle (in target 'GoogleToolboxForMac-GoogleToolboxForMac_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m FBLPromises_Privacy.bundle (in target 'PromisesObjC-FBLPromises_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m GoogleToolboxForMac_Logger_Privacy.bundle (in target 'GoogleToolboxForMac-GoogleToolboxForMac_Logger_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mTouching[0m GTMSessionFetcher_Core_Privacy.bundle (in target 'GTMSessionFetcher-GTMSessionFetcher_Core_Privacy' from project 'Pods')[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m PromisesObjC-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m GoogleToolboxForMac-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/GoogleToolboxForMac.framework/GoogleToolboxForMac_Privacy.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/GoogleToolboxForMac.framework/GoogleToolboxForMac_Logger_Privacy.bundle[0m
[17:36:52]: [35m[33m▸[0m [39;1mProcessing[0m GTMSessionFetcher-Info.plist[0m
[17:36:52]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher_Core_Privacy.bundle[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/LineSDKUI/SharingUI/PageViewController/PageTabView.swift:141:12: [33mextraneous whitespace between attribute name and '('; this is an error in the Swift 6 language mode[0m
[17:36:57]: ▸ [35m    private (set) var selectedIndex: Int = 0[0m
[17:36:57]: ▸ [35m[36m           ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/LineSDKUI/SharingUI/TargetSearch/ShareTargetSearchResultViewController.swift:46:12: [33mextraneous whitespace between attribute name and '('; this is an error in the Swift 6 language mode[0m
[17:36:57]: ▸ [35m    private (set) lazy var panelViewController = SelectedTargetPanelViewController(store: store)[0m
[17:36:57]: ▸ [35m[36m           ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/Utils/Colors.swift:182:42: [33m'scanHexInt32' was deprecated in iOS 13.0[0m
[17:36:57]: ▸ [35m        guard Scanner(string: hexString).scanHexInt32(&hexValue) else {[0m
[17:36:57]: ▸ [35m[36m                                         ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/Crypto/CryptoHelpers.swift:178:19: [33munnecessary check for 'iOS'; enclosing scope ensures guard will always be true[0m
[17:36:57]: ▸ [35m        } else if #available(iOS 10.3, *) {[0m
[17:36:57]: ▸ [35m[36m                  ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/Utils/Helpers.swift:159:19: [33munnecessary check for 'iOS'; enclosing scope ensures guard will always be true[0m
[17:36:57]: ▸ [35m        } else if #available(iOS 11.0, *) {[0m
[17:36:57]: ▸ [35m[36m        ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/Crypto/JWT/JWTCoder.swift:25:7: [33mclass 'Base64JSONDecoder' must restate inherited '@unchecked Sendable' conformance[0m
[17:36:57]: ▸ [35mclass Base64JSONDecoder: JSONDecoder {[0m
[17:36:57]: ▸ [35m[36m        ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/Utils/LoadingIndicator.swift:49:53: [33m'whiteLarge' was deprecated in iOS 13.0: renamed to 'UIActivityIndicatorView.Style.large'[0m
[17:36:57]: ▸ [35m        indicator = UIActivityIndicatorView(style: .whiteLarge)[0m
[17:36:57]: ▸ [35m[36m      ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/Login/LoginProcess.swift:470:46: [33m'keyWindow' was deprecated in iOS 13.0: Should not be used for applications that support multiple scenes as it returns a key window across all connected scenes[0m
[17:36:57]: ▸ [35m        if let window = UIApplication.shared.keyWindow, window.windowLevel == .normal {[0m
[17:36:57]: ▸ [35m[36m                                                    ^~~~~~~~~~[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/LineSDKUI/SharingUI/TargetSearch/ShareTargetSearchResultTableViewController.swift:120:9: [33m'automaticallyAdjustsScrollViewInsets' was deprecated in iOS 11.0: Use UIScrollView's contentInsetAdjustmentBehavior instead[0m
[17:36:57]: ▸ [35m        automaticallyAdjustsScrollViewInsets = false[0m
[17:36:57]: ▸ [35m[36m                                             ^[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/LineSDKSwift/LineSDK/LineSDK/LineSDKUI/SharingUI/TargetSearch/ShareTargetSearchResultViewController.swift:78:9: [33m'automaticallyAdjustsScrollViewInsets' was deprecated in iOS 11.0: Use UIScrollView's contentInsetAdjustmentBehavior instead[0m
[17:36:57]: ▸ [35m        automaticallyAdjustsScrollViewInsets = false[0m
[17:36:57]: ▸ [35m[36m        ^[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m SQLCipher_vers.c[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor/ios/Capacitor/Capacitor/WebViewDelegationHandler.swift:2:1: [33madd '@preconcurrency' to suppress 'Sendable'-related warnings from module 'WebKit'[0m
[17:36:57]: ▸ [35mimport WebKit[0m
[17:36:57]: ▸ [35m[36m        ^[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m nanopb_vers.c[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImage_vers.c[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m pb_encode.c[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m pb_decode.c[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m pb_common.c[0m
[17:36:57]: [35m[33m▸[0m [39;1mCompiling[0m sqlite3.c[0m
[17:36:57]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/SQLCipher/sqlite3.c:32187:25: [33mambiguous expansion of macro 'MAX' [-Wambiguous-macro][0m
[17:36:57]: ▸ [35m 32187 |           szBufNeeded = MAX(e2,0)+(i64)precision+(i64)width+15;[0m
[17:36:57]: ▸ [35m[36m^[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromises_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromiseError.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Wrap.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m PromisesObjC-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Validate.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Timeout.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Then.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Testing.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Retry.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SQLCipher-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Reduce.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Recover.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Race.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Do.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Delay.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Catch.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Await.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Async.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-LineSDK-LineSDKSwift-Info.plist[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Any.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+All.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m FBLPromise+Always.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GoogleUtilities_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m FBLPromises[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GoogleToolboxForMac_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m nanopb-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIView+WebCacheState.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIView+WebCacheOperation.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIView+WebCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImageView+WebCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImageView+HighlightedWebCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+Transform.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m nanopb[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+MultiFormat.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+Metadata.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+MemoryCacheCost.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+GIF.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+ForceDecode.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIImage+ExtendedCacheData.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIColor+SDHexString.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIButton+WebCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageTransition.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImagePrefetcher.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageOptionsProcessor.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageOperation.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageManager.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageIndicator.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageError.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDownloaderResponseModifier.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDownloaderRequestModifier.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDownloaderOperation.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDownloaderDecryptor.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDownloaderConfig.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDownloader.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageDefine.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageCompat.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageCacheSerializer.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImageCacheKeyFilter.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWebImage-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDWeakProxy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDMemoryCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDInternalMacros.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageTransformer.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageLoadersManager.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageLoader.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageIOCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageIOAnimatedCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageHEICCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageGraphics.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageGIFCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageFramePool.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageFrame.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCodersManager.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCoderHelper.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCachesManagerOperation.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCachesManager.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCacheDefine.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCacheConfig.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageAssetManager.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageAWebPCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDImageAPNGCoder.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDGraphicsImageRenderer.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDFileAttributeHelper.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDDisplayLink.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDDiskCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDDeviceHelper.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDCallbackQueue.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAsyncBlockOperation.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAssociatedObject.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAnimatedImageView.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAnimatedImageView+WebCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAnimatedImageRep.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAnimatedImagePlayer.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m SDAnimatedImage.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m NSImage+Compatibility.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m NSData+ImageContentType.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m NSButton+WebCache.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m NSBezierPath+SDRoundedCorners.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GoogleToolboxForMac-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMNSData+zlib.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m SDWebImage[0m
[17:37:05]: [35m[33m▸[0m [39;1mGenerating 'nanopb.framework.dSYM'[0m
[17:37:05]: [35m[33m▸[0m [39;1mGenerating 'FBLPromises.framework.dSYM'[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GoogleDataTransport_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m external_privacy_context.nanopb.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m external_prequest_context.nanopb.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m compliance.nanopb.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m client_metrics.nanopb.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m cct.nanopb.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mGenerating 'SDWebImage.framework.dSYM'[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m IsAppEncrypted.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMSessionFetcher_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMSessionFetcher.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMSessionFetcherLogging.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMSessionFetcherService.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMSessionUploadFetcher.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMSessionFetcher-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m NSDictionary+CordovaPreferences.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m Cordova_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCordova-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVWebViewProcessPoolFactory.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVViewController.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVPluginResult.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVURLProtocol.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVPluginManager.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GoogleUtilities-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GULUserDefaults.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GULLogger.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GULKeychainUtils.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GULKeychainStorage.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVPlugin.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GoogleDataTransport-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORUploadBatch.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORStorageMetadata.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORStorageEventSelector.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORProductData.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORMetricsMetadata.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORMetrics.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORLogSourceMetrics.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCOREvent+GDTMetricsSupport.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCOREvent+GDTCCTSupport.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m GTMSessionFetcher[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCOREndpoints.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORConsoleLogger.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORClock.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORAssert.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCCTURLSessionDataResponse.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCCTCompressionHelper.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVPlugin+Resources.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GULNetworkInfo.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVInvokedUrlCommand.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVCommandDelegateImpl.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m AppDelegate.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m ZIPFoundation_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CDVConfigParser.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mGenerating 'GTMSessionFetcher.framework.dSYM'[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m Cordova[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m Capacitor_vers.c[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m ZIPFoundation-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m ZIPFoundation[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m Capacitor-dummy.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPPluginCall.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m WKWebView+Capacitor.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPPluginMethod.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m UIStatusBarManager+CAPHandleTapAction.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mGenerating 'Cordova.framework.dSYM'[0m
[17:37:05]: [35m[33m▸[0m [39;1mGenerating 'ZIPFoundation.framework.dSYM'[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPPlugin.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPInstanceDescriptor.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPInstanceConfiguration.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPBridgedJSTypes.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m CAPBridgeViewController+CDVScreenOrientationDelegate.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mTouching[0m nanopb.framework (in target 'nanopb' from project 'Pods')[0m
[17:37:05]: [35m[33m▸[0m [39;1mTouching[0m SDWebImage.framework (in target 'SDWebImage' from project 'Pods')[0m
[17:37:05]: [35m[33m▸[0m [39;1mTouching[0m LineSDK.bundle (in target 'LineSDKSwift-LineSDK' from project 'Pods')[0m
[17:37:05]: [35m[33m▸[0m [39;1mTouching[0m FBLPromises.framework (in target 'PromisesObjC' from project 'Pods')[0m
[17:37:05]: [35m[33m▸[0m [39;1mTouching[0m ZIPFoundation.framework (in target 'ZIPFoundation' from project 'Pods')[0m
[17:37:05]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor/push-notifications/ios/Sources/PushNotificationsPlugin/PushNotificationsHandler.swift:38:49: [33m'alert' was deprecated in iOS 14.0[0m
[17:37:05]: ▸ [35m                    presentationOptions.insert(.alert)[0m
[17:37:05]: ▸ [35m[36m                                                ^[0m
[17:37:05]: [35m[33m▸[0m [39;1mProcessing[0m LineSDKSwift-Info.plist[0m
[17:37:05]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/LineSDK.framework/LineSDK.bundle[0m
[17:37:05]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-mlkit/barcode-scanning/ios/Plugin/BarcodeScannerView.swift:115:23: [33mvalue 'captureDevice' was defined but never used; consider replacing with boolean test[0m
[17:37:05]: ▸ [35m            guard let captureDevice = self.captureDevice else { return }[0m
[17:37:05]: ▸ [35m[36m                  ~~~~^~~~~~~~~~~~~~~~[0m
[17:37:05]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-mlkit/barcode-scanning/ios/Plugin/BarcodeScannerView.swift:116:23: [33mvalue 'captureSession' was defined but never used; consider replacing with boolean test[0m
[17:37:05]: ▸ [35m            guard let captureSession = self.captureSession else { return }[0m
[17:37:05]: ▸ [35m[36m                  ~~~~^~~~~~~~~~~~~~~~~[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GULAppEnvironmentUtil.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mProcessing[0m ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORUploadCoordinator.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORTransport.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GTMLogger.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORTransformer.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mLinking[0m GoogleUtilities[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORRegistrar.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORReachability.m[0m
[17:37:05]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORPlatform.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORLifecycle.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m GoogleToolboxForMac[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORFlatFileStorage.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCOREvent.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORDirectorySizeTracker.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCCTUploader.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'GoogleUtilities.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORMetrics+GDTCCTSupport.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCCTNanopbHelpers.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m LineSDK_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORMetricsController.m[0m
[17:37:06]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor/filesystem/ios/Sources/FilesystemPlugin/LegacyFilesystemImplementation.swift:77:13: [33minitialization of immutable value 'responseType' was never used; consider replacing with assignment to '_' or removing it[0m
[17:37:06]: ▸ [35m        let responseType = call.getString("responseType", "text")[0m
[17:37:06]: ▸ [35m[36m        ~~~~^~~~~~~~~~~~[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCORFlatFileStorage+Promises.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m GDTCCTUploadOperation.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'GoogleToolboxForMac.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m LineSDKSwift-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorStatusBar_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m LineSDK[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m GTMSessionFetcher.framework (in target 'GTMSessionFetcher' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorShare_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorPushNotifications_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m GoogleUtilities.framework (in target 'GoogleUtilities' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m GoogleDataTransport_Privacy.bundle (in target 'GoogleDataTransport-GoogleDataTransport_Privacy' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m GoogleToolboxForMac.framework (in target 'GoogleToolboxForMac' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorDevice_vers.c[0m
[17:37:06]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-community/sqlite/ios/Plugin/BiometricIDAuthentication.swift:35:9: [33mswitch must be exhaustive; this is an error in the Swift 6 language mode[0m
[17:37:06]: ▸ [35m        switch context.biometryType {[0m
[17:37:06]: ▸ [35m[36m        ^[0m
[17:37:06]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-community/sqlite/ios/Plugin/Utils/UtilsDownloadFromHTTP.swift:167:37: [33m'init(url:accessMode:preferredEncoding:)' is deprecated: Please use the throwing initializer.[0m
[17:37:06]: ▸ [35m                guard let archive = Archive(url: zipFile, accessMode: .read) else {[0m
[17:37:06]: ▸ [35m[36m        ^[0m
[17:37:06]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-community/sqlite/ios/Plugin/Utils/UtilsFile.swift:428:33: [33m'init(url:accessMode:preferredEncoding:)' is deprecated: Please use the throwing initializer.[0m
[17:37:06]: ▸ [35m            guard let archive = Archive(url: zipAsset, accessMode: .read) else {[0m
[17:37:06]: ▸ [35m[36m                                    ^[0m
[17:37:06]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-community/sqlite/ios/Plugin/Utils/UtilsSQLCipher.swift:894:13: [33minitialization of immutable value 'curTime' was never used; consider replacing with assignment to '_' or removing it[0m
[17:37:06]: ▸ [35m        let curTime = UtilsDelete.getCurrentTimeAsInteger()[0m
[17:37:06]: ▸ [35m[36m                                ^[0m
[17:37:06]: ▸ [35m[33m⚠️  [0m/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/node_modules/@capacitor-community/sqlite/ios/Plugin/Utils/UtilsSQLStatement.swift:549:17: [33minitialization of immutable value 'intParenthesisValue' was never used; consider replacing with assignment to '_' or removing it[0m
[17:37:06]: ▸ [35m            let intParenthesisValue = mStmt.distance(from: mStmt.startIndex, to: closingParenthesisIndex)[0m
[17:37:06]: ▸ [35m[36m        ~~~~^~~~~~~[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m GoogleDataTransport-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorStatusBar-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorShare-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m Cordova.framework (in target 'CapacitorCordova' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorPushNotifications-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m GoogleDataTransport[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m Capacitor[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorDevice-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'LineSDK.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'GoogleDataTransport.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m Capacitor-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/Capacitor.framework/native-bridge.js[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'Capacitor.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Library/Frameworks/Capacitor.framework/PrivacyInfo.xcprivacy[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m GoogleDataTransport.framework (in target 'GoogleDataTransport' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m LineSDK.framework (in target 'LineSDKSwift' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorApp_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m Capacitor.framework (in target 'Capacitor' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorStatusBar-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorStatusBar[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorShare[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorShare-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorApp-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorPushNotifications-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorPushNotifications[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorMlkitBarcodeScanning-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorFilesystem-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorDevice[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorDevice-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorCommunityMedia-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorStatusBar.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorShare.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorApp[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorCamera-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorApp-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m AileCapacitorLineLogin-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorPushNotifications.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorMlkitBarcodeScanning_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorFilesystem_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorDevice.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorMlkitBarcodeScanning-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorFilesystem-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCommunityMedia_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m AileCapacitorLineLogin_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorApp.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorFilesystem[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorStatusBar.framework (in target 'CapacitorStatusBar' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorShare.framework (in target 'CapacitorShare' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCommunityMedia-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCamera_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m BarcodeScannerPlugin.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorPushNotifications.framework (in target 'CapacitorPushNotifications' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m AileCapacitorLineLogin-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m MediaPlugin.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorDevice.framework (in target 'CapacitorDevice' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorApp.framework (in target 'CapacitorApp' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m AileCapacitorLineLogin[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCamera-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorCommunityMedia[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorMlkitBarcodeScanning.framework (in target 'CapacitorMlkitBarcodeScanning' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorCamera[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorFilesystem.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorFilesystem.framework (in target 'CapacitorFilesystem' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'AileCapacitorLineLogin.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorCommunityMedia.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorCamera.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m AileCapacitorLineLogin.framework (in target 'AileCapacitorLineLogin' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorCommunityMedia.framework (in target 'CapacitorCommunityMedia' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorCamera.framework (in target 'CapacitorCamera' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCommunitySqlite_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m Pods_App_vers.c[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorCommunitySqlite-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m CapacitorSQLitePlugin.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m Pods-App-dummy.m[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m SQLCipher[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'SQLCipher.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m SQLCipher.framework (in target 'SQLCipher' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m CapacitorCommunitySqlite-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mLinking[0m CapacitorCommunitySqlite[0m
[17:37:06]: [35m[33m▸[0m [39;1mGenerating 'CapacitorCommunitySqlite.framework.dSYM'[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m CapacitorCommunitySqlite.framework (in target 'CapacitorCommunitySqlite' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mProcessing[0m Pods-App-Info.plist[0m
[17:37:06]: [35m[33m▸[0m [39;1mTouching[0m Pods_App.framework (in target 'Pods-App' from project 'Pods')[0m
[17:37:06]: [35m[33m▸[0m [39;1mRunning script[0m '[CP] Check Pods Manifest.lock'[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Applications/App.app/public[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Applications/App.app/capacitor.config.json[0m
[17:37:06]: [35m[33m▸[0m [39;1mCopying[0m /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Applications/App.app/config.xml[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m Main.storyboard[0m
[17:37:06]: [35m[33m▸[0m [39;1mCompiling[0m LaunchScreen.storyboard[0m
[17:37:07]: [35m[33m▸[0m [39;1mLinking[0m App[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: no platform load command found in '/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitBarcodeScanning/Frameworks/MLKitBarcodeScanning.framework/MLKitBarcodeScanning', assuming: iOS[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][2](MLKAnalyticsLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][3](MLKContext.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][4](MLKFeatureFlags.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][5](MLKFileManager.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][6](MLKFirelogTransport.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][7](MLKLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][8](MLKUtilities.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][9](NSError+MLKReporting.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][10](NSProgress+MLKCompletion.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][11](MLKCustomModelManager.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][12](MLKCustomRemoteModel.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][13](MLKLocalModel.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][14](MLKModelDownloadConditions.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][15](MLKModelDownloadLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][16](MLKModelDownloadNotifications.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][17](MLKModelDownloadUtilities.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][18](MLKModelDownloader.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][19](MLKModelHasher.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][20](MLKModelManager.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][21](MLKModelManifest.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][22](MLKModelPreferencesManager.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][23](MLKRemoteModel.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][24](MLKRemoteModelInfo.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][25](MLKRemoteModelSource.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][26](CCTClearcutUploader.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][27](CCTBackgroundTask.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][28](CCTClearcutMetaLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][29](CCTClearcutLogger+Counters.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][30](CCTClearcutLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][31](CCTClearcutComplianceDataProviderOptionalInjector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][32](CCTClearcutComplianceDataProvider_APIRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][33](CCTClearcutLoggerConfiguration.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][34](CCTBootCountLogTransformer.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][35](CCTLogSampler.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][36](CCTClearcutFlags.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][37](CCTClearcutPhenotypeConfiguration.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][38](CCTFarmHash.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][40](CCTPhenotypeLogTransformer.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][41](CCTClearcutProtoUtility.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][42](CCTLogFile.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][43](CCTClearcutMetaLoggerComplianceDataProvider.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][44](CCTUploaderLock.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][45](PHTPhenotypeFlags.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][46](PHTPhenotype.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][47](PHTFlatFilePhenotype.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][48](PHTHeterodyneSyncer.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][49](PHTPhenotypeBuildData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][50](PHTInternalFlagsConstants.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][51](PHTInternalHeterodyneSyncer.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][52](PHTBreakableLock.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][53](PHTPhenotypeSyncAfterConfiguration.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][54](PHTPhenotypeLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][55](PHTInternalPhenotypeFlags.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][56](PHTLoggedOutAccount.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][57](PHTSnapshot.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][58](PHTURL.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][59](CCTClearcutAutoCounters.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][60](CCTLogWriter.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][61](CCTBootCountCache.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][62](CCTBootCounter.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][63](CCTClearcutLogEvent+Private.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][64](CCTLogOutputStreamImpl.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][65](CCTConstants.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][66](CCTNSErrorHandling.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][67](CCTQosTierOverrides.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][68](CCTClearcutFileUtility.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][69](CCTLogContext+Hash.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][70](CCTClearcutLogEvent.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][71](CCTClearcutComplianceProductData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][72](CCTClearcutComplianceSOCSData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][73](CCTClientInfoMaker.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][74](BuildData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][75](builddata.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][76](builddata_globals_faked.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][77](ascii.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][78](charconv.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][79](escaping.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][80](charconv_bigint.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][81](charconv_parse.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][82](damerau_levenshtein_distance.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][83](memutil.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][84](stringify_sink.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][85](match.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][86](numbers.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][87](str_cat.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][88](str_replace.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][89](str_split.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][90](substitute.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][91](bytestream.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][92](string_view_utils.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][94](escaping.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][96](utf8.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][97](string_view.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][98](cycleclock.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][99](spinlock.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][100](sysinfo.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][101](thread_identity.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][103](spinlock_wait.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][104](throw_delegate.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][105](raw_logging.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][107](int128.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][108](CCTClearcutGlobalLogTransformerInjector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][109](CCTClearcutGlobalLogTransformer_APIRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][110](MSPInfoPlistMobilespecID.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][111](GNSStreamProviderImpl.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][112](CCTClearcutCounters.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][113](CCTClock.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][114](PHTDeviceTokenCacheServiceOptionalInjector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][115](PHTDeviceTokenCacheService_APIRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][116](PHTUserTokenCacheServiceOptionalInjector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][117](PHTUserTokenCacheService_APIRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][118](GTMDecoratedSessionFetcherServiceFactoryOptionalInjector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][119](GTMDecoratedSessionFetcherServiceFactory_APIRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][120](GIPGaiaAccountID.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][121](GIPNoAccountID.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][122](GIPPseudonymousIDStore.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][124](PHTPhenotypeFlagsSnapshot.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][125](PHTHermeticOverridesServiceOptionalInjector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][126](SRLMemoizingScope.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][127](SRLProviderHelpers.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][128](SRLRegistry.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][129](GOOProvider.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][130](SRLShouldAssertMainThreadInit.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][131](PHTHermeticOverridesService_APIRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][132](SRLBindingsRegistrationData.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][133](SRLImplementation.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][134](SRLImplementationConfiguration.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][135](SRLScopeTagSet.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][136](SRLScopeTagSetCodableWrapper.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][137](SRLClassProtocolType.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][138](SRLClassType.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][139](SRLInstanceType.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][140](SRLProtocolType.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][141](GSCErrorHandler+Test.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][142](GSCErrorHandler.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][143](PHTAppWideProperties.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][144](PHTPhenotypeConfiguration.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][145](PHTPhenotypeConfigurationFlag.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][146](PHTPhenotypeExperimentTokens.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][147](GPBAny.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][148](GPBApi.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][149](GPBArray.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][150](GPBCodedInputStream.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][151](GPBCodedOutputStream.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][152](GPBDescriptor.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][153](GPBDictionary.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][154](GPBDuration.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][155](GPBEmpty.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][156](GPBExtensionInternals.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][157](GPBExtensionRegistry.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][158](GPBFieldMask.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][159](GPBMessage.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][160](GPBRootObject.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][161](GPBSourceContext.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][162](GPBStruct.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][163](GPBTimestamp.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][164](GPBType.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][165](GPBUnknownField.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][166](GPBUnknownFieldSet.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][167](GPBUtilities.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][168](GPBWellKnownTypes.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][169](GPBWireFormat.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][170](GPBWrappers.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][171](PHTPhenotypeUtil.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][172](PHTPhenotypeError.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][173](PHTLog.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][174](GIPAppGroups.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][175](NSURL+AppGroups.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][176](NSUserDefaults+AppGroups.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][177](GIPAppGroupsSearchList.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][178](GIPAppGroupsErrors.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][179](PRMGCDWrappers.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][180](PRMGCDQueueHelpers.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][181](PRMQueueBarrierTracker.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][182](PRMSignalTracker.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][183](PRMTraceContextManagerSharedInstance.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][184](PRMCStringLiteralName.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][185](PRMComposedName.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][186](PRMKnownSafeName.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][187](NSString+PRMName.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][188](CMCCodeModuleCache.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][189](CMCCodeModule.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][190](arch_utilities.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][191](file_id.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][192](macho_id.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][193](macho_utilities.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][194](macho_walker.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][195](md5.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][196](PRMTraceContextManagerPrivateSharedInstanceDisabled.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][197](PRMLog.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][199](GIPLog.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][200](GIPLoggingReroutingGTMLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][201](GIPLogMultiplexer.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][202](GIPDefaultMinimumLogLevelFilterError.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][203](GIPSystemLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][204](GIPLogCallstack.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][205](GIPLogFilter.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][206](GIPLogMessage.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][207](GIPLogMetadata.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][208](GIPSafeParameter.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][210](PRMTraceConfiguration.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][211](GULCCComponent.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][212](GULCCComponentContainer.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][213](GULCCComponentType.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][214](GULCCDependency.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][215](FirebaseMlSdk.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][233](VisionExtension.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][235](Barhopper.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][255](MobileSignedOutConsent.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][256](SignedOutStateContainer.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][257](BootCount.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][258](LogContext.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][259](Clientanalytics.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][260](Compliance.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][261](ExternalPrivacyContext.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][262](ExternalPrequestContext.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][264](LogSourceEnum.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][270](ExperimentIds.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][276](UploaderState.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][277](DeviceExperimentIds.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][278](Experiments.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][280](Enums.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][281](Metalog.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][283](LogSamplingRules.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][284](TypedFeatures.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][285](Counters.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][286](PhenotypeLog.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][287](RegistrationInfo.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][289](FlatFile.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][290](FlagValues.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitCommon/Frameworks/MLKitCommon.framework/MLKitCommon[arm64][291](SignedOutState.pbobjc.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][2](MLKVision3DPoint.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][3](MLKVisionDetectorUtil.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][4](MLKVisionImage.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][5](MLKVisionPoint.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][6](GMVDetector.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][7](GMVCloudVisionClient.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][8](GMVFeature.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][9](GMVLabelDetectorFlags.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][10](GMVLogger.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][11](GMVUtility.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/App/Pods/MLKitVision/Frameworks/MLKitVision.framework/MLKitVision[arm64][12](GMVUtility+Internal.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: ▸ [35m[33m⚠️  ld: object file (/Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/BuildProductsPath/Release-iphoneos/Pods_App.framework/Pods_App[3](Pods-App-dummy.o)) was built for newer 'iOS' version (15.5) than being linked (14.0)[0m
[17:37:07]: [35m[33m▸[0m [39;1mProcessing[0m Info.plist[0m
[17:37:07]: [35m[33m▸[0m [39;1mRunning script[0m '[CP] Embed Pods Frameworks'[0m
[17:37:09]: [35m[33m▸[0m [39;1mGenerating 'App.app.dSYM'[0m
[17:37:10]: [35m[33m▸[0m [39;1mTouching[0m App.app (in target 'App' from project 'App')[0m
[17:37:10]: ▸ [35m    [33mRun script build phase '[CP] Copy XCFrameworks' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'IONFilesystemLib' from project 'Pods')[0m
[17:37:10]: ▸ [35m    [33mRun script build phase '[CP] Embed Pods Frameworks' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'App' from project 'App')[0m
[17:37:10]: [35m[33m▸[0m [39;1mArchive[0m Succeeded[0m
[17:37:10]: [33mGenerated plist file with the following values:[0m
[17:37:10]: ▸ [35m-----------------------------------------[0m
[17:37:10]: ▸ [35m{[0m
[17:37:10]: ▸ [35m  "method": "app-store",[0m
[17:37:10]: ▸ [35m  "teamID": "XK59M655TD",[0m
[17:37:10]: ▸ [35m  "provisioningProfiles": {[0m
[17:37:10]: ▸ [35m    "cloud.aile.aile": "b2a80967-76b9-450a-9ec6-c9084b19a84c"[0m
[17:37:10]: ▸ [35m  },[0m
[17:37:10]: ▸ [35m  "signingCertificate": "Apple Distribution",[0m
[17:37:10]: ▸ [35m  "signingStyle": "manual",[0m
[17:37:10]: ▸ [35m  "compileBitcode": false,[0m
[17:37:10]: ▸ [35m  "stripSwiftSymbols": true[0m
[17:37:10]: ▸ [35m}[0m
[17:37:10]: ▸ [35m-----------------------------------------[0m
[17:37:10]: [36m$ /usr/bin/xcrun /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/vendor/bundle/ruby/3.3.0/gems/fastlane-2.228.0/gym/lib/assets/wrap_xcodebuild/xcbuild-safe.sh -exportArchive -exportOptionsPlist '/var/folders/82/bcs5_mqs0c1ddv0rkp2ztr8r0000gn/T/gym_config20250731-95231-htm95y.plist' -archivePath /Users/<USER>/Library/Developer/Xcode/Archives/2025-07-31/App\ 2025-07-31\ 17.36.48.xcarchive -exportPath '/var/folders/82/bcs5_mqs0c1ddv0rkp2ztr8r0000gn/T/gym_output20250731-95231-s3pyq4' SWIFT_OPTIMIZATION_LEVEL=-Onone ENABLE_BITCODE=NO EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE=NO SKIP_INSTALL=NO [0m
rbenv detected, removing env variables
rbenv: shell integration not enabled. Run `rbenv init' for instructions.
+ xcodebuild -exportArchive -exportOptionsPlist /var/folders/82/bcs5_mqs0c1ddv0rkp2ztr8r0000gn/T/gym_config20250731-95231-htm95y.plist -archivePath '/Users/<USER>/Library/Developer/Xcode/Archives/2025-07-31/App 2025-07-31 17.36.48.xcarchive' -exportPath /var/folders/82/bcs5_mqs0c1ddv0rkp2ztr8r0000gn/T/gym_output20250731-95231-s3pyq4 SWIFT_OPTIMIZATION_LEVEL=-Onone ENABLE_BITCODE=NO EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE=NO SKIP_INSTALL=NO
2025-07-31 17:37:10.745 xcodebuild[96906:137683795] [MT] IDEDistribution: -[IDEDistributionLogging _createLoggingBundleAtPath:]: Created bundle at path "/var/folders/82/bcs5_mqs0c1ddv0rkp2ztr8r0000gn/T/App_2025-07-31_17-37-10.744.xcdistributionlogs".
2025-07-31 17:37:10.762 xcodebuild[96906:137683795] [MT] IDEDistribution: -[IDEDistributionMethodManager orderedDistributionMethodsForTask:archive:logAspect:]: Error = Error Domain=IDEDistributionMethodManagerErrorDomain Code=2 "Unknown Distribution Error" UserInfo={NSLocalizedDescription=Unknown Distribution Error}
2025-07-31 17:37:10.763 xcodebuild[96906:137683795] [MT] IDEDistribution: -[IDEDistributionMethodManager orderedDistributionMethodsForTask:archive:logAspect:]: Error = Error Domain=IDEDistributionMethodManagerErrorDomain Code=2 "Unknown Distribution Error" UserInfo={NSLocalizedDescription=Unknown Distribution Error}
2025-07-31 17:37:10.765 xcodebuild[96906:137683795] [MT] IDEDistribution: -[IDEDistributionMethodManager orderedDistributionMethodsForTask:archive:logAspect:]: Error = Error Domain=IDEDistributionMethodManagerErrorDomain Code=2 "Unknown Distribution Error" UserInfo={NSLocalizedDescription=Unknown Distribution Error}
error: exportArchive exportOptionsPlist error for key "method" expected one {} but found app-store

** EXPORT FAILED **
[17:37:10]: [31mExit status: 70[0m

+-----------------------------------------+
|            [32m[33mBuild environment[0m            |
+---------------+-------------------------+
| xcode_path    | /Applications/Xcode.app |
| gym_version   | 2.228.0                 |
| export_method | app-store               |
| sdk           | iPhoneOS18.2.sdk        |
+---------------+-------------------------+

[17:37:10]: ▸ [35m    /usr/bin/touch -c /Users/<USER>/Library/Developer/Xcode/DerivedData/App-dmqfkidiuiuipmbjgckuekwdelqh/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Applications/App.app[0m
[17:37:10]: ▸ [35mwarning: Run script build phase '[CP] Copy XCFrameworks' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'IONFilesystemLib' from project 'Pods')[0m
[17:37:10]: ▸ [35mwarning: Run script build phase '[CP] Embed Pods Frameworks' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'App' from project 'App')[0m
[17:37:10]: ▸ [35m** ARCHIVE SUCCEEDED **[0m
[17:37:10]: 
[17:37:10]: [31m⬆️  Check out the few lines of raw `xcodebuild` output above for potential hints on how to solve this error[0m
[17:37:10]: [33m📋  For the complete and more detailed error log, check the full log at:[0m
[17:37:10]: [33m📋  /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app/ios/build/logs/App-App.log[0m
[17:37:10]: 
[17:37:10]: [31mLooks like fastlane ran into a build/archive error with your project[0m
[17:37:10]: [31mIt's hard to tell what's causing the error, so we wrote some guides on how[0m
[17:37:10]: [31mto troubleshoot build and signing issues: https://docs.fastlane.tools/codesigning/getting-started/[0m
[17:37:10]: [31mBefore submitting an issue on GitHub, please follow the guide above and make[0m
[17:37:10]: [31msure your project is set up correctly.[0m
[17:37:10]: [31mfastlane uses `xcodebuild` commands to generate your binary, you can see the[0m
[17:37:10]: [31mthe full commands printed out in yellow in the above log.[0m
[17:37:10]: [31mMake sure to inspect the output above, as usually you'll find more error information there[0m
[17:37:10]: 
+-------------------------------------------------------+
|                     [33mLane Context[0m                      |
+------------------+------------------------------------+
| DEFAULT_PLATFORM | android                            |
| PLATFORM_NAME    | ios                                |
| LANE_NAME        | ios build_and_upload               |
| KEYCHAIN_PATH    | ~/Library/Keychains/build_keychain |
+------------------+------------------------------------+
[17:37:10]: [31mCalled from Fastfile at line 137[0m
[17:37:10]: [31m```[0m
[17:37:10]: [31m    135:	    [0m
[17:37:10]: [31m    136:	    # 構建 IPA[0m
[17:37:10]: [31m => 137:	    gym([0m
[17:37:10]: [31m    138:	      workspace: "./ios/App/App.xcworkspace",[0m
[17:37:10]: [31m    139:	      scheme: "App",[0m
[17:37:10]: [31m```[0m
[17:37:10]: [31mError packaging up the application[0m

+------------------------------------------------------------------------------------+
|                                  [32mfastlane summary[0m                                  |
+------+---------------------------------------------------------------+-------------+
| Step | Action                                                        | Time (in s) |
+------+---------------------------------------------------------------+-------------+
| 1    | default_platform                                              | 0           |
| 2    | mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/       | 0           |
| 3    | grep UUID -A1 -a /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cl | 0           |
| 4    | cp /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_ap | 0           |
| 5    | create_keychain                                               | 0           |
| 6    | import_certificate                                            | 0           |
| 7    | grep UUID -A1 -a /Users/<USER>/.builds/t2_wm8Q2_/0/aile_cl | 0           |
| 8    | update_code_signing_settings                                  | 0           |
| 💥   | [31mgym[0m                                                           | 23          |
+------+---------------------------------------------------------------+-------------+

[17:37:10]: [31mfastlane finished with errors[0m
[31m
[!] Error packaging up the application[0m
section_end:1753954630:step_script
[0Ksection_start:1753954630:upload_artifacts_on_failure
[0K[0K[36;1mUploading artifacts for failed job[0;m[0;m
[32;1mUploading artifacts...[0;m
Runtime platform                                  [0;m  arch[0;m=arm64 os[0;m=darwin pid[0;m=97171 revision[0;m=3153ccc6 version[0;m=17.7.0
[0;33mWARNING: ios/build/App.ipa: no matching files. Ensure that the artifact path is relative to the working directory (/Users/<USER>/.builds/t2_wm8Q2_/0/aile_cloud/newaile_app)[0;m 
ios/build/logs/: found 2 matching artifact files and directories[0;m 
Uploading artifacts as "archive" to coordinator... 201 Created[0;m  id[0;m=10871205701 responseStatus[0;m=201 Created token[0;m=glcbt-69
section_end:1753954633:upload_artifacts_on_failure
[0Ksection_start:1753954633:cleanup_file_variables
[0K[0K[36;1mCleaning up project directory and file based variables[0;m[0;m
section_end:1753954633:cleanup_file_variables
[0K[31;1mERROR: Job failed: exit status 1
[0;m