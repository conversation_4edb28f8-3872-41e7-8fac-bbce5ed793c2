default_platform(:android)

platform :android do
  desc "Build debug APK"
  lane :build_debug do
    gradle(
      task: "assembleDebug",
      project_dir: "android",
    )
    
    now = Time.now.strftime("%Y%m%d")
    UI.message("Verifying AILE_SERVICE_KEY value: '#{ENV["AILE_SERVICE_KEY"]}'")
    google_cloud_storage_upload(
      project: "aile-infra-common",
      bucket: "aile-distribute",
      keyfile: ENV["AILE_SERVICE_KEY"],
      content_path: lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH],
      destination_path: "Android/QA/#{now}"
    )
  end
end

platform :ios do
  desc "構建並上傳到 TestFlight"
  lane :build_and_upload do |options|
    # 獲取傳入的參數或使用默認值
    optimization_level = options[:swift_optimization_level] || "-Onone"
    
    # 建立 Xcode 參數
    xcode_args = "SWIFT_OPTIMIZATION_LEVEL=#{optimization_level}"
    if options[:xcargs]
      xcode_args = "#{options[:xcargs]} #{xcode_args}"
    end
    
    # 使用 Match 同步證書和 Provisioning Profile
    match(
      type: "appstore",
      readonly: true,
      force_for_new_devices: true
    )
    
    UI.message("Running pod deintegrate and install...")
    
    # 執行 pod deintegrate 清理舊的 Pods 設定
    sh("cd ./ios/App && pod deintegrate || true")
    
    # 刪除 Pods 相關文件，確保是全新安裝
    sh("rm -rf ./ios/App/Pods ./ios/App/Podfile.lock ./ios/App/App.xcworkspace")

    # 執行 cocoapods (pod install)
    cocoapods(
      # 確保指定正確的目錄
      podfile: "./ios/App/Podfile",
      clean_install: true # 確保是全新安裝，而不是更新
    )
    
    # 構建 IPA
    gym(
      workspace: "./ios/App/App.xcworkspace",
      scheme: "App",
      configuration: "Release",
      export_method: "app-store",
      clean: true,
      # 關鍵參數：跳過框架嵌入檢查
      skip_package_dependencies_resolution: true,
      output_directory: "./ios/build",
      output_name: "App.ipa",
      xcargs: "SWIFT_OPTIMIZATION_LEVEL=-Onone ENABLE_BITCODE=NO EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE=NO SKIP_INSTALL=NO",
      buildlog_path: "./ios/build/logs"
    )
    
    # 上傳到 TestFlight
    pilot(
      username: ENV["APPLE_ID"],
      apple_id: ENV["APP_IDENTIFIER"] || "cloud.aile.aile",
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false
    )
  end
end