import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, Toast, Popup, Button } from 'antd-mobile';
import type { Rule } from 'antd-mobile/es/components/form';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../app/hooks';
import {
  sendOtp,
  login,
  resetOtpState,
  AccountResponse,
  restoreAuthToken,
  setAccount
} from '../../app/slices/authSlice';
import { logService } from '../../services/system/logService';
import { ConstantUtil } from '../../utils/constantUtil';
import downArrow from '../../assets/images/down_arrow.svg';
import backArrow from '../../assets/images/back_arrow.svg';
import './OtpLoginPage.css';

import authService, {UpdateAccountMobileRespon} from '../../services/core/auth/authService';
import stateService from '../../services/stateService';
import {LogInAccount} from "@/types/chat.types.ts";
import { ROUTE_LOGIN } from '../../config/app/routes';


/**
 * 國家代碼選項
 */
type CountryCode = '+886' | '+86';

/**
 * 手機號碼登錄頁面組件
 * 提供手機號碼和驗證碼登錄功能
 */
const OtpLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  
  // 檢查是否從登錄頁面直接跳轉而來
  const fromLoginPage = location.state && (location.state as any).fromLoginPage;
  const skipTokenCheck = location.state && (location.state as any).skipTokenCheck;
  
  // 從 Redux store 獲取狀態
  const { 
    sendingOtp,
    otpSent,
    sendOtpError,
    loggingIn,
    loginError,
    onceToken,
    otpValidSeconds,
  } = useAppSelector((state) => state.auth);
  
  // 記錄 useEffect 中是否已經執行過 token 檢查，避免重複執行
  const [tokenCheckExecuted, setTokenCheckExecuted] = useState(false);
  
  // 如果是從登錄頁面跳轉而來，並且設置了跳過 token 檢查，則記錄已執行過
  useEffect(() => {
    if (fromLoginPage && skipTokenCheck && !tokenCheckExecuted) {
      logService.info('從登錄頁跳轉而來，跳過 token 驗證');
      setTokenCheckExecuted(true);
    }
  }, [fromLoginPage, skipTokenCheck, tokenCheckExecuted]);
  
  const [countryCode, setCountryCode] = useState<CountryCode>('+886');
  const [showCountryCodePopup, setShowCountryCodePopup] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 表單是否有效
  const [isFormValid, setIsFormValid] = useState(false);
  const [isPhoneValid, setIsPhoneValid] = useState(false);
  
  // 驗證碼輸入框是否可用
  const [verificationInputEnabled, setVerificationInputEnabled] = useState(false);

  // 記錄發送驗證碼時使用的手機號，用於驗證一致性
  const [otpPhoneNumber, setOtpPhoneNumber] = useState<string>('');
  const [otpCountryCode, setOtpCountryCode] = useState<CountryCode>('+886');

  // 處理登錄後的初始化流程狀態，防止重複點擊
  const [isProcessingLogin, setIsProcessingLogin] = useState(false);

  /**
   * 處理返回按鈕點擊
   */
  const handleBackClick = () => {
    logService.info('用戶點擊返回按鈕');
    dispatch(resetOtpState());
    setOtpPhoneNumber('');
    setOtpCountryCode('+886');
    // 重置倒計時狀態
    setCountdown(0);
    navigate(ROUTE_LOGIN);
  };

  /**
   * 處理倒數計時效果
   */
  useEffect(() => {
    if (otpSent && otpValidSeconds) {
      setCountdown(otpValidSeconds);
    }
  }, [otpSent, otpValidSeconds]);

  /**
   * 處理倒計時
   */
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  /**
   * 處理國家代碼選擇
   */
  const handleCountryCodeSelect = (code: CountryCode) => {
    const oldCountryCode = countryCode;
    logService.info('用戶選擇國家代碼', { oldCode: oldCountryCode, newCode: code });

    setCountryCode(code);
    setShowCountryCodePopup(false);

    // 重新驗證手機號
    const phoneNumber = form.getFieldValue('phoneNumber') || '';
    let valid = false;
    if (code === '+886') {
      valid = /^09\d{8}$/.test(phoneNumber);
    } else if (code === '+86') {
      valid = /^1\d{10}$/.test(phoneNumber);
    }

    setIsPhoneValid(valid);

    // 如果國家代碼發生變化，重置驗證碼相關狀態
    if (oldCountryCode !== code && phoneNumber) {
      logService.info('國家代碼已變更，重置驗證碼狀態', {
        oldCode: oldCountryCode,
        newCode: code,
        phoneNumber
      });

      // 重置驗證碼相關狀態
      dispatch(resetOtpState());
      setVerificationInputEnabled(false);
      form.setFieldValue('verificationCode', '');
      setOtpPhoneNumber('');
      setOtpCountryCode(code);
      // 重置倒計時狀態
      setCountdown(0);

      // 顯示提示信息
      if (onceToken) {
        Toast.show({
          content: '國家代碼已變更，請重新發送驗證碼',
          duration: 3000,
        });
      }
    }

    // 如果手機號無效且有輸入，顯示錯誤信息
    if (phoneNumber && !valid) {
      form.setFields([
        { name: 'phoneNumber', errors: ['請輸入正確格式'] }
      ]);
    } else {
      form.setFields([
        { name: 'phoneNumber', errors: [] }
      ]);
    }
  };

  /**
   * 根據國家代碼獲取手機號驗證規則
   */
  const getPhoneRules = useCallback((): Rule[] => {
    return [
      { required: true, message: '請輸入手機號碼' },
      {
        validator: (_: any, value: string) => {
          if (!value) return Promise.resolve();
          
          // 根據國家代碼驗證手機號格式
          let isValid = false;
          
          if (countryCode === '+886') {
            // 台灣手機號驗證: 09開頭加8位數字
            isValid = /^09\d{8}$/.test(value);
          } else if (countryCode === '+86') {
            // 中國大陸手機號驗證: 1開頭加10位數字
            isValid = /^1\d{10}$/.test(value);
          }
          
          return isValid 
            ? Promise.resolve() 
            : Promise.reject(new Error('請輸入正確格式'));
        }
      }
    ];
  }, [countryCode]);

  /**
   * 驗證碼驗證規則
   */
  const verificationCodeRules: Rule[] = [
    { required: true, message: '請輸入驗證碼' },
    {
      pattern: /^\d{6}$/,
      message: '驗證碼錯誤請重新確認'
    }
  ];

  /**
   * 發送驗證碼
   */
  const handleSendCode = async () => {
    try {
      // 驗證手機號碼格式
      const phoneValidation = await form.validateFields(['phoneNumber']);
      if (!phoneValidation.phoneNumber) {
        Toast.show({
          content: '請輸入手機號碼',
          duration: 3000,
        });
        return;
      }

      // 清除之前的驗證碼輸入
      form.setFieldValue('verificationCode', '');

      logService.info('用戶請求發送驗證碼', {
        phoneNumber: phoneValidation.phoneNumber,
        countryCode,
        fullPhoneNumber: `${countryCode}${phoneValidation.phoneNumber}`
      });

      // 使用當前表單中的手機號碼發送驗證碼
      await dispatch(sendOtp({
        mobile: phoneValidation.phoneNumber,
        countryCode: countryCode,
        type: ConstantUtil.LOGIN_TYPE_OTP
      })).unwrap();

      // 記錄發送驗證碼時使用的手機號和國家代碼
      setOtpPhoneNumber(phoneValidation.phoneNumber);
      setOtpCountryCode(countryCode);

      // 發送成功後，啟用驗證碼輸入框
      setVerificationInputEnabled(true);

      Toast.show({
        content: '驗證碼已發送',
        duration: 2000,
      });

    } catch (error: any) {
      logService.error('發送驗證碼失敗', error);
      Toast.show({
        content: error || '發送驗證碼失敗',
        duration: 3000,
      });
    }
  };

  /**
   * 繼續驗證
   */
  const handleContinue = async () => {
    // 防止重複點擊
    if (loggingIn || isProcessingLogin) {
      logService.warn('登錄正在進行中，忽略重複點擊');
      return;
    }

    // 立即設置處理狀態，防止重複點擊
    setIsProcessingLogin(true);

    try {
      // 驗證表單
      const values = await form.validateFields();
      const { phoneNumber, verificationCode } = values;

      if (!onceToken) {
        setIsProcessingLogin(false);
        Toast.show({
          content: '請先發送驗證碼',
          duration: 3000,
        });
        return;
      }

      if (!phoneNumber) {
        setIsProcessingLogin(false);
        Toast.show({
          content: '請輸入手機號碼',
          duration: 3000,
        });
        return;
      }

      // 檢查當前手機號是否與發送驗證碼時的手機號一致
      if (otpPhoneNumber && (phoneNumber !== otpPhoneNumber || countryCode !== otpCountryCode)) {
        logService.warn('手機號碼不一致，需要重新發送驗證碼', {
          currentPhone: `${countryCode}${phoneNumber}`,
          otpPhone: `${otpCountryCode}${otpPhoneNumber}`
        });

        setIsProcessingLogin(false);
        Toast.show({
          content: '手機號已變更，請重新發送驗證碼',
          duration: 3000,
        });

        // 重置驗證碼狀態
        dispatch(resetOtpState());
        setVerificationInputEnabled(false);
        form.setFieldValue('verificationCode', '');
        setOtpPhoneNumber('');
        setOtpCountryCode('+886');
        // 重置倒計時狀態
        setCountdown(0);
        return;
      }

      // 獲取登錄賬號
      const loginAccount = stateService.loginAccount();
      logService.info('登錄賬號', {
        loginAccount
      });
      // 檢查是否符合特殊處理條件：
      // 1. 登錄賬號為空，或者
      // 2. 登錄類型為LINE且mobile為空
      if (loginAccount && (loginAccount.loginType === ConstantUtil.LOGIN_TYPE_LINE && !loginAccount.mobile)) {
        logService.info('符合特殊處理條件，執行validateOtp流程', {
          hasLoginAccount: !!loginAccount,
          loginType: loginAccount?.loginType,
          hasMobile: !!loginAccount?.mobile
        });

        // 驗證OTP
        const validateResult = await authService.validateOtp({
          onceToken: onceToken,
          checkCode: verificationCode
        });
        console.log('validateResult', validateResult.toString());
        if (validateResult?.success) {
          logService.info('OTP驗證成功，更新賬號手機號碼');
          
          // 更新賬號手機號碼
          const accountId = loginAccount?.accountId || '';
          if (!accountId) {
            logService.error('缺少賬號ID，無法更新手機號碼');
            setIsProcessingLogin(false);
            Toast.show({
              content: '賬號資訊不完整，請重新登錄',
              duration: 3000,
            });
            return;
          }
          console.log('validateResult', validateResult.toString());
          const updateResult:UpdateAccountMobileRespon = await authService.updateAccountMobile({
            mobile: phoneNumber,
            countryCode: countryCode,
            accountId: accountId
          });
          console.log('updateResult', updateResult.toString());
          if (updateResult?.success) {
            logService.info('手機號碼更新成功，更新本地賬號資訊並跳轉');
            
            // 更新本地賬號資訊
            if (loginAccount) {
              const updatedAccount: LogInAccount = {
                ...loginAccount,
                mobile: phoneNumber,
                countryCode: countryCode,
               };

              stateService.setLoginAccount(updatedAccount);

              try {
                const accountData = updatedAccount as unknown as AccountResponse;
                // 先執行 Redux 操作
                dispatch(restoreAuthToken(accountData.tokenId ?? null));
                dispatch(setAccount(accountData));

                // 然後處理登錄成功邏輯
                await authService.handleLoginSuccess(accountData, navigate);
                // 成功跳轉後不重置狀態，讓按鈕保持登錄中狀態
              } catch (error) {
                logService.error('後台登錄初始化失敗', { error });
                setIsProcessingLogin(false);
              }
            }else{
              logService.error('更新手機號碼失敗', updateResult);
              setIsProcessingLogin(false);
              Toast.show({
                content: updateResult?.msg || '更新手機號碼失敗',
                duration: 3000,
              });
            }

        
            // 跳轉到用戶註冊頁面
           // navigate(ConstantUtil.ROUTE_USER_SIGNUP, { replace: true });
          } else {
            logService.error('更新手機號碼失敗', updateResult);
            setIsProcessingLogin(false);
            Toast.show({
              content: updateResult?.msg || '更新手機號碼失敗',
              duration: 3000,
            });
          }
        } else {
          logService.error('OTP驗證失敗', validateResult);
          setIsProcessingLogin(false);
          Toast.show({
            content: validateResult?.msg || '驗證碼驗證失敗',
            duration: 3000,
          });
        }
      } else {
        // 常規OTP登錄流程
        logService.info('執行常規OTP登錄流程');

        // 調用 login action，使用當前表單中的手機號碼
        const loginResult = await dispatch(login({
          onceToken: onceToken,
          checkCode: verificationCode,
          type: ConstantUtil.LOGIN_TYPE_OTP
        })).unwrap();

        if (loginResult && loginResult.data) {
          try {
            if (loginResult.data) {
              // 先執行 Redux 操作
              dispatch(restoreAuthToken(loginResult.data.tokenId ?? null));
              dispatch(setAccount(loginResult.data));

              // 然後處理登錄成功邏輯
              await authService.handleLoginSuccess(loginResult.data, navigate);
              // 成功跳轉後不重置狀態，讓按鈕保持登錄中狀態
            }
          } catch (error) {
            logService.error('後台登錄初始化失敗', { error });
            setIsProcessingLogin(false);
          }
        }
      }

    } catch (error: any) {
      logService.error('登錄流程失敗', { error: error?.message || '未知錯誤' });
      setIsProcessingLogin(false);
      Toast.show({
        content: error?.message || '登錄失敗，請重試',
        duration: 3000,
      });
    }
    // 移除 finally 塊，只在錯誤情況下重置狀態
  };

  // 處理手機號碼輸入
  const handlePhoneChange = (value: string) => {
    // 只允許輸入數字
    const numericValue = value.replace(/[^0-9]/g, '');
    const currentPhoneNumber = form.getFieldValue('phoneNumber') || '';

    // 檢查手機號是否發生變化
    const phoneChanged = numericValue !== currentPhoneNumber;

    form.setFieldValue('phoneNumber', numericValue);

    // 實時驗證手機號格式
    let valid = false;
    if (countryCode === '+886') {
      valid = /^09\d{8}$/.test(numericValue);
    } else if (countryCode === '+86') {
      valid = /^1\d{10}$/.test(numericValue);
    }

    setIsPhoneValid(valid);

    // 如果手機號發生變化，重置驗證碼相關狀態
    if (phoneChanged && numericValue !== currentPhoneNumber) {
      logService.info('手機號碼已變更，重置驗證碼狀態', {
        oldPhone: currentPhoneNumber,
        newPhone: numericValue
      });

      // 重置驗證碼相關狀態
      dispatch(resetOtpState());
      setVerificationInputEnabled(false);
      form.setFieldValue('verificationCode', '');
      setOtpPhoneNumber('');
      setOtpCountryCode('+886');
      // 重置倒計時狀態
      setCountdown(0);

      // 顯示提示信息
      if (onceToken) {
        Toast.show({
          content: '手機號已變更，請重新發送驗證碼',
          duration: 3000,
        });
      }
    }

    // 如果手機號無效，顯示錯誤信息
    if (numericValue && !valid) {
      form.setFields([
        { name: 'phoneNumber', errors: ['請輸入正確格式'] }
      ]);
    } else {
      form.setFields([
        { name: 'phoneNumber', errors: [] }
      ]);
    }

    // 更新表單有效狀態
    const verificationCode = form.getFieldValue('verificationCode') || '';
    const codeValid = Boolean(verificationCode) && /^\d{6}$/.test(verificationCode);
    setIsFormValid(valid && codeValid);
  };

  // 處理驗證碼輸入
  const handleVerificationChange = (value: string) => {
    // 如果驗證碼輸入框未啟用，則不允許輸入
    if (!verificationInputEnabled) return;
    
    // 只允許輸入數字
    const numericValue = value.replace(/[^0-9]/g, '');
    form.setFieldValue('verificationCode', numericValue);
    
    // 更新表單有效狀態
    const codeValid = Boolean(numericValue) && /^\d{6}$/.test(numericValue);
    setIsFormValid(isPhoneValid && codeValid);
  };

  // 監聽OTP發送成功事件
  useEffect(() => {
    if (otpSent) {
      setVerificationInputEnabled(true);
    }
  }, [otpSent]);



  return (
    <div className="phone-login-page">
      {/* 頂部導航 */}
      <div className="phone-login-page__navbar">
        <div 
          className="phone-login-page__back-button"
          onClick={handleBackClick}
        >
          <img src={backArrow} alt="返回" className="phone-login-page__back-icon" />
        </div>
        <div className="phone-login-page__title-container">
          <h1 className="phone-login-page__title">手機驗證碼登錄</h1>
        </div>
        <div className="phone-login-page__spacer"></div>
      </div>

      {/* 表單區域 */}
      <Form 
        form={form}
        className="phone-login-page__form"
        requiredMarkStyle="none"
        layout="vertical"
        initialValues={{
          phoneNumber: '',
          verificationCode: ''
        }}
        onFinish={handleContinue}
      >
        {/* 手機號碼輸入 */}
        <div className="phone-login-page__form-group">
          <div className="phone-login-page__form-label">手機號碼</div>
          <Form.Item
            name="phoneNumber"
            rules={getPhoneRules()}
            validateTrigger={['onBlur']}
          >
            <div className="phone-login-page__phone-input-container">
              <div className="phone-login-page__country-code-container">
                <div
                  className="phone-login-page__country-code"
                  onClick={() => setShowCountryCodePopup(true)}
                >
                  <span className="phone-login-page__country-code-text">{countryCode}</span>
                  <img src={downArrow} alt="選擇" className="phone-login-page__down-icon" />
                </div>
                
                <Popup
                  visible={showCountryCodePopup}
                  onMaskClick={() => setShowCountryCodePopup(false)}
                  position="bottom"
                  bodyStyle={{ 
                    borderTopLeftRadius: '8px', 
                    borderTopRightRadius: '8px',
                    padding: '16px'
                  }}
                >
                  <div className="phone-login-page__popup-title">選擇國家/地區代碼</div>
                  <div className="phone-login-page__popup-list">
                    <div 
                      className="phone-login-page__popup-item"
                      onClick={() => handleCountryCodeSelect('+886')}
                    >
                      <span>台灣 +886</span>
                    </div>
                    <div 
                      className="phone-login-page__popup-item--last"
                      onClick={() => handleCountryCodeSelect('+86')}
                    >
                      <span>中國大陸 +86</span>
                    </div>
                  </div>
                </Popup>
              </div>
              <div className="phone-login-page__divider"></div>
              <Input 
                onChange={handlePhoneChange}
                placeholder="請輸入您的手機號碼"
                className="phone-login-page__phone-input"
                maxLength={countryCode === '+886' ? 10 : 11}
              />
            </div>
          </Form.Item>
        </div>

        {/* 驗證碼輸入 */}
        <div className="phone-login-page__form-group">
          <Form.Item
            name="verificationCode"
            rules={verificationCodeRules}
            validateTrigger={['onBlur']}
          >
            <div className="phone-login-page__verification-row">
              <div className="phone-login-page__form-label-inline">驗證碼</div>
              <div className="phone-login-page__verification-container">
                <Input
                  onChange={handleVerificationChange}
                  placeholder="請輸入驗證碼"
                  maxLength={6}
                  className="phone-login-page__verification-input"
                  disabled={!verificationInputEnabled}
                />
                <button
                  type="button"
                  onClick={handleSendCode}
                  disabled={!isPhoneValid || sendingOtp || countdown > 0}
                  className={`phone-login-page__send-button ${(!isPhoneValid || sendingOtp || countdown > 0) ? 'phone-login-page__send-button--disabled' : ''}`}
                >
                  {sendingOtp ? '發送中...' : countdown > 0 ? `${countdown}秒後重發` : '發送驗證碼'}
                </button>
              </div>
            </div>
          </Form.Item>
        </div>

        {/* 錯誤信息顯示 */}
        {sendOtpError && (
          <div className="phone-login-page__error-message">{sendOtpError}</div>
        )}
        {loginError && (
          <div className="phone-login-page__error-message">{loginError}</div>
        )}
      </Form>

      {/* 底部固定按鈕 */}
      <div className="phone-login-page__button-container">
        <Button
          block
          color="primary"
          disabled={!isFormValid || !verificationInputEnabled || loggingIn || isProcessingLogin}
          onClick={() => form.submit()}
          loading={loggingIn || isProcessingLogin}
          className="phone-login-page__continue-button"
          style={{
            opacity: isFormValid ? 1 : 0.4,
            backgroundColor: '#1677FF'
          }}
        >
          {isProcessingLogin ? '登錄中...' : '繼續'}
        </Button>
      </div>
    </div>
  );
};

export default OtpLoginPage; 