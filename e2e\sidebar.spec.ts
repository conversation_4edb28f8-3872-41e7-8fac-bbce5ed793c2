import { test, expect } from '@playwright/test';

test.describe('Sidebar 組件 E2E 測試', () => {
  test.beforeEach(async ({ page }) => {
    // 模擬登入狀態
    await page.addInitScript(() => {
      // 模擬 localStorage 中的登入信息
      localStorage.setItem('authToken', 'mock-token');
      localStorage.setItem('loginUser', JSON.stringify({
        id: 'user1',
        name: '測試用戶',
        accountId: 'account1',
        avatarId: 'avatar1',
        tenantId: 'tenant1'
      }));
      localStorage.setItem('currentTenantId', 'tenant1');
    });

    // 模擬 API 響應
    await page.route('**/api/tenant/relations', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'tenant1',
              name: '我的團隊',
              accountId: 'account1',
              isLastTenant: true,
              avatarId: 'avatar1',
              unReadCount: 0
            },
            {
              id: 'tenant2',
              name: '其他團隊',
              accountId: 'account2',
              isLastTenant: false,
              avatarId: 'avatar2',
              unReadCount: 5
            },
            {
              id: 'tenant3',
              name: '我的另一個團隊',
              accountId: 'account1',
              isLastTenant: false,
              avatarId: 'avatar3',
              unReadCount: 0
            }
          ]
        })
      });
    });

    // 模擬 applyToken API
    await page.route('**/api/token/apply', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: {
              id: 'user1',
              name: '測試用戶',
              tenantId: 'tenant2'
            },
            tenantInfo: {
              id: 'tenant2',
              name: '其他團隊'
            }
          }
        })
      });
    });

    // 導航到首頁
    await page.goto('/');
    
    // 等待頁面加載完成
    await page.waitForLoadState('networkidle');
  });

  test('應該正確顯示側邊欄和用戶信息', async ({ page }) => {
    // 點擊側邊欄按鈕打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();
    
    // 檢查用戶信息是否正確顯示
    await expect(page.locator('.sidebar-username')).toHaveText('測試用戶');
    await expect(page.locator('.sidebar-team-title')).toHaveText('團隊');
  });

  test('應該正確排序租戶列表（accountId 相同的在前）', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 獲取所有團隊項目
    const teamItems = page.locator('.sidebar-team-item');
    
    // 檢查第一個團隊應該是 "我的團隊"（accountId 相同）
    await expect(teamItems.nth(0).locator('.sidebar-team-text')).toHaveText('我的團隊');
    
    // 檢查第三個團隊應該是 "我的另一個團隊"（accountId 相同）
    await expect(teamItems.nth(2).locator('.sidebar-team-text')).toHaveText('我的另一個團隊');
  });

  test('只有相同 accountId 的租戶才顯示 user add 圖標', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 檢查 user add 圖標的數量（只有 accountId 為 'account1' 的租戶才有）
    const userAddIcons = page.locator('.sidebar-user-add-icon');
    await expect(userAddIcons).toHaveCount(2);
  });

  test('只有相同 accountId 的租戶才顯示加入團隊按鈕', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 檢查加入團隊按鈕是否存在
    await expect(page.locator('.sidebar-team-text').filter({ hasText: '加入其他團隊' })).toBeVisible();
  });

  test('點擊當前活躍租戶應該只關閉側邊欄', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 點擊當前活躍的團隊
    const activeTeam = page.locator('.sidebar-team-item.active .sidebar-team-text');
    await activeTeam.click();

    // 側邊欄應該關閉
    await expect(page.locator('.sidebar.open')).not.toBeVisible();
  });

  test('點擊非活躍租戶應該進行切換', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 點擊非活躍的團隊
    const inactiveTeam = page.locator('.sidebar-team-item:not(.active) .sidebar-team-text').first();
    await inactiveTeam.click();

    // 應該顯示切換中的加載狀態
    await expect(page.locator('.sidebar-team-switching')).toBeVisible();

    // 等待切換完成
    await page.waitForTimeout(1000);

    // 側邊欄應該關閉
    await expect(page.locator('.sidebar.open')).not.toBeVisible();
  });

  test('切換租戶失敗時應該顯示錯誤提示', async ({ page }) => {
    // 模擬 applyToken API 失敗
    await page.route('**/api/token/apply', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          msg: '切換失敗'
        })
      });
    });

    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 點擊非活躍的團隊
    const inactiveTeam = page.locator('.sidebar-team-item:not(.active) .sidebar-team-text').first();
    await inactiveTeam.click();

    // 等待錯誤提示出現
    await page.waitForTimeout(1000);

    // 檢查是否有錯誤提示（Toast 或其他錯誤顯示）
    // 這裡需要根據實際的錯誤提示實現來調整
  });

  test('數據加載失敗時應該顯示重試選項', async ({ page }) => {
    // 模擬 API 失敗
    await page.route('**/api/tenant/relations', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          msg: '服務器錯誤'
        })
      });
    });
    
    // 重新加載頁面
    await page.reload();
    await page.waitForLoadState('networkidle');

    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 檢查是否顯示重試選項
    await expect(page.locator('.sidebar-error')).toHaveText('載入失敗，點擊重試');
  });

  test('沒有數據時應該顯示空狀態', async ({ page }) => {
    // 模擬空數據
    await page.route('**/api/tenant/relations', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: []
        })
      });
    });

    // 重新加載頁面
    await page.reload();
    await page.waitForLoadState('networkidle');

    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 檢查是否顯示空狀態
    await expect(page.locator('.sidebar-empty')).toHaveText('暫無團隊數據');
  });

  test('點擊側邊欄背景應該關閉側邊欄', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();

    // 點擊側邊欄背景（不是內容區域）
    const sidebar = page.locator('.sidebar');
    await sidebar.click({ position: { x: 10, y: 10 } });

    // 側邊欄應該關閉
    await expect(page.locator('.sidebar.open')).not.toBeVisible();
  });

  test('點擊側邊欄內容區域不應該關閉側邊欄', async ({ page }) => {
    // 打開側邊欄
    const sidebarButton = page.locator('[data-testid="sidebar-button"]');
    await sidebarButton.click();
    
    // 點擊側邊欄內容區域
    const sidebarContent = page.locator('.sidebar-content');
    await sidebarContent.click();
    
    // 側邊欄應該保持打開狀態
    await expect(page.locator('.sidebar.open')).toBeVisible();
  });
}); 