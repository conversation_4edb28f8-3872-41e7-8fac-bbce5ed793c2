import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState, AppThunk } from '../store';
import { messageService, Type } from '@/services/core/chat';
import { logService } from '@/services/system/logService';
import { ConstantUtil } from '@/utils/constantUtil';
import type { DateMessageGroup, IChatMessage } from '@/types/chat.types';
import { authService } from '@/services/core/auth';
import { roomDao } from '@/services/dao';

// 每個房間的消息狀態
interface RoomMessageState {
  dateGroups: DateMessageGroup[];
  currentPage: number;
  hasNextPage: boolean;
  isLoading: boolean;
  error: string | null;
  lastSequence: number;
}

// 全局消息狀態
export // 缓存相关接口
interface CacheEntry {
  key: string;
  value: IChatMessage[];
  timestamp: number;
  accessCount: number;
  lastAccessTime: number;
}

interface CacheState {
  entries: Record<string, CacheEntry>;
  stats: {
    hitCount: number;
    missCount: number;
    maxSize: number;
    maxMemoryMB: number;
  };
}

interface MessageState {
  rooms: Record<string, RoomMessageState>;
  activeRoomId: string | null;
  cache: CacheState;
}

const initialState: MessageState = {
  rooms: {},
  activeRoomId: null,
  cache: {
    entries: {},
    stats: {
      hitCount: 0,
      missCount: 0,
      maxSize: 100,
      maxMemoryMB: 50
    }
  }
};

/**
 * 计算历史消息请求的 lastSequence
 * 规则：lastSequence - pageSize + 1，最小值为1
 */
function calculateHistoryLastSequence(
  currentSequence: number,
  pageSize: number = ConstantUtil.DEFAULT_PAGE_SIZE
): number {
  return currentSequence<pageSize?currentSequence:Math.max(1, currentSequence - pageSize + 1);
}

/**
 * 从消息数组中获取最小序列号
 */
function getMinSequenceFromMessages(messages: any[]): number {
  const sequences = messages
    .map(msg => msg.sequence || 0)
    .filter(seq => typeof seq === 'number' && seq > 0);

  return sequences.length > 0 ? Math.min(...sequences) : 0;
}

/**
 * 从现有的日期组中创建消息映射，用于保留消息状态
 */
function createExistingMessagesMap(dateGroups: DateMessageGroup[]): Map<string, IChatMessage> {
  const messageMap = new Map<string, IChatMessage>();
  dateGroups.forEach(group => {
    group.messages.forEach(msg => {
      messageMap.set(msg.id, msg);
    });
  });
  return messageMap;
}

// 將原始消息格式化為按日期分組的格式
function formatMessagesToDateGroups(messages: any[], preserveStatus: boolean = false, existingMessages?: Map<string, IChatMessage>): DateMessageGroup[] {
  if (!messages || messages.length === 0) return [];
  const groups: { [date: string]: IChatMessage[] } = {};

  messages.forEach((msg) => {
    // 檢查消息發送時間是否為今天
    const msgDate = new Date(msg.sendTime);
    const today = new Date();
    const isToday = msgDate.getDate() === today.getDate() &&
                   msgDate.getMonth() === today.getMonth() &&
                   msgDate.getFullYear() === today.getFullYear();

    // 使用 "今天" 作為日期key，如果是今天的消息
    const dateKey = isToday ? '今天' : new Date(msg.sendTime).toLocaleDateString();

    // 如果需要保留狀態且存在已有消息，則使用已有消息的狀態
    let messageStatus = (msg.status || 'sent') as 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
    if (preserveStatus && existingMessages && existingMessages.has(msg.id)) {
      const existingMsg = existingMessages.get(msg.id);
      if (existingMsg && existingMsg.status) {
        const originalStatus = messageStatus;
        messageStatus = existingMsg.status;
        logService.info('🔄 保留消息狀態', {
          messageId: msg.id,
          originalStatus,
          preservedStatus: messageStatus,
          content: msg.content?.substring(0, 30),
          sequence: msg.sequence,
          说明: '补充遗漏消息时保留已读状态'
        });
      }
    }

    const chatMsg: IChatMessage = {
      id: msg.id,
      content: msg.content,
      time: new Date(msg.sendTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      timestamp: msg.sendTime,
      isUser: msg.senderId === authService.getUserId(),
      sender: {
        id: msg.senderId,
        name: msg.senderName,
        avatar: msg.senderAvatar || ''
      },
      type: msg.type as Type,
      status: messageStatus,
      metadata: msg.metadata,
      channel: msg.channel,
      sequence: msg.sequence // 添加sequence字段
    };

    if (!groups[dateKey]) groups[dateKey] = [];
    groups[dateKey].push(chatMsg);
  });
  
  // 對每個日期組內的消息按時間排序
  Object.keys(groups).forEach(dateKey => {
    groups[dateKey].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  });
  
  // 轉換為數組並按日期排序
  return Object.entries(groups)
    .map(([date, messages]) => ({ date, messages }))
    .sort((a, b) => {
      // 確保 "今天" 總是最後一個
      if (a.date === '今天') return 1;
      if (b.date === '今天') return -1;
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
}

// 輔助函數：檢查消息日期是否為今天
function isMessageFromToday(timestamp: string | number): boolean {
  const msgDate = new Date(timestamp);
  const today = new Date();
  return msgDate.getDate() === today.getDate() &&
         msgDate.getMonth() === today.getMonth() &&
         msgDate.getFullYear() === today.getFullYear();
}

const messageSlice = createSlice({
  name: 'message',
  initialState,
  reducers: {
    setActiveRoomId: (state, action: PayloadAction<string | null>) => {
      state.activeRoomId = action.payload;
    },
    setRoomMessages: (state, action: PayloadAction<{ 
      roomId: string, 
      dateGroups: DateMessageGroup[], 
      replace?: boolean 
    }>) => {
      const { roomId, dateGroups, replace = false } = action.payload;
      
      // 如果房間不存在，創建新房間
      if (!state.rooms[roomId]) {
        state.rooms[roomId] = {
          dateGroups: [],
          currentPage: 0,
          hasNextPage: true,
          isLoading: false,
          error: null,
          lastSequence: 0
        };
      }
      
      if (replace) {
        // 替換全部消息
        state.rooms[roomId].dateGroups = dateGroups;
      } else {
        // 合併消息組
        // 需要合併相同日期的消息
        const mergedGroups: DateMessageGroup[] = [...state.rooms[roomId].dateGroups];
        dateGroups.forEach(newGroup => {
          const existingGroupIndex = mergedGroups.findIndex(group => group.date === newGroup.date);
          if (existingGroupIndex >= 0) {
            // 合併相同日期的消息組
            const existingGroup = mergedGroups[existingGroupIndex];

            // 創建現有消息的映射，以便保留狀態
            const existingMsgMap = new Map(existingGroup.messages.map(msg => [msg.id, msg]));

            // 處理新消息，保留已有消息的狀態
            const processedNewMessages = newGroup.messages.map(newMsg => {
              const existingMsg = existingMsgMap.get(newMsg.id);
              if (existingMsg) {
                // 如果消息已存在，保留原有的狀態（特別是已讀狀態）
                if (existingMsg.status !== newMsg.status) {
                  logService.info('🔄 合并时保留消息状态', {
                    messageId: newMsg.id,
                    originalStatus: newMsg.status,
                    preservedStatus: existingMsg.status,
                    content: newMsg.content?.substring(0, 30),
                    sequence: newMsg.sequence,
                    说明: '合并消息时保留已有状态'
                  });
                }
                return {
                  ...newMsg,
                  status: existingMsg.status // 保留原有狀態
                };
              }
              return newMsg; // 新消息保持原狀態
            });

            // 檢查重複訊息並合併
            const existingMsgIds = new Set(existingGroup.messages.map(msg => msg.id));
            const uniqueNewMessages = processedNewMessages.filter(msg => !existingMsgIds.has(msg.id));

            // 更新已有消息的內容（保留狀態）
            const updatedExistingMessages = existingGroup.messages.map(existingMsg => {
              const newMsg = processedNewMessages.find(msg => msg.id === existingMsg.id);
              if (newMsg) {
                // 更新消息內容但保留狀態
                return {
                  ...newMsg,
                  status: existingMsg.status // 確保保留原有狀態
                };
              }
              return existingMsg;
            });

            mergedGroups[existingGroupIndex] = {
              ...existingGroup,
              messages: [...updatedExistingMessages, ...uniqueNewMessages]
            };

            // 確保消息按時間排序
            mergedGroups[existingGroupIndex].messages.sort((a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
            );
          } else {
            // 新增日期組
            mergedGroups.push(newGroup);
          }
        });
        
        // 按日期排序，確保"今天"在最後
        mergedGroups.sort((a, b) => {
          if (a.date === '今天') return 1;
          if (b.date === '今天') return -1;
          
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          return dateA.getTime() - dateB.getTime();
        });
        
        state.rooms[roomId].dateGroups = mergedGroups;
      }
    },
    updateRoomMessageStatus: (state, action: PayloadAction<{
      roomId: string,
      currentPage: number,
      hasNextPage: boolean,
      lastSequence: number,
      source:string
    }>) => {
      const { roomId, currentPage, hasNextPage, lastSequence,source } = action.payload;
      if (state.rooms[roomId]) {
        const oldState = {
          currentPage: state.rooms[roomId].currentPage,
          hasNextPage: state.rooms[roomId].hasNextPage,
          lastSequence: state.rooms[roomId].lastSequence
        };

        state.rooms[roomId].currentPage = currentPage;
        state.rooms[roomId].hasNextPage = hasNextPage;
        state.rooms[roomId].lastSequence = lastSequence;

        // 添加详细日志
        logService.info('🔄 更新房间消息状态', {
          roomId,
          变更前: oldState,
          来源: source,
          变更后: { currentPage, hasNextPage, lastSequence },
          hasNextPage变化: oldState.hasNextPage !== hasNextPage ? `${oldState.hasNextPage} → ${hasNextPage}` : '无变化'
        });
      }
    },
    setRoomLoading: (state, action: PayloadAction<{ roomId: string, isLoading: boolean }>) => {
      const { roomId, isLoading } = action.payload;
      if (state.rooms[roomId]) {
        state.rooms[roomId].isLoading = isLoading;
      } else {
        state.rooms[roomId] = {
          dateGroups: [],
          currentPage: 0,
          hasNextPage: true,
          isLoading,
          error: null,
          lastSequence: 0
        };
      }
    },
    setRoomError: (state, action: PayloadAction<{ roomId: string, error: string | null }>) => {
      const { roomId, error } = action.payload;
      if (state.rooms[roomId]) {
        state.rooms[roomId].error = error;
      } else {
        state.rooms[roomId] = {
          dateGroups: [],
          currentPage: 0,
          hasNextPage: true,
          isLoading: false,
          error,
          lastSequence: 0
        };
      }
    },
    addMessage: (state, action: PayloadAction<{ roomId: string, message: IChatMessage }>) => {
      const { roomId, message } = action.payload;

      logService.info('添加消息到Redux', {
        roomId,
        messageId: message.id,
        senderId: message.sender?.id,
        content: message.content?.substring(0, 20) + '...',
        sequence: message.sequence,
        status: message.status,
        timestamp: message.timestamp,
        templateId: message.templateId
      });
      
      // 如果房間不存在，創建它
      if (!state.rooms[roomId]) {
        state.rooms[roomId] = {
          dateGroups: [],
          currentPage: 0,
          hasNextPage: true,
          isLoading: false,
          error: null,
          lastSequence: 0
        };
      }
      
      // 檢查消息是否為今天的
      const isToday = isMessageFromToday(message.timestamp);
      const messageDate = isToday ? '今天' : new Date(message.timestamp).toLocaleDateString();
      
      // 檢查是否有 "今天" 的消息組
      const dateGroupIndex = state.rooms[roomId].dateGroups.findIndex(
        group => group.date === messageDate
      );
      
      if (dateGroupIndex >= 0) {
        // 檢查消息是否已存在
        const existingMsgIndex = state.rooms[roomId].dateGroups[dateGroupIndex].messages.findIndex(
          msg => msg.id === message.id
        );
        
        if (existingMsgIndex >= 0) {
          // 更新現有消息
          const oldMessage = state.rooms[roomId].dateGroups[dateGroupIndex].messages[existingMsgIndex];
          state.rooms[roomId].dateGroups[dateGroupIndex].messages[existingMsgIndex] = message;

          // 如果消息新增了sequence字段，记录日志
          if (!oldMessage.sequence && message.sequence) {
            logService.info('消息sequence已更新', {
              roomId,
              messageId: message.id,
              oldSequence: oldMessage.sequence,
              newSequence: message.sequence,
              senderId: message.sender?.id,
              content: message.content?.substring(0, 20) + '...'
            });
          }
        } else {
          // 添加新消息
          state.rooms[roomId].dateGroups[dateGroupIndex].messages.push(message);
          
          // 按時間排序消息
          state.rooms[roomId].dateGroups[dateGroupIndex].messages.sort(
            (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
        }
      } else {
        // 創建新日期組
        const newGroup: DateMessageGroup = {
          date: messageDate,
          messages: [message]
        };
        
        // 添加新組並按日期排序
        state.rooms[roomId].dateGroups.push(newGroup);
        
        // 按日期排序，確保 "今天" 在最後
        state.rooms[roomId].dateGroups.sort((a, b) => {
          if (a.date === '今天') return 1;
          if (b.date === '今天') return -1;
          
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          return dateA.getTime() - dateB.getTime();
        });
      }
    },
    updateMessageStatus: (state, action: PayloadAction<{
      roomId: string,
      messageId: string,
      tempId: string | null,
      status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed',
      updatedId?: string,
      lastSequence?: number,
      sequence?: number
    }>) => {
      const { roomId, messageId, tempId, status, updatedId, sequence } = action.payload;

      if (!state.rooms[roomId]) return;

      // 查找並更新消息狀態
      for (const group of state.rooms[roomId].dateGroups) {
        const messageIndex = group.messages.findIndex(
          msg => msg.id === (tempId || messageId)
        );

        if (messageIndex >= 0) {
          group.messages[messageIndex] = {
            ...group.messages[messageIndex],
            id: messageId || updatedId|| group.messages[messageIndex].id,
            status,
            ...(sequence && { sequence }), // 如果有sequence则添加
            templateId: undefined // 清除templateId，因为消息已被服务端确认
          };
          break;
        }
      }
    },
    // 批量更新消息已读已到状态
    updateMessagesStatusBySequence: (state, action: PayloadAction<{
      roomId: string,
      maxSequence: number,
      status: 'delivered' | 'read',
      ownerId: string
    }>) => {
      const { roomId, maxSequence, status, ownerId } = action.payload;

      if (!state.rooms[roomId]) return;

      let updatedCount = 0;

      // 获取当前用户ID
      const currentUserId = authService.getUserId();

      // 检查房间是否存在以及消息组数量
      if (!state.rooms[roomId]) {
        logService.warn('房间不存在', { roomId });
        return;
      }

      const dateGroups = state.rooms[roomId].dateGroups;
      logService.info('开始批量更新消息状态', {
        roomId,
        maxSequence,
        status,
        ownerId,
        currentUserId,
        dateGroupsCount: dateGroups.length,
        totalMessages: dateGroups.reduce((total, group) => total + group.messages.length, 0)
      });

      // 遍历所有日期组的消息
      for (const group of dateGroups) {
        logService.debug('检查日期组', {
          date: group.date,
          messagesCount: group.messages.length
        });
        for (const message of group.messages) {
          // 记录每条消息的详细信息
          const hasSequence = message.sequence !== undefined;
          const sequenceInRange = hasSequence ? message.sequence! <= maxSequence : false;

          logService.debug('检查消息', {
            messageId: message.id,
            senderId: message.sender?.id,
            currentUserId,
            ownerId,
            sequence: message.sequence,
            maxSequence,
            currentStatus: message.status,
            targetStatus: status,
            content: message.content?.substring(0, 20) + '...',
            hasSequence,
            sequenceInRange,
          });

          // 只更新当前用户发送的消息（右侧消息），且序列号小于等于指定值
          // 对于没有sequence的消息，暂时跳过状态更新，等待WebSocket推送提供sequence
          if (
            message.sender?.id != ownerId &&
            message.sequence !== undefined &&
            message.sequence <= maxSequence &&
            message.status !== 'failed' &&
            message.status !== status // 避免重复更新
          ) {
            logService.debug('检查消息更新条件通过', {
              messageId: message.id,
              content:message.content,
              senderId: message.sender?.id,
              currentUserId,
              ownerId,
              sequence: message.sequence,
              maxSequence,
              currentStatus: message.status,
              targetStatus: status
            });
            // 状态优先级：read > delivered > sent > sending
            const shouldUpdate =
              (status === 'delivered' && (message.status === 'sent' || message.status === 'sending')) ||
              (status === 'read' && (message.status === 'delivered' || message.status === 'sent' || message.status === 'sending'));

            if (shouldUpdate) {
              logService.debug ('更新消息状态', {
                messageId: message.id,
                senderId: message.sender?.id,
                currentUserId,
                sequence: message.sequence,
                oldStatus: message.status,
                newStatus: status,
                content:message.content,
                isUser: message.isUser
              });
              message.status = status;
              updatedCount++;
            }
          }
        }
      }

      logService.info('批量更新消息状态完成', {
        roomId,
        maxSequence,
        status,
        updatedCount,
        ownerId,
        说明: updatedCount === 0 ? '没有消息被更新，可能是sequence缺失' : `成功更新${updatedCount}条消息`
      });
    },
    // 重新检查并更新消息状态（用于处理sequence延迟到达的情况）
    recheckMessagesStatus: (state, action: PayloadAction<{
      roomId: string,
      maxSequence: number,
      status: 'delivered' | 'read',
      ownerId: string
    }>) => {
      const { roomId, maxSequence, status, ownerId } = action.payload;

      if (!state.rooms[roomId]) return;

      const currentUserId = authService.getUserId();
      let updatedCount = 0;

      logService.info('重新检查消息状态', {
        roomId,
        maxSequence,
        status,
        ownerId,
        currentUserId,
        说明: '处理sequence延迟到达的情况'
      });

      // 遍历所有消息，查找之前因为缺少sequence而未更新的消息
      for (const group of state.rooms[roomId].dateGroups) {
        for (const message of group.messages) {
          if (
            message.sender?.id != ownerId &&
            message.sequence !== undefined &&
            message.sequence <= maxSequence &&
            message.status !== 'failed' &&
            message.status !== status
          ) {
            // 状态优先级检查
            const shouldUpdate =
              (status === 'delivered' && (message.status === 'sent' || message.status === 'sending')) ||
              (status === 'read' && (message.status === 'delivered' || message.status === 'sent' || message.status === 'sending'));

            if (shouldUpdate) {
              logService.debug('重新检查时更新消息状态', {
                messageId: message.id,
                sequence: message.sequence,
                oldStatus: message.status,
                newStatus: status
              });
              message.status = status;
              updatedCount++;
            }
          }
        }
      }

      logService.info('重新检查消息状态完成', {
        roomId,
        maxSequence,
        status,
        updatedCount
      });
    },
    deleteMessageById: (state, action: PayloadAction<{ roomId: string, messageId: string }>) => {
      const { roomId, messageId } = action.payload;

      if (!state.rooms[roomId]) return;

      // 遍历所有日期组，查找并删除消息
      for (const group of state.rooms[roomId].dateGroups) {
        const messageIndex = group.messages.findIndex(msg => msg.id === messageId);
        if (messageIndex >= 0) {
          group.messages.splice(messageIndex, 1);
          logService.info('从Redux中删除消息', { roomId, messageId });
          break;
        }
      }

      // 清理空的日期组
      state.rooms[roomId].dateGroups = state.rooms[roomId].dateGroups.filter(
        group => group.messages.length > 0
      );
    },
    resetMessages: () => initialState
  }
});

export const {
  setActiveRoomId,
  setRoomMessages,
  updateRoomMessageStatus,
  setRoomLoading,
  setRoomError,
  addMessage,
  updateMessageStatus,
  updateMessagesStatusBySequence,
  recheckMessagesStatus,
  deleteMessageById,
  resetMessages
} = messageSlice.actions;

// 從數據庫獲取消息的thunk（帶緩存）
export const fetchMessagesFromDB = (roomId: string, page: number = 0): AppThunk => async (dispatch, getState) => {
  if (!roomId) return;

  try {
    dispatch(setRoomLoading({ roomId, isLoading: true }));

    // 获取当前房间状态，确定查询起点
    const currentState = getState();
    const roomState = currentState.message.rooms[roomId];
    let beforeSequence: number | undefined = undefined;

    // 如果不是第一页，需要使用序列号过滤
    if (page > 0 && roomState) {
      const currentMessages = roomState.dateGroups.flatMap(group => group.messages);
      if (currentMessages.length > 0) {
        beforeSequence = Math.min(...currentMessages.map(msg => (msg as any).sequence || 0).filter(seq => seq > 0));
      }
    }

    logService.info('🗃️ fetchMessagesFromDB 查询参数', {
      roomId,
      page,
      beforeSequence,
      isFirstPage: page === 0
    });

    // 从数据库获取消息
    const messages = await messageService.getMessagesFromDB(
      roomId,
      page,
      ConstantUtil.DEFAULT_PAGE_SIZE,
      beforeSequence, // 使用序列号过滤（第一页时为undefined）
      'desc'
    );

    // 更新房间状态（重用之前获取的状态）
    const updatedRoomState = roomState || { lastSequence: 0, currentPage: 0 };

    if (messages.length > 0) {
      // 對消息進行時間排序（從早到晚）
      const sortedMessages = [...messages].sort((a, b) =>
        (a.sendTime || 0) - (b.sendTime || 0)
      );

      // 获取现有消息映射以保留状态（仅在非替换模式下）
      let existingMessagesMap: Map<string, IChatMessage> | undefined;
      if (page !== 0) {
        const existingDateGroups = updatedRoomState.dateGroups || [];
        existingMessagesMap = createExistingMessagesMap(existingDateGroups);
      }

      // 格式化消息，在非替换模式下保留已有状态
      const formattedGroups = formatMessagesToDateGroups(sortedMessages, page !== 0, existingMessagesMap);

      // 更新Redux狀態
      dispatch(setRoomMessages({
        roomId,
        dateGroups: formattedGroups,
        replace: page === 0
      }));

      // 獲取最新的序列號
      let latestSequence = updatedRoomState.lastSequence;
      
      // 查找最大序列號
      const sequences = messages
        .map(msg => msg.sequence || 0)
        .filter(seq => typeof seq === 'number');
        
      if (sequences.length > 0) {
        latestSequence = Math.max(...sequences);
      }
      
      // 使用當前頁碼的消息數量判斷是否還有下一頁
      // 只有在第一页且消息数量少于页面大小时才认为没有下一页
      // 对于后续页面，即使消息数量少于页面大小，也可能还有更多历史消息
      const hasNextPage = page === 0 ?
        messages.length >= ConstantUtil.DEFAULT_PAGE_SIZE :
        messages.length > 0;

      // 添加详细的分页判断日志
      logService.info('📊 分页状态判断 (fetchMessagesFromDB)', {
        roomId,
        page,
        消息数量: messages.length,
        页面大小: ConstantUtil.DEFAULT_PAGE_SIZE,
        判断逻辑: page === 0 ? '第一页：消息数量 >= 页面大小' : '后续页：消息数量 > 0',
        hasNextPage,
        计算过程: page === 0 ?
          `${messages.length} >= ${ConstantUtil.DEFAULT_PAGE_SIZE} = ${hasNextPage}` :
          `${messages.length} > 0 = ${hasNextPage}`
      });
      
      dispatch(updateRoomMessageStatus({
        roomId,
        currentPage: page,
        hasNextPage,
        lastSequence: latestSequence,
        source:'fetchMessagesFromDB:messages.length > 0'
      }));

      logService.debug('更新房間消息狀態', {
        roomId,
        page,
        messageCount: messages.length,
        hasNextPage,
        lastSequence: latestSequence,
        source: 'fetchMessagesFromDB'
      });
      
      // 新增: 獲取當前的消息已讀/已到狀態並更新新獲取的消息
      try {
        // 檢查當前房間是否有消息已讀/已到狀態
        const roomData = await roomDao.getRoomById(roomId);
        if (roomData && roomData.lastReadSequence && roomData.ownerId) {
          // 更新已讀狀態
          dispatch(updateMessagesStatusBySequence({
            roomId,
            maxSequence: roomData.lastReadSequence,
            status: 'read',
            ownerId: roomData.ownerId
          }));
          
          logService.info('💬 更新新獲取消息的已讀狀態', {
            roomId,
            page,
            lastReadSequence: roomData.lastReadSequence,
            ownerId: roomData.ownerId,
            source: 'fetchMessagesFromDB'
          });
        }
        
        if (roomData && roomData.lastDeliveredSequence && roomData.ownerId) {
          // 更新已到狀態 (只有當已到序列號大於已讀序列號時才需要)
          if (!roomData.lastReadSequence || roomData.lastDeliveredSequence > roomData.lastReadSequence) {
            dispatch(updateMessagesStatusBySequence({
              roomId,
              maxSequence: roomData.lastDeliveredSequence,
              status: 'delivered',
              ownerId: roomData.ownerId
            }));
            
            logService.info('💬 更新新獲取消息的已到狀態', {
              roomId,
              page,
              lastDeliveredSequence: roomData.lastDeliveredSequence,
              ownerId: roomData.ownerId,
              source: 'fetchMessagesFromDB'
            });
          }
        }
      } catch (statusError) {
        logService.warn('獲取房間狀態失敗，無法更新消息已讀/已到狀態', {
          roomId, 
          error: statusError, 
          source: 'fetchMessagesFromDB'
        });
      }
      
      dispatch(setRoomError({ roomId, error: null }));
    } else {
      // 如果沒有獲取到消息，但這不是第一頁，則可能是已經到達了末尾
      logService.warn('📭 未获取到消息', {
        roomId,
        page,
        是否第一页: page === 0,
        当前房间状态: roomState ? {
          currentPage: roomState.currentPage,
          hasNextPage: roomState.hasNextPage,
          lastSequence: roomState.lastSequence
        } : '房间状态不存在'
      });

      if (page > 0) {
        logService.info('🔚 后续页面无消息，设置 hasNextPage=false', { roomId, page });
        dispatch(updateRoomMessageStatus({
          roomId,
          currentPage: page,
          hasNextPage: false,
          lastSequence: updatedRoomState.lastSequence || 0,
          source:"fetchMessagesFromDB:page > 0"
        }));
      } else {
        // 如果是第一頁且沒有數據，設置空的消息組但不設置錯誤
        if (page === 0) {
          logService.info('📄 第一页无消息，初始化空状态', { roomId });
          dispatch(setRoomMessages({
            roomId,
            dateGroups: [],
            replace: true
          }));
          dispatch(updateRoomMessageStatus({
            roomId,
            currentPage: 0,
            hasNextPage: false,
            lastSequence: 0,
            source:"fetchMessagesFromDB:page === 0"  
          }));
          dispatch(setRoomError({ roomId, error: null }));
        }
      }
    }
  } catch (error: any) {
    logService.error('從數據庫獲取消息失敗', { error, roomId, page });
    dispatch(setRoomError({ roomId, error: error?.message || '獲取消息失敗' }));
  } finally {
    dispatch(setRoomLoading({ roomId, isLoading: false }));
  }
};

// 從API獲取消息的thunk
export const fetchMessagesFromAPI = (roomId: string, page: number,previous_sequence: number, last_sequence?: number): AppThunk => async (dispatch, getState) => {
  if (!roomId) return;

  try {
    logService.info('🌐 开始从API获取消息', {
      roomId,
      page,
      last_sequence,
      说明: page === 0 ? '获取最新消息' : '获取历史消息'
    });

    // 從API獲取消息
    const requestParams: any = {
      roomId,
      pageIndex: page,
      pageSize: ConstantUtil.DEFAULT_PAGE_SIZE,
      previous_sequence: previous_sequence,
      sort: 'desc'
    };

    // 只有當 last_sequence 有值時才添加到請求參數中
    if (last_sequence) {
      requestParams.last_sequence = last_sequence;
      requestParams.sort = 'asc';
    }

    const response = await messageService.fetchBaseSyncMessage(requestParams);

    if (response.success && response.data?.items) {
      const apiMessages = response.data.items;

      logService.info('📥 API返回消息', {
        roomId,
        page,
        previous_sequence,
        apiMessagesCount: apiMessages.length,
        apiSequenceRange: apiMessages.length > 0 ?
          `${Math.min(...apiMessages.map(m => m.sequence || 0))} - ${Math.max(...apiMessages.map(m => m.sequence || 0))}` : 'empty'
      });

      if(apiMessages.length === 0){
        logService.info('📭 API返回空数据', { roomId, page, previous_sequence });

        // 只有在第一页且没有数据时才设置 hasNextPage=false
        if (page === 0) {
          dispatch(updateRoomMessageStatus({
            roomId,
            currentPage: 0,
            hasNextPage: false,
            lastSequence: previous_sequence,
            source:"fetchMessagesFromAPI:page=0且API返回空数据"
          }));
        }
        return;
      }

      // 更新數據庫中的消息
      await messageService.saveMessagesToDb(apiMessages);

      logService.info('💾 消息已保存到数据库', {
        roomId,
        savedCount: apiMessages.length,
        说明: '重新从数据库查询最新状态'
      });

      // 從數據庫重新獲取最新消息状态
      if (page === 0) {
        // 第一页：重新加载最新消息
        const messages = await messageService.getMessagesFromDB(
          roomId,
          0,
          ConstantUtil.DEFAULT_PAGE_SIZE,
          undefined, // 不使用 sequence 過濾，获取真正的最新消息
          'desc'
        );

        if (messages.length > 0) {
          // 對消息進行時間排序（從早到晚）
          const sortedMessages = [...messages].sort((a, b) =>
            (a.sendTime || 0) - (b.sendTime || 0)
          );

          // 获取现有消息映射以保留状态
          const currentState = getState();
          const existingDateGroups = currentState.message.rooms[roomId]?.dateGroups || [];
          const existingMessagesMap = createExistingMessagesMap(existingDateGroups);

          // 格式化消息，保留已有状态
          const formattedGroups = formatMessagesToDateGroups(sortedMessages, true, existingMessagesMap);

          // 更新 Redux 狀態
          dispatch(setRoomMessages({
            roomId,
            dateGroups: formattedGroups,
            replace: true
          }));

          // 獲取最新的序列號
          const sequences = messages
            .map(msg => msg.sequence || 0)
            .filter(seq => typeof seq === 'number' && seq > 0);

          if (sequences.length > 0) {
            const latestSequence = Math.max(...sequences);
            const minSequence = Math.min(...sequences);

            // 判断是否还有更多历史消息
            const hasNextPage = messages.length >= ConstantUtil.DEFAULT_PAGE_SIZE;

            logService.info('📊 更新消息状态', {
              roomId,
              messagesCount: messages.length,
              latestSequence,
              minSequence,
              hasNextPage,
              说明: '第一页API获取完成'
            });

            dispatch(updateRoomMessageStatus({
              roomId,
              currentPage: page,
              hasNextPage,
              lastSequence: latestSequence,
              source:"fetchMessagesFromAPI:第一页完成"
            }));

            // 如果还有更多历史消息，继续获取
            if(hasNextPage){
              const historyLastSequence = calculateHistoryLastSequence(minSequence);

              logService.info('🔄 继续获取历史消息', {
                roomId,
                当前最小序列号: minSequence,
                历史请求序列号: historyLastSequence,
                计算公式: `max(1, ${minSequence} - ${ConstantUtil.DEFAULT_PAGE_SIZE} + 1) = ${historyLastSequence}`
              });

              // 延迟获取历史消息，避免过于频繁的API调用
              setTimeout(() => {
                dispatch(fetchMessagesFromAPI(roomId, 1, historyLastSequence));
              }, 200);
            }
          }
        }
      } else {
        // 历史页面：追加到现有消息
        logService.info('📜 处理历史消息页面', {
          roomId,
          page,
          apiMessagesCount: apiMessages.length,
          说明: '历史消息已保存到数据库，由loadMoreMessages处理显示'
        });
      }
    } else {
      logService.warn('⚠️ API响应异常', {
        roomId,
        page,
        last_sequence,
        success: response.success,
        hasData: !!response.data,
        hasItems: !!response.data?.items
      });
    }
  } catch (error: any) {
    logService.error('❌ 从API获取消息失败', {
      error: error.message || error,
      roomId,
      page,
      last_sequence
    });
    // 不显示错误给用户，因为可能已经有数据库中的消息可以显示
  }
};

// 检测并修复消息序列号缺失的专用函数
export const detectAndFixMissingSequences = (roomId: string, lastSequence: number): AppThunk => async (dispatch) => {
  if (!roomId || lastSequence <= 0) return;

  try {
    logService.info('🔍 开始检测消息序列号缺失', { roomId, lastSequence });

    // 获取数据库中的所有消息序列号
    const dbMessages = await messageService.getMessagesFromDB(roomId, 0, 1000, undefined, 'asc');
    const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0).sort((a, b) => a - b);

    if (sequences.length === 0) {
      logService.info('📭 数据库无消息，需要获取全部消息', { roomId, 需要获取: `1-${lastSequence}` });
      // 分批获取所有消息
      dispatch(fetchMissingSequencesBatch(roomId, 1, lastSequence));
      return;
    }

    const dbMinSequence = sequences[0];
    const dbMaxSequence = sequences[sequences.length - 1];

    // 检测缺失的序列号
    const missingSequences: number[] = [];

    // 1. 检查开头缺失的序列号 (1 到 dbMinSequence-1)
    for (let i = 1; i < dbMinSequence; i++) {
      missingSequences.push(i);
    }

    // 2. 检查中间缺失的序列号
    for (let i = dbMinSequence; i <= dbMaxSequence; i++) {
      if (!sequences.includes(i)) {
        missingSequences.push(i);
      }
    }

    // 3. 检查末尾缺失的序列号 (dbMaxSequence+1 到 lastSequence)
    for (let i = dbMaxSequence + 1; i <= lastSequence; i++) {
      missingSequences.push(i);
    }

    logService.info('📊 序列号缺失分析结果', {
      roomId,
      lastSequence,
      dbSequenceRange: `${dbMinSequence}-${dbMaxSequence}`,
      dbMessagesCount: dbMessages.length,
      expectedCount: lastSequence,
      missingCount: missingSequences.length,
      missingSequences: missingSequences.length > 20 ?
        `${missingSequences.slice(0, 10).join(',')}...${missingSequences.slice(-10).join(',')}` :
        missingSequences.join(','),
      完整性: missingSequences.length === 0 ? '完整' : '有缺失'
    });

    if (missingSequences.length > 0) {
      // 将缺失的序列号分组为连续范围，提高API调用效率
      const missingRanges = groupSequencesToRanges(missingSequences);

      logService.info('🔄 开始修复缺失的消息序列号', {
        roomId,
        missingRanges: missingRanges.map(range => `${range.start}-${range.end}`).join(', '),
        totalMissing: missingSequences.length
      });

      // 分批修复缺失的消息
      for (let i = 0; i < missingRanges.length; i++) {
        const range = missingRanges[i];
        setTimeout(() => {
          dispatch(fetchMissingSequencesBatch(roomId, range.start, range.end));
        }, i * 200); // 每批间隔200ms，避免API压力
      }
    } else {
      logService.info('✅ 消息序列号完整，无需修复', { roomId });
    }

  } catch (error) {
    logService.error('❌ 检测消息序列号缺失失败', { error, roomId, lastSequence });
  }
};

// 将序列号数组分组为连续范围
const groupSequencesToRanges = (sequences: number[]): Array<{start: number, end: number}> => {
  if (sequences.length === 0) return [];

  const ranges: Array<{start: number, end: number}> = [];
  let start = sequences[0];
  let end = sequences[0];

  for (let i = 1; i < sequences.length; i++) {
    if (sequences[i] === end + 1) {
      // 连续序列号，扩展当前范围
      end = sequences[i];
    } else {
      // 非连续，保存当前范围并开始新范围
      ranges.push({ start, end });
      start = sequences[i];
      end = sequences[i];
    }
  }

  // 添加最后一个范围
  ranges.push({ start, end });

  return ranges;
};

// 分批获取缺失的序列号范围
export const fetchMissingSequencesBatch = (roomId: string, startSequence: number, endSequence: number): AppThunk => async (dispatch) => {
  try {
    const batchSize = ConstantUtil.DEFAULT_PAGE_SIZE;
    const totalCount = endSequence - startSequence + 1;
    const batchCount = Math.ceil(totalCount / batchSize);

    logService.info('🔄 开始分批获取缺失序列号', {
      roomId,
      sequenceRange: `${startSequence}-${endSequence}`,
      totalCount,
      batchSize,
      batchCount
    });

    for (let i = 0; i < batchCount; i++) {
      const batchStart = startSequence + (i * batchSize);
      const batchEnd = Math.min(batchStart + batchSize - 1, endSequence);

      // 使用API的previous_sequence参数来获取特定范围的消息
      // 注意：这里需要根据API的实际行为调整参数
      setTimeout(() => {
        logService.info(`📦 获取第${i + 1}/${batchCount}批缺失消息`, {
          roomId,
          batchRange: `${batchStart}-${batchEnd}`,
          apiSequence: batchStart
        });

        dispatch(fetchMessagesFromAPI(roomId, 0, batchStart-1, batchEnd));
      }, i * 300);
    }

  } catch (error) {
    logService.error('❌ 分批获取缺失序列号失败', { error, roomId, startSequence, endSequence });
  }
};

// 初始化消息加載（重新设计版）
export const initializeRoomMessages = (roomId: string, lastSequence: number): AppThunk => async (dispatch, getState) => {
  if (!roomId) return;

  dispatch(setActiveRoomId(roomId));

  try {
    // 先嘗試獲取房間狀態用於后續消息狀態更新
    let roomData;
    try {
      roomData = await roomDao.getRoomById(roomId);
      logService.info('📊 獲取房間狀態成功', {
        roomId,
        lastReadSequence: roomData?.lastReadSequence,
        lastDeliveredSequence: roomData?.lastDeliveredSequence,
        ownerId: roomData?.ownerId,
        source: 'initializeRoomMessages:prepare'
      });
    } catch (roomErr) {
      logService.warn('獲取房間狀態失敗', { 
        roomId, 
        error: roomErr, 
        source: 'initializeRoomMessages:prepare' 
      });
    }

    // 1. 先获取数据库中的最新消息（不使用 sequence 过滤，获取真正的最新数据）
    const dbMessages = await messageService.getMessagesFromDB(
      roomId,
      0, // 第0页
      ConstantUtil.DEFAULT_PAGE_SIZE,
      undefined, // 不使用 sequence 过滤，获取最新的消息
      'desc'
    );

    logService.info('🔍 数据库查询结果', {
      roomId,
      lastSequence,
      dbMessagesCount: dbMessages.length,
      dbSequenceRange: dbMessages.length > 0 ?
        `${Math.min(...dbMessages.map(m => m.sequence || 0))} - ${Math.max(...dbMessages.map(m => m.sequence || 0))}` : 'empty'
    });

    if (dbMessages.length > 0) {
      // 数据库有数据：立即渲染
      logService.info('📦 数据库有数据，立即渲染', {
        roomId,
        messagesCount: dbMessages.length
      });

      // 立即显示数据库中的消息
      const dateGroups = formatMessagesToDateGroups(dbMessages);
      dispatch(setRoomMessages({ roomId, dateGroups, replace: true }));

      // 计算序列号范围
      const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0);
      const dbMaxSequence = sequences.length > 0 ? Math.max(...sequences) : 0;
      const dbMinSequence = sequences.length > 0 ? Math.min(...sequences) : 0;

      // 检查数据完整性
      const hasMissingNewData = lastSequence > dbMaxSequence; // 新消息缺失
      const hasMissingHistoryData = dbMinSequence > 1; // 历史消息缺失
      const hasDataGaps = sequences.length > 0 && (dbMaxSequence - dbMinSequence + 1) !== sequences.length; // 中间有断层

      logService.info('📊 数据完整性检查', {
        roomId,
        lastSequence,
        dbMaxSequence,
        dbMinSequence,
        dbMessagesCount: dbMessages.length,
        sequencesCount: sequences.length,
        hasMissingNewData,
        hasMissingHistoryData,
        hasDataGaps,
        说明: {
          新消息缺失: hasMissingNewData ? `缺失序列号 ${dbMaxSequence + 1}-${lastSequence}` : '无',
          历史消息缺失: hasMissingHistoryData ? `缺失序列号 1-${dbMinSequence - 1}` : '无',
          数据断层: hasDataGaps ? '序列号不连续' : '无'
        }
      });

      // 更新状态
      dispatch(updateRoomMessageStatus({
        roomId,
        currentPage: 0,
        hasNextPage: dbMessages.length >= ConstantUtil.DEFAULT_PAGE_SIZE || hasMissingHistoryData,
        lastSequence: Math.max(lastSequence, dbMaxSequence), // 使用更大的序列号
        source:"initializeRoomMessages:dbMessages.length > 0"
      }));

      // 立即更新第一页消息的已讀/已到狀態
      if (roomData) {
        // 更新已讀狀態
        if (roomData.lastReadSequence && roomData.ownerId) {
          dispatch(updateMessagesStatusBySequence({
            roomId,
            maxSequence: roomData.lastReadSequence,
            status: 'read',
            ownerId: roomData.ownerId
          }));
          
          logService.info('💬 更新初始頁面消息的已讀狀態', {
            roomId,
            lastReadSequence: roomData.lastReadSequence,
            ownerId: roomData.ownerId,
            source: 'initializeRoomMessages:firstPage'
          });
        }
        
        // 更新已到狀態
        if (roomData.lastDeliveredSequence && roomData.ownerId) {
          if (!roomData.lastReadSequence || roomData.lastDeliveredSequence > roomData.lastReadSequence) {
            dispatch(updateMessagesStatusBySequence({
              roomId,
              maxSequence: roomData.lastDeliveredSequence,
              status: 'delivered',
              ownerId: roomData.ownerId
            }));
            
            logService.info('💬 更新初始頁面消息的已到狀態', {
              roomId,
              lastDeliveredSequence: roomData.lastDeliveredSequence,
              ownerId: roomData.ownerId,
              source: 'initializeRoomMessages:firstPage'
            });
          }
        }
      }
      
      // 如果检测到任何类型的数据缺失，启动修复流程
      if (hasMissingNewData || hasMissingHistoryData || hasDataGaps) {
        logService.info(' 检测到数据缺失，启动修复流程', {
          roomId,
          hasMissingNewData,
          hasMissingHistoryData,
          hasDataGaps
        });

        // 延迟启动修复，避免影响初始渲染
        setTimeout(() => {
          dispatch(detectAndFixMissingSequences(roomId, lastSequence));
        }, 1000);
      }

      // 直接加載第二頁而非預加載，確保能同步更新狀態
      const nextPage = 1;
      const beforeSequence = calculateHistoryLastSequence(dbMinSequence);
      
      logService.info('🔄 直接加載第二頁', {
        roomId,
        當前最小序列號: dbMinSequence,
        歷史請求序列號: beforeSequence
      });
      
      try {
        // 設置加載狀態
        dispatch(setRoomLoading({ roomId, isLoading: true }));
        
        // 直接從數據庫獲取第二頁
        const page2Messages = await messageService.getMessagesFromDB(
          roomId,
          nextPage,
          ConstantUtil.DEFAULT_PAGE_SIZE,
          beforeSequence,
          'desc'
        );
        
        if (page2Messages.length > 0) {
          // 對消息進行時間排序（從早到晚）
          const sortedMessages = [...page2Messages].sort((a, b) =>
            (a.sendTime || 0) - (b.sendTime || 0)
          );

          // 获取现有消息映射以保留状态
          const currentState = getState();
          const existingDateGroups = currentState.message.rooms[roomId]?.dateGroups || [];
          const existingMessagesMap = createExistingMessagesMap(existingDateGroups);

          // 格式化消息，保留已有状态
          const formattedGroups = formatMessagesToDateGroups(sortedMessages, true, existingMessagesMap);

          // 更新 Redux 狀態，合併消息
          dispatch(setRoomMessages({
            roomId,
            dateGroups: formattedGroups,
            replace: false // 合併消息
          }));

          // 使用當前頁碼的消息數量判斷是否還有下一頁
          const hasNextPage = page2Messages.length >= ConstantUtil.DEFAULT_PAGE_SIZE;
          
          // 查找最小序列號
          const sequences = page2Messages
            .map(msg => msg.sequence || 0)
            .filter(seq => typeof seq === 'number');
            
          const minSequence = sequences.length > 0 ? Math.min(...sequences) : beforeSequence;

          dispatch(updateRoomMessageStatus({
            roomId,
            currentPage: nextPage,
            hasNextPage,
            lastSequence: minSequence,
            source:"initializeRoomMessages:page2Messages.length > 0"
          }));
          
          logService.info('📊 第二頁加載成功', {
            roomId,
            消息數量: page2Messages.length,
            序列號範圍: sequences.length > 0 ? `${Math.min(...sequences)}-${Math.max(...sequences)}` : '無序列號',
            hasNextPage
          });
          
          // 立即更新第二頁消息的已讀/已到狀態
          if (roomData) {
            if (roomData.lastReadSequence && roomData.ownerId) {
              // 使用增強版的recheckMessagesStatus確保更新第二頁消息狀態
              dispatch(forceUpdateMessagesStatus({
                roomId,
                maxSequence: roomData.lastReadSequence,
                status: 'read',
                ownerId: roomData.ownerId,
                targetPage: nextPage
              }));
              
              logService.info('💬 強制更新第二頁消息的已讀狀態', {
                roomId,
                page: nextPage,
                lastReadSequence: roomData.lastReadSequence,
                ownerId: roomData.ownerId,
                source: 'initializeRoomMessages:secondPage'
              });
            }
            
            if (roomData.lastDeliveredSequence && roomData.ownerId) {
              if (!roomData.lastReadSequence || roomData.lastDeliveredSequence > roomData.lastReadSequence) {
                dispatch(forceUpdateMessagesStatus({
                  roomId,
                  maxSequence: roomData.lastDeliveredSequence,
                  status: 'delivered',
                  ownerId: roomData.ownerId,
                  targetPage: nextPage
                }));
                
                logService.info('💬 強制更新第二頁消息的已到狀態', {
                  roomId,
                  page: nextPage,
                  lastDeliveredSequence: roomData.lastDeliveredSequence,
                  ownerId: roomData.ownerId,
                  source: 'initializeRoomMessages:secondPage'
                });
              }
            }
          }
          
          // 異步從API獲取上一頁內容，確保數據完整性
          setTimeout(() => {
            // 计算历史消息的 lastSequence：当前最小序列号计算历史请求序列号
            const historyLastSequence = calculateHistoryLastSequence(minSequence);
            
            logService.info('🔄 异步从API获取历史内容', {
              roomId,
              apiPage: nextPage + 1,
              当前最小序列号: minSequence,
              历史请求序列号: historyLastSequence
            });
            dispatch(fetchMessagesFromAPI(roomId, nextPage + 1, historyLastSequence));
          }, 500);
        }
        
      } catch (page2Error) {
        logService.error('❌ 加載第二頁失敗', {
          error: page2Error,
          roomId,
          beforeSequence
        });
      } finally {
        dispatch(setRoomLoading({ roomId, isLoading: false }));
      }

    } else {
      // 数据库无数据：直接请求API
      logService.info('📭 数据库无数据，直接请求API', {
        roomId,
        lastSequence,
        说明: '从API获取最新消息'
      });

      dispatch(setRoomLoading({ roomId, isLoading: true }));
      // 获取最新消息，使用 lastSequence + 1 确保获取到所有新消息
      dispatch(fetchMessagesFromAPI(roomId, 0, lastSequence > 0 ? lastSequence + 1 : 1));
      dispatch(setRoomLoading({ roomId, isLoading: false }));
    }

  } catch (error) {
    logService.error('❌ 初始化房间消息失败', { error, roomId });
    dispatch(setRoomError({ roomId, error: '初始化消息失败' }));
  }
};

// 添加一個強制更新消息狀態的action，專門用於第二頁數據
export const forceUpdateMessagesStatus = (payload: {
  roomId: string,
  maxSequence: number,
  status: 'delivered' | 'read',
  ownerId: string,
  targetPage?: number
}): AppThunk => async (dispatch, getState) => {
  const { roomId, maxSequence, status, ownerId, targetPage } = payload;
  
  logService.info('🔄 強制更新消息狀態', {
    roomId,
    maxSequence,
    status,
    ownerId,
    targetPage: targetPage !== undefined ? targetPage : '所有頁'
  });
  
  const state = getState();
  const roomState = state.message.rooms[roomId];
  
  if (!roomState) {
    logService.warn('房間狀態不存在，無法強制更新消息狀態', { roomId });
    return;
  }
  
  let updatedCount = 0;
  
  // 遍歷所有日期組，逐條檢查並更新消息
  for (const group of roomState.dateGroups) {
    for (const message of group.messages) {
      // 日誌每條消息的詳細檢查
      const hasSequence = message.sequence !== undefined;
      const sequenceInRange = hasSequence ? message.sequence! <= maxSequence : false;
      
      // 特別標記第二頁消息
      const isPage2Message = targetPage !== undefined && 
        hasSequence && 
        message.sequence! < roomState.lastSequence;
      
      logService.debug('檢查消息更新條件', {
        messageId: message.id,
        content: message.content?.substring(0, 10) + (message.content && message.content.length > 10 ? '...' : ''),
        senderId: message.sender?.id,
        ownerId,
        sequence: message.sequence,
        maxSequence,
        currentStatus: message.status,
        targetStatus: status,
        hasSequence,
        sequenceInRange,
        isTargetPage: isPage2Message
      });
      
      // 判斷消息是否需要更新狀態
      if (
        message.sender?.id != ownerId &&
        message.sequence !== undefined &&
        message.sequence <= maxSequence &&
        message.status !== 'failed' &&
        message.status !== status && // 避免重複更新
        (targetPage === undefined || isPage2Message) // 如果有指定頁面，則只更新該頁面的消息
      ) {
        const shouldUpdate =
          (status === 'delivered' && (message.status === 'sent' || message.status === 'sending')) ||
          (status === 'read' && (message.status === 'delivered' || message.status === 'sent' || message.status === 'sending'));
        
        if (shouldUpdate) {
          logService.debug('強制更新消息狀態', {
            messageId: message.id,
            senderId: message.sender?.id,
            sequence: message.sequence,
            oldStatus: message.status,
            newStatus: status,
            content: message.content?.substring(0, 15) + (message.content && message.content.length > 15 ? '...' : ''),
            isPage2Message
          });
          
          // 直接在 message 對象上修改狀態
          // 注意：這裡直接修改了 state 中的對象，這通常不是推薦的 Redux 做法
          // 但在這個特殊情況下，我們需要確保立即更新狀態
          message.status = status;
          updatedCount++;
        }
      }
    }
  }
  
  // 如果有消息被更新，觸發 state 更新以確保 UI 重渲染
  if (updatedCount > 0) {
    logService.info('🔄 強制更新完成，觸發UI刷新', {
      roomId,
      updatedCount,
      targetPage: targetPage !== undefined ? targetPage : '所有頁'
    });
    
    // 獲取當前日期組並創建新的引用
    const updatedDateGroups = [...roomState.dateGroups];
    
    // 使用 setRoomMessages 來觸發 state 更新
    dispatch(setRoomMessages({
      roomId,
      dateGroups: updatedDateGroups,
      replace: true
    }));
  } else {
    logService.info('🔄 強制更新完成，但沒有消息需要更新', {
      roomId,
      targetPage: targetPage !== undefined ? targetPage : '所有頁'
    });
  }
};

// 加載更多消息（優化版）
export const loadMoreMessages = (roomId: string, isPreload: boolean = false): AppThunk => async (dispatch, getState) => {
  if (!roomId) return;

  const state = getState();
  const roomState = state.message.rooms[roomId];

  // 檢查是否有下一頁，如果房間狀態不存在則初始化
  if (!roomState) {
    logService.warn('房間狀態不存在，無法載入更多消息', { roomId });
    return;
  }

  if (!roomState.hasNextPage || roomState.isLoading) {
    logService.info('🚫 无法加载更多消息', {
      roomId,
      原因: !roomState.hasNextPage ? 'hasNextPage=false' : 'isLoading=true',
      hasNextPage: roomState.hasNextPage,
      isLoading: roomState.isLoading,
      currentPage: roomState.currentPage,
      lastSequence: roomState.lastSequence,
      isPreload // 添加預加載標記到日誌
    });
    return;
  }

  const nextPage = roomState.currentPage + 1;

  logService.info('📥 开始加载更多消息', {
    roomId,
    当前页: roomState.currentPage,
    下一页: nextPage,
    hasNextPage: roomState.hasNextPage,
    lastSequence: roomState.lastSequence,
    isPreload, // 添加預加載標記到日誌
    来源: isPreload ? '預加載' : '用户滾動加載'
  });

  try {
    dispatch(setRoomLoading({ roomId, isLoading: true }));

    // 获取当前已显示消息的最小序列号
    const currentMessages = roomState.dateGroups.flatMap(group => group.messages);
    const currentSequences = currentMessages
      .map(msg => (msg as any).sequence || 0)
      .filter(seq => seq > 0);

    const currentMinSequence = currentSequences.length > 0 ? Math.min(...currentSequences) : roomState.lastSequence;
    const beforeSequence = calculateHistoryLastSequence(currentMinSequence);

    logService.info('📊 loadMoreMessages 序列号分析', {
      roomId,
      nextPage,
      currentMessagesCount: currentMessages.length,
      currentMinSequence,
      beforeSequence,
      roomStateLastSequence: roomState.lastSequence
    });

    // 使用序列号获取更早的消息（beforeSequence）
    let messages = await messageService.getMessagesFromDB(
      roomId,
      nextPage,
      ConstantUtil.DEFAULT_PAGE_SIZE,
      beforeSequence, // 使用计算出的 beforeSequence
      'desc'
    );
    
    if (messages.length > 0) {
      // 對消息進行時間排序（從早到晚）
      const sortedMessages = [...messages].sort((a, b) =>
        (a.sendTime || 0) - (b.sendTime || 0)
      );

      // 获取现有消息映射以保留状态
      const existingDateGroups = roomState.dateGroups || [];
      const existingMessagesMap = createExistingMessagesMap(existingDateGroups);

      // 格式化消息，保留已有状态
      const formattedGroups = formatMessagesToDateGroups(sortedMessages, true, existingMessagesMap);

      // 更新 Redux 狀態，合併消息
      dispatch(setRoomMessages({
        roomId,
        dateGroups: formattedGroups,
        replace: false // 合併消息
      }));


      // 使用當前頁碼的消息數量判斷是否還有下一頁
      const hasNextPage = messages.length >= ConstantUtil.DEFAULT_PAGE_SIZE;

      logService.info('📊 分页状态判断 (loadMoreMessages)', {
        roomId,
        nextPage,
        消息数量: messages.length,
        判断逻辑: `消息数量(${messages.length}) >= 页大小(${ConstantUtil.DEFAULT_PAGE_SIZE})`,
        hasNextPage,
        beforeSequence
      });

      dispatch(updateRoomMessageStatus({
        roomId,
        currentPage: nextPage,
        hasNextPage,
        lastSequence: beforeSequence,
        source:"loadMoreMessages:messages.length > 0"
      }));
      
      // 新增: 載入更多消息后立即更新消息已讀/已到狀態
      try {
        // 檢查當前房間是否有消息已讀/已到狀態
        const roomData = await roomDao.getRoomById(roomId);
        if (roomData && roomData.lastReadSequence && roomData.ownerId) {
          // 更新已讀狀態
          dispatch(updateMessagesStatusBySequence({
            roomId,
            maxSequence: roomData.lastReadSequence,
            status: 'read',
            ownerId: roomData.ownerId
          }));
          
          logService.info('💬 更新歷史消息的已讀狀態', {
            roomId,
            page: nextPage,
            lastReadSequence: roomData.lastReadSequence,
            ownerId: roomData.ownerId,
            source: 'loadMoreMessages'
          });
        }
        
        if (roomData && roomData.lastDeliveredSequence && roomData.ownerId) {
          // 更新已到狀態 (只有當已到序列號大於已讀序列號時才需要)
          if (!roomData.lastReadSequence || roomData.lastDeliveredSequence > roomData.lastReadSequence) {
            dispatch(updateMessagesStatusBySequence({
              roomId,
              maxSequence: roomData.lastDeliveredSequence,
              status: 'delivered',
              ownerId: roomData.ownerId
            }));
            
            logService.info('💬 更新歷史消息的已到狀態', {
              roomId,
              page: nextPage,
              lastDeliveredSequence: roomData.lastDeliveredSequence,
              ownerId: roomData.ownerId,
              source: 'loadMoreMessages'
            });
          }
        }
      } catch (statusError) {
        logService.warn('獲取房間狀態失敗，無法更新歷史消息的已讀/已到狀態', {
          roomId, 
          page: nextPage,
          error: statusError, 
          source: 'loadMoreMessages'
        });
      }

      // 異步從API獲取上一頁內容
      setTimeout(() => {
        // 计算历史消息的 lastSequence：当前最小序列号计算历史请求序列号
        const minSequence = getMinSequenceFromMessages(messages) || beforeSequence;
        const historyLastSequence = calculateHistoryLastSequence(minSequence);

        logService.info('🔄 异步从API获取上一页内容', {
          roomId,
          apiPage: nextPage + 1,
          当前最小序列号: minSequence,
          历史请求序列号: historyLastSequence,
          计算公式: `max(1, ${minSequence} - ${ConstantUtil.DEFAULT_PAGE_SIZE} + 1) = ${historyLastSequence}`
        });
        dispatch(fetchMessagesFromAPI(roomId, nextPage + 1, historyLastSequence));
      }, 100);

    } else {
      // 數據庫無數據：直接請求API
      logService.info('📭 数据库无历史消息，直接请求API', {
        roomId,
        nextPage,
        currentMinSequence,
        beforeSequence,
        说明: '数据库中没有更早的消息，从API获取'
      });

      // 使用当前最小序列号来计算历史请求序列号
      const historyLastSequence = calculateHistoryLastSequence(currentMinSequence);

      logService.info('🔄 直接从API获取历史消息', {
        roomId,
        nextPage,
        当前最小序列号: currentMinSequence,
        历史请求序列号: historyLastSequence,
        计算公式: `max(1, ${currentMinSequence} - ${ConstantUtil.DEFAULT_PAGE_SIZE} + 1) = ${historyLastSequence}`,
        说明: '获取比当前最小序列号更早的消息'
      });

      dispatch(fetchMessagesFromAPI(roomId, nextPage, historyLastSequence));

      // API调用后重新查询数据库
      setTimeout(async () => {
        logService.info('🔍 API调用后重新查询本地数据库', { roomId, nextPage });

        // 重新获取当前状态中的最早序列号
        const currentState = getState();
        const currentRoomState = currentState.message.rooms[roomId];
        const currentMessages = currentRoomState?.dateGroups.flatMap(group => group.messages) || [];
        const earliestSequence = currentMessages.length > 0
          ? Math.min(...currentMessages.map(msg => (msg as any).sequence || 0).filter(seq => seq > 0))
          : currentRoomState?.lastSequence || 0;

        const retryMessages = await messageService.getMessagesFromDB(
          roomId,
          nextPage,
          ConstantUtil.DEFAULT_PAGE_SIZE,
          earliestSequence, // 使用最早序列号
          'desc'
        );

        if (retryMessages.length > 0) {
          logService.info('✅ API调用后成功获取到消息', {
            roomId,
            nextPage,
            count: retryMessages.length
          });

          // 获取现有消息映射以保留状态
          const existingDateGroups = roomState.dateGroups || [];
          const existingMessagesMap = createExistingMessagesMap(existingDateGroups);

          // 格式化消息，保留已有状态
          const dateGroups = formatMessagesToDateGroups(retryMessages, true, existingMessagesMap);
          dispatch(setRoomMessages({ roomId, dateGroups, replace: false }));

          const sequences = retryMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0);
          const latestSequence = sequences.length > 0 ? Math.max(...sequences) : roomState.lastSequence;

          dispatch(updateRoomMessageStatus({
            roomId,
            currentPage: nextPage,
            hasNextPage: retryMessages.length >= ConstantUtil.DEFAULT_PAGE_SIZE,
            lastSequence: latestSequence,
            source:"loadMoreMessages:retryMessages.length > 0"
          }));
          
          // 新增: 從API獲取後也需要更新消息已讀/已到狀態
          try {
            const roomData = await roomDao.getRoomById(roomId);
            if (roomData && roomData.lastReadSequence && roomData.ownerId) {
              dispatch(updateMessagesStatusBySequence({
                roomId,
                maxSequence: roomData.lastReadSequence,
                status: 'read',
                ownerId: roomData.ownerId
              }));
              
              logService.info('💬 更新API獲取消息的已讀狀態', {
                roomId,
                page: nextPage,
                lastReadSequence: roomData.lastReadSequence,
                source: 'loadMoreMessages:retryMessages'
              });
            }
            
            if (roomData && roomData.lastDeliveredSequence && roomData.ownerId) {
              if (!roomData.lastReadSequence || roomData.lastDeliveredSequence > roomData.lastReadSequence) {
                dispatch(updateMessagesStatusBySequence({
                  roomId,
                  maxSequence: roomData.lastDeliveredSequence,
                  status: 'delivered',
                  ownerId: roomData.ownerId
                }));
                
                logService.info('💬 更新API獲取消息的已到狀態', {
                  roomId,
                  page: nextPage,
                  lastDeliveredSequence: roomData.lastDeliveredSequence,
                  source: 'loadMoreMessages:retryMessages'
                });
              }
            }
          } catch (statusError) {
            logService.warn('獲取房間狀態失敗，無法更新API獲取消息的已讀/已到狀態', {
              roomId, 
              error: statusError, 
              source: 'loadMoreMessages:retryMessages'
            });
          }
        } else {
          logService.warn('❌ 本地和API都无更多历史消息', { roomId, nextPage });
          dispatch(updateRoomMessageStatus({
            roomId,
            currentPage: roomState.currentPage, // 保持当前页不变
            hasNextPage: false,
            lastSequence: roomState.lastSequence,
            source:"loadMoreMessages:retryMessages.length <= 0"
          }));
        }
      }, 500);
    }
  } catch (error: any) {
    logService.error('加載更多消息失敗', { error, roomId, nextPage });
  } finally {
    dispatch(setRoomLoading({ roomId, isLoading: false }));
  }
};

export const selectActiveRoomId = (state: RootState) => state.message.activeRoomId;
export const selectRoomMessages = (roomId: string | null) => (state: RootState) => 
  roomId ? state.message.rooms[roomId]?.dateGroups || [] : [];
export const selectRoomLoading = (roomId: string | null) => (state: RootState) => 
  roomId ? state.message.rooms[roomId]?.isLoading || false : false;
export const selectRoomError = (roomId: string | null) => (state: RootState) => 
  roomId ? state.message.rooms[roomId]?.error || null : null;
export const selectRoomHasNextPage = (roomId: string | null) => (state: RootState) =>
  roomId ? state.message.rooms[roomId]?.hasNextPage || false : false;
export const selectRoomCurrentPage = (roomId: string | null) => (state: RootState) =>
  roomId ? state.message.rooms[roomId]?.currentPage || 0 : 0;

// 优化的加载更多消息方法
export const loadMoreMessagesOptimized = (roomId: string): AppThunk => async (dispatch, getState) => {
  if (!roomId) return;

  const state = getState();
  const roomState = state.message.rooms[roomId];

  if (!roomState || !roomState.hasNextPage || roomState.isLoading) {
    logService.info('🚫 跳过优化加载更多消息', {
      roomId,
      原因: !roomState ? '房间状态不存在' :
            !roomState.hasNextPage ? 'hasNextPage=false' : 'isLoading=true'
    });
    return;
  }

  const nextPage = roomState.currentPage + 1;

  logService.info('📥 开始优化加载更多消息', {
    roomId,
    当前页: roomState.currentPage,
    下一页: nextPage,
    lastSequence: roomState.lastSequence
  });

  dispatch(setRoomLoading({ roomId, isLoading: true }));

  try {
    // 使用优化的历史消息查询
    const result = await messageService.getMessagesOptimized(roomId, {
      type: 'history',
      page: nextPage,
      pageSize: ConstantUtil.DEFAULT_PAGE_SIZE,
      startSequence: roomState.lastSequence ?
        Math.max(1, roomState.lastSequence - ConstantUtil.DEFAULT_PAGE_SIZE + 1) :
        undefined
    });

    if (result.messages.length > 0) {
      // 获取现有消息映射以保留状态
      const existingDateGroups = roomState.dateGroups || [];
      const existingMessagesMap = createExistingMessagesMap(existingDateGroups);

      // 格式化并添加消息，保留已有状态
      const dateGroups = formatMessagesToDateGroups(result.messages, true, existingMessagesMap);
      dispatch(setRoomMessages({ roomId, dateGroups, replace: false }));

      // 更新序列号范围
      const latestSequence = result.sequenceRange?.max || roomState.lastSequence;

      dispatch(updateRoomMessageStatus({
        roomId,
        currentPage: nextPage,
        hasNextPage: result.hasMore,
        lastSequence: latestSequence,
        source:"loadMoreMessagesOptimized:result.messages.length > 0"
      }));
      
      // 新增: 優化版本中也需要更新消息已讀/已到狀態
      try {
        // 檢查當前房間是否有消息已讀/已到狀態
        const roomData = await roomDao.getRoomById(roomId);
        if (roomData && roomData.lastReadSequence && roomData.ownerId) {
          // 更新已讀狀態
          dispatch(updateMessagesStatusBySequence({
            roomId,
            maxSequence: roomData.lastReadSequence,
            status: 'read',
            ownerId: roomData.ownerId
          }));
          
          logService.info('💬 更新優化加載歷史消息的已讀狀態', {
            roomId,
            page: nextPage,
            lastReadSequence: roomData.lastReadSequence,
            ownerId: roomData.ownerId,
            source: 'loadMoreMessagesOptimized'
          });
        }
        
        if (roomData && roomData.lastDeliveredSequence && roomData.ownerId) {
          // 更新已到狀態 (只有當已到序列號大於已讀序列號時才需要)
          if (!roomData.lastReadSequence || roomData.lastDeliveredSequence > roomData.lastReadSequence) {
            dispatch(updateMessagesStatusBySequence({
              roomId,
              maxSequence: roomData.lastDeliveredSequence,
              status: 'delivered',
              ownerId: roomData.ownerId
            }));
            
            logService.info('💬 更新優化加載歷史消息的已到狀態', {
              roomId,
              page: nextPage,
              lastDeliveredSequence: roomData.lastDeliveredSequence,
              ownerId: roomData.ownerId,
              source: 'loadMoreMessagesOptimized'
            });
          }
        }
      } catch (statusError) {
        logService.warn('獲取房間狀態失敗，無法更新優化加載歷史消息的已讀/已到狀態', {
          roomId, 
          page: nextPage,
          error: statusError, 
          source: 'loadMoreMessagesOptimized'
        });
      }

      logService.info('✅ 优化加载更多消息完成', {
        roomId,
        nextPage,
        消息数量: result.messages.length,
        hasNextPage: result.hasMore,
        sequenceRange: result.sequenceRange
      });

    } else {
      // 无更多历史消息
      logService.info('🔚 无更多历史消息', { roomId, nextPage });
      dispatch(updateRoomMessageStatus({
        roomId,
        currentPage: roomState.currentPage,
        hasNextPage: false,
        lastSequence: roomState.lastSequence,
        source:"loadMoreMessagesOptimized:result.messages.length <= 0"
      }));
    }

  } catch (error: any) {
    logService.error('❌ 优化加载更多消息失败', { error, roomId });
    dispatch(setRoomError({ roomId, error: error.message || '加载更多消息失败' }));
  } finally {
    dispatch(setRoomLoading({ roomId, isLoading: false }));
  }
};

// 检查并填补历史数据缺失的函数
export const checkAndFillMissingHistoryData = (roomId: string): AppThunk => async (dispatch, getState) => {
  if (!roomId) return;

  try {
    const state = getState();
    const roomState = state.message.rooms[roomId];

    if (!roomState) {
      logService.warn('房间状态不存在，无法检查历史数据', { roomId });
      return;
    }

    // 获取当前显示的消息
    const currentMessages = roomState.dateGroups.flatMap(group => group.messages);
    const currentSequences = currentMessages
      .map(msg => (msg as any).sequence || 0)
      .filter(seq => seq > 0);

    if (currentSequences.length === 0) {
      logService.info('当前无消息，无需检查历史数据', { roomId });
      return;
    }

    const currentMinSequence = Math.min(...currentSequences);
    const currentMaxSequence = Math.max(...currentSequences);

    logService.info('🔍 检查历史数据完整性', {
      roomId,
      currentMinSequence,
      currentMaxSequence,
      currentMessagesCount: currentMessages.length,
      expectedContinuousCount: currentMaxSequence - currentMinSequence + 1,
      actualCount: currentSequences.length,
      说明: '检查是否缺少早期历史消息'
    });

    // 检查是否缺少早期历史消息（序列号小于当前最小序列号）
    if (currentMinSequence > 1) {
      const missingHistoryCount = currentMinSequence - 1;

      logService.info('🔄 检测到历史数据缺失，开始获取', {
        roomId,
        缺失范围: `1-${currentMinSequence - 1}`,
        缺失数量: missingHistoryCount,
        说明: '需要获取比当前最小序列号更早的消息'
      });

      // 分批获取历史数据
      const batchSize = ConstantUtil.DEFAULT_PAGE_SIZE;
      const batchCount = Math.ceil(missingHistoryCount / batchSize);

      for (let i = 0; i < batchCount; i++) {
        const endSequence = currentMinSequence - 1 - (i * batchSize);
        const startSequence = Math.max(1, endSequence - batchSize + 1);

        logService.info(`📥 获取第${i + 1}/${batchCount}批历史数据`, {
          roomId,
          startSequence,
          endSequence,
          batchSize: endSequence - startSequence + 1
        });

        // 延迟执行，避免API调用过于频繁
        setTimeout(() => {
          dispatch(fetchMessagesFromAPI(roomId, i + 1, startSequence));
        }, i * 300);
      }
    } else {
      logService.info('✅ 历史数据完整，无需补充', {
        roomId,
        currentMinSequence,
        说明: '已有最早的消息'
      });
    }

  } catch (error) {
    logService.error('❌ 检查历史数据完整性失败', {
      error,
      roomId
    });
  }
};

// 检查并填补数据缺失的函数
export const checkAndFillMissingData = (roomId: string, expectedLastSequence: number): AppThunk => async (dispatch) => {
  if (!roomId || expectedLastSequence <= 0) return;

  try {
    logService.info('🔍 开始检查数据完整性', {
      roomId,
      expectedLastSequence,
      说明: '检查数据库中是否有遗漏的消息'
    });

    // 获取数据库中的最新消息
    const dbMessages = await messageService.getMessagesFromDB(
      roomId,
      0,
      ConstantUtil.DEFAULT_PAGE_SIZE * 2, // 获取更多数据以便分析
      undefined,
      'desc'
    );

    if (dbMessages.length === 0) {
      logService.info('📭 数据库无数据，从API获取所有消息', { roomId, expectedLastSequence });
      dispatch(fetchMessagesFromAPI(roomId, 0, 1));
      return;
    }

    const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0);
    const dbMaxSequence = Math.max(...sequences);
    const dbMinSequence = Math.min(...sequences);

    logService.info('📊 数据完整性分析', {
      roomId,
      expectedLastSequence,
      dbMaxSequence,
      dbMinSequence,
      dbMessagesCount: dbMessages.length,
      缺失的消息数: expectedLastSequence - dbMaxSequence,
      是否有缺失: expectedLastSequence > dbMaxSequence
    });

    // 如果有缺失的数据，分批获取
    if (expectedLastSequence > dbMaxSequence) {
      const missingCount = expectedLastSequence - dbMaxSequence;
      const batchSize = ConstantUtil.DEFAULT_PAGE_SIZE;
      const batchCount = Math.ceil(missingCount / batchSize);

      logService.info('🔄 开始分批获取缺失数据', {
        roomId,
        missingCount,
        batchSize,
        batchCount,
        从序列号: dbMaxSequence + 1,
        到序列号: expectedLastSequence
      });

      // 分批获取缺失的数据
      for (let i = 0; i < batchCount; i++) {
        const startSequence = dbMaxSequence + 1 + (i * batchSize);
        const endSequence = Math.min(startSequence + batchSize - 1, expectedLastSequence);

        logService.info(`📥 获取第${i + 1}/${batchCount}批缺失数据`, {
          roomId,
          startSequence,
          endSequence,
          batchSize: endSequence - startSequence + 1
        });

        // 延迟执行，避免API调用过于频繁
        setTimeout(() => {
          dispatch(fetchMessagesFromAPI(roomId, 0, startSequence));
        }, i * 300); // 每批间隔300ms
      }
    } else {
      logService.info('✅ 数据完整，无需补充', {
        roomId,
        expectedLastSequence,
        dbMaxSequence
      });
    }

  } catch (error) {
    logService.error('❌ 检查数据完整性失败', {
      error,
      roomId,
      expectedLastSequence
    });
  }
};

export default messageSlice.reducer;