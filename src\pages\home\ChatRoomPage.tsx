import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import './ChatRoomPage.css';
import { ChatRoom } from './chat';
import { ChatRoomType } from '../../types/chat.types';
import { fetchRoomItem } from '@/services/core/chat/roomService';
import { logService } from '@/services/system/logService';
import { useFixedStatusBarColor } from '../../hooks/useStatusBarColor';
import { PAGE_COLORS } from '../../config/app/pageColors';

const ChatRoomPage: React.FC = () => {
  const navigate = useNavigate();
  const { roomId } = useParams<{ roomId: string }>();
  const [roomInfo, setRoomInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 设置聊天室状态栏颜色
  useFixedStatusBarColor(PAGE_COLORS.CHAT.MY);

  const handleBack = () => {
    navigate(-1);
  };

  useEffect(() => {
    if (!roomId) return;
    setError(null);

    logService.info('開始獲取我的聊天室資訊', { roomId });

    fetchRoomItem({ roomId })
      .then((res) => {
        const roomData = res?.data || null;
        logService.info('我的聊天室資訊獲取成功', {
          roomId,
          hasData: !!roomData,
          lastSequence: roomData?.lastSequence
        });
        setRoomInfo(roomData);
      })
      .catch((err) => {
        logService.error('房間資訊獲取失敗', { error: err, roomId });
        setError('房間資訊獲取失敗');
      });
  }, [roomId]);

  return (
    <div className="chat-room-page">
      {error ? (
        <div className="page-error">
          <span>{error}</span>
          <button onClick={() => window.location.reload()}>重新載入</button>
        </div>
      ) : (
        <ChatRoom
          type={ChatRoomType.MY}
          title={'我的聊天室'}
          dateGroups={[]}
          roomId={roomId || ''}
          roomInfo={roomInfo}
          onBackClick={handleBack}
        />
      )}
    </div>
  );
};

export default ChatRoomPage; 