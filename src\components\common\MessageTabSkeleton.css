/* MessageTab骨架屏样式 */
.message-tab-skeleton {
  height: 100%;
  background-color: #ffffff;
  overflow: hidden;
}

/* Filter Tabs 骨架样式 */
.message-skeleton-filter-tabs {
  display: flex;
  padding: 16px;
  gap: 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
}

.message-skeleton-filter-tab {
  padding: 8px 0;
  position: relative;
}

.message-skeleton-filter-tab-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1677FF 25%, #4096FF 50%, #1677FF 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* List 骨架样式 */
.message-skeleton-list {
  padding: 0 16px;
  background-color: #ffffff;
}

.message-skeleton-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.message-skeleton-list-item:last-child {
  border-bottom: none;
}

.skeleton-item-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  flex-shrink: 0;
}

.skeleton-item-content {
  flex: 1;
  min-width: 0;
}

.skeleton-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.skeleton-item-badge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(90deg, #ff4d4f 25%, #ff7875 50%, #ff4d4f 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  flex-shrink: 0;
}

/* 通用骨架线条样式 */
.skeleton-line {
  height: 14px;
  border-radius: 7px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 6px;
}

.skeleton-line:last-child {
  margin-bottom: 0;
}

.skeleton-line-mini {
  width: 30px;
}

.skeleton-line-short {
  width: 60px;
}

.skeleton-line-medium {
  width: 120px;
}

.skeleton-line-long {
  width: 80%;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式调整 */
@media (max-width: 375px) {
  .message-skeleton-filter-tabs {
    padding: 12px;
    gap: 16px;
  }
  
  .message-skeleton-list {
    padding: 0 12px;
  }
}
