import { RoomWeightScoreUtil } from './roomWeightScoreUtil';
import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from '@/types/aile.enum';
import { RoomWeightDto } from '@/types/room.types';

describe('RoomWeightScoreUtil', () => {
  
  describe('calculateScore', () => {
    it('应该正确计算基础权重Score', () => {
      const roomWeightDto: RoomWeightDto = {
        p1: P1WeightEnum.Default,
        p2: P2WeightEnum.Default,
        p3: [P3WeightEnum.Default],
        timestamp: 1719273600000, // 2024-06-25 00:00:00 UTC
      };

      const result = RoomWeightScoreUtil.calculateScore(roomWeightDto);
      
      expect(result.p1).toBe(0);
      expect(result.p2).toBe(0);
      expect(result.p3).toBe(1);
      expect(result.p4).toBe(1719273600);
      expect(result.score).toBe(2200742529152); // (0 << 50) | (0 << 49) | (1 << 41) | 1719273600
    });

    it('应该正确计算高优先级权重Score', () => {
      const roomWeightDto: RoomWeightDto = {
        p1: P1WeightEnum.JustReceived,
        p2: P2WeightEnum.Unread,
        p3: [P3WeightEnum.Top, P3WeightEnum.Favorite],
        timestamp: 1719273600000,
      };

      const result = RoomWeightScoreUtil.calculateScore(roomWeightDto);
      
      expect(result.p1).toBe(3);
      expect(result.p2).toBe(1);
      expect(result.p3).toBe(192); // 128 | 64
      expect(result.p4).toBe(1719273600);
      // (3 << 50) | (1 << 49) | (192 << 41) | 1719273600
      expect(result.score).toBeGreaterThan(0);
    });

    it('应该使用默认值处理空参数', () => {
      const roomWeightDto: RoomWeightDto = {};
      
      const result = RoomWeightScoreUtil.calculateScore(roomWeightDto);
      
      expect(result.p1).toBe(P1WeightEnum.Default);
      expect(result.p2).toBe(P2WeightEnum.Default);
      expect(result.p3).toBe(P3WeightEnum.Default);
      expect(result.p4).toBeGreaterThan(0); // 应该是当前时间戳
    });
  });

  describe('reCalculateScore', () => {
    it('应该正确重新计算权重Score', () => {
      const originalScore = 2200742529152; // 基础Score
      const updatedDto: RoomWeightDto = {
        p1: P1WeightEnum.InService,
        p3: [P3WeightEnum.Top],
      };

      const result = RoomWeightScoreUtil.reCalculateScore(originalScore, updatedDto);
      
      expect(result.p1).toBe(2); // 更新为InService
      expect(result.p2).toBe(0); // 保持原值
      expect(result.p3).toBe(128); // 更新为Top
      expect(result.p4).toBeGreaterThan(0); // 使用当前时间
    });

    it('应该保持未更新的字段不变', () => {
      const originalScore = 3377699720527872; // 高优先级Score
      const originalParsed = RoomWeightScoreUtil.parseScore(originalScore);
      
      const updatedDto: RoomWeightDto = {
        p2: P2WeightEnum.Default, // 只更新P2
      };

      const result = RoomWeightScoreUtil.reCalculateScore(originalScore, updatedDto);
      
      expect(result.p1).toBe(originalParsed.p1); // 保持不变
      expect(result.p2).toBe(0); // 更新为Default
      expect(result.p3).toBe(originalParsed.p3); // 保持不变
    });
  });

  describe('parseScore', () => {
    it('应该正确解析Score为各个组成部分', () => {
      const score = 3377699720527872;
      
      const result = RoomWeightScoreUtil.parseScore(score);
      
      expect(result.p1).toBeGreaterThanOrEqual(0);
      expect(result.p1).toBeLessThanOrEqual(3);
      expect(result.p2).toBeGreaterThanOrEqual(0);
      expect(result.p2).toBeLessThanOrEqual(1);
      expect(result.p3).toBeGreaterThanOrEqual(0);
      expect(result.p3).toBeLessThanOrEqual(255);
      expect(result.p4).toBeGreaterThanOrEqual(0);
    });

    it('应该正确解析零Score', () => {
      const score = 0;
      
      const result = RoomWeightScoreUtil.parseScore(score);
      
      expect(result.p1).toBe(0);
      expect(result.p2).toBe(0);
      expect(result.p3).toBe(0);
      expect(result.p4).toBe(0);
    });
  });

  describe('formatP3', () => {
    it('应该正确格式化P3标签列表为位掩码', () => {
      const p3List = [P3WeightEnum.Top, P3WeightEnum.Favorite];
      
      const result = RoomWeightScoreUtil.formatP3(p3List);
      
      expect(result).toBe(192); // 128 | 64
    });

    it('应该处理空列表', () => {
      const result = RoomWeightScoreUtil.formatP3([]);
      
      expect(result).toBe(P3WeightEnum.Default);
    });

    it('应该处理单个标签', () => {
      const result = RoomWeightScoreUtil.formatP3([P3WeightEnum.AtMe]);
      
      expect(result).toBe(32);
    });
  });

  describe('parseP3ToList', () => {
    it('应该正确解析位掩码为标签列表', () => {
      const p3Mask = 192; // Top | Favorite
      
      const result = RoomWeightScoreUtil.parseP3ToList(p3Mask);
      
      expect(result).toContain(P3WeightEnum.Top);
      expect(result).toContain(P3WeightEnum.Favorite);
      expect(result.length).toBe(2);
    });

    it('应该处理零掩码', () => {
      const result = RoomWeightScoreUtil.parseP3ToList(0);
      
      expect(result).toEqual([P3WeightEnum.Default]);
    });

    it('应该处理复杂掩码', () => {
      const p3Mask = P3WeightEnum.Top | P3WeightEnum.Favorite | P3WeightEnum.AtMe | P3WeightEnum.Draft;
      
      const result = RoomWeightScoreUtil.parseP3ToList(p3Mask);
      
      expect(result).toContain(P3WeightEnum.Top);
      expect(result).toContain(P3WeightEnum.Favorite);
      expect(result).toContain(P3WeightEnum.AtMe);
      expect(result).toContain(P3WeightEnum.Draft);
      expect(result.length).toBe(4);
    });
  });

  describe('formatP4', () => {
    it('应该正确格式化时间戳为P4值', () => {
      const timestamp = 1719273600000; // 毫秒
      
      const result = RoomWeightScoreUtil.formatP4(timestamp);
      
      expect(result).toBe(1719273600); // 秒
    });

    it('应该处理当前时间戳', () => {
      const now = Date.now();
      
      const result = RoomWeightScoreUtil.formatP4(now);
      
      expect(result).toBe(Math.floor(now / 1000));
    });
  });

  describe('compareScores', () => {
    it('应该正确比较Score大小', () => {
      const score1 = 3377699720527872; // 高优先级
      const score2 = 2199023255552;    // 低优先级
      
      expect(RoomWeightScoreUtil.compareScores(score1, score2)).toBe(1);
      expect(RoomWeightScoreUtil.compareScores(score2, score1)).toBe(-1);
      expect(RoomWeightScoreUtil.compareScores(score1, score1)).toBe(0);
    });
  });

  describe('getCurrentP4', () => {
    it('应该返回当前时间的P4值', () => {
      const before = Math.floor(Date.now() / 1000);
      const result = RoomWeightScoreUtil.getCurrentP4();
      const after = Math.floor(Date.now() / 1000);
      
      expect(result).toBeGreaterThanOrEqual(before);
      expect(result).toBeLessThanOrEqual(after);
    });
  });

  describe('边界值测试', () => {
    it('应该正确处理最大值', () => {
      const roomWeightDto: RoomWeightDto = {
        p1: P1WeightEnum.JustReceived, // 3 (最大值)
        p2: P2WeightEnum.Unread,       // 1 (最大值)
        p3: [P3WeightEnum.Top, P3WeightEnum.Favorite, P3WeightEnum.AtMe, 
             P3WeightEnum.Draft, P3WeightEnum.SendFailed, P3WeightEnum.MarkUnread,
             P3WeightEnum.Other, P3WeightEnum.Default], // 所有标签
        timestamp: Date.now(),
      };

      expect(() => {
        RoomWeightScoreUtil.calculateScore(roomWeightDto);
      }).not.toThrow();
    });
  });
});
