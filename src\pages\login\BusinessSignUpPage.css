/* BusinessSignUpPage.css */
.business-register-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: white;
  position: relative;
  padding-bottom: 120px; /* 為底部按鈕預留空間 */
}

.business-register-page__navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 48px 16px 20px;
  gap: 8px;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 100;
}

.business-register-page__back-button {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.business-register-page__back-icon {
  width: 24px;
  height: 24px;
}

.business-register-page__title-container {
  flex: 1;
}

.business-register-page__title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.4em;
  color: #333333;
}

.business-register-page__spacer {
  width: 24px;
}

.business-register-page__form {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  flex: 1;
  --border-top: none !important;
  --border-bottom: none !important;
  margin-bottom: 24px; /* 確保表單內容不會被底部按鈕覆蓋 */
  overflow-y: auto;
  max-height: calc(100vh - 200px); /* 減去導航欄和按鈕的高度 */
}

.business-register-page__form-group {
  margin-bottom: 24px;
}

.business-register-page__form-group.mb-10 {
  margin-bottom: 40px; /* 為最後一個表單項增加更多間距 */
}

.business-register-page__form-group .adm-list-item-content{
  border-top: none !important;
  border-bottom: solid 1px var(--adm-border-color);
}

.business-register-page__form-group .adm-form-item-child-position-normal{
  height: 22px;
}

.business-register-page__form-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666666;
  margin-bottom: 4px;
}

.business-register-page__label-text {
  margin-right: 4px;
}

.business-register-page__required {
  color: #FF3141;
}

.business-register-page__input-container {
  border-bottom-width: 1px;
  padding-top: 12px;
  padding-bottom: 12px;
}

.business-register-page__input-container--error {
  border-color: #FF3141;
}

.business-register-page__input-container--normal {
  border-color: #EEEEEE;
}

.business-register-page__input {
  width: 100%;
  outline: none;
  font-size: 16px;
}

.business-register-page__logo-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4px;
}

.business-register-page__logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #F7F7F7;
  border-radius: 50%;
  cursor: pointer;
  overflow: hidden;
  margin-bottom: 8px;
}

.business-register-page__logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.business-register-page__logo-placeholder {
  width: 32px;
  height: 32px;
}

.business-register-page__logo-input {
  display: none;
}

.business-register-page__error-text {
  font-size: 12px;
  color: #FF3141;
  margin-top: 4px;
}

/* 底部按钮容器 */
.business-register-page__button-container {
  padding: 16px 16px 48px;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #FFFFFF;
  z-index: 1000;
  /* 支持安全區域 */
  padding-bottom: calc(48px + env(safe-area-inset-bottom, 0px));
}

.business-register-page__confirm-button {
  height: 49px;
  font-size: 18px;
  border-radius: 4px;
  opacity: 1;
  background-color: #1677FF;
  transition: opacity 0.3s ease;
}

.business-register-page__confirm-button--loading {
  background-color: #1677FF;
  cursor: not-allowed;
  opacity: 0.8;
}

/* 加载中的省略号动画 */
.business-register-page__loading-text::after {
  content: '';
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
  100% {
    content: '';
  }
}

/* 虛擬鍵盤適配 - 當視窗高度變小時（鍵盤彈出） */
@media (max-height: 600px) {
  .business-register-page {
    padding-bottom: 80px; /* 減少底部間距 */
  }

  .business-register-page__form {
    max-height: calc(100vh - 180px); /* 調整表單最大高度 */
  }

  .business-register-page__button-container {
    padding: 12px 16px 24px; /* 減少按鈕容器的內邊距 */
    padding-bottom: calc(24px + env(safe-area-inset-bottom, 0px));
  }

  .business-register-page__form-group {
    margin-bottom: 16px; /* 減少表單項間距 */
  }
}

/* 極小屏幕適配（鍵盤彈出時） */
@media (max-height: 500px) {
  .business-register-page {
    padding-bottom: 60px;
  }

  .business-register-page__form {
    max-height: calc(100vh - 160px); /* 進一步調整表單最大高度 */
  }

  .business-register-page__button-container {
    padding: 8px 16px 16px;
    padding-bottom: calc(16px + env(safe-area-inset-bottom, 0px));
  }

  .business-register-page__form-group {
    margin-bottom: 12px;
  }

  .business-register-page__navbar {
    padding: 24px 16px 16px; /* 減少頂部導航間距 */
  }
}

/* 移動端特殊處理 - 確保地址輸入框不被覆蓋 */
@media (max-width: 768px) {
  .business-register-page__form {
    scroll-behavior: smooth;
  }

  /* 當輸入框聚焦時的特殊處理 */
  .business-register-page__input:focus {
    outline: none;
    border-color: #1677FF;
  }
}
