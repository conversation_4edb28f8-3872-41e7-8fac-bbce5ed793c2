/**
 * 页面颜色配置
 * 定义不同页面的状态栏背景色
 */

// 页面颜色映射
export const PAGE_COLORS = {
  // 默认颜色
  DEFAULT: '#FFFFFF',
  
  // 主页颜色（根据用户身份）
  HOME: {
    ADMIN: '#E4F4FD',      // 管理员 - 蓝色系
    USER: '#E6FBF3',       // 普通用户 - 绿色系
  },
  
  // 聊天室颜色
  CHAT: {
    DEFAULT: '#FFFFFF',     // 默认聊天室
    MY: '#F2F2F2',         // 我的聊天室
    TEAM: '#E4F4FD',       // 团队聊天室
    CUSTOMER: '#E6FBF3',   // 客户聊天室
    SYSTEM: '#FFF7E6',     // 系统聊天室
  },
  
  // 登录页面颜色
  AUTH: {
    LOGIN: '#FFFFFF',
    SIGNUP: '#F8F9FA',
    OTP: '#FFFFFF',
  },
  
  // 其他页面颜色
  PROFILE: '#F5F5F5',
  SETTINGS: '#FFFFFF',
  ERROR: '#FFEBEE',
} as const;

// 颜色工具函数
export class PageColorUtils {
  /**
   * 获取主页颜色（根据用户身份）
   */
  static getHomeColor(isAdmin: boolean): string {
    return isAdmin ? PAGE_COLORS.HOME.ADMIN : PAGE_COLORS.HOME.USER;
  }
  
  /**
   * 获取聊天室颜色（根据聊天室类型）
   */
  static getChatColor(chatType: string): string {
    switch (chatType.toLowerCase()) {
      case 'my':
        return PAGE_COLORS.CHAT.MY;
      case 'team':
        return PAGE_COLORS.CHAT.TEAM;
      case 'customer':
        return PAGE_COLORS.CHAT.CUSTOMER;
      case 'system':
        return PAGE_COLORS.CHAT.SYSTEM;
      default:
        return PAGE_COLORS.CHAT.DEFAULT;
    }
  }
  
  /**
   * 获取认证页面颜色
   */
  static getAuthColor(authType: string): string {
    switch (authType.toLowerCase()) {
      case 'login':
        return PAGE_COLORS.AUTH.LOGIN;
      case 'signup':
      case 'business-signup':
        return PAGE_COLORS.AUTH.SIGNUP;
      case 'otp':
      case 'otp-login':
        return PAGE_COLORS.AUTH.OTP;
      default:
        return PAGE_COLORS.AUTH.LOGIN;
    }
  }
  
  /**
   * 根据路径自动获取页面颜色
   */
  static getColorByPath(pathname: string, isAdmin: boolean = false): string {
    // 移除查询参数和hash
    const cleanPath = pathname.split('?')[0].split('#')[0];
    
    // 主页
    if (cleanPath === '/' || cleanPath === '/home') {
      return this.getHomeColor(isAdmin);
    }
    
    // 聊天室页面
    if (cleanPath.includes('/chat')) {
      if (cleanPath.includes('/customer-chat')) {
        return this.getChatColor('customer');
      } else if (cleanPath.includes('/team-chat')) {
        return this.getChatColor('team');
      } else if (cleanPath.includes('/system-chat')) {
        return this.getChatColor('system');
      } else {
        return this.getChatColor('my');
      }
    }
    
    // 认证页面
    if (cleanPath.includes('/login')) {
      return this.getAuthColor('login');
    }
    if (cleanPath.includes('/signup')) {
      return this.getAuthColor('signup');
    }
    if (cleanPath.includes('/otp')) {
      return this.getAuthColor('otp');
    }
    
    // 其他页面
    if (cleanPath.includes('/profile')) {
      return PAGE_COLORS.PROFILE;
    }
    if (cleanPath.includes('/settings')) {
      return PAGE_COLORS.SETTINGS;
    }
    if (cleanPath.includes('/error') || cleanPath.includes('/404') || cleanPath.includes('/500')) {
      return PAGE_COLORS.ERROR;
    }
    
    // 默认颜色
    return PAGE_COLORS.DEFAULT;
  }
  
  /**
   * 验证颜色格式是否正确
   */
  static isValidColor(color: string): boolean {
    // 检查是否为有效的十六进制颜色
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexRegex.test(color);
  }
  
  /**
   * 将颜色转换为标准格式
   */
  static normalizeColor(color: string): string {
    if (!color) return PAGE_COLORS.DEFAULT;
    
    // 确保颜色以 # 开头
    if (!color.startsWith('#')) {
      color = '#' + color;
    }
    
    // 将3位十六进制转换为6位
    if (color.length === 4) {
      color = '#' + color[1] + color[1] + color[2] + color[2] + color[3] + color[3];
    }
    
    // 验证格式
    if (!this.isValidColor(color)) {
      console.warn(`Invalid color format: ${color}, using default`);
      return PAGE_COLORS.DEFAULT;
    }
    
    return color.toUpperCase();
  }
}

// 导出常用颜色
export const COMMON_COLORS = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  TRANSPARENT: 'transparent',
  
  // 品牌色
  PRIMARY: '#1677FF',
  SUCCESS: '#00B578',
  WARNING: '#FF8F1F',
  DANGER: '#FF3141',
  
  // 灰色系
  GRAY_50: '#FAFAFA',
  GRAY_100: '#F5F5F5',
  GRAY_200: '#EEEEEE',
  GRAY_300: '#E0E0E0',
  GRAY_400: '#BDBDBD',
  GRAY_500: '#9E9E9E',
  GRAY_600: '#757575',
  GRAY_700: '#616161',
  GRAY_800: '#424242',
  GRAY_900: '#212121',
} as const;
