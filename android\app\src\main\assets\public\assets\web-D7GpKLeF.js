import{W as t}from"./index-Bw_DHHKR.js";class m extends t{getMedias(e){throw console.log("getMedias",e),this.unimplemented("Not implemented on web.")}getMediaByIdentifier(e){throw console.log("getMediaByIdentifier",e),this.unimplemented("Not implemented on web.")}getAlbums(){throw this.unimplemented("Not implemented on web.")}savePhoto(e){throw console.log("savePhoto",e),this.unimplemented("Not implemented on web.")}saveVideo(e){throw console.log("saveVideo",e),this.unimplemented("Not implemented on web.")}createAlbum(e){throw console.log("createAlbum",e),this.unimplemented("Not implemented on web.")}getAlbumsPath(){throw console.log("getAlbumsPath"),this.unimplemented("Not implemented on web.")}}export{m as MediaWeb};
