import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from '@/types/aile.enum';
import { RoomWeightDto, WeightCalculationResult, ScoreParseResult } from '@/types/room.types';

/**
 * 聊天室权重评分工具类
 * 
 * 实现聊天室权重评分系统，将多个关键属性编码到64位long整数中：
 * - P1 (2位): 最高优先级 (位50-51)
 * - P2 (1位): 次要优先级 (位49)
 * - P3 (8位): 标签优先级位掩码 (位41-48)
 * - P4 (41位): 活跃时间戳秒 (位0-40)
 * 
 * Score = (P1 << 50) | (P2 << 49) | (P3 << 41) | P4
 */
export class RoomWeightScoreUtil {
  
  // 位宽定义
  private static readonly P1_BITS = 2;   // P1位宽
  private static readonly P2_BITS = 1;   // P2位宽
  private static readonly P3_BITS = 8;   // P3位宽
  private static readonly P4_BITS = 41;  // P4位宽
  
  // 位偏移定义
  private static readonly P4_OFFSET = 0;                                    // P4偏移: 0
  private static readonly P3_OFFSET = this.P4_OFFSET + this.P4_BITS;       // P3偏移: 41
  private static readonly P2_OFFSET = this.P3_OFFSET + this.P3_BITS;       // P2偏移: 49
  private static readonly P1_OFFSET = this.P2_OFFSET + this.P2_BITS;       // P1偏移: 50
  
  // 掩码定义 - 使用BigInt确保精度
  private static readonly P1_MASK = (1n << BigInt(this.P1_BITS)) - 1n;     // P1掩码: 0x3
  private static readonly P2_MASK = (1n << BigInt(this.P2_BITS)) - 1n;     // P2掩码: 0x1
  private static readonly P3_MASK = (1n << BigInt(this.P3_BITS)) - 1n;     // P3掩码: 0xFF
  private static readonly P4_MASK = (1n << BigInt(this.P4_BITS)) - 1n;     // P4掩码: 0x1FFFFFFFFFF
  
  /**
   * 计算聊天室权重Score
   * @param roomWeightDto 聊天室权重DTO
   * @returns 权重计算结果
   */
  public static calculateScore(roomWeightDto: RoomWeightDto): WeightCalculationResult {
    const p1 = roomWeightDto.p1 ?? P1WeightEnum.Default;
    const p2 = roomWeightDto.p2 ?? P2WeightEnum.Default;
    const p3 = this.formatP3(roomWeightDto.p3 ?? [P3WeightEnum.Default]);
    const p4 = this.formatP4(roomWeightDto.timestamp ?? Date.now());
    
    // 验证值范围
    this.validateValues(p1, p2, p3, p4);
    
    // 使用BigInt进行64位计算，然后转换为number
    // Score = (P1 << 50) | (P2 << 49) | (P3 << 41) | P4
    const score = Number(
      (BigInt(p1) << BigInt(this.P1_OFFSET)) |
      (BigInt(p2) << BigInt(this.P2_OFFSET)) |
      (BigInt(p3) << BigInt(this.P3_OFFSET)) |
      BigInt(p4)
    );
    
    return {
      score,
      p1,
      p2,
      p3,
      p4
    };
  }
  
  /**
   * 重新计算聊天室权重Score
   * 只更新提供的字段，其他字段从原Score中解析
   * @param oldScore 原始Score
   * @param updatedRoomWeightDto 更新的权重DTO
   * @returns 权重计算结果
   */
  public static reCalculateScore(oldScore: number, updatedRoomWeightDto: RoomWeightDto): WeightCalculationResult {
    // 解析原始Score
    const originalParsed = this.parseScore(oldScore);
    
    // 确定新的值，优先使用更新的值，否则使用原始值
    const p1 = updatedRoomWeightDto.p1 ?? originalParsed.p1;
    const p2 = updatedRoomWeightDto.p2 ?? originalParsed.p2;
    const p3 = updatedRoomWeightDto.p3 !== undefined 
      ? this.formatP3(updatedRoomWeightDto.p3) 
      : originalParsed.p3;
    const p4 = updatedRoomWeightDto.timestamp !== undefined 
      ? this.formatP4(updatedRoomWeightDto.timestamp) 
      : this.formatP4(Date.now()); // 如果没有提供时间戳，使用当前时间
    
    // 验证值范围
    this.validateValues(p1, p2, p3, p4);
    
    // 计算新的Score
    const score = Number(
      (BigInt(p1) << BigInt(this.P1_OFFSET)) |
      (BigInt(p2) << BigInt(this.P2_OFFSET)) |
      (BigInt(p3) << BigInt(this.P3_OFFSET)) |
      BigInt(p4)
    );
    
    return {
      score,
      p1,
      p2,
      p3,
      p4
    };
  }
  
  /**
   * 解析Score为各个组成部分
   * @param score 要解析的Score
   * @returns Score解析结果
   */
  public static parseScore(score: number): ScoreParseResult {
    const scoreBigInt = BigInt(score);
    
    const p1 = Number((scoreBigInt >> BigInt(this.P1_OFFSET)) & this.P1_MASK);
    const p2 = Number((scoreBigInt >> BigInt(this.P2_OFFSET)) & this.P2_MASK);
    const p3 = Number((scoreBigInt >> BigInt(this.P3_OFFSET)) & this.P3_MASK);
    const p4 = Number(scoreBigInt & this.P4_MASK);
    
    return { p1, p2, p3, p4 };
  }
  
  /**
   * 格式化P3标签列表为位掩码
   * @param p3List P3标签列表
   * @returns 位掩码值
   */
  public static formatP3(p3List: P3WeightEnum[]): number {
    if (!p3List || p3List.length === 0) {
      return P3WeightEnum.Default;
    }
    
    return p3List.reduce((mask, tag) => mask | tag, 0);
  }
  
  /**
   * 格式化时间戳为P4值
   * @param timestamp 时间戳(毫秒)
   * @returns P4值(秒，低41位)
   */
  public static formatP4(timestamp: number): number {
    // 转换为秒并取低41位
    const timestampSeconds = Math.floor(timestamp / 1000);
    return Number(BigInt(timestampSeconds) & this.P4_MASK);
  }
  
  /**
   * 将P3位掩码解析为标签列表
   * @param p3Mask P3位掩码
   * @returns P3标签列表
   */
  public static parseP3ToList(p3Mask: number): P3WeightEnum[] {
    const tags: P3WeightEnum[] = [];
    
    // 检查每个位
    Object.values(P3WeightEnum).forEach(tag => {
      if (typeof tag === 'number' && (p3Mask & tag) !== 0) {
        tags.push(tag);
      }
    });
    
    return tags.length > 0 ? tags : [P3WeightEnum.Default];
  }
  
  /**
   * 验证各个值的范围
   * @param p1 P1值
   * @param p2 P2值
   * @param p3 P3值
   * @param p4 P4值
   */
  private static validateValues(p1: number, p2: number, p3: number, p4: number): void {
    const p1Max = Number(this.P1_MASK);
    const p2Max = Number(this.P2_MASK);
    const p3Max = Number(this.P3_MASK);
    const p4Max = Number(this.P4_MASK);
    
    if (p1 < 0 || p1 > p1Max) {
      throw new Error(`P1值超出范围: ${p1}, 有效范围: 0-${p1Max}`);
    }
    if (p2 < 0 || p2 > p2Max) {
      throw new Error(`P2值超出范围: ${p2}, 有效范围: 0-${p2Max}`);
    }
    if (p3 < 0 || p3 > p3Max) {
      throw new Error(`P3值超出范围: ${p3}, 有效范围: 0-${p3Max}`);
    }
    if (p4 < 0 || p4 > p4Max) {
      throw new Error(`P4值超出范围: ${p4}, 有效范围: 0-${p4Max}`);
    }
  }
  
  /**
   * 获取当前时间戳的P4值
   * @returns 当前时间的P4值
   */
  public static getCurrentP4(): number {
    return this.formatP4(Date.now());
  }
  
  /**
   * 比较两个Score的大小
   * @param score1 Score1
   * @param score2 Score2
   * @returns 比较结果 (score1 > score2 返回1, score1 < score2 返回-1, 相等返回0)
   */
  public static compareScores(score1: number, score2: number): number {
    if (score1 > score2) return 1;
    if (score1 < score2) return -1;
    return 0;
  }
}
