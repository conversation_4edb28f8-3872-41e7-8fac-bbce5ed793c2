import { EventEmitter } from '../utils/EventEmitter';
import { LogInState } from '../types/aile.enum';
import { ConstantUtil } from '../utils/constantUtil';
import logService from './system/logService';
import { BehaviorSubject, Observable } from 'rxjs';
import type { RoomVO } from './core/chat/roomService.types';
import type { LogInAccount, LoginTenant } from '@/types/chat.types';
import { getLocalStorage, setLocalStorage } from '@/utils/storage';
import { MessageData } from './core/chat/messageService';

// Enums (previously imported from aile.enum)
enum ProcessState {
  Nono = 'nono',
  Login = 'login',
  Register = 'register',
  Logout = 'logout',
  ProcessStart = 'processStart',
  ProcessEnd = 'processEnd',
}

enum MessageAction {
  add = 'add',
  update = 'update',
  delete = 'delete',
  sessionStatusChanged = 'sessionStatusChanged'
}

enum RoomAction {
  add = 'add',
  update = 'update',
  delete = 'delete',
  lastMessageUpdate = 'lastMessageUpdate',
  topStatusChanged = 'topStatusChanged',
  muteStatusChanged = 'muteStatusChanged',
  notificationAction = 'notificationAction',
  membersUpdate = 'membersUpdate',
}


enum MemberChangedAction {
  Received = 'received',
  Readed = 'readed',
}

enum UnReadType {
  CommonUnRead = 'common',
  ServiceUnRead = 'service',
  SystemUnRead = 'system',
  AccountUnRead = 'account',
  RoomUnRead = 'room',
}

enum RoomAIAction {
  AICreate = 'aiCreate',
  AIUpdate = 'aiUpdate',
  AIDelete = 'aiDelete',
  AIStatusChanged = 'aiStatusChanged',
}

// Types (previously imported from aile.model)
interface DeviceData {
  deviceId: string;
  osType: string;
  osVersion: string;
  deviceModel: string;
  bundleId: string;
  appName: string;
  appVersion: string;
  language: string;
  platform: string;
  [key: string]: any;
}

interface LoginUser {
  id: string;
  createTime: number;
  updateTime: number;
  name: string;
  avatarId: string | null;
  mood: string | null;
  age: number | null;
  gender: string | null;
  birthday: string | null;
  status: string;
  accountId: string;
  tenantId: string;
  channel: string;
  personRoomId: string;
  joinType: string;
  openId: string;
  isJoinAile: boolean | null;
  isBindAile: boolean | null;
  isCollectInfo: boolean | null;
  homePagePicId: string | null;
  [key: string]: any;
}

interface Message {
  id: string;
  roomId: string;
  messageId: string;
  _from?: string;
  from?: string;
  channel?: string;
  content: any;
  tenantCode?: string;
  tenantId?: string;
  sendTime?: Date;
  sentAt: Date;
  sequence: number;
  isUser?: boolean;
  [key: string]: any;
}

interface Tenant {
  tenantId: string;
  tenantCode: string;
  tenantName: string;
  isPerson: boolean;
  [key: string]: any;
}

class StateService {
  private eventEmitter = new EventEmitter();
  
  // Observable subjects
  private _deviceData$ = new BehaviorSubject<DeviceData | null>(null);
  private _loginUser$ = new BehaviorSubject<LoginUser | null>(null);
  private _loginState$ = new BehaviorSubject<{ status: LogInState, user: LoginUser | null }>({
    status: LogInState.LoggedOut,
    user: null
  });
  private _netStatus$ = new BehaviorSubject<boolean>(true);
  private _tenantId$ = new BehaviorSubject<string>('');
  private _tenantCode$ = new BehaviorSubject<string>('');
  private _officialTenantId$ = new BehaviorSubject<string>('');
  private _isPersonTenant$ = new BehaviorSubject<boolean>(false);
  private _openRooms$ = new BehaviorSubject<RoomVO[]>([]);
  private _loginTenant$ = new BehaviorSubject<LoginTenant | null>(null);
  private _loginAccount$ = new BehaviorSubject<LogInAccount | null>(null);
  private _bossServiceNumberId: string = '';
  private _bossServiceNumberId$ = new BehaviorSubject<string>('');
  private _isAdmin: boolean = false;
  
  // State properties (formerly signals)
  private _deviceData: DeviceData | null = null;
  private _openRooms: RoomVO[] = [];
  private _officialTenantId: string = '';
  private _tenantCode: string = '';
  private _tenantId: string = '';
  private _isPersonTenant: boolean = false;
  private _loginTokenId: string = '';
  private _loginUser: LoginUser | null = null;
  private _netStatus: boolean = true;
  private _transTenant: {
    tenantId: string;
    transMemberArray: {
      status: number;
      name: string;
      accountId: string;
    }[];
  } | null = null;
  private _loginTenant: LoginTenant | null = null;
  private _loginAccount: LogInAccount | null = null;

  constructor() {
    logService.info('StateService initialized');
    this.loadFromCache();
  }

  // Load data from cache
  public async loadFromCache(): Promise<void> {
    if (ConstantUtil.isNull(this._transTenant) && typeof (ConstantUtil as any).isAile === 'function' && (ConstantUtil as any).isAile()) {
      const transTenant = getLocalStorage<any>(ConstantUtil.TRANS_TENANT_KEY, null);
      if (transTenant) {
        this._transTenant = transTenant;
        this.emit('transTenantChanged', this._transTenant);
      }
    }

    if (ConstantUtil.isNull(this._loginUser)) {
      const loginUser = getLocalStorage<LoginUser | null>(ConstantUtil.LOGIN_USER_KEY, null);
      logService.info('從快取載入用戶資訊', {
        hasLoginUser: !!loginUser,
        userTenantId: loginUser?.tenantId || null,
      });
      
      if (loginUser) {
        this._loginUser = loginUser;
        this._loginTokenId = loginUser.tokenId;
        this._tenantId = loginUser.tenantId || '';
        this._tenantCode = loginUser.tenantCode || '';
        this._loginUser$.next(loginUser);
        this.emit('loginUserChanged', loginUser);
        
        logService.info('設置 stateService 租戶 ID', { tenantId: this._tenantId });
      }
    }

    // 從 localStorage 直接讀取租戶 ID
    const localTenantId = getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
    if (localTenantId && !this._tenantId) {
      this._tenantId = localTenantId;
      this._tenantId$.next(localTenantId);
      logService.info('從 localStorage 直接讀取租戶 ID', { tenantId: localTenantId });
    }

    if (ConstantUtil.isNull(this._loginTenant)) {
      const loginTenant = getLocalStorage<LoginTenant | null>(ConstantUtil.LOGIN_TENANT_KEY, null);
      logService.info('從快取載入租戶資訊', {
        hasLoginTenant: !!loginTenant,
        tenantId: loginTenant?.id || null,
      });
      
      if (loginTenant) {
        this._loginTenant = loginTenant;
        this._loginTenant$.next(loginTenant);
        this.emit('loginTenantChanged', loginTenant);
      }
    }

    if (ConstantUtil.isNull(this._loginAccount)) {
      const loginAccount = getLocalStorage<LogInAccount | null>(ConstantUtil.LOGIN_ACCOUNT_KEY, null);
      if (loginAccount) {
        this._loginAccount = loginAccount;
        this._loginAccount$.next(loginAccount);
        this.emit('loginAccountChanged', loginAccount);
      }
    }

    const bossServiceNumberId = getLocalStorage<string>(ConstantUtil.BOSS_SERVICENUMBERID, '');
    if (bossServiceNumberId) {
      this._bossServiceNumberId = bossServiceNumberId;
      this._bossServiceNumberId$.next(bossServiceNumberId);
      this.emit('bossServiceNumberIdChanged', bossServiceNumberId);
    }
  }

  // Event handlers - Legacy API
  public on(event: string, listener: (...args: any[]) => void): () => void {
    this.eventEmitter.on(event, listener);
    return () => this.eventEmitter.off(event, listener);
  }

  public emit(event: string, ...args: any[]): void {
    this.eventEmitter.emit(event, ...args);
  }

  // Observable API - New RxJS style
  public deviceData$(): Observable<DeviceData | null> {
    return this._deviceData$.asObservable();
  }
  
  public loginUser$(): Observable<LoginUser | null> { 
    return this._loginUser$.asObservable();
  }
  
  public loginState$(): Observable<{ status: LogInState, user: LoginUser | null }> {
    return this._loginState$.asObservable();
  }
  
  public netStatus$(): Observable<boolean> {
    return this._netStatus$.asObservable();
  }
  
  public tenantId$(): Observable<string> {
    return this._tenantId$.asObservable();
  }
  
  public tenantCode$(): Observable<string> {
    return this._tenantCode$.asObservable();
  }
  
  public officialTenantId$(): Observable<string> {
    return this._officialTenantId$.asObservable();
  }
  
  public isPersonTenant$(): Observable<boolean> {
    return this._isPersonTenant$.asObservable();
  }
  
  public openRooms$(): Observable<RoomVO[]> {
    return this._openRooms$.asObservable();
  }

  public loginTenant$(): Observable<LoginTenant | null> {
    return this._loginTenant$.asObservable();
  }

  public loginAccount$(): Observable<LogInAccount | null> {
    return this._loginAccount$.asObservable();
  }

  public bossServiceNumberId$(): Observable<string> {
    return this._bossServiceNumberId$.asObservable();
  }

  // Getters for state properties - Legacy API
  public deviceData(): DeviceData | null {
    return this._deviceData;
  }

  public setDeviceData(data: DeviceData): void {
    this._deviceData = data;
    this._deviceData$.next(data);
    this.emit('deviceDataChanged', this._deviceData);
  }

  public openRooms(): RoomVO[] {
    return [...this._openRooms];
  }

  public officialTenantId(): string {
    return this._officialTenantId;
  }

  public setOfficialTenantId(id: string): void {
    this._officialTenantId = id;
    this._officialTenantId$.next(id);
    this.emit('officialTenantIdChanged', this._officialTenantId);
  }

  public tenantCode(): string {
    return this._tenantCode;
  }

  public setTenantCode(code: string): void {
    this._tenantCode = code;
    this._tenantCode$.next(code);
    this.emit('tenantCodeChanged', this._tenantCode);
  }

  public tenantId(): string {
    return this._tenantId;
  }

  public setTenantId(id: string): void {
    this._tenantId = id;
    this._tenantId$.next(id);
    this.emit('tenantIdChanged', this._tenantId);
  }

  public isPersonTenant(): boolean {
    return this._isPersonTenant;
  }

  public setIsPersonTenant(isPerson: boolean): void {
    this._isPersonTenant = isPerson;
    this._isPersonTenant$.next(isPerson);
    this.emit('isPersonTenantChanged', this._isPersonTenant);
  }

  public loginTokenId(): string {
    return this._loginTokenId;
  }

  public setLoginTokenId(id: string): void {
    this._loginTokenId = id;
    this.emit('loginTokenIdChanged', this._loginTokenId);
  }

  public netStatus(): boolean {
    return this._netStatus;
  }

  public loginUser(): LoginUser | null {
    return this._loginUser;
  }

  public setLoginUser(user: LoginUser | null): void {
    this._loginUser = user;
    if (user) {
      this._loginTokenId = user.tokenId;
      this._tenantId = user.tenantId || '';
      this._tenantCode = user.tenantCode || '';
      setLocalStorage(ConstantUtil.LOGIN_USER_KEY, user);
    } else {
      this._loginTokenId = '';
      this._tenantId = '';
      this._tenantCode = '';
    }
    this._loginUser$.next(user);
    this.emit('loginUserChanged', this._loginUser);
  }

  public loginTenant(): LoginTenant | null {
    return this._loginTenant;
  }

  public setLoginTenant(tenant: LoginTenant | null): void {
    this._loginTenant = tenant;
    this._loginTenant$.next(tenant);
    setLocalStorage(ConstantUtil.LOGIN_TENANT_KEY, tenant);
    this.emit('loginTenantChanged', this._loginTenant);
  }

  public loginAccount(): LogInAccount | null {
    return this._loginAccount;
  }

  public setLoginAccount(account: LogInAccount | null): void {
    this._loginAccount = account;
    this._loginAccount$.next(account);
    setLocalStorage(ConstantUtil.LOGIN_ACCOUNT_KEY, account);
    this.emit('loginAccountChanged', this._loginAccount);
  }

  public transTenant(): any {
    return this._transTenant;
  }

  public setTransTenant(tenant: any): void {
    this._transTenant = tenant;
    if (tenant) {
      setLocalStorage(ConstantUtil.TRANS_TENANT_KEY, tenant);
    }
    this.emit('transTenantChanged', this._transTenant);
  }

  public bossServiceNumberId(): string {
    return this._bossServiceNumberId;
  }

  public setBossServiceNumberId(id: string): void {
    // 添加調試日誌
    console.log('設置 bossServiceNumberId:', id);
    logService.debug('設置 bossServiceNumberId', { id });
    
    this._bossServiceNumberId = id;
    this._bossServiceNumberId$.next(id);
    setLocalStorage(ConstantUtil.BOSS_SERVICENUMBERID, id);
    this.emit('bossServiceNumberIdChanged', id);
  }
  
  /**
   * 判斷當前用戶是否為管理員（owner）身份
   * @returns {boolean} 是否為管理員
   */
  public isAdmin(): boolean {
    // 如果已經設置了 _isAdmin，直接返回
    if (this._isAdmin !== undefined) {
      return this._isAdmin;
    }
    
    // 從登入用戶資訊中判斷
    if (this._loginUser && this._loginUser.joinType) {
      // 如果 joinType 為 'owner' 或 'admin'，則為管理員身份
      this._isAdmin = ['owner', 'admin'].includes(this._loginUser.joinType.toLowerCase());
      return this._isAdmin;
    }
    
    // 從租戶資訊中判斷
    if (this._loginTenant && this._loginTenant.role) {
      // 如果角色為 'owner' 或 'admin'，則為管理員身份
      this._isAdmin = ['owner', 'admin'].includes(this._loginTenant.role.toLowerCase());
      return this._isAdmin;
    }
    
    // 默認返回 false
    return false;
  }
  
  /**
   * 設置用戶是否為管理員
   * @param isAdmin 是否為管理員
   */
  public setIsAdmin(isAdmin: boolean): void {
    this._isAdmin = isAdmin;
  }

  // Room management methods
  public setOpenRoom(room: RoomVO | null, isMulti: boolean = false): void {
    if (isMulti) {
      if (room == null) {
        return;
      }
      
      // Remove room if exists, then add the updated one
      this._openRooms = this._openRooms.filter((item) => item.id !== room.id);
      this._openRooms.push(room);
    } else {
      if (room) {
        this._openRooms = [room];
      } else {
        this._openRooms = [];
      }
    }
    
    this._openRooms$.next(this._openRooms);
    this.emit('openRoomsChanged', this._openRooms);
  }

  public closeRoom(roomId: string): void {
    this._openRooms = this._openRooms.filter((item) => item.id !== roomId);
    this._openRooms$.next(this._openRooms);
    this.emit('openRoomsChanged', this._openRooms);
  }

  public isInOpen(roomId: string): boolean {
    return this._openRooms.some((item) => item.id === roomId);
  }

  // Notification methods
  public notifyLoginChanged(status: LogInState, user: any): void {
    this._loginUser = user;
    this._loginUser$.next(user);
    this._loginState$.next({ status, user });
    this.emit('loginChanged', status, user);
    
    // Also emit legacy event for backwards compatibility
    if (user) {
      this.emit('loginUserChanged', user);
    } else {
      this.emit('logoutUserChanged', null);
    }
  }

  public notifyNetChanged(status: boolean): void {
    this._netStatus = status;
    this._netStatus$.next(status);
    this.emit('netChanged', status);
  }

  public notifyProcessChanged(state: ProcessState, tenantInfo: Tenant | null): void {
    this.emit('processChanged', {
      state,
      tenantId: tenantInfo?.tenantId || '',
      tenantName: tenantInfo?.tenantName || '',
    });
  }

  public notifyMessageChanged(data: {
    action: MessageAction;
    roomId?: string;
    messageId?: string;
    message: MessageData | null;
    tenantId?: string;
  }): void {
    this.emit('messageChanged', data);
  }

  public notifyRoomChanged(data: {
    action: RoomAction;
    roomId?: string;
    room?: RoomVO | null;
    message: MessageData | null;
    tenantId?: string;
  }): void {
    this.emit('roomChanged', data);
  }

  public notifyMemberChanged(data: {
    action: MemberChangedAction;
    roomId?: string;
    memberId?: string;
    sequence?: number;
  }): void {
    this.emit('memberChanged', data);
  }

  public notifyUnReadChanged(data: {
    type: UnReadType;
    count: number;
    roomId?: string;
  }): void {
    this.emit('unReadChanged', data);
  }

  public notifyTenantChanged(tenantInfo: Tenant | null): void {
    this.emit('tenantChanged', { tenantInfo });
  }

  public notifyServiceNumberChanged(): void {
    this.emit('serviceNumberChanged');
  }

  public notifyContactChanged(contactEvent: string): void {
    this.emit('contactChanged', contactEvent);
  }

  public notifyEmployeeChanged(employeeEvent: string): void {
    this.emit('employeeChanged', employeeEvent);
  }

  public notifyContactListChanged(): void {
    this.emit('contactListChanged');
  }

  /**
   * 通知聯繫人頭像已更新
   * @param contactId 聯繫人ID
   */
  public notifyContactAvatarChanged(contactId: string): void {
    this.emit('contactAvatarChanged', contactId);
  }

  public notifyAppStateChanged(state: 'active' | 'background'): void {
    this.emit('appStateChanged', { state });
  }

  public notifyMessagePlayChanged(data: {
    messageId: string;
    status: 'playing' | 'pause' | 'stop';
  }): void {
    this.emit('messagePlayChanged', data);
  }

  public notifyNoticeAlertChanged(status: boolean): void {
    this.emit('noticeAlertChanged', status);
  }

  public notifyProcessMessageEvent(data: {
    name: string;
    code: string;
    event: string;
    sequence: number;
    content: any;
    tenantId: string;
    callback: any;
    isOff: any;
  }): void {
    this.emit('processMessageEvent', data);
  }

  public notifyRoomAIStatusChanged(data: {
    type: RoomAIAction;
    tenantId: string;
    roomId: string;
  }): void {
    this.emit('roomAIStatusChanged', data);
  }

  // 添加 LoginChanged Observable 專門給 pushService 使用
  public get LoginChanged(): Observable<{ status: LogInState, user: LoginUser | null }> {
    return this._loginState$.asObservable();
  }
}

export {
  StateService,
  ProcessState,
  MessageAction,
  RoomAction,
  MemberChangedAction,
  UnReadType,
  RoomAIAction
};

export type {
  DeviceData,
  LoginUser,
  Message,
  RoomVO,
  Tenant
};

// Create a singleton instance
const stateService = new StateService();
export default stateService;
