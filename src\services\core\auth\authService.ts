// 添加 SQLitePlugin 類型定義
declare global {
  interface Window {
    sqlitePlugin?: {
      deleteDatabase: (config: { name: string; location: string }) => Promise<void>;
      [key: string]: any;
    };
  }
}

import { logService } from '../../system/logService';
import { <PERSON>le<PERSON><PERSON> } from '../../api/aileApi';
import { ConstantUtil } from '../../../utils/constantUtil';
import { CryptoUtil } from '../../../utils/cryptoUtil';
import { getLocalStorage, setLocalStorage, removeLocalStorage, clearLocalStorage } from '../../../utils/storage';
import stateService from '../../stateService';
import aileDBService from '../../db/aileDBService';
import tenantService from '../tenant/tenantService';
import accountSwitchManager from '../../../utils/accountSwitchManager';
import { NavigateFunction } from 'react-router-dom';
import { closeAileDB } from '../../db/aileDBService';
import { RelationTenantVO, TransTenantVO } from '../tenant';
import { LogInAccount } from '../../../types/chat.types';
import { LogInState } from '../../../types/aile.enum';
import { LineLogin } from 'aile-capacitor-line-login';
import { deviceService } from '../../platform/deviceService';
import { envConfig } from '../../../config/env';
import { ROUTE_OTP_LOGIN, ROUTE_USER_SIGNUP, ROUTE_BUSINESS_REGISTER, ROUTE_HOME } from '@/config/app/routes';
import apolloClient from '../../../apolloClient';
import { RequestLoginOtpDocument, RequestLoginOtpMutation, RequestLoginOtpMutationVariables } from '../../../gql/graphql';

// 用户接口
export interface AccountResponse {
  accountId?: string;
  name?: string;
  countryCode?: string;
  mobile?: string;
  type?: string;
  onlineId?: string;
  tokenId?: string;
  systemRoomId?: string;
  personRoomId?: string;
  systemAccountId?: string;
  isInitial?: boolean;
  transTenant?: TransTenantVO;
  tenantInfo?: RelationTenantVO[];
  [property: string]: any;
}

// 統一響應結構
export interface CommonResponse<T> {
  code?: string;
  data?: T;
  msg?: string;
  status?: number;
  success?: boolean;
  timeCost?: number;
  [property: string]: any;
}

// 驗證碼請求/響應類型
export interface OtpRequest {
  mobile: string;
  countryCode: string;
  type: string;
}

export interface OtpData {
  onceToken: string;
  validSecond: number;
}

export type OtpResponse = CommonResponse<OtpData>;

// 登錄請求/響應類型
export interface LoginRequest {
  onceToken: string;
  checkCode: string;
  type: string;
}

export type LoginResponse = CommonResponse<AccountResponse>;

export interface ThirdPartyUser {
  id: string;
  name: string;
  channel: string;
  email?: string;
  pictureUrl?: string;
  error?: string; // 错误信息
}
/**
 * Token 校驗請求/響應類型
 */
export interface CheckTokenRequest {
  token: string;
}

export interface CheckTokenData {
  valid: boolean;
  expired: boolean;
  userId?: string;
  [property: string]: any;
}

export type CheckTokenResponse = CommonResponse<CheckTokenData>;

// 第三方登录请求接口
export interface ThirdPartyLoginRequest {
  loginType: string;
  thirdChannel: string;
  useNonMobile: boolean;
  scopeId: string;
}

export interface UpdateAccountMobileRequest {
  mobile: string; 
  countryCode: string; 
  accountId: string; 
}
export interface UpdateAccountMobileRespon {
  code?: string;
  data?: boolean;
  msg?: string;
  status?: number;
  success?: boolean;
  timeCost?: number;
  [property: string]: any;
}
// 驗證OTP請求
export interface ValidateOtpRequest {
  onceToken: string;
  checkCode: string;
}

export type ValidateOtpResponse = CommonResponse<boolean>;

/**
 * 綜合認證服務
 * 提供認證相關的 API 調用和本地存儲功能
 */
class AuthService {
  /**
   * LINE Login 相關屬性和方法
   */
  private lineInitialized = false;
  private isDevelopment = process.env.NODE_ENV === 'development';
  
  /**
   * 初始化 LINE Login SDK
   */
  public async initializeLineLogin(): Promise<void> {
    if (this.lineInitialized) return;
    
    try {
      // 在開發環境打印日誌
      if (this.isDevelopment) {
        logService.info('AuthService: 初始化 LINE Login SDK (開發環境模式)');
      }
      
      await LineLogin.initialize({
        channelId: envConfig.VITE_LINE_CHANNEL_ID,
        redirectUri: envConfig.VITE_LINE_REDIRECT_URI,
        scope: ['profile', 'openid'],
        botPrompt: 'normal',
        debug: envConfig.DEBUG
      });
      this.lineInitialized = true;
    } catch (error) {
      logService.error('LINE Login 初始化失敗:', error);
      throw error;
    }
  }

  /**
   * 執行 LINE 登入
   * @returns LINE登入用戶資訊
   */
  public async lineLogin(): Promise<ThirdPartyUser> {
    try {
      // 確保已初始化
      if (!this.lineInitialized) {
        await this.initializeLineLogin();
      }

      // 根據不同平台設置登入選項
      const loginOptions = deviceService.isWeb() 
        ? { onlyWebLogin: true } 
        : {};
      
      // 在開發環境打印日誌
      if (this.isDevelopment) {
        logService.info('AuthService: 執行 LINE 登入', { loginOptions, platform: deviceService.getPlatform() });
      }
      
      const result = await LineLogin.login(loginOptions);
      if (!result.userProfile) {
        throw new Error('LINE 登入失敗: 未獲取用戶資訊');
      }
      const user: ThirdPartyUser = {
        id: result.userProfile.userId,
        name: result.userProfile.displayName || '未命名用户',
        channel: 'Line',
        pictureUrl: result.userProfile.pictureUrl,
        email: '',
      };
      return user;
    } catch (error) {
      logService.error('LINE 登入失敗:', error);
      throw error;
    }
  }

  /**
   * 獲取 LINE 用戶資訊
   */
  public async getLineUserProfile() {
    try {
      const profile = await LineLogin.getUserProfile();
      return profile;
    } catch (error) {
      logService.error('獲取 LINE 用戶資訊失敗:', error);
      throw error;
    }
  }

  /**
   * 檢查 LINE 登入狀態
   */
  public async isLineLoggedIn() {
    try {
      const { isLoggedIn } = await LineLogin.isLoggedIn();
      return isLoggedIn;
    } catch (error) {
      logService.error('檢查 LINE 登入狀態失敗:', error);
      return false;
    }
  }

  /**
   * 登出 LINE
   */
  public async logoutLine() {
    try {
      await LineLogin.logout();
      return true;
    } catch (error) {
      logService.error('LINE 登出失敗:', error);
      throw error;
    }
  }

  /**
   * 發送驗證碼
   * @param request 驗證碼請求
   * @returns 驗證碼響應
   */
  public async sendOtp(request: OtpRequest): Promise<OtpResponse> {
    try {
      logService.info('發送驗證碼請求', { mobile: request.mobile, countryCode: request.countryCode });

      const response = await AileApi.sendOtp(request);

      if (response?.success) {
        logService.info('驗證碼發送成功，獲得 onceToken', {
          onceToken: response.data?.onceToken,
          expireTime: response.data?.expireTime
        });

        // 转换 API 响应格式为 authService 期望的格式
        const authResponse: OtpResponse = {
          success: response.success,
          data: response.data ? {
            onceToken: response.data.onceToken,
            validSecond: Math.floor((response.data.expireTime - Date.now()) / 1000)
          } : undefined,
          msg: response.msg
        };
        return authResponse;
      } else {
        throw new Error(response?.msg || '發送驗證碼失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('發送驗證碼異常', { error: errorMsg, stack: error?.stack });
      throw new Error(errorMsg);
    }
  }
  

  /**
 * 發送驗證碼 (GraphQL 版本)
 * @param request 驗證碼請求
 * @returns 驗證碼響應
 */
  public async sendOtpGraphql(request: OtpRequest): Promise<OtpResponse> {
    try {
      // 使用 Apollo Client 執行 GraphQL mutation
      const { data } = await apolloClient.mutate<RequestLoginOtpMutation, RequestLoginOtpMutationVariables>({
        mutation: RequestLoginOtpDocument,
        variables: {
          requestLoginOtpRequest2: {
            countryCode: request.countryCode,
            mobile: request.mobile,
          },
        },
      });

      // 根據 BFF 的回傳數據處理結果
      if (data?.requestLoginOtp.success) {
        logService.info('驗證碼發送成功，獲得 onceToken', {
          onceToken: data.requestLoginOtp.data?.onceToken,
          validSecond: data.requestLoginOtp.data?.validSecond
        });
        return {
          ...data.requestLoginOtp,
          code: data.requestLoginOtp.code || undefined,
          timeCost: data.requestLoginOtp.timeCost || undefined,
          data: data.requestLoginOtp.data || undefined
        };
      } else {
        throw new Error(data?.requestLoginOtp.msg || '發送驗證碼失敗');
      }
    } catch (err: any) {
      logService.error('GraphQL 發送驗證碼失敗', { error: err });
      throw new Error(err?.message || '發送驗證碼失敗');
    }
  };
  
  /**
   * 用戶登錄
   * @param request 登錄請求
   * @returns 登錄響應
   */
  public async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      logService.info('用戶登錄', { onceToken: request.onceToken, type: request.type ?? '' });
      
      const response = await AileApi.login(request);
      logService.debug('登錄成功', response);
      
      if (response?.success && response.data) {
        // 保存用戶信息和 token
        this.saveAuth(response.data);
        
        // 異步保存租户信息到數據庫

        
        logService.info('登錄成功', {
          user: response.data,
          authToken: response.data.authToken
        });
        
        return response;
      } else {
        throw new Error(response?.msg || '登錄失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('登錄異常', { error: errorMsg, stack: error?.stack });
      throw new Error(errorMsg);
    }
  }
  
  /**
   * 通過第三方令牌登錄（LINE、Google等）
   * @param request 第三方登錄請求
   * @returns 登錄響應
   */
  public async loginWithThirdParty(request: ThirdPartyLoginRequest): Promise<LoginResponse> {
    try {
      logService.info('第三方登錄ThirdPartyLoginRequest', request.toString());
      // 構造登錄請求
      
      // 這裡應該調用後端 API 驗證第三方令牌
      // 實際項目中需要替換為真實的 API 端點
      const response = await AileApi.accountLogin(request);
      logService.info('第三方登錄成功', response);
      
      if (response?.success && response.data) {
        // 保存用戶信息和 token
        this.saveAuth(response.data);
        
        logService.info('第三方登錄成功', {
          user: response.data,
          authToken: response.data.tokenId
        });
        
        return response;
      } else {
        throw new Error(response?.msg || '第三方登錄失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('第三方登錄異常', { error: errorMsg, stack: error?.stack });
      throw new Error(errorMsg);
    }
  }

  /**
   * 處理登錄成功後的通用操作（不包含 Redux 操作）
   * @param accountData賬號數據
   * @param navigate 導航函數
   * @returns 返回導航結果和租戶信息
   */
  public async handleLoginSuccess(
    accountData: AccountResponse,
    navigate: NavigateFunction
  ): Promise<{ shouldNavigate: boolean; route?: string; tenantList?: any[] }> {
    try {
      // 檢查是否是賬號切換情況
      const previousAccountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
      const isAccountSwitching = previousAccountId &&
                               previousAccountId !== accountData.accountId &&
                               !!accountData.accountId;

      // 如果是账号切换，使用账号切换管理器处理
      if (isAccountSwitching && accountData.accountId) {
        logService.info('检测到账号切换，使用账号切换管理器处理', {
          from: previousAccountId,
          to: accountData.accountId
        });

        const switchSuccess = await accountSwitchManager.switchAccount(accountData.accountId);
        if (!switchSuccess) {
          logService.error('账号切换失败，但继续登录流程');
        }
      }

      if (isAccountSwitching) {
        logService.info('檢測到賬號切換', {
          from: previousAccountId,
          to: accountData.accountId
        });

        // 賬號切換時，先確保完全關閉舊數據庫連接
        try {
          await closeAileDB();
          // 額外添加延遲，確保資源完全釋放
          await new Promise(resolve => setTimeout(resolve, 800));
        } catch (closeError) {
          logService.warn('關閉舊賬號數據庫失敗，但將繼續初始化', { error: closeError });
        }
      }

      // 1. 初始化資料庫
      if (accountData.accountId) {
        logService.info('開始初始化數據庫', { accountId: accountData.accountId });
        await aileDBService.initForAccount(accountData.accountId);
      }

      // 2. 保存認證信息到本地存儲
      this.saveAuth(accountData);

      // 3. 統一處理用戶數據
      const loginAccount: LogInAccount = {
        name: accountData.name ?? '',
        accountId: accountData.accountId ?? '',
        countryCode: accountData.countryCode ?? '',
        mobile: accountData.mobile ?? '',
        tokenId: accountData.tokenId ?? '',
        loginType: accountData.loginType ?? '',
        onlineId: accountData.onlineId ?? '',
        transTenant: accountData.transTenant ?? null,
        tenantInfo: accountData.tenantInfo ?? [],
        systemRoomId: accountData.systemRoomId ?? '',
        personRoomId: accountData.personRoomId ?? '',
        systemAccountId: accountData.systemAccountId ?? '',
        isInitial: accountData.isInitial ?? false,
        status: accountData.status ?? '',
        ...accountData
      };
      // 只保留 setLoginAccount，移除其他狀態設置
      stateService.setLoginAccount(loginAccount);

      // 确保数据库已初始化
      if (accountData.accountId && !aileDBService.isInitialized()) {
        try {
          logService.info('检测到数据库未初始化，立即初始化', { accountId: accountData.accountId });
          await aileDBService.initForAccount(accountData.accountId);

          // 数据库初始化成功后，通知登录状态变化
          stateService.notifyLoginChanged(LogInState.LoggedIn, accountData);

          logService.info('数据库初始化完成，已通知登录状态变化');
        } catch (dbError) {
          logService.error('数据库初始化失败', { error: dbError, accountId: accountData.accountId });
          throw new Error('数据库初始化失败，无法完成登录');
        }
      } else if (aileDBService.isInitialized()) {
        // 数据库已初始化，直接通知登录状态变化
        stateService.notifyLoginChanged(LogInState.LoggedIn, accountData);
        logService.info('数据库已初始化，已通知登录状态变化');
      }

      logService.info('登入成功並已存儲本地資料', { accountId: accountData.accountId, tokenId: accountData.tokenId });

      // 4. 檢查特殊情況需要導航
      // line未绑定手机号码
      if (accountData.loginType === ConstantUtil.LOGIN_TYPE_LINE && !accountData.mobile ) {
        logService.info('LINE未绑定手机号码，跳转到OTP登录页面');
        navigate(ROUTE_OTP_LOGIN, { replace: true });
        return { shouldNavigate: true, route: ROUTE_OTP_LOGIN };
      }

      // 處理用戶初始化狀態
      if (accountData.isInitial === true ) {
        logService.info('isInitial為true，跳轉UserSignUpPage');
        navigate(ROUTE_USER_SIGNUP, { replace: true });
        return { shouldNavigate: true, route: ROUTE_USER_SIGNUP };
      }

      // 5. 處理租戶相關邏輯並返回租戶信息
      const tenantResult = await this.handleTenantInitializationCore(navigate);
      return { shouldNavigate: true, route: ROUTE_HOME, tenantList: tenantResult || undefined };
    } catch (error: any) {
      logService.error('登錄成功後處理失敗', { error: error?.message || '未知錯誤' });
      throw error;
    }
  }

  /**
   * 處理租戶初始化核心邏輯（不包含 Redux 操作）
   * @param navigate 導航函數
   * @returns 租戶列表
   */
  private async handleTenantInitializationCore(
    navigate: NavigateFunction
  ): Promise<RelationTenantVO[] | null> {
    try {
      // 拉取租戶關係列表
      logService.info('开始获取租户关系列表');
      const tenantList = await tenantService.fetchTenantRelationList();

      if (tenantList && tenantList.length > 0) {
        logService.info('租戶关系列表获取成功', { count: tenantList?.length });
      } else {
        logService.error('获取租户关系列表失败');
        return null;
      }

      const hasOwnerTenant = await tenantService.hasOwnerTenant(tenantList ?? []);
      const lastTenantId = tenantService.getLastTenantId(tenantList ?? []);

      if(tenantList?.length === 0  || hasOwnerTenant === undefined || !hasOwnerTenant ||lastTenantId == null){
        logService.info('無個人租戶，跳轉BusinessSignUpPage');
        navigate(ROUTE_BUSINESS_REGISTER, { replace: true });
        return null;
      } else {
        // 應用租戶令牌（這部分需要在調用方處理 Redux）
        logService.info('設置當前租戶', { tenantId: lastTenantId });

        // 導航到首頁
        logService.info('登錄成功，跳轉到首頁');
        navigate(ROUTE_HOME, { replace: true });

        return tenantList;
      }

    } catch (error: any) {
      logService.error('租戶初始化失敗', { error: error?.message || '未知錯誤' });
      throw error;
    }
  }

  /**
   * 用戶登出
   * @returns 是否登出成功
   */
  public async logout(): Promise<boolean> {
    try {
      // 如果是 LINE 登录，尝试调用 LINE 登出
      try {
        if (this.lineInitialized) {
          await this.logoutLine();
        }
      } catch (lineError) {
        logService.warn('LINE 登出失敗，但繼續登出流程', { error: lineError as Error });
      }
      
      // 保存當前 accountId 用於後續處理
      const currentAccountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
      const dbName = currentAccountId ? `${currentAccountId}_data` : null;
      
      // 先保存並關閉數據庫
      try {
        // 確保強制關閉數據庫並重置其狀態
        logService.info('開始關閉數據庫連接', { dbName });
        await closeAileDB();
        
        // 確保有足夠的延遲讓數據庫連接完全釋放
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 額外調用一次關閉，確保數據庫連接被釋放
        if (dbName && window.sqlitePlugin) {
          if (typeof window.sqlitePlugin.closeConnection === 'function') {
            try {
              await window.sqlitePlugin.closeConnection({ database: dbName });
              logService.info('通過原生API額外確保數據庫連接關閉', { dbName });
            } catch (e) {
              // 忽略錯誤，這只是一個額外的安全措施
              logService.warn('嘗試通過原生API關閉連接時出錯', e);
            }
          }
          
          // 執行測試確認連接狀態
          if (typeof window.sqlitePlugin.echoTest === 'function') {
            try {
              await window.sqlitePlugin.echoTest();
              logService.info('數據庫連接已完全釋放', { dbName });
            } catch (e) {
              // 忽略錯誤，這只是一個測試
              logService.warn('測試數據庫連接狀態時出錯', e);
            }
          }
        }
      } catch (dbError) {
        logService.error('登出時關閉數據庫失敗', { error: dbError as Error });
        // 繼續進行登出流程，不影響登出
      }
      
      // 清理 stateService 中的狀態
      stateService.setLoginUser(null);
      stateService.setLoginTenant(null);
      stateService.setLoginAccount(null);
      stateService.setTenantId('');
      stateService.setBossServiceNumberId('');
      stateService.setIsAdmin(false);
      
      // 清理所有本地存儲
      clearLocalStorage();
      logService.info('用戶已登出，所有本地資訊已清除');
      return true;
    } catch (error: any) {
      logService.error('登出失敗', { error: error as Error });
      return false;
    }
  }
  
  // ========================
  // 本地存儲相關方法
  // ========================
  
  /**
   * 保存用戶信息到本地存儲
   * @param account 用戶信息
   */
  public saveAccount(account: AccountResponse | null): void {
    try {
      if (account) {
        setLocalStorage(ConstantUtil.LOGIN_ACCOUNT_KEY, account);
        if (account.accountId) {
          setLocalStorage(ConstantUtil.ACCOUNT_ID_KEY, account.accountId);
          // 當獲取到 accountId 時初始化數據庫
        //  this.initAccountDatabase(account.accountId);
        }
        logService.info('用戶信息已保存到本地存儲', { 
          accountId: account.accountId,
          name: account.name 
        });
      } else {
        removeLocalStorage(ConstantUtil.LOGIN_ACCOUNT_KEY);
        removeLocalStorage(ConstantUtil.ACCOUNT_ID_KEY);
        logService.info('用戶信息已從本地存儲中移除');
      }
    } catch (error) {
      logService.error('操作用戶信息存儲失敗', { error: error as Error });
    }
  }

  /**
   * 保存認證令牌到本地存儲
   * @param token 認證令牌
   */
  public saveAuthToken(token: string | null): void {
    try {
      if (token) {
        setLocalStorage(ConstantUtil.TOKEN_KEY, token);
        logService.info('認證令牌已保存到本地存儲');
      } else {
        removeLocalStorage(ConstantUtil.TOKEN_KEY);
        logService.info('認證令牌已從本地存儲中移除');
      }
    } catch (error) {
      logService.error('操作認證令牌存儲失敗', { error: error as Error });
    }
  }
  public getUserId(): string | null {
    // 直接從 stateService 的 LoginUser 取 id
    const loginUser = stateService.loginUser();
    return loginUser?.id || null;
  }
  
  /**
   * 保存完整的認證信息到本地存儲
   * @param accountResponse 用戶響應信息
   */
  public saveAuth(accountResponse: AccountResponse): void {
    this.saveAccount(accountResponse);
    if (accountResponse.tokenId) {
      this.saveAuthToken(accountResponse.tokenId);
    }
    logService.info('完整認證信息已保存到本地存儲', { userId: accountResponse.accountId });
  }

  /**
   * 從本地存儲獲取用戶信息
   * @returns 用戶信息或 null
   */
  public getAccount(): AccountResponse | null {
    try {
      return getLocalStorage<AccountResponse | null>(ConstantUtil.LOGIN_ACCOUNT_KEY, null);
    } catch (error) {
      logService.error('獲取用戶信息失敗', { error: error as Error });
      return null;
    }
  }

  /**
   * 從本地存儲獲取認證令牌
   * @returns 認證令牌或 null
   */
  public getAuthToken(): string | null {
    try {
      return getLocalStorage<string | null>(ConstantUtil.TOKEN_KEY, null);
    } catch (error) {
      logService.error('獲取認證令牌失敗', { error: error as Error });
      return null;
    }
  }

  /**
   * 從本地存儲刪除所有認證信息
   */
  public clearAuth(): void {
    try {
      removeLocalStorage(ConstantUtil.LOGIN_ACCOUNT_KEY);
      removeLocalStorage(ConstantUtil.TOKEN_KEY);
      logService.info('本地認證信息已清除');
    } catch (error) {
      logService.error('清除認證信息失敗', { error: error as Error });
    }
  }

  /**
   * 檢查用戶是否已登錄（根據本地存儲中的信息）
   * @returns 是否已登錄
   */
  public isLoggedIn(): boolean {
    return !!this.getAuthToken() && !!this.getAccount();
  }
  
  /**
   * 獲取認證狀態
   * @returns 認證狀態
   */
  public getAuthState(): { 
    account: AccountResponse | null; 
    authToken: string | null;
    isAuthenticated: boolean;
  } {
    const account = this.getAccount();
    const authToken = this.getAuthToken();
    const isAuthenticated = !!account && !!authToken;
    
    return {
      account,
      authToken,
      isAuthenticated
    };
  }

  /**
   * 初始化認證狀態
   * 從本地存儲中讀取用戶信息和 token
   * @returns 認證狀態
   */
  public initAuth(): { 
    account: AccountResponse | null; 
    authToken: string | null;
    isAuthenticated: boolean;
  } {
    try {
      const authState = this.getAuthState();
      
      if (authState.isAuthenticated && authState.account?.accountId) {
        logService.info('從本地存儲中恢復認證狀態', { 
          userId: authState.account?.accountId,
          name: authState.account?.name
        });
        
        // 注意：不在這裡初始化數據庫，由 App.tsx 統一控制
        // 避免重複初始化導致的錯誤
      } else {
        logService.info('未找到保存的認證狀態');
      }
      
      return authState;
    } catch (error) {
      logService.error('初始化認證狀態失敗', { error: error as Error });
      return {
        account: null,
        authToken: null,
        isAuthenticated: false
      };
    }
  }
 
  /**
   * 更新用戶個人資料（支援 args 加密 + avatar 檔案）
   */
  async updateUserProfile(params: { name: string; avatar?: File | null }): Promise<any> {
    const formData = new FormData();
    const argsObj: Record<string, any> = { name: params.name };
    const encryptedArgs = CryptoUtil.encryptApiRequest(argsObj);
    formData.append('args', encryptedArgs);
    if (params.avatar) {
      formData.append('file', params.avatar);
    }
    return await AileApi.updateUserProfileWithFile(formData);
  }
  
  /**
   * 校驗驗證碼
   * @param request 校驗驗證碼請求
   * @returns 校驗驗證碼響應
   */
  public async validateOtp(request: ValidateOtpRequest): Promise<ValidateOtpResponse> {
    try {
      logService.info('校驗驗證碼請求', { onceToken: request.onceToken });
      
      const decryptedResponse = await AileApi.validateOtp(request);
      
      if (!decryptedResponse || !decryptedResponse.success) {
        logService.warn('校驗驗證碼請求失敗', decryptedResponse);
      } else {
        logService.info('校驗驗證碼請求成功');
      }
      
      return decryptedResponse;
    } catch (error) {
      logService.error('校驗驗證碼請求出錯:', error);
      throw error;
    }
  }

  /**
   * 更新帳號手機號碼
   * @param params 更新帳號手機號碼參數
   * @returns 
   */
  async updateAccountMobile(params: { 
    mobile: string; 
    countryCode: string; 
    accountId: string; 
  }): Promise<any> {
    try {
      logService.info('更新帳號手機號碼', { mobile: params.mobile, countryCode: params.countryCode });
      
      const response = await AileApi.setMobile(params);
      logService.debug('更新手機號碼成功', response);
      
      if (response?.success) {
        logService.info('更新手機號碼成功', { mobile: params.mobile });
        return response;
      } else {
        throw new Error(response?.msg || '更新手機號碼失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('更新手機號碼異常', { error: errorMsg, stack: error?.stack });
      throw new Error(errorMsg);
    }
  }
 
  /**
   * 校驗帳號 Token 是否有效
   * @param request Token 校驗請求
   * @returns 校驗結果
   */
  public async checkAccountToken(): Promise<Boolean> {
    try {
      
      const response = await AileApi.checkAccountToken({});
      logService.debug('Token 校驗成功', response);

      if (response?.success && response.data) {
        logService.info('Token 校驗成功', response);
        return true;
      } else {
        return false;
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('Token 校驗異常', { error: errorMsg, stack: error?.stack });
      throw new Error(errorMsg);
    }
  }
  
  /**
   * 將用戶信息保存到 localStorage
   * 此方法用於從舊版 localStorage 直接保存用戶信息的情況
   * 新代碼應該使用 saveAccount 方法
   * @deprecated 使用 saveAccount 方法代替
   */
  public setAccountToLocalStorage(account: AccountResponse): void {
    logService.warn('使用過時的 setAccountToLocalStorage 方法，應該使用 saveAccount');
    this.saveAccount(account);
  }
}

// 導出單例
const authService = new AuthService();
export default authService; 