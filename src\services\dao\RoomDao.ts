import { logService } from '../system/logService';
import type { RoomVO, ChatMemberVO } from '../core/chat/roomService.types';
import aileDBService from '../db/aileDBService';
import { BaseDao } from './base/BaseDao';
import { getRequiredTenantId } from '@/utils/tenantUtil';

export interface IRoomDao {
  upsertRooms(rooms: RoomVO[]): Promise<void>;
  getRoomsByRobot(page: number, pageSize: number): Promise<RoomVO[]>;
  getRoomsByWeight(page: number, pageSize: number): Promise<RoomVO[]>;
  getRoomsByEnd(page: number, pageSize: number): Promise<RoomVO[]>;
  getRoomById(roomId: string): Promise<RoomVO | null>;
  getBasicRoomsFromDB(): Promise<RoomVO[]>;
  upsertChatMembers(chatMembers: ChatMemberVO[]): Promise<void>;
  getChatMembersByRoomId(roomId: string): Promise<ChatMemberVO[]>;
  getChatMemberById(memberId: string, roomId: string): Promise<ChatMemberVO | null>;
  getChatMemberSyncTimestamp(roomId: string): Promise<number | null>;
  updateChatMemberSyncTimestamp(roomId: string, timestamp: number): Promise<void>;
}

class RoomDao extends BaseDao implements IRoomDao {

  /**
   * 批量 upsert RoomVO 到 room table
   * 純粹的數據存儲操作，不包含業務邏輯處理
   */
  public async upsertRooms(rooms: RoomVO[]): Promise<void> {
    if (!rooms || rooms.length === 0) return;

    try {
      // 使用 BaseDao 驗證和確保租戶ID
      const validatedRooms = this.ensureTenantIdBatch(rooms, 'room');
      this.validateTenantIdBatch(validatedRooms, 'room');

      // 定義房間表的所有欄位
      const columns = [
        'accountId', 'agentId', 'avatarId', 'businessDescription', 'businessEndTime', 'businessId', 'businessName', 'businessStatus',
        'createTime', 'deleted', 'dfrTime', 'homePagePicId', 'id', 'isCustomName', 'isExternal', 'isMute', 'isOwnerStop', 'isTop',
        'isTransfer', 'lastMessage', 'lastSequence', 'mainRoomId', 'member_deleted', 'memberCount', 'memberIds', 'name', 'ownerId',
        'provisionalIds', 'serviceNumberId', 'sessionId', 'sessionStatus', 'status', 'tenantId', 'topTime', 'transferReason', 'type',
        'unreadCount', 'updateTime', 'warned', 'weight'
      ];

      // 準備批量插入的SQL
      const placeholders = columns.map(() => '?').join(',');
      const sql = `INSERT OR REPLACE INTO room (${columns.join(',')}) VALUES (${placeholders})`;

      // 批量處理所有房間數據
      const allValues: any[][] = [];

      for (const room of validatedRooms) {
        const values = columns.map(col => {
          let v = (room as any)[col];
          if (col === 'isTop' && typeof v === 'boolean') return v ? 1 : 0;
          if (Array.isArray(v)) return JSON.stringify(v);
          if (typeof v === 'object' && v !== null && (col === 'lastMessage')) return JSON.stringify(v);
          return v;
        });
        allValues.push(values);
      }

      // 批量執行插入操作
      for (const values of allValues) {
        await aileDBService.run(sql, values);
      }

      this.logOperation('upsertRooms', 'room', { roomsCount: rooms.length }, true);

    } catch (error) {
      this.handleError('upsertRooms', 'room', error as Error, { roomsCount: rooms.length });
    }
  }

  public async getRoomsByRobot(page: number, pageSize: number): Promise<RoomVO[]> {
    try {
      let sql = `SELECT * FROM room WHERE type NOT IN ('accountPerson', 'aileSystem', 'serviceMember') and sessionStatus = 'RobotActive' ORDER BY isTop DESC, updateTime DESC LIMIT ? OFFSET ?`;
      let params = [pageSize, page * pageSize];

      // 使用 BaseDao 添加租戶過濾
      const filtered = this.addTenantFilter(sql, params, 'room');

      this.logOperation('getRoomsByRobot', 'room', { page, pageSize });
      return await aileDBService.all<RoomVO>(filtered.sql, filtered.params);
    } catch (error) {
      this.handleError('getRoomsByRobot', 'room', error as Error, { page, pageSize });
      return [];
    }
  }

  public async getRoomsByWeight(page: number, pageSize: number): Promise<RoomVO[]> {
    try {
      let sql = `SELECT * FROM room WHERE type NOT IN ('accountPerson', 'aileSystem', 'serviceMember') and (sessionStatus is null OR sessionStatus IN ('DistributeActive', 'AgentActive')) ORDER BY isTop DESC, weight DESC LIMIT ? OFFSET ?`;
      let params = [pageSize, page * pageSize];

      // 使用 BaseDao 添加租戶過濾
      const filtered = this.addTenantFilter(sql, params, 'room');

      this.logOperation('getRoomsByWeight', 'room', { page, pageSize });
      return await aileDBService.all<RoomVO>(filtered.sql, filtered.params);
    } catch (error) {
      this.handleError('getRoomsByWeight', 'room', error as Error, { page, pageSize });
      return [];
    }
  }

  public async getRoomsByEnd(page: number, pageSize: number): Promise<RoomVO[]> {
    try {
      let sql = `SELECT * FROM room WHERE type NOT IN ('accountPerson', 'aileSystem', 'serviceMember') and sessionStatus NOT IN ('RobotActive', 'DistributeActive', 'AgentActive') ORDER BY isTop DESC, updateTime DESC LIMIT ? OFFSET ?`;
      let params = [pageSize, page * pageSize];

      // 使用 BaseDao 添加租戶過濾
      const filtered = this.addTenantFilter(sql, params, 'room');

      this.logOperation('getRoomsByEnd', 'room', { page, pageSize });
      return await aileDBService.all<RoomVO>(filtered.sql, filtered.params);
    } catch (error) {
      this.handleError('getRoomsByEnd', 'room', error as Error, { page, pageSize });
      return [];
    }
  }

  /**
   * 根據ID獲取聊天室
   */
  public async getRoomById(roomId: string): Promise<RoomVO | null> {
    try {
      let sql = `SELECT * FROM room WHERE id = ?`;
      let params = [roomId];

      // 使用 BaseDao 添加租戶過濾
      const filtered = this.addTenantFilter(sql, params, 'room');

      this.logOperation('getRoomById', 'room', { roomId });
      const rooms = await aileDBService.all<RoomVO>(filtered.sql, filtered.params);
      return rooms.length > 0 ? rooms[0] : null;
    } catch (error) {
      this.handleError('getRoomById', 'room', error as Error, { roomId });
      return null;
    }
  }

  /**
   * 查詢 type 為 person、aileSystem、group 的聊天室
   */
  public async getBasicRoomsFromDB(): Promise<RoomVO[]> {
    const tenantId = getRequiredTenantId();

    const sql = `SELECT * FROM room WHERE type IN ('accountPerson', 'aileSystem', 'serviceMember') AND tenantId = ? ORDER BY updateTime DESC`;
    return await aileDBService.all<RoomVO>(sql, [tenantId]);
  }

  /**
   * 批量 upsert ChatMemberVO 到 RoomMembers table
   * 純粹的數據存儲操作，不包含業務邏輯處理
   */
  public async upsertChatMembers(chatMembers: ChatMemberVO[]): Promise<void> {
    if (!chatMembers || chatMembers.length === 0) return;

    try {
      // 定義聊天室成員表的所有欄位
      const columns = [
        'id', 'createTime', 'updateTime', 'memberId', 'type', 'roomId', 'roomType', 'accountId', 'tenantId',
        'status', 'privilege', 'lastReceivedSequence', 'lastReadSequence', 'firstSequence', 'dfrTime',
        'mute', 'top', 'topTime', 'mainRoomId', 'serviceNumberId', 'lastMessage'
      ];

      // 準備批量插入的SQL
      const placeholders = columns.map(() => '?').join(',');
      const sql = `INSERT OR REPLACE INTO RoomMembers (${columns.join(',')}) VALUES (${placeholders})`;

      // 批量處理所有聊天室成員數據
      const allValues: any[][] = [];

      for (const member of chatMembers) {
        const values = columns.map(col => {
          let v = (member as any)[col];
          if (col === 'mute' || col === 'top') {
            if (typeof v === 'boolean') return v ? 1 : 0;
            if (v === null || v === undefined) return null;
            return v;
          }
          if (col === 'lastMessage' && typeof v === 'object' && v !== null) {
            return JSON.stringify(v);
          }
          return v;
        });
        allValues.push(values);
      }

      // 批量執行插入操作
      for (const values of allValues) {
        await aileDBService.run(sql, values);
      }

      logService.info('批量儲存聊天室成員資料成功', {
        membersCount: chatMembers.length,
        memberIds: chatMembers.map(m => m.id).filter(Boolean)
      });

    } catch (error) {
      logService.error('批量儲存聊天室成員資料失敗', { error: error as Error, membersCount: chatMembers.length });
      throw error;
    }
  }

  /**
   * 根據聊天室ID獲取聊天室成員列表
   */
  public async getChatMembersByRoomId(roomId: string): Promise<ChatMemberVO[]> {
    const tenantId = getRequiredTenantId();

    const sql = `SELECT * FROM RoomMembers WHERE roomId = ? AND tenantId = ? ORDER BY createTime DESC`;
    try {
      const members = await aileDBService.all<any>(sql, [roomId, tenantId]);

      // 轉換數據格式，處理布林值和JSON字段
      return members.map(member => ({
        ...member,
        mute: member.mute === 1 ? true : member.mute === 0 ? false : null,
        top: member.top === 1 ? true : member.top === 0 ? false : null,
        lastMessage: member.lastMessage ? JSON.parse(member.lastMessage) : undefined
      }));
    } catch (error) {
      logService.error('根據聊天室ID查詢成員失敗', { error, roomId });
      throw error;
    }
  }

  /**
   * 根據成員ID和聊天室ID獲取特定聊天室成員
   */
  public async getChatMemberById(memberId: string, roomId: string): Promise<ChatMemberVO | null> {
    const tenantId = getRequiredTenantId();

    const sql = `SELECT * FROM RoomMembers WHERE memberId = ? AND roomId = ? AND tenantId = ?`;
    try {
      const members = await aileDBService.all<any>(sql, [memberId, roomId, tenantId]);
      if (members.length === 0) return null;

      const member = members[0];
      // 轉換數據格式，處理布林值和JSON字段
      return {
        ...member,
        mute: member.mute === 1 ? true : member.mute === 0 ? false : null,
        top: member.top === 1 ? true : member.top === 0 ? false : null,
        lastMessage: member.lastMessage ? JSON.parse(member.lastMessage) : undefined
      };
    } catch (error) {
      logService.error('根據成員ID和聊天室ID查詢成員失敗', { error, memberId, roomId });
      throw error;
    }
  }

  /**
   * 獲取聊天室成員同步時間戳
   * @param roomId 聊天室ID
   * @returns 同步時間戳，如果不存在則返回null
   */
  public async getChatMemberSyncTimestamp(roomId: string): Promise<number | null> {
    const tenantId = getRequiredTenantId();
    const key = `chat_member_sync_${tenantId}_${roomId}`;

    try {
      const sql = `SELECT value FROM KVStore WHERE key = ?`;
      const result = await aileDBService.all<{ value: string }>(sql, [key]);

      if (result.length > 0 && result[0].value) {
        const timestamp = parseInt(result[0].value, 10);
        return isNaN(timestamp) ? null : timestamp;
      }

      return null;
    } catch (error) {
      logService.error('獲取聊天室成員同步時間戳失敗', { error, roomId, key });
      return null;
    }
  }

  /**
   * 更新聊天室成員同步時間戳
   * @param roomId 聊天室ID
   * @param timestamp 時間戳
   */
  public async updateChatMemberSyncTimestamp(roomId: string, timestamp: number): Promise<void> {
    const tenantId = getRequiredTenantId();
    const key = `chat_member_sync_${tenantId}_${roomId}`;
    const now = Date.now();

    try {
      const sql = `INSERT OR REPLACE INTO KVStore (key, value, createTime, updateTime) VALUES (?, ?, ?, ?)`;
      await aileDBService.run(sql, [key, timestamp.toString(), now, now]);

      logService.debug('更新聊天室成員同步時間戳成功', { roomId, timestamp, key });
    } catch (error) {
      logService.error('更新聊天室成員同步時間戳失敗', { error, roomId, timestamp, key });
      throw error;
    }
  }
}

const roomDao: IRoomDao = new RoomDao();
export default roomDao;