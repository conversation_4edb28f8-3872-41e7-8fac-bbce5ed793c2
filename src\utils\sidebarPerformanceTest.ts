import { logService } from '@/services/system/logService';
import { store } from '@/app/store';

/**
 * 侧边栏性能测试工具
 * 用于验证优化效果
 */
export class SidebarPerformanceTest {
  /**
   * 测试 localStorage 数据恢复性能
   */
  static testLocalStorageRestore(): void {
    const startTime = performance.now();
    
    try {
      // 模拟从 localStorage 恢复数据
      const savedState = localStorage.getItem('redux_state');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        const hasTeamData = parsed?.tenant?.tenantRelations?.length > 0;
        const dataAge = parsed?.tenant?.tenantRelationsLastFetched ? 
          Date.now() - parsed.tenant.tenantRelationsLastFetched : 
          Infinity;
        
        const duration = performance.now() - startTime;
        
        logService.info('侧边栏性能测试：localStorage 数据恢复', {
          duration: Math.round(duration * 100) / 100 + 'ms',
          hasTeamData,
          teamCount: parsed?.tenant?.tenantRelations?.length || 0,
          dataAge: dataAge === Infinity ? '无数据' : Math.floor(dataAge / 1000 / 60) + '分钟',
          isDataFresh: dataAge < 10 * 60 * 1000
        });
      } else {
        const duration = performance.now() - startTime;
        logService.info('侧边栏性能测试：无 localStorage 数据', {
          duration: Math.round(duration * 100) / 100 + 'ms'
        });
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logService.error('侧边栏性能测试：localStorage 恢复失败', {
        duration: Math.round(duration * 100) / 100 + 'ms',
        error
      });
    }
  }

  /**
   * 测试 Redux 状态访问性能
   */
  static testReduxStateAccess(): void {
    const startTime = performance.now();
    
    try {
      const state = store.getState();
      const tenantRelations = state.tenant.tenantRelations;
      const lastFetched = state.tenant.tenantRelationsLastFetched;
      
      const duration = performance.now() - startTime;
      
      logService.info('侧边栏性能测试：Redux 状态访问', {
        duration: Math.round(duration * 100) / 100 + 'ms',
        teamCount: tenantRelations.length,
        hasData: tenantRelations.length > 0,
        lastFetched: lastFetched ? new Date(lastFetched).toISOString() : '无数据'
      });
    } catch (error) {
      const duration = performance.now() - startTime;
      logService.error('侧边栏性能测试：Redux 状态访问失败', {
        duration: Math.round(duration * 100) / 100 + 'ms',
        error
      });
    }
  }

  /**
   * 测试侧边栏组件渲染性能
   */
  static testSidebarRenderTime(): void {
    const startTime = performance.now();
    
    // 模拟侧边栏渲染逻辑
    setTimeout(() => {
      const duration = performance.now() - startTime;
      logService.info('侧边栏性能测试：组件渲染时间', {
        duration: Math.round(duration * 100) / 100 + 'ms'
      });
    }, 0);
  }

  /**
   * 运行完整的性能测试套件
   */
  static runFullTest(): void {
    logService.info('开始侧边栏性能测试套件');
    
    this.testLocalStorageRestore();
    this.testReduxStateAccess();
    this.testSidebarRenderTime();
    
    logService.info('侧边栏性能测试套件完成');
  }

  /**
   * 比较优化前后的性能数据
   */
  static comparePerformance(beforeData: any, afterData: any): void {
    const improvement = {
      loadTime: beforeData.loadTime - afterData.loadTime,
      renderTime: beforeData.renderTime - afterData.renderTime,
      cacheHitRate: afterData.cacheHitRate - beforeData.cacheHitRate
    };

    logService.info('侧边栏性能优化对比', {
      before: beforeData,
      after: afterData,
      improvement: {
        loadTimeImprovement: improvement.loadTime + 'ms',
        renderTimeImprovement: improvement.renderTime + 'ms',
        cacheHitRateImprovement: (improvement.cacheHitRate * 100).toFixed(1) + '%'
      }
    });
  }
}

// 在开发环境中自动运行测试
if (process.env.NODE_ENV === 'development') {
  // 延迟运行，确保应用已初始化
  setTimeout(() => {
    SidebarPerformanceTest.runFullTest();
  }, 2000);
}

export default SidebarPerformanceTest;
