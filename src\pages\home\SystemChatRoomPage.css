/* 导入基础样式 */
@import './chat/ChatRoom.css';
@import './chat/ChatRoomIcons.css';

/* 特定样式覆盖 */
.system-chat-room-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #E4F4FD;
}

/* Navbar Styles */
.system-chat-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
}

.system-chat-navbar-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 6px 16px 6px 0;
}

.system-chat-back-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.system-chat-room-title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4em;
  color: #333333;
}

.system-chat-navbar-right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.system-chat-navbar-icon {
  width: 24px;
  height: 24px;
}

.system-chat-navbar-menu {
  position: relative;
}

/* Chat Content */
.system-chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-bottom: 16px;
}

.system-chat-date-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  width: 100%;
}

.system-chat-date-header span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4em;
  color: #666666;
}

/* Message Styles */
.system-chat-message-row {
  display: flex;
  gap: 8px;
  width: 100%;
}

/* 系统头像样式覆盖 */
.system-chat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 22px;
  background-color: #1677FF;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.system-avatar-letter {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.4em;
  color: #FFFFFF;
  text-align: center;
}

.system-chat-message-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.system-chat-message-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 8px;
}

/* 系统消息样式覆盖 */
.system-chat-message-bubble {
  background-color: #FFFFFF;
  border-radius: 0 8px 8px 8px;
  padding: 8px 12px;
}

.system-chat-message-bubble span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
  color: #333333;
}

.system-chat-message-time {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 9px;
  line-height: 1.4em;
  color: #999999;
}

/* Chat Input */
.system-chat-input-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
}

.system-chat-input-toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #FFFFFF;
}

.system-chat-toolbar-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
}

.system-chat-toolbar-icon img {
  width: 24px;
  height: 24px;
}

.system-chat-input {
  flex: 1;
  padding: 8px 12px;
  background-color: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #333333;
  min-height: 36px;
  max-height: 120px;
  resize: none;
}

.system-chat-input .adm-text-area {
  border: none;
  background: transparent;
  padding: 0;
}

.system-chat-input .adm-text-area-element {
  min-height: 20px !important;
  line-height: 20px;
  font-size: 14px;
  padding: 0;
  border: none;
  background: transparent;
  resize: none;
}

.system-chat-input::placeholder {
  color: #999999;
  opacity: 0.7;
}

/* 页面错误状态样式 */
.page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #E4F4FD;
  color: #ff4d4f;
  font-size: 14px;
  gap: 16px;
  padding: 20px;
  text-align: center;
}

.page-error button {
  background-color: #1677FF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.page-error button:hover {
  background-color: #0958d9;
}