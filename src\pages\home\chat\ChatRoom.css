/* 添加 CSS 變量來控制聊天室背景色 */
:root {
  --chat-background-color: #E4F4FD;
  --link-color: #1677FF;
  --link-hover-color: #0958d9;
}

/* 通用聊天室样式 */
.base-chat-room-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F2;
}

/* 导航栏通用样式 */
.base-chat-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  background-color: #FFFFFF;
  padding: 0 16px;
  position: relative;
  z-index: 1000;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);
}

.base-chat-navbar-left {
  display: flex;
  align-items: center;
}

.base-chat-navbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.base-chat-back-button {
  margin-right: 8px;
}

.base-chat-room-title {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
}

.base-chat-navbar-icon {
  width: 24px;
  height: 24px;
  margin-left: 16px;
  cursor: pointer;
}

.base-chat-navbar-menu {
  position: relative;
}

.base-chat-badge-content {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 10px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

/* 聊天内容区域通用样式 */
.base-chat-content {
  flex: 1;
  padding: 16px;
  overflow-x: hidden;
  background-color: var(--chat-background-color);
  position: relative; /* 为绝对定位的空状态容器提供定位上下文 */
}

/* 非管理员用户的聊天内容背景色 */
.base-chat-content.non-admin {
  background-color: #E6FBF3;
}

/* 空状态时的聊天内容区域 - 使用flexbox布局 */
.base-chat-content.chat-content-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* 移除默认padding，让空状态容器控制间距 */
}

/* 历史消息加载指示器 */
.history-loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: fadeInDown 0.3s ease-out;
}

.history-loading-spinner {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.spinner-dot {
  width: 6px;
  height: 6px;
  background-color: #00B578;
  border-radius: 50%;
  margin: 0 2px;
  animation: spinnerPulse 1.4s infinite ease-in-out both;
}

.spinner-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.history-loading-text {
  font-size: 13px;
  color: #666;
  font-weight: 400;
}

/* 动画定义 */
@keyframes spinnerPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.base-chat-date-header {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.base-chat-date-header span {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333333;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.base-chat-message-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.base-chat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.base-chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  background-color: #FFFFFF;
}

.base-chat-message-container {
  display: flex;
  flex-direction: column;
}

.base-chat-message-wrapper {
  display: flex;
  align-items: flex-end;
  margin-bottom: 4px;
}

.base-chat-message-bubble {
  background-color: #FFFFFF;
  border-radius: 0 8px 8px 8px;
  padding: 8px 12px;
  font-size: 15px;
  color: #333333;
  word-break: break-word;
  max-width: 280px;
  position: relative;
}

.base-chat-message-bubble a {
  color: #FFFFFF !important;
}

/* 图片消息样式 */
.chat-image-message {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  object-fit: contain;
}

.base-chat-message-bubble img.chat-image-message {
  margin: -8px -12px;
  border-radius: 0 8px 8px 8px;
  overflow: hidden;
}

.base-chat-message-bubble.user img.chat-image-message {
  border-radius: 8px 0px 8px 8px;
}

.base-chat-message-time {
  font-size: 11px;
  color: #999999;
  margin: 0 8px;
}

/* 發送者姓名樣式 */
.base-chat-sender-name {
  font-size: 12px;
  color: #666666;
  text-align: right;
  padding-right: 8px;
}

/* 不同聊天室類型的發送者姓名樣式 */
.customer-chat-sender-name {
  font-size: 12px;
  color: #666666;
  text-align: right;
  padding-right: 8px;
}

.team-chat-sender-name {
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  text-align: right;
  padding-right: 8px;
}

.system-chat-sender-name {
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  text-align: right;
  padding-right: 8px;
}

/* 用户消息通用样式 */
.base-chat-message-row.user {
  justify-content: flex-end;
}

.base-chat-message-container.user {
  align-items: flex-end;
}

.base-chat-message-wrapper.user {
  flex-direction: row;
}

.base-chat-message-bubble.user {
  background-color: #386591;
  color: #FFFFFF;
  border-radius: 8px 0px 8px 8px !important;
}

/* 非管理员用户的消息气泡背景色 */
.base-chat-message-bubble.user.non-admin {
  background-color: #419E7B;
}

/* 输入框区域通用样式 */
.base-chat-input-container {
  background-color: white;
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 10;
}

.base-chat-input-toolbar {
  display: flex;
  align-items: flex-end;
  background-color: #FFFFFF;
  border-radius: 20px;
  gap: 8px;
  padding: 8px 12px 24px;
  min-height: 36px; /* 確保容器最小高度 */
}

.base-chat-input-toolbar.expanded {
  padding-bottom: 8px;
}

.base-chat-toolbar-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.base-chat-toolbar-icon img {
  width: 20px;
  height: 20px;
}

.base-chat-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 15px;
  padding: 8px 12px; /* 移除padding，讓內部元素控制 */
  border: 1px solid #EEEEEE;
  min-height: 36px; /* 最小高度36px */
  border-radius: 8px;
  resize: none; /* 禁用手動調整大小 */
  box-sizing: border-box;
}

.base-chat-input .adm-text-area {
  border: none;
  background: transparent;
  padding: 8px 12px; /* 將padding移到這裡 */
  min-height: 36px;
}

.base-chat-input .adm-text-area-element {
  min-height: 20px !important;
  height: 20px; /* 單行高度20px */
  line-height: 20px;
  font-size: 15px;
  padding: 0;
  border: none;
  background: transparent;
  resize: none;
  box-sizing: border-box;
  overflow-y: auto; /* 超過最大高度時顯示滾動條 */
}

/* 任务栏样式 */
.base-tasks-container {
  background-color: var(--chat-background-color);
  padding: 8px;
  position: relative;
  z-index: 5;
}

/* 非管理员用户的任务容器背景色 */
.base-tasks-container.non-admin {
  background-color: #E6FBF3;
}

.base-tasks-content {
  display: flex;
  align-items: center;
}

.base-tasks-icon {
  margin-right: 8px;
}

.base-tasks-text {
  flex: 1;
  font-size: 14px;
  color: #333333;
}

.base-tasks-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 团队选择器样式 */
.base-team-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #E4F4FD80;
  padding: 0 8px;
}

/* 非管理员用户的团队选择器背景色 */
.base-team-selector.non-admin {
  background-color: #E6FBF380;
}

.base-team-button {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.base-team-text {
  font-size: 14px;
  color: #333333;
}

.base-team-badge {
  background-color: #FF3141;
  color: #FFFFFF;
  font-size: 9px;
  border-radius: 100px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  padding: 0 4px;
}

.base-team-avatar {
  width: 28px;
  height: 28px;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #00B578;
}

.base-team-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.base-team-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: #00B578;
  border-radius: 50%;
  border: 2px solid white;
}

/* 团队聊天室样式 */
.team-member-count {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.team-chat-room-title-center {
  flex: 1;
  font-size: 17px;
  font-weight: 600;
  color: #333333;
}

/* 扩展工具栏样式 */
.expanded-toolbar {
  background-color: white;
  padding: 12px 20px 24px;
}

.expanded-toolbar-row {
  display: flex;
  gap: 37px;
  padding-bottom: 12px;
}

.expanded-toolbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 56px;
  cursor: pointer;
}

.expanded-toolbar-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  background-color: #EAF1F5;
  border-radius: 8px;
}

.expanded-toolbar-icon {
  width: 24px;
  height: 24px;
  fill: #386591;
}

.expanded-toolbar-text {
  font-size: 13px;
  color: #333333;
  text-align: center;
  height: 18px;
}

/* 文件附件样式 */
.file-attachment {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 150px;
}

.file-icon {
  font-size: 24px;
  line-height: 1;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-weight: 500;
  margin-bottom: 2px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  opacity: 0.7;
}

/* 为不同类型的文件定义不同的图标 */
.file-attachment[data-file-type^="application/pdf"] .file-icon {
  content: "📄";
}

.file-attachment[data-file-type^="application/msword"] .file-icon,
.file-attachment[data-file-type^="application/vnd.openxmlformats-officedocument.wordprocessingml.document"] .file-icon {
  content: "📝";
}

.file-attachment[data-file-type^="application/vnd.ms-excel"] .file-icon,
.file-attachment[data-file-type^="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"] .file-icon {
  content: "📊";
}

.file-attachment[data-file-type^="text/"] .file-icon {
  content: "📃";
}

.file-attachment[data-file-type^="video/"] .file-icon {
  content: "🎬";
}

.file-attachment[data-file-type^="audio/"] .file-icon {
  content: "🎵";
} 

/* 系統消息樣式 - 與日期標題樣式一致 */
.base-system-message {
  display: flex;
  justify-content: center;
  padding: 8px 0;
  margin: 0 auto;
}

.base-system-message .base-chat-date-header {
  padding: 0;
  margin: 0;
}

.base-system-message .base-chat-date-header span {
  background-color: rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
  line-height: 1.2;
}

/* 讓系統消息間距更緊湊 */
.base-system-message + .base-chat-message-row,
.base-chat-message-row + .base-system-message {
  margin-top: 6px;
}

/* 創建一些空間在系統消息之間，如果它們連續出現 */
.base-system-message + .base-system-message {
  margin-top: 2px;
} 

/* .chat-avatar-img{
  width: unset !important;
  height: unset !important;
}  */

/* 僅針對 ChatRoom 頁面下的 avatar-fallback 設置字體大小 */
.customer-chat-avatar .avatar-fallback {
  font-size: 13px !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 聊天室空状态容器 - 完美居中显示 */
.chat-empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 40px 20px;
}

/* 确保ErrorBlock在空状态容器中不会被拉伸 */
.chat-empty-container .adm-error-block {
  flex-shrink: 0;
  max-width: 280px;
  text-align: center;
}

/* 调整ErrorBlock内部元素的样式 */
.chat-empty-container .adm-error-block .adm-error-block-image {
  margin-bottom: 16px;
}

.chat-empty-container .adm-error-block .adm-error-block-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 8px;
}

.chat-empty-container .adm-error-block .adm-error-block-description {
  font-size: 14px;
  color: #ccc;
}

/* 聊天室骨架屏样式 */
.chat-skeleton-container {
  padding: 16px;
  background-color: #E4F4FD;
}

.message-skeleton {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.message-skeleton-right {
  flex-direction: row-reverse;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-content {
  flex: 1;
  max-width: 60%;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-skeleton-right .skeleton-content {
  background-color: #1677FF;
}

.skeleton-line {
  height: 14px;
  border-radius: 7px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 6px;
}

.message-skeleton-right .skeleton-line {
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0.3) 75%);
  background-size: 200% 100%;
}

.skeleton-line-short {
  width: 60%;
}

.skeleton-line-medium {
  width: 80%;
}

.skeleton-line-long {
  width: 100%;
}

.skeleton-line:last-child {
  margin-bottom: 0;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 聊天室错误状态样式 */
.chat-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: #ff4d4f;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

/* 自定義 Modal 樣式覆蓋 */
:global(.adm-modal-title) {
  font-size: 18px !important;
  font-weight: 600 !important;
  margin-top: 12px !important;
  margin-bottom: 0 !important;
  padding: 0 20px !important;
}

:global(.adm-modal-body) {
  padding-top: 20px !important;
  padding-bottom: 24px !important;
}

/* 水平排列的 Modal 按鈕樣式 */
.horizontal-modal-actions {
  display: flex;
  justify-content: space-between;
  border-top: 0.5px solid #EEEEEE;
}

.horizontal-modal-btn {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  font-size: 16px;
  transition: background-color 0.3s;
  font-weight: 500;
}

.horizontal-modal-btn-cancel {
  color: #1677FF;
  font-size: 18px;
  font-family: SF Pro;
  font-weight: 400;
  line-height: 140%;
  letter-spacing: 0%;
  text-align: center;
  border-right: 0.5px solid #EEEEEE;
}

.horizontal-modal-btn-confirm {
  color: #FF3141;
  font-size: 18px;
  font-family: SF Pro;
  font-weight: 400;
  line-height: 140%;
  letter-spacing: 0%;
  text-align: center;
}

.adm-modal-content{
  /* border-top: 0.5px solid #EEEEEE; */
  padding: 0%;
}

.adm-modal-title{
  margin-bottom: 20px !important;
  font-family: SF Pro;
  font-weight: 400;
  font-style: Regular;
  font-size: 15px;
  line-height: 140%;
  letter-spacing: 0%;
  text-align: center;
  color: #333333;
}

.adm-modal-footer {
  display: none !important;
}

/* URL 連結樣式 */
.base-chat-message-bubble a {
  color: var(--link-color);
  text-decoration: underline;
  word-break: break-all;
  transition: color 0.2s ease;
}

.base-chat-message-bubble a:hover {
  color: var(--link-hover-color);
  text-decoration: underline;
}

.base-chat-message-bubble a:visited {
  color: var(--link-color);
}

/* 確保連結在不同聊天室類型中都有正確的樣式 */
.my-chat-message-bubble a,
.team-chat-message-bubble a,
.customer-chat-message-bubble a,
.system-chat-message-bubble a {
  color: var(--link-color);
  text-decoration: underline;
  word-break: break-all;
  transition: color 0.2s ease;
}

.my-chat-message-bubble a:hover,
.team-chat-message-bubble a:hover,
.customer-chat-message-bubble a:hover,
.system-chat-message-bubble a:hover {
  color: var(--link-hover-color);
}

/* 用戶消息氣泡中的連結樣式（白色文字背景） */
.base-chat-message-bubble.user a {
  color: #ffffff;
  text-decoration: underline;
}

.base-chat-message-bubble.user a:hover {
  color: #e6f7ff;
}

.base-chat-message-bubble.user a:visited {
  color: #ffffff;
}