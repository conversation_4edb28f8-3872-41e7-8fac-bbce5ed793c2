/**
 * 通用类型定义
 */

// 基础实体类型
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

// 可选字段类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// 必需字段类型
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// 深度可选类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 键值对类型
export type KeyValuePair<T = any> = {
  key: string;
  value: T;
};

// 选项类型
export interface Option<T = any> {
  label: string;
  value: T;
  disabled?: boolean;
  icon?: string;
}

// 文件类型
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  thumbnail?: string;
  uploadedAt: Date;
}

// 地理位置类型
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

// 时间范围类型
export interface TimeRange {
  start: Date;
  end: Date;
}

// 排序类型
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// 过滤类型
export interface FilterConfig {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in';
  value: any;
}

// 搜索配置类型
export interface SearchConfig {
  query: string;
  fields?: string[];
  filters?: FilterConfig[];
  sort?: SortConfig[];
  page?: number;
  pageSize?: number;
}

// 结果类型
export type Result<T, E = Error> = {
  success: true;
  data: T;
} | {
  success: false;
  error: E;
};

// 异步结果类型
export type AsyncResult<T, E = Error> = Promise<Result<T, E>>;
