/**
 * Aile Capacitor Line Login 模擬模塊
 * 用於開發環境，提供與真實插件相同的接口但使用模擬數據
 */

export interface LineLoginConfig {
  channelId: string;
  universalLinkURL?: string;
  redirectUri?: string;
  scope?: string[];
  botPrompt?: string;
  debug?: boolean;
}

export interface LoginOptions {
  onlyWebLogin?: boolean;
  botPrompt?: 'normal' | 'aggressive';
  scopes?: string[];
}

export interface UserProfile {
  userId: string;
  displayName: string;
  pictureUrl?: string;
  statusMessage?: string;
  language?: string;
}

export interface TokenResult {
  accessToken: string;
  expiresIn: number;
  refreshToken: string;
  tokenType: string;
}

export interface LoginResult extends TokenResult {
  scope: string;
  userProfile?: UserProfile;
}

// 模擬的 LINE Login 實例
export const LineLogin = {
  initialize: async (options: LineLoginConfig): Promise<void> => {
    console.log('[模擬] LINE Login 初始化', options);
    return Promise.resolve();
  },

  login: async (options?: LoginOptions): Promise<LoginResult> => {
    console.log('[模擬] LINE Login 登入', options);
    // 返回模擬的登入結果
    return {
      accessToken: 'mock-access-token-12345',
      expiresIn: 3600,
      refreshToken: 'mock-refresh-token-67890',
      tokenType: 'Bearer',
      scope: 'profile openid',
      userProfile: {
        userId: 'mock-user-123',
        displayName: '模擬用戶',
        pictureUrl: 'https://example.com/mock-avatar.jpg',
        statusMessage: '這是一個模擬用戶',
        language: 'zh-TW'
      }
    };
  },

  getUserProfile: async (): Promise<UserProfile> => {
    console.log('[模擬] 獲取 LINE 用戶資料');
    return {
      userId: 'mock-user-123',
      displayName: '模擬用戶',
      pictureUrl: 'https://example.com/mock-avatar.jpg',
      statusMessage: '這是一個模擬用戶',
      language: 'zh-TW'
    };
  },

  isLoggedIn: async (): Promise<{ isLoggedIn: boolean }> => {
    console.log('[模擬] 檢查 LINE 登入狀態');
    return { isLoggedIn: true };
  },

  logout: async (): Promise<void> => {
    console.log('[模擬] LINE 登出');
    return Promise.resolve();
  },

  refreshToken: async (): Promise<TokenResult> => {
    console.log('[模擬] 刷新 LINE 令牌');
    return {
      accessToken: 'mock-access-token-refresh-12345',
      expiresIn: 3600,
      refreshToken: 'mock-refresh-token-refresh-67890',
      tokenType: 'Bearer'
    };
  },

  echo: async (options: { value: string }): Promise<{ value: string }> => {
    console.log('[模擬] LINE Echo', options);
    return { value: options.value };
  }
};

// 模擬的輔助函數
export const LineLoginHelpers = {
  getCurrentPlatform: (): 'web' | 'ios' | 'android' => {
    return 'web';
  },
  isPlatformSupported: (): boolean => {
    return true;
  },
  isWebPlatform: (): boolean => {
    return true;
  },
  isNativePlatform: (): boolean => {
    return false;
  }
}; 