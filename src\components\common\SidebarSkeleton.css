/* 侧边栏骨架屏样式 */
.sidebar-skeleton {
  padding: 16px;
  background: #fff;
  height: 100%;
}

/* 用户信息骨架 */
.sidebar-skeleton-user {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 12px;
}

.sidebar-skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.sidebar-skeleton-username {
  height: 16px;
  width: 80px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 团队标题骨架 */
.sidebar-skeleton-title {
  height: 18px;
  width: 60px;
  border-radius: 4px;
  margin-bottom: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 团队列表骨架 */
.sidebar-skeleton-teams {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-skeleton-team-item {
  display: flex;
  align-items: center;
  padding: 12px;
  gap: 12px;
  border-radius: 8px;
  background: #fafafa;
}

/* 团队图标骨架（用于加入团队按钮） */
.sidebar-skeleton-team-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 团队头像骨架 */
.sidebar-skeleton-team-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 团队内容骨架 */
.sidebar-skeleton-team-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-skeleton-team-name {
  height: 16px;
  width: 120px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.sidebar-skeleton-team-text {
  height: 16px;
  width: 100px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 团队操作按钮骨架 */
.sidebar-skeleton-team-action {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-skeleton {
    padding: 12px;
  }
  
  .sidebar-skeleton-team-item {
    padding: 8px;
  }
}
