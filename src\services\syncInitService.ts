
import { logService } from './system/logService';

import aileDBService from './db/aileDBService';
// 避免循环依赖，使用延迟获取 store
// import { store } from '../app/store';
import { fetchAndSetTenants } from '../app/slices/tenantSlice';

// 延迟获取 store 以避免循环依赖
function getStore() {
  return (window as any).__STORE__;
}

/**
 * 同步初始化服務
 * 專注於數據同步和非關鍵的異步初始化任務
 */
class SyncInitService {
  private static instance: SyncInitService;
  private onForceLogout?: () => void;
  private isSyncing: boolean = false;
  private initialized: boolean = false;

  /**
   * 註冊強制登出回調，由 UI 層調用 dispatch(logout())
   */
  public registerForceLogoutCallback(cb: () => void) {
    this.onForceLogout = cb;
  }

  /**
   * 同步租戶資料到本地資料庫
   * @returns 同步的租戶數量
   */
  public async syncTenantsToDb(): Promise<number> {
    try {
      logService.info('開始同步租戶資料到本地資料庫');
      
      // 使用 Redux thunk 同步租戶數據
      const store = getStore();
      if (!store) {
        throw new Error('Store not available');
      }
      const resultAction = await store.dispatch(fetchAndSetTenants({}));
      
      if (fetchAndSetTenants.fulfilled.match(resultAction)) {
        const syncedCount = resultAction.payload?.length || 0;
        logService.info('租戶資料同步完成', { syncedCount });
        return syncedCount;
      } else {
        logService.warn('通過 Redux 同步租戶資料失敗', { 
          error: resultAction.error || '未知錯誤' 
        });
        return 0;
      }
    } catch (error) {
      logService.error('同步租戶資料失敗', { error });
      return 0;
    }
  }
  

  /**
   * 持久化數據庫到存儲
   */
  public async persistDatabase(): Promise<boolean> {
    try {
      const dbName = aileDBService.getDbName();
      if (!dbName) {
        logService.warn('未找到活躍的數據庫，跳過持久化');
        return false;
      }
      
      // 使用重試機制保存數據庫
      await this.saveToStoreWithRetry();
      return true;
    } catch (error) {
      logService.error('持久化數據庫失敗', { error });
      return false;
    }
  }

  /**
   * 帶重試邏輯的持久化數據保存
   * @private
   */
  private async saveToStoreWithRetry(retries = 2): Promise<void> {
    let lastError;
    
    for (let i = 0; i <= retries; i++) {
      try {
        if (i > 0) {
          logService.info(`嘗試第${i}次重新保存數據庫到持久存儲`);
          // 重試前短暫延遲
          await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        await aileDBService.saveToStore();
        logService.info('數據已持久化到存儲');
        return; // 成功保存，退出函數
      } catch (error) {
        lastError = error;
        logService.warn(`持久化數據失敗，嘗試重試 (${i}/${retries})`, { error });
      }
    }
    
    // 所有重試都失敗，拋出最後一個錯誤
    throw lastError;
  }

  /**
   * 檢查數據庫是否已初始化
   * 如果沒有初始化，則不執行數據同步
   */
  private isDbInitialized(): boolean {
    const isInitialized = aileDBService.isInitialized();
    if (!isInitialized) {
      logService.warn('數據庫未初始化，跳過數據同步');
    }
    return isInitialized;
  }

  /**
   * App 啟動或登入後的同步初始化入口
   * 負責同步租戶資料、客戶資料等必要數據
   * @param options 初始化選項
   */
  public async init(_options?: { skipEssential?: boolean }): Promise<void> {
    // 防止重複調用
    if (this.isSyncing) {
      logService.warn('同步初始化已在進行中，忽略重複調用');
      return Promise.resolve();
    }
    
    this.isSyncing = true;
    logService.info('開始執行同步初始化任務');
    
    try {
      // 檢查數據庫是否已初始化
      if (!this.isDbInitialized()) {
        this.isSyncing = false;
        return Promise.resolve();
      }

      
      // 5. 完成初始化
      this.initialized = true;
     
    } catch (error) {
      logService.error('一次性初始化任務失敗', { error });
    }
  }

  /**
   * 檢查是否已經初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 執行強制登出
   */
  public executeForceLogout() {
    if (this.onForceLogout) {
      logService.warn('執行強制登出', {
        stack: new Error().stack  // 記錄調用堆疊
      });
      this.onForceLogout();
    } else {
      logService.warn('嘗試強制登出但未設置回調');
    }
  }

  public static getInstance(): SyncInitService {
    if (!SyncInitService.instance) {
      SyncInitService.instance = new SyncInitService();
    }
    return SyncInitService.instance;
  }
}

const syncInitService = SyncInitService.getInstance();
export default syncInitService; 