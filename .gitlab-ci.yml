# .gitlab-ci.yml
# 適用於 React + Capacitor + Fastlane 的 GitLab CI/CD 配置
image: node:22.10.0
# 定義流程階段
stages:
    - build
    - deploy

# --- 變數定義 ---
variables:
    LC_ALL: "en_US.UTF-8"
    LANG: "en_US.UTF-8"
    # 設置 iOS 構建所需變數 - 使用預設環境變數
    FASTLANE_USER: ${APPLE_ID}
    FASTLANE_PASSWORD: ${APPLE_APP_SPECIFIC_PASSWORD}
    DEVELOPER_TEAM_ID: ${DEVELOPER_TEAM_ID}
    APP_IDENTIFIER: "cloud.aile.aile"
    
# --- 模板定義 ---
# 用於 Android 構建作業的模板，避免重複配置
.android-build-template: &android-build-rules
    image: fabernovel/android:api-34-v1.8.0
    tags:
        - saas-linux-medium-amd64
    cache:
        key: ${CI_COMMIT_REF_SLUG}
        paths:
            - node_modules/
            - android/.gradle/
        policy: pull-push
    before_script:
        # Install Node.js
        - apt-get update -qq && apt-get install -y -qq curl gnupg
        - curl -sL https://deb.nodesource.com/setup_20.x | bash -
        - apt-get install -y -qq nodejs
        # Install Ruby dependencies (Fastlane)
        - bundle check --path vendor/bundle || bundle install --path vendor/bundle --jobs $(nproc)
        # Setup Gradle and Java
        - export GRADLE_USER_HOME=`pwd`/.gradle
        # 使用 Java 21 而非 Java 17
        - apt-get install -y -qq openjdk-21-jdk
        - export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
        - java -version
        # Setup GCP service account key if provided
        - if [ -n "$AILE_SERVICE_KEY_BASE64" ]; then echo "$AILE_SERVICE_KEY_BASE64" | base64 -d > ./aile-service-key.json && export AILE_SERVICE_KEY=./aile-service-key.json; fi

# 用於 iOS 構建作業的模板
.ios-build-template: &ios-build-rules
    image: ghcr.io/cirruslabs/macos-sequoia-xcode:16.0
    tags:
        - macos
    variables:
        SWIFT_OPTIMIZATION_LEVEL: "-Onone"
    cache:
        key: ${CI_COMMIT_REF_SLUG}-ios
        paths:
            - node_modules/
            - ios/App/Pods/
            - vendor/bundle/
        policy: pull-push
    before_script:
        # 設置環境
        - export LANG=en_US.UTF-8
        - export LC_ALL=en_US.UTF-8
        # 設置證書和團隊ID
        - export DEVELOPER_TEAM_ID=${DEVELOPER_TEAM_ID}
        - export IOS_CERTIFICATE_BASE64=${IOS_CERTIFICATE_BASE64}
        - export IOS_CERTIFICATE_PASSWORD=${IOS_CERTIFICATE_PASSWORD}
        - export PROVISIONING_PROFILE_BASE64=${PROVISIONING_PROFILE_BASE64}
        - export APPLE_ID=${APPLE_ID}
        - export APP_IDENTIFIER="cloud.aile.aile"
        # 安裝 Ruby 依賴 (Fastlane)
        - bundle config set path 'vendor/bundle'
        - bundle check || bundle install --jobs 4
        # 確保環境變數優先級
        - export SWIFT_OPTIMIZATION_LEVEL="-Onone"
 
build-android-debug:
    stage: build
    <<: *android-build-rules
    script:
        - npm install --no-audit --no-fund --legacy-peer-deps
        - npm run build
        - npx cap sync android
        - bundle exec fastlane build_debug
    artifacts:
        paths:
            - android/app/build/outputs/apk/debug/app-debug.apk
        expire_in: 1 week
    rules:
        - if: '$CI_COMMIT_BRANCH == "dev"'

# 簡化的iOS構建和部署流程 - 合併構建和上傳到一個任務
build-and-deploy-ios:
    stage: build
    <<: *ios-build-rules
    script:
        # 開啟詳細日誌以便調試，並強制清理建置快取
        - export FL_LOG_LEVEL="verbose"
        - export GYM_CLEAN=true
        - npm install --no-audit --no-fund --legacy-peer-deps
        - npm run build
        - npx cap sync ios
        - cd ios/App
        # 積極地清理舊的 Pods 設置，避免 CI 快取問題
        # - bundle exec pod deintegrate
        # - rm -rf Pods Podfile.lock ../build
        # - bundle exec pod cache clean --all
        # - echo "Re-installing pods with repo update..."
        # - bundle exec pod install --repo-update
        # # 授權 Pods 腳本執行權限
        # - find . -name "*.sh" -path "*/Pods/*" -exec chmod +x {} \; || true
        # 移除手動的 pod deintegrate/install
        # - sed -i '' 's/ENABLE_USER_SCRIPT_SANDBOXING = YES;/ENABLE_USER_SCRIPT_SANDBOXING = NO;/g' App.xcodeproj/project.pbxproj
        - cd ../..
        # 繼續使用 fastlane，並傳遞參數跳過 pod install
        - bundle exec fastlane ios build_and_upload
    artifacts:
        paths:
            - ios/build/App.ipa
            - ios/build/logs/
        when: always
        expire_in: 1 week
    rules:
        - if: '$CI_COMMIT_BRANCH == "dev"'