
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import TasksPanel, { ITask } from './TasksPanel';

describe('TasksPanel Component', () => {
  const mockTasks: ITask[] = [
    {
      id: '1',
      title: '任務標題任務標題任務標題任務標題任務標題任務標題',
      dueTime: '2小時',
      isUrgent: true
    },
    {
      id: '2',
      title: '任務標題任務標題任務標題任務標題任務標題任務標題',
      dueTime: '4天',
      isUrgent: false
    },
    {
      id: '3',
      title: '任務標題任務標題任務',
      dueTime: '7天',
      isUrgent: false
    }
  ];

  const mockOnTaskClick = jest.fn();

  beforeEach(() => {
    mockOnTaskClick.mockClear();
  });

  test('renders tasks header correctly', () => {
    render(<TasksPanel tasks={mockTasks} />);
    
    expect(screen.getByText('客戶相關任務 (3)')).toBeInTheDocument();
  });

  test('expands task list when header is clicked', () => {
    render(<TasksPanel tasks={mockTasks} />);
    
    // 初始状态下任务列表应该是折叠的
    expect(screen.queryByText('查看更多任務')).not.toBeInTheDocument();
    
    // 点击标题展开任务列表
    fireEvent.click(screen.getByText('客戶相關任務 (3)'));
    
    // 检查覆盖层是否存在
    expect(screen.getByTestId('tasks-list-overlay')).toBeInTheDocument();
    
    // 展开后应该能看到任务列表和"查看更多任務"按钮
    expect(screen.getByText('查看更多任務')).toBeInTheDocument();
    expect(screen.getByText('任務標題任務標題任務標題任務標題任務標題任務標題')).toBeInTheDocument();
    expect(screen.getByText('2小時')).toBeInTheDocument();
  });

  test('calls onTaskClick when a task is clicked', () => {
    render(<TasksPanel tasks={mockTasks} onTaskClick={mockOnTaskClick} />);
    
    // 点击标题展开任务列表
    fireEvent.click(screen.getByText('客戶相關任務 (3)'));
    
    // 点击第一个任务
    fireEvent.click(screen.getByText('任務標題任務標題任務標題任務標題任務標題任務標題'));
    
    // 检查回调函数是否被调用
    expect(mockOnTaskClick).toHaveBeenCalledWith('1');
  });

  test('renders urgent tasks with correct color', () => {
    render(<TasksPanel tasks={mockTasks} />);
    
    // 点击标题展开任务列表
    fireEvent.click(screen.getByText('客戶相關任務 (3)'));
    
    // 获取第一个任务标题元素（紧急任务）
    const urgentTaskTitle = screen.getByText('任務標題任務標題任務標題任務標題任務標題任務標題');
    
    // 检查紧急任务的标题和时间颜色是否正确
    expect(urgentTaskTitle).toHaveStyle('color: #FF3141');
    expect(screen.getByText('2小時')).toHaveStyle('color: #FF3141');
  });
}); 