import { messageService } from '../services/core/chat/messageService';
import { store } from '../app/store';
import { detectAndFixMissingSequences } from '../app/slices/messageSlice';

/**
 * 消息序列号修复工具
 * 用于检测和修复聊天室中缺失的消息序列号
 */
export class MessageSequenceRepair {
  
  /**
   * 检测指定聊天室的消息序列号缺失情况
   * @param roomId 聊天室ID
   * @param expectedLastSequence 期望的最后序列号
   * @returns 检测结果
   */
  public static async detectMissingSequences(
    roomId: string, 
    expectedLastSequence: number
  ): Promise<{
    isComplete: boolean;
    missingSequences: number[];
    missingRanges: Array<{start: number, end: number}>;
    dbSequenceRange: {min: number, max: number} | null;
    totalMissing: number;
    repairPlan: string[];
  }> {
    try {
      console.log(`🔍 开始检测房间 ${roomId} 的消息序列号缺失情况`);
      console.log(`   期望最后序列号: ${expectedLastSequence}`);

      const result = await messageService.detectMissingSequences(roomId, expectedLastSequence);
      
      // 生成修复计划
      const repairPlan: string[] = [];
      if (result.totalMissing > 0) {
        repairPlan.push(`检测到 ${result.totalMissing} 个缺失的序列号`);
        repairPlan.push(`缺失范围: ${result.missingRanges.map(r => `${r.start}-${r.end}`).join(', ')}`);
        repairPlan.push(`建议分 ${result.missingRanges.length} 批次从API获取缺失消息`);
        
        result.missingRanges.forEach((range, index) => {
          const count = range.end - range.start + 1;
          repairPlan.push(`  批次${index + 1}: 获取序列号 ${range.start}-${range.end} (${count}条消息)`);
        });
      } else {
        repairPlan.push('消息序列号完整，无需修复');
      }

      console.log('📊 检测结果:');
      console.log(`   数据完整性: ${result.isComplete ? '完整' : '有缺失'}`);
      console.log(`   数据库序列号范围: ${result.dbSequenceRange ? `${result.dbSequenceRange.min}-${result.dbSequenceRange.max}` : '无数据'}`);
      console.log(`   缺失消息数量: ${result.totalMissing}`);
      console.log(`   修复计划:`);
      repairPlan.forEach(plan => console.log(`     ${plan}`));

      return {
        ...result,
        repairPlan
      };

    } catch (error) {
      console.error('❌ 检测消息序列号缺失失败:', error);
      throw error;
    }
  }

  /**
   * 自动修复指定聊天室的消息序列号缺失
   * @param roomId 聊天室ID
   * @param expectedLastSequence 期望的最后序列号
   * @param options 修复选项
   */
  public static async repairMissingSequences(
    roomId: string,
    expectedLastSequence: number,
    options: {
      dryRun?: boolean; // 是否只是预演，不实际执行
      batchDelay?: number; // 批次间延迟时间(ms)
      maxBatches?: number; // 最大批次数限制
    } = {}
  ): Promise<{
    success: boolean;
    message: string;
    repairedCount: number;
    skippedCount: number;
  }> {
    try {
      const { dryRun = false, batchDelay = 300, maxBatches = 10 } = options;

      console.log(`🔧 开始修复房间 ${roomId} 的消息序列号缺失`);
      console.log(`   模式: ${dryRun ? '预演模式' : '实际修复'}`);
      console.log(`   批次延迟: ${batchDelay}ms`);
      console.log(`   最大批次: ${maxBatches}`);

      // 先检测缺失情况
      const detection = await this.detectMissingSequences(roomId, expectedLastSequence);
      
      if (detection.isComplete) {
        return {
          success: true,
          message: '消息序列号完整，无需修复',
          repairedCount: 0,
          skippedCount: 0
        };
      }

      if (detection.missingRanges.length > maxBatches) {
        return {
          success: false,
          message: `缺失范围过多(${detection.missingRanges.length}批次)，超过最大限制(${maxBatches}批次)`,
          repairedCount: 0,
          skippedCount: detection.totalMissing
        };
      }

      if (dryRun) {
        console.log('🎭 预演模式 - 不会实际执行API调用');
        detection.repairPlan.forEach(plan => console.log(`   ${plan}`));
        return {
          success: true,
          message: `预演完成，计划修复 ${detection.totalMissing} 个缺失序列号`,
          repairedCount: 0,
          skippedCount: 0
        };
      }

      // 实际执行修复
      console.log('🚀 开始实际修复...');
      store.dispatch(detectAndFixMissingSequences(roomId, expectedLastSequence));

      return {
        success: true,
        message: `修复任务已启动，将分 ${detection.missingRanges.length} 批次获取 ${detection.totalMissing} 个缺失序列号`,
        repairedCount: detection.totalMissing,
        skippedCount: 0
      };

    } catch (error) {
      console.error('❌ 修复消息序列号缺失失败:', error);
      return {
        success: false,
        message: `修复失败: ${error instanceof Error ? error.message : '未知错误'}`,
        repairedCount: 0,
        skippedCount: 0
      };
    }
  }

  /**
   * 批量检测多个聊天室的消息完整性
   * @param roomConfigs 聊天室配置数组
   */
  public static async batchDetectMissingSequences(
    roomConfigs: Array<{roomId: string, expectedLastSequence: number}>
  ): Promise<Array<{
    roomId: string;
    isComplete: boolean;
    totalMissing: number;
    summary: string;
  }>> {
    console.log(`🔍 开始批量检测 ${roomConfigs.length} 个聊天室的消息完整性`);
    
    const results = [];
    
    for (let i = 0; i < roomConfigs.length; i++) {
      const { roomId, expectedLastSequence } = roomConfigs[i];
      
      try {
        console.log(`\n📋 检测进度: ${i + 1}/${roomConfigs.length} - 房间 ${roomId}`);
        
        const detection = await this.detectMissingSequences(roomId, expectedLastSequence);
        
        const summary = detection.isComplete 
          ? '完整' 
          : `缺失${detection.totalMissing}条消息，范围: ${detection.missingRanges.map(r => `${r.start}-${r.end}`).join(', ')}`;
        
        results.push({
          roomId,
          isComplete: detection.isComplete,
          totalMissing: detection.totalMissing,
          summary
        });
        
        // 避免API压力，每次检测后稍作延迟
        if (i < roomConfigs.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
      } catch (error) {
        console.error(`❌ 检测房间 ${roomId} 失败:`, error);
        results.push({
          roomId,
          isComplete: false,
          totalMissing: 0,
          summary: `检测失败: ${error instanceof Error ? error.message : '未知错误'}`
        });
      }
    }
    
    // 输出汇总报告
    console.log('\n📊 批量检测汇总报告:');
    console.log('='.repeat(60));
    
    const completeRooms = results.filter(r => r.isComplete);
    const incompleteRooms = results.filter(r => !r.isComplete);
    const totalMissing = incompleteRooms.reduce((sum, r) => sum + r.totalMissing, 0);
    
    console.log(`总检测房间数: ${results.length}`);
    console.log(`完整房间数: ${completeRooms.length}`);
    console.log(`有缺失房间数: ${incompleteRooms.length}`);
    console.log(`总缺失消息数: ${totalMissing}`);
    
    if (incompleteRooms.length > 0) {
      console.log('\n❌ 有缺失的房间:');
      incompleteRooms.forEach(room => {
        console.log(`   ${room.roomId}: ${room.summary}`);
      });
    }
    
    return results;
  }

  /**
   * 显示使用帮助
   */
  public static showHelp(): void {
    console.log(`
🔧 消息序列号修复工具使用指南
=================================

1. 检测单个聊天室:
   MessageSequenceRepair.detectMissingSequences('roomId', 100)

2. 修复单个聊天室(预演):
   MessageSequenceRepair.repairMissingSequences('roomId', 100, { dryRun: true })

3. 修复单个聊天室(实际执行):
   MessageSequenceRepair.repairMissingSequences('roomId', 100)

4. 批量检测多个聊天室:
   MessageSequenceRepair.batchDetectMissingSequences([
     { roomId: 'room1', expectedLastSequence: 100 },
     { roomId: 'room2', expectedLastSequence: 200 }
   ])

5. 显示帮助:
   MessageSequenceRepair.showHelp()

注意事项:
- 修复操作会调用API获取缺失消息，请确保网络连接正常
- 建议先使用预演模式(dryRun: true)查看修复计划
- 大量缺失消息的修复可能需要较长时间
- 修复过程中会有延迟以避免API压力
    `);
  }
}

// 导出便捷方法
export const detectMissingSequences = MessageSequenceRepair.detectMissingSequences;
export const repairMissingSequences = MessageSequenceRepair.repairMissingSequences;
export const batchDetectMissingSequences = MessageSequenceRepair.batchDetectMissingSequences;

// 在开发环境下将工具挂载到全局对象，方便调试
if (process.env.NODE_ENV === 'development') {
  (window as any).MessageSequenceRepair = MessageSequenceRepair;
  console.log('🔧 MessageSequenceRepair 工具已挂载到 window.MessageSequenceRepair');
  console.log('   使用 MessageSequenceRepair.showHelp() 查看使用指南');
}
