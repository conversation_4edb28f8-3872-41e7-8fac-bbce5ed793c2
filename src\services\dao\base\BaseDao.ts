/**
 * @file BaseDao.ts
 * @description 基礎 DAO 類，提供 tenantId 隔離和通用數據庫操作
 * <AUTHOR> Team
 */

import { store } from '../../../app/store';
import { logService } from '../../system/logService';

/**
 * 基礎 DAO 抽象類
 * 提供 tenantId 隔離、數據驗證等通用功能
 */
export abstract class BaseDao {
  
  /**
   * 獲取當前租戶ID
   * @returns 當前租戶ID
   * @throws 如果租戶ID不存在則拋出錯誤
   */
  protected getCurrentTenantId(): string {
    const tenantId = store.getState().tenant.currentTenantId;
    if (!tenantId) {
      throw new Error('當前租戶ID不能為空');
    }
    return tenantId;
  }

  /**
   * 檢查是否需要租戶隔離
   * 租戶表本身不需要租戶隔離
   * @param tableName 表名
   * @returns 是否需要租戶隔離
   */
  protected needsTenantIsolation(tableName: string): boolean {
    const excludedTables = ['tenant', 'kvstore']; // 不需要租戶隔離的表
    return !excludedTables.includes(tableName.toLowerCase());
  }

  /**
   * 為 SQL 查詢添加租戶過濾條件
   * @param sql SQL 查詢語句
   * @param params 查詢參數
   * @param tableName 表名（用於判斷是否需要租戶隔離）
   * @returns 包含租戶過濾條件的 SQL 和參數
   */
  protected addTenantFilter(
    sql: string, 
    params: any[], 
    tableName: string
  ): { sql: string; params: any[] } {
    // 如果不需要租戶隔離，直接返回原始 SQL
    if (!this.needsTenantIsolation(tableName)) {
      return { sql, params };
    }

    const tenantId = this.getCurrentTenantId();
    
    // 檢查 SQL 是否已包含 tenantId 條件
    const sqlLower = sql.toLowerCase();
    if (sqlLower.includes('tenantid')) {
      // 已包含 tenantId 條件，不重複添加
      return { sql, params };
    }
    
    // 添加 tenantId 過濾條件
    if (!sqlLower.includes('where')) {
      sql += ' WHERE tenantId = ?';
    } else {
      sql += ' AND tenantId = ?';
    }
    params.push(tenantId);
    
    return { sql, params };
  }

  /**
   * 驗證數據的租戶ID
   * @param data 要驗證的數據
   * @param tableName 表名（用於判斷是否需要驗證）
   * @throws 如果租戶ID不匹配則拋出錯誤
   */
  protected validateTenantId(data: any, tableName: string): void {
    // 如果不需要租戶隔離，跳過驗證
    if (!this.needsTenantIsolation(tableName)) {
      return;
    }

    const currentTenantId = this.getCurrentTenantId();
    
    // 如果數據沒有 tenantId，自動設置
    if (!data.tenantId) {
      data.tenantId = currentTenantId;
      return;
    }
    
    // 如果數據有 tenantId，驗證是否匹配
    if (data.tenantId !== currentTenantId) {
      throw new Error(`數據租戶ID不匹配: ${data.tenantId} !== ${currentTenantId}`);
    }
  }

  /**
   * 批量驗證數據的租戶ID
   * @param dataList 要驗證的數據列表
   * @param tableName 表名
   */
  protected validateTenantIdBatch(dataList: any[], tableName: string): void {
    if (!this.needsTenantIsolation(tableName)) {
      return;
    }

    dataList.forEach((data, index) => {
      try {
        this.validateTenantId(data, tableName);
      } catch (error) {
        throw new Error(`批量數據第 ${index} 項租戶ID驗證失敗: ${(error as Error).message}`);
      }
    });
  }

  /**
   * 確保數據包含租戶ID
   * @param data 數據對象
   * @param tableName 表名
   * @returns 包含租戶ID的數據
   */
  protected ensureTenantId(data: any, tableName: string): any {
    if (!this.needsTenantIsolation(tableName)) {
      return data;
    }

    const currentTenantId = this.getCurrentTenantId();
    
    return {
      ...data,
      tenantId: data.tenantId || currentTenantId
    };
  }

  /**
   * 批量確保數據包含租戶ID
   * @param dataList 數據列表
   * @param tableName 表名
   * @returns 包含租戶ID的數據列表
   */
  protected ensureTenantIdBatch(dataList: any[], tableName: string): any[] {
    if (!this.needsTenantIsolation(tableName)) {
      return dataList;
    }

    const currentTenantId = this.getCurrentTenantId();
    
    return dataList.map(data => ({
      ...data,
      tenantId: data.tenantId || currentTenantId
    }));
  }

  /**
   * 記錄 DAO 操作日誌
   * @param operation 操作名稱
   * @param tableName 表名
   * @param params 操作參數
   * @param result 操作結果
   */
  protected logOperation(
    operation: string, 
    tableName: string, 
    params?: any, 
    result?: any
  ): void {
    const tenantId = this.needsTenantIsolation(tableName) 
      ? this.getCurrentTenantId() 
      : 'N/A';
      
    logService.debug(`DAO操作: ${operation}`, {
      table: tableName,
      tenantId,
      params,
      result: result ? '成功' : '失敗'
    });
  }

  /**
   * 處理 DAO 操作錯誤
   * @param operation 操作名稱
   * @param tableName 表名
   * @param error 錯誤對象
   * @param params 操作參數
   */
  protected handleError(
    operation: string, 
    tableName: string, 
    error: Error, 
    params?: any
  ): void {
    const tenantId = this.needsTenantIsolation(tableName) 
      ? this.getCurrentTenantId() 
      : 'N/A';
      
    logService.error(`DAO操作失敗: ${operation}`, {
      table: tableName,
      tenantId,
      error: error.message,
      params
    });
    
    throw error;
  }

  /**
   * 獲取安全的表名（防止 SQL 注入）
   * @param tableName 表名
   * @returns 安全的表名
   */
  protected getSafeTableName(tableName: string): string {
    // 簡單的表名驗證，只允許字母、數字和下劃線
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tableName)) {
      throw new Error(`無效的表名: ${tableName}`);
    }
    return tableName;
  }

  /**
   * 構建安全的 WHERE 條件
   * @param conditions 條件對象
   * @param tableName 表名
   * @returns WHERE 條件字符串和參數
   */
  protected buildWhereConditions(
    conditions: Record<string, any>, 
    tableName: string
  ): { whereClause: string; params: any[] } {
    const params: any[] = [];
    const clauses: string[] = [];
    
    // 添加業務條件
    Object.entries(conditions).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        clauses.push(`${key} = ?`);
        params.push(value);
      }
    });
    
    // 添加租戶隔離條件
    if (this.needsTenantIsolation(tableName)) {
      clauses.push('tenantId = ?');
      params.push(this.getCurrentTenantId());
    }
    
    const whereClause = clauses.length > 0 ? `WHERE ${clauses.join(' AND ')}` : '';
    
    return { whereClause, params };
  }
}

/**
 * 獲取必需的租戶ID（用於向後兼容）
 * @returns 當前租戶ID
 * @throws 如果租戶ID不存在則拋出錯誤
 */
export function getRequiredTenantId(): string {
  const tenantId = store.getState().tenant.currentTenantId;
  if (!tenantId) {
    throw new Error('當前租戶ID不能為空');
  }
  return tenantId;
}

/**
 * 獲取當前租戶ID（可選）
 * @returns 當前租戶ID或null
 */
export function getCurrentTenantId(): string | null {
  return store.getState().tenant.currentTenantId;
}
