import { logService } from '../../system/logService';
import type { Response, SyncChatMemberRequest, SyncChatMemberResponse, ChatMemberVO } from './roomService.types';
import { roomDao } from '../../dao';
import { IWeightRoomListRequest, IServiceRoomListRequest } from '../../../types/room.types';
import type { RoomVO } from './roomService.types';

import stateService from '../../stateService';
import snService from '../tenant/snService';
import { fetchUserItems } from '../tenant/userService';
import { getRequiredTenantId } from '../../../utils/tenantUtil';
import aileDBService from '../../db/aileDBService';
import { AileApi } from '../../api/aileApi';


export enum SessionStatus {
  AgentActive = 'AgentActive',
  RobotActive = 'RobotActive',
  AgentStop = 'AgentStop',
  RobotStop = 'RobotStop',
  DistributeActive = 'DistributeActive',
  CustomerStop = 'CustomerStop',
  Timeout = 'Timeout',
}
/**
 * 處理房間數據的業務邏輯
 * @param rooms 原始房間數據
 * @param isSingleOperation 是否為單筆操作
 * @returns 處理後的房間數據
 */
async function processRoomData(rooms: RoomVO[], isSingleOperation: boolean = false): Promise<RoomVO[]> {
  if (!rooms || rooms.length === 0) return [];

  const tenantId = getRequiredTenantId();

  try {
    // 先查詢本地所有房間的 isTop 狀態
    const ids = rooms.map(r => r.id).filter(Boolean);
    let localIsTopMap: Record<string, boolean> = {};

    if (ids.length > 0) {
      const placeholders = ids.map(() => '?').join(',');
      const sql = `SELECT id, isTop FROM room WHERE id IN (${placeholders}) AND tenantId = ?`;
      const localRooms = await aileDBService.all<{id: string, isTop: number}>(sql, [...ids, tenantId]);
      localIsTopMap = localRooms.reduce((acc, cur) => {
        acc[cur.id] = !!cur.isTop;
        return acc;
      }, {} as Record<string, boolean>);
    }

    logService.info('processRoomData API rooms', { rooms });
    logService.info('processRoomData localIsTopMap', { localIsTopMap });

    // 處理每個房間的數據
    const processedRooms = rooms.map(room => {
      const processedRoom = { ...room };
      processedRoom.tenantId = tenantId;
      // 僅當單筆操作且 isTop 明確為 true/false 時才允許覆蓋，否則一律補本地 map 狀態
      if (isSingleOperation && typeof processedRoom.isTop === 'boolean') {
        // 單筆操作，嚴格以傳入 isTop 為準
      } else if (processedRoom.id && localIsTopMap[processedRoom.id] !== undefined) {
        // 批量操作才補本地 map 狀態
        processedRoom.isTop = localIsTopMap[processedRoom.id];
      }
      return processedRoom;
    });

    return processedRooms;
  } catch (error) {
    logService.error('處理房間數據失敗', { error: error as Error, rooms });
    throw error;
  }
}

/**
 * 同步房間通用權重
 * @param params 請求參數
 */
export async function fetchRoomServiceWeightSync(params: IWeightRoomListRequest): Promise<Response> {
  try {
    const response: Response = await AileApi.fetchRoomServiceWeightSync(params);

    if (response?.data?.items?.length) {
      // 在Service層處理業務邏輯
      const processedRooms = await processRoomData(response.data.items, false);
      // 更新response中的數據為處理後的數據
      response.data.items = processedRooms;
      // 存儲到DB
      await roomDao.upsertRooms(processedRooms);
    }

    logService.info('fetchRoomCommonWeightSync success', { params, response });
    return response;
  } catch (error) {
    logService.error('fetchRoomCommonWeightSync error', { params, error });
    throw error;
  }
}

/**
 * 取得服務機器人列表
 * @param params 請求參數
 */
export async function fetchRoomServiceRobotList(params: IServiceRoomListRequest): Promise<any> {
  try {
    const response: Response = await AileApi.fetchRoomServiceRobotList(params);

    if (response?.data?.items?.length) {
      // 在Service層處理業務邏輯
      const processedRooms = await processRoomData(response.data.items, false);
      // 更新response中的數據為處理後的數據
      response.data.items = processedRooms;
      // 存儲到DB
      await roomDao.upsertRooms(processedRooms);
    }

    logService.info('fetchRoomServiceRobotList success', { params, response });
    return response;
  } catch (error) {
    logService.error('fetchRoomServiceRobotList error', { params, error });
    throw error;
  }
}

/**
 * 取得服務結束列表
 * @param params 請求參數
 */
export async function fetchRoomServiceEndList(params: IServiceRoomListRequest): Promise<any> {
  try {
    const response: Response = await AileApi.fetchRoomServiceEndList(params);

    if (response?.data?.items?.length) {
      // 在Service層處理業務邏輯
      const processedRooms = await processRoomData(response.data.items, false);
      // 更新response中的數據為處理後的數據
      response.data.items = processedRooms;
      // 存儲到DB
      await roomDao.upsertRooms(processedRooms);
    }

    logService.info('fetchRoomServiceEndList success', { params, response });
    return response;
  } catch (error) {
    logService.error('fetchRoomServiceEndList error', { params, error });
    throw error;
  }
}


/**
 * 根據 filter 與 page 查詢房間列表
 */
export async function getRoomsByFilter(filter: string, page: number, pageSize: number): Promise<RoomVO[]> {
  let result: RoomVO[] = [];
  try {
    if (filter === 'robot') {
      result = await roomDao.getRoomsByRobot(page, pageSize);
    } else if (filter === 'active') {
      result = await roomDao.getRoomsByWeight(page, pageSize);
    } else {
      result = await roomDao.getRoomsByEnd(page, pageSize);
    }
    logService.info('getRoomsByFilter success', { filter, page, pageSize, result });
    return result;
  } catch (error) {
    logService.error('查詢DB房間列表失敗', { error, filter, page });
    throw error;
  }
} 

/**
 * 從本地 DB 查詢 type 為 person、aileSystem 或 group 的聊天室
 */
export async function getBasicRoomsFromDB(): Promise<RoomVO[]> {
  
  /**
   * 先通過三個特定ID直接查詢DB，若查不到再通過API獲取數據
   * 支持的房間類型：person、aileSystem 或 group (serviceMember)
   * @returns Promise<RoomVO[]>
   */
  try {
    const loginUser = stateService.loginUser();
    const loginAccount = stateService.loginAccount();
    if (!loginUser) {
      logService.warn('getBasicRoomsFromDB: 未獲取到登入用戶資訊');
      return [];
    }

    // 收集需要查詢的ID
    const roomIds: string[] = [];
    if (loginAccount?.personRoomId) {
      roomIds.push(loginAccount.personRoomId);
    }
    if (loginAccount?.systemRoomId) {
      roomIds.push(loginAccount.systemRoomId);
    }
    
    // 查詢服務號資料，獲取 bossServiceNumberId 對應的 memberRoomId
    let memberRoomId = '';
    if (stateService.bossServiceNumberId()) {
      try {
        const serviceNumber = await snService.getServiceNumberById(stateService.bossServiceNumberId());
        if (serviceNumber?.memberRoomId) {
          memberRoomId = serviceNumber.memberRoomId;
          roomIds.push(memberRoomId);
        }
      } catch (error) {
        logService.error('查詢服務號資料失敗', { error, bossServiceNumberId: stateService.bossServiceNumberId() });
      }
    }

    if (roomIds.length === 0) {
      logService.warn('getBasicRoomsFromDB: 未獲取到任何房間ID');
      return [];
    }

    // 先通過ID直接查詢數據庫
    const dbRooms: RoomVO[] = [];
    const foundRoomIds: Set<string> = new Set();
    for (const roomId of roomIds) {
      try {
        const room = await roomDao.getRoomById(roomId);
        if (room) {
          dbRooms.push(room);
          foundRoomIds.add(roomId);
        }
      } catch (error) {
        logService.error('通過ID查詢房間失敗', { error, roomId });
      }
    }

    // 檢查是否所有ID都已找到
    const allRoomsFound = roomIds.length > 0 && roomIds.every(id => foundRoomIds.has(id));

    // 如果所有特定ID的房間都已找到，直接返回結果
    if (allRoomsFound) {
      logService.info('getBasicRoomsFromDB: 已通過ID查詢到所有房間', { dbRooms });
      return dbRooms;
    }

    // 收集未找到的房間ID，通過API獲取
    const fetchRoomIds = roomIds.filter(id => !foundRoomIds.has(id));

    // 調用API補齊缺失房間
    const fetchedRooms: RoomVO[] = [];
    for (const roomId of fetchRoomIds) {
      try {
        const res = await fetchRoomItem({ roomId });
        if (res?.data) {
          fetchedRooms.push(res.data as RoomVO);
        }
      } catch (error) {
        logService.error('fetchRoomItem 補齊房間失敗', { error, roomId });
      }
    }

    // 合併本地與新獲取的房間
    const allRooms = [...dbRooms, ...fetchedRooms];
    logService.info('getBasicRoomsFromDB: 補齊房間完成', { allRooms });

    return allRooms;
  } catch (error) {
    logService.error('getBasicRoomsFromDB error', { error });
    throw error;
  }
}

/**
 * 優化的獲取房間信息方法：優先從DB獲取，沒有則調用API
 * @param roomId 房間ID
 * @returns 房間信息
 */
export async function getRoomInfoOptimized(roomId: string): Promise<any> {
  try {
    logService.info('開始優化獲取房間信息', { roomId });

    // 1. 優先從數據庫獲取
    const dbRoom = await roomDao.getRoomById(roomId);
    if (dbRoom) {
      logService.info('從數據庫獲取房間信息成功', {
        roomId,
        hasData: true,
        lastSequence: dbRoom.lastSequence,
        type: dbRoom.type
      });

      return {
        success: true,
        data: dbRoom,
        source: 'database'
      };
    }

    // 2. 數據庫沒有，調用API獲取
    logService.info('數據庫無房間信息，調用API獲取', { roomId });
    const apiResponse = await fetchRoomItem({ roomId });

    if (apiResponse?.data) {
      logService.info('從API獲取房間信息成功', {
        roomId,
        hasData: true,
        lastSequence: apiResponse.data.lastSequence,
        type: apiResponse.data.type
      });

      return {
        success: true,
        data: apiResponse.data,
        source: 'api'
      };
    }

    // 3. API也沒有數據
    logService.warn('數據庫和API都無法獲取房間信息', { roomId });
    return {
      success: false,
      data: null,
      source: 'none',
      error: '房間信息不存在'
    };

  } catch (error) {
    logService.error('獲取房間信息失敗', { error, roomId });
    return {
      success: false,
      data: null,
      source: 'error',
      error: error instanceof Error ? error.message : '未知錯誤'
    };
  }
}

/**
 * 查詢單一聊天室資料，並寫入本地資料庫
 * 如果 memberIds 不為空，還會異步查詢用戶資料
 * @param params 請求參數（RoomDto）
 */
export async function fetchRoomItem(params: any): Promise<any> {
  try {
    const response = await AileApi.fetchRoomItem(params);
    if (response?.data) {
      // 在Service層處理業務邏輯（單筆操作）
      const processedRooms = await processRoomData([response.data], true);
      // 更新response中的數據為處理後的數據
      response.data = processedRooms[0];
      // 存儲到DB
      await roomDao.upsertRooms(processedRooms);
      logService.info('fetchRoomItem 寫入本地DB成功', { params, room: response.data });
      
      // 背景異步處理: 獲取房間用戶資料
      const room = response.data as RoomVO;
      if (room.memberIds?.length && room.type === 'serviceMember') {
        // 排除 ownerId
        const userIds = room.memberIds.filter(id => id !== room.ownerId);
        
        if (userIds.length > 0) {
          // 立即執行的異步函數 (IIFE)，但主流程不等待它完成
          (async () => {
            try {
              logService.info('開始異步獲取用戶資料', { roomId: room.id, userIds });
              await fetchUserItems({ userIds });
              // fetchUserItems 內部已經處理了保存用戶資料到數據庫的邏輯
            } catch (userError) {
              logService.error('異步獲取用戶資料失敗', { 
                error: userError, 
                roomId: room.id, 
                userIds 
              });
            }
          })();
          // 注意：我們不等待上面的異步操作完成，直接繼續主流程
        }
      }
    }
    
    logService.info('fetchRoomItem success', { params, response });
    return response;
  } catch (error) {
    logService.error('fetchRoomItem error', { params, error });
    throw error;
  }
}

/**
 * 同步聊天室成員
 * @param params 請求參數
 */
export async function syncChatMember(params: SyncChatMemberRequest): Promise<SyncChatMemberResponse> {
  try {
    const response: SyncChatMemberResponse = await AileApi.syncChatMember(params);

    logService.info('syncChatMember success', { params, response });
    return response;
  } catch (error) {
    logService.error('syncChatMember error', { params, error });
    throw error;
  }
}

/**
 * 同步聊天室成員並存儲到本地數據庫（直接API請求）
 * @param params 請求參數
 */
export async function syncChatMemberAndStore(
  params: SyncChatMemberRequest
): Promise<SyncChatMemberResponse> {
  try {
    logService.info('開始同步聊天室成員數據', {
      roomId: params.roomId
    });

    // 直接調用API獲取聊天室成員數據
    const response = await syncChatMember(params);

    // 如果有成員數據，存儲到本地數據庫
    if (response.data?.items && response.data.items.length > 0) {
      await roomDao.upsertChatMembers(response.data.items);

      // 更新同步時間戳
      await updateSyncTimestamp(params.roomId);

      logService.info('syncChatMemberAndStore 存儲成員數據成功', {
        roomId: params.roomId,
        membersCount: response.data.items.length
      });
    }

    return response;
  } catch (error) {
    logService.error('syncChatMemberAndStore error', { params, error });
    throw error;
  }
}

/**
 * 從本地數據庫獲取聊天室成員列表
 * @param roomId 聊天室ID
 */
export async function getChatMembersFromDB(roomId: string): Promise<ChatMemberVO[]> {
  try {
    const members = await roomDao.getChatMembersByRoomId(roomId);
    logService.info('getChatMembersFromDB success', { roomId, membersCount: members.length });
    return members;
  } catch (error) {
    logService.error('getChatMembersFromDB error', { roomId, error });
    throw error;
  }
}

/**
 * 從本地數據庫獲取特定聊天室成員
 * @param memberId 成員ID
 * @param roomId 聊天室ID
 */
export async function getChatMemberFromDB(memberId: string, roomId: string): Promise<ChatMemberVO | null> {
  try {
    const member = await roomDao.getChatMemberById(memberId, roomId);
    logService.info('getChatMemberFromDB success', { memberId, roomId, found: !!member });
    return member;
  } catch (error) {
    logService.error('getChatMemberFromDB error', { memberId, roomId, error });
    throw error;
  }
}


/**
 * 更新聊天室成員同步時間戳
 * @param roomId 聊天室ID
 */
async function updateSyncTimestamp(roomId: string): Promise<void> {
  try {
    const timestamp = Date.now();
    await roomDao.updateChatMemberSyncTimestamp(roomId, timestamp);
    logService.debug('更新同步時間戳成功', { roomId, timestamp });
  } catch (error) {
    logService.error('更新同步時間戳失敗', { roomId, error });
    // 不拋出錯誤，避免影響主流程
  }
}