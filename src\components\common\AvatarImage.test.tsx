
import { render, screen, waitFor } from '@testing-library/react';
import { AvatarImage, AvatarImageProps } from './AvatarImage';
import * as avatarImageUtil from '@/utils/avatarImageUtil';


jest.mock('@/utils/avatarImageUtil');

const mockBase64 = 'data:image/png;base64,xxx';
const defaultProps: AvatarImageProps = {
  avatarId: 'avatar-1',
  size: 'S',
  name: 'Test User',
  alt: '頭像',
  className: 'w-10 h-10',
};

function renderWithI18n(props: Partial<AvatarImageProps> = {}) {
  return render(<AvatarImage {...defaultProps} {...props} />);
}

describe('AvatarImage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('加載狀態顯示首字母頭像', async () => {
    (avatarImageUtil.downloadAndCacheAvatarImage as jest.Mock).mockReturnValue(new Promise(() => {}));
    renderWithI18n();
    expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-fallback').textContent).toBe('TU');
  });

  it('快取命中顯示圖片', async () => {
    (avatarImageUtil.downloadAndCacheAvatarImage as jest.Mock).mockResolvedValue(mockBase64);
    renderWithI18n();
    await waitFor(() => expect(screen.getByTestId('avatar-image')).toBeInTheDocument());
    expect(screen.getByTestId('avatar-image')).toHaveAttribute('src', mockBase64);
  });

  it('下載失敗顯示首字母頭像', async () => {
    (avatarImageUtil.downloadAndCacheAvatarImage as jest.Mock).mockResolvedValue(null);
    renderWithI18n();
    await waitFor(() => expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument());
    expect(screen.getByTestId('avatar-fallback').textContent).toBe('TU');
  });

  it('下載失敗顯示 fallback 圖片', async () => {
    (avatarImageUtil.downloadAndCacheAvatarImage as jest.Mock).mockResolvedValue(null);
    renderWithI18n({ fallbackSrc: '/fallback.png' });
    await waitFor(() => expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument());
    expect(screen.getByTestId('avatar-fallback')).toHaveAttribute('src', '/fallback.png');
  });

  it('無頭像ID顯示首字母頭像', async () => {
    renderWithI18n({ avatarId: null, name: 'John Doe' });
    expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-fallback').textContent).toBe('JD');
  });
}); 