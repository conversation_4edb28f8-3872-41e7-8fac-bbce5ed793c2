.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1100;
  display: flex;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-content {
  width: 275px;
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 0px 16px 16px 0px;
  display: flex;
  flex-direction: column;
  padding: 24px 0px 0px;
}

.sidebar-user {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 8px 12px;
  border-bottom: 1px solid #EEEEEE;
  gap: 10px;
}

.sidebar-avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: 4px !important;
}

.sidebar-username {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 15px;
  line-height: 1.4em;
  color: #333333;
}

.sidebar-team-title {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 16px;
  line-height: 1.4em;
  color: #000000;
  padding: 12px 12px 8px;
}

.sidebar-teams {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex: 1;
}

.sidebar-team-item {
  padding: 0px 0px 0px 12px;
  cursor: pointer;
}

.sidebar-team-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px 0px 0px;
}

.sidebar-team-prefix {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0px 12px 0px 0px;
  width: 44px;
  height: 24px;
}

.sidebar-add-icon {
  width: 20px;
  height: 20px;
}

.sidebar-team-main {
  display: flex;
  flex-direction: column;
  padding: 12px 0px;
  flex: 1;
}

.sidebar-team-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #333333;
}

.sidebar-team-avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: 4px !important;
  margin-right: 10px;
}

.sidebar-team-item.active .sidebar-team-content {
  background-color: rgba(22, 119, 255, 0.2);
  border-radius: 8px;
}

.sidebar-team-badge {
  width: 20px;
  height: 10px;
  position: relative;
}

.sidebar-badge-dot {
  position: absolute;
  top: 0;
  left: 5px;
  width: 10px;
  height: 10px;
  background-color: #FF3141;
  border-radius: 50%;
}

.sidebar-team-add {
  width: 18px;
  height: 18px;
}

.sidebar-user-add-icon {
  width: 18px;
  height: 18px;
}

/* 新增: 加載中樣式 */
.sidebar-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  gap: 8px;
}

.sidebar-loading span {
  font-family: 'SF Pro', sans-serif;
  font-size: 14px;
  color: #999999;
}

/* 新增: 錯誤狀態樣式 */
.sidebar-error {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  cursor: pointer;
}

.sidebar-error span {
  font-family: 'SF Pro', sans-serif;
  font-size: 14px;
  color: #FF3141;
}

/* 新增: 空狀態樣式 */
.sidebar-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.sidebar-empty span {
  font-family: 'SF Pro', sans-serif;
  font-size: 14px;
  color: #999999;
} 

/* 租戶切換中的加載狀態 */
.sidebar-team-switching {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.sidebar-team-item {
  position: relative;
}

/* 當正在切換時，禁用點擊 */
.sidebar-team-item.switching {
  pointer-events: none;
  opacity: 0.7;
} 