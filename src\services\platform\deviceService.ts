/**
 * 设备服务 - AileApp
 * 管理设备信息、系统检测与处理设备相关的功能
 */

import { Capacitor } from '@capacitor/core';
import { Device } from '@capacitor/device';
import { BehaviorSubject } from 'rxjs';
import { ConstantUtil } from '../../utils/constantUtil';
import logService from '../system/logService';
import { setLocalStorage, getLocalStorage } from '../../utils/storage';

// 平台类型
export type PlatformType = 'web' | 'ios' | 'android';

// deviceData 头部所需的数据类型
export interface DeviceDataHeader {
  appName: string;
  version: string;
  platform: string;
  deviceId: string;
  timestamp: number;
}

// 平台能力接口
export interface PlatformCapabilities {
  hasCamera: boolean;
  hasMicrophone: boolean;
  hasGeolocation: boolean;
  hasBluetooth: boolean;
  hasNFC: boolean;
  supportsPWA: boolean;
  supportsNotifications: boolean;
  isIMBrowser: boolean;
}

// 平台信息接口
export interface PlatformInfo {
  platform: PlatformType;
  isNative: boolean;
  isWeb: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isMobile: boolean;
  capabilities: PlatformCapabilities;
  summary: string;
}

export interface DeviceInfo {
  // 应用信息
  appName: string;
  appVersion: string;
  appId: string;
  appBuild?: string;
  
  // 设备信息
  platform: string;
  manufacturer?: string;
  model?: string;
  operatingSystem?: string;
  osVersion?: string;
  webViewVersion?: string;
  
  // 唯一标识
  deviceId: string;
  uuid?: string;
  
  // 硬件信息
  isVirtual?: boolean;
  memoryTotal?: number;
  diskFree?: number;
  diskTotal?: number;
  
  // 屏幕信息
  screenWidth?: number;
  screenHeight?: number;
  pixelDensity?: number;
  
  // 网络信息
  networkType?: string;
  isOnline?: boolean;
  
  // 系统信息
  language: string;
  userAgent?: string;
  
  // 其他信息
  timestamp: number;
  batteryLevel?: number;
  
  // 平台信息（整合）
  platformInfo: PlatformInfo;
}

/**
 * 设备信息服务（单例）
 * 整合设备信息和平台检测功能
 */
class DeviceService {
  private static instance: DeviceService;
  private _deviceInfo = new BehaviorSubject<DeviceInfo | null>(null);
  public deviceInfo$ = this._deviceInfo.asObservable();
  private initialized = false;
  
  /**
   * 构造函数
   * 私有化防止外部实例化
   */
  private constructor() {
    // 初始化从缓存加载设备信息
    this.loadFromCache();
  }
  
  /**
   * 获取设备服务单例
   */
  public static getInstance(): DeviceService {
    if (!DeviceService.instance) {
      DeviceService.instance = new DeviceService();
    }
    return DeviceService.instance;
  }
  
  // ============== 平台检测方法（整合自 PlatformUtil） ==============
  
  /**
   * 获取当前平台
   */
  public getPlatform(): PlatformType {
    return Capacitor.getPlatform() as PlatformType;
  }

  /**
   * 是否为原生平台
   */
  public isNative(): boolean {
    return Capacitor.isNativePlatform();
  }

  /**
   * 是否为Web平台
   */
  public isWeb(): boolean {
    return this.getPlatform() === 'web';
  }

  /**
   * 是否为iOS平台
   */
  public isIOS(): boolean {
    return this.getPlatform() === 'ios';
  }

  /**
   * 是否为Android平台
   */
  public isAndroid(): boolean {
    return this.getPlatform() === 'android';
  }

  /**
   * 是否为移动端（iOS或Android）
   */
  public isMobile(): boolean {
    return this.isIOS() || this.isAndroid();
  }

  /**
   * 检查是否在IM客户端的内置浏览器中
   */
  public isInIMBrowser(): boolean {
    if (!this.isWeb()) {
      return false; // 原生环境不受影响
    }
    
    const userAgent = navigator.userAgent.toLowerCase();
    const imBrowsers = [
      'line',
      'fbav',
      'fban',
      'micromessenger',
      'instagram',
      'twitter',
      'weibo',
      'qq'
    ];
    
    return imBrowsers.some(browser => userAgent.includes(browser));
  }

  /**
   * 检查是否支持PWA
   */
  public supportsPWA(): boolean {
    return 'serviceWorker' in navigator;
  }

  /**
   * 检查是否支持推送通知
   */
  public supportsNotifications(): boolean {
    return 'Notification' in window;
  }

  /**
   * 检查是否支持地理位置
   */
  public supportsGeolocation(): boolean {
    return 'geolocation' in navigator;
  }

  /**
   * 获取平台能力信息
   */
  public getPlatformCapabilities(): PlatformCapabilities {
    return {
      hasCamera: false, // 需要运行时检测
      hasMicrophone: false, // 需要运行时检测
      hasGeolocation: this.supportsGeolocation(),
      hasBluetooth: 'bluetooth' in navigator,
      hasNFC: 'nfc' in navigator,
      supportsPWA: this.supportsPWA(),
      supportsNotifications: this.supportsNotifications(),
      isIMBrowser: this.isInIMBrowser(),
    };
  }

  /**
   * 获取完整的平台信息
   */
  public getPlatformInfo(): PlatformInfo {
    const platform = this.getPlatform();
    const isNative = this.isNative();
    const isIOS = this.isIOS();
    const isAndroid = this.isAndroid();
    const isMobile = this.isMobile();
    
    return {
      platform,
      isNative,
      isWeb: this.isWeb(),
      isIOS,
      isAndroid,
      isMobile,
      capabilities: this.getPlatformCapabilities(),
      summary: `${platform}${isNative ? ' (Native)' : ''}${isMobile ? ' (Mobile)' : ''}`,
    };
  }

  /**
   * 获取平台特定的CSS类名
   */
  public getPlatformClasses(): Record<string, boolean> {
    const platform = this.getPlatform();
    return {
      'platform-web': platform === 'web',
      'platform-ios': platform === 'ios',
      'platform-android': platform === 'android',
      'platform-native': this.isNative(),
      'platform-mobile': this.isMobile(),
    };
  }

  /**
   * 获取平台特定的配置
   */
  public getPlatformConfig<T>(configs: Record<PlatformType, T>): T {
    const platform = this.getPlatform();
    return configs[platform] || configs.web;
  }

  /**
   * 执行平台特定的代码
   */
  public runPlatformSpecific<T>(handlers: {
    web?: () => T;
    ios?: () => T;
    android?: () => T;
    native?: () => T;
    default?: () => T;
  }): T | undefined {
    const platform = this.getPlatform();
    
    try {
      // 优先执行特定平台的处理函数
      if (handlers[platform]) {
        logService.debug(`执行${platform}平台特定代码`);
        return handlers[platform]!();
      }
      
      // 其次执行原生平台通用处理函数
      if (this.isNative() && handlers.native) {
        logService.debug('执行原生平台通用代码');
        return handlers.native();
      }
      
      // 最后执行默认处理函数
      if (handlers.default) {
        logService.debug('执行默认平台代码');
        return handlers.default();
      }
      
      logService.warn(`未找到${platform}平台的处理函数`);
      return undefined;
    } catch (error) {
      logService.error(`执行平台特定代码失败`, error, `DeviceService:${platform}`);
      return handlers.default?.();
    }
  }

  /**
   * 条件执行：仅在特定平台执行
   */
  public onPlatform(platform: PlatformType | PlatformType[], callback: () => void): void {
    const currentPlatform = this.getPlatform();
    const targetPlatforms = Array.isArray(platform) ? platform : [platform];
    
    if (targetPlatforms.includes(currentPlatform)) {
      callback();
    }
  }

  /**
   * 条件执行：仅在原生平台执行
   */
  public onNative(callback: () => void): void {
    if (this.isNative()) {
      callback();
    }
  }

  /**
   * 条件执行：仅在Web平台执行
   */
  public onWeb(callback: () => void): void {
    if (this.isWeb()) {
      callback();
    }
  }

  // ============== 设备信息方法（原有功能保持） ==============
  
  /**
   * 初始化设备信息
   * 应在应用启动时调用此方法
   */
  public async initialize(): Promise<DeviceInfo> {
    if (this.initialized) {
      return this._deviceInfo.getValue() as DeviceInfo;
    }
    
    try {
      // 获取设备信息
      const deviceInfo = await this.collectDeviceInfo();
      
      // 更新内部状态
      this._deviceInfo.next(deviceInfo);
      this.initialized = true;
      
      // 保存到本地存储
      this.saveToCache(deviceInfo);
      
      logService.info('设备信息已初始化', {
        platform: deviceInfo.platform,
        deviceId: deviceInfo.deviceId,
        platformInfo: deviceInfo.platformInfo
      });
      return deviceInfo;
    } catch (error) {
      logService.error('初始化设备信息失败', error);
      // 返回基本信息
      const basicInfo = this.getBasicInfo();
      this._deviceInfo.next(basicInfo);
      return basicInfo;
    }
  }
  
  /**
   * 获取当前设备信息
   */
  public getDeviceInfo(): DeviceInfo {
    const info = this._deviceInfo.getValue();
    if (info) {
      return info;
    }
    
    // 如果没有初始化，返回基本信息
    const basicInfo = this.getBasicInfo();
    this._deviceInfo.next(basicInfo);
    return basicInfo;
  }
  
  /**
   * 获取设备 ID
   */
  public getDeviceId(): string {
    const deviceInfo = this.getDeviceInfo();
    return deviceInfo.deviceId;
  }

  /**
   * 获取设备名称
   */
  public getDeviceName(): string {
    const deviceInfo = this.getDeviceInfo();
    if (deviceInfo.manufacturer && deviceInfo.model) {
      return `${deviceInfo.manufacturer} ${deviceInfo.model}`;
    }
    if (deviceInfo.model) {
      return deviceInfo.model;
    }
    
    // 根据平台返回默认名称
    const platform = this.getPlatform();
    switch (platform) {
      case 'ios':
        return 'iOS Device';
      case 'android':
        return 'Android Device';
      case 'web':
        return 'Web Browser';
      default:
        return 'Unknown Device';
    }
  }

  /**
   * 获取设备类型
   */
  public getDeviceType(): string {
    const deviceInfo = this.getDeviceInfo();
    
    if (deviceInfo.operatingSystem && deviceInfo.osVersion) {
      return `${deviceInfo.operatingSystem} ${deviceInfo.osVersion}`;
    }
    
    if (deviceInfo.operatingSystem) {
      return deviceInfo.operatingSystem;
    }
    
    // 根据用户代理或平台信息推断
    const platform = this.getPlatform();
    const userAgent = navigator.userAgent;
    
    if (platform === 'ios') {
      const match = userAgent.match(/OS (\d+)_(\d+)/);
      return match ? `iOS ${match[1]}.${match[2]}` : 'iOS';
    }
    
    if (platform === 'android') {
      const match = userAgent.match(/Android (\d+(?:\.\d+)*)/);
      return match ? `Android ${match[1]}` : 'Android';
    }
    
    if (platform === 'web') {
      // 检测浏览器类型
      if (userAgent.includes('Chrome')) return 'Chrome Browser';
      if (userAgent.includes('Firefox')) return 'Firefox Browser';
      if (userAgent.includes('Safari')) return 'Safari Browser';
      if (userAgent.includes('Edge')) return 'Edge Browser';
      return 'Web Browser';
    }
    
    return 'Unknown Type';
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): string {
    // 从环境或i18n服务获取当前语言，默认为zh-tw
    const deviceInfo = this.getDeviceInfo();
    let language = deviceInfo.language || navigator.language || 'zh-TW';
    
    // 转换为标准格式
    language = language.toLowerCase().replace('_', '-');
    
    // 映射到支持的语言
    if (language.startsWith('zh-tw') || language.startsWith('zh-hant')) {
      return 'zh-tw';
    }
    if (language.startsWith('zh-cn') || language.startsWith('zh-hans') || language.startsWith('zh')) {
      return 'zh-cn';
    }
    if (language.startsWith('en')) {
      return 'en-us';
    }
    
    // 默认返回繁体中文
    return 'zh-tw';
  }

  /**
   * 获取操作系统类型 (Aile自定义)
   */
  public getOsType(): string {
    const platform = this.getPlatform();
    
    switch (platform) {
      case 'ios':
        return 'ios';
      case 'android':
        return 'android';
      case 'web':
        // 检测是否为桌面环境
        const userAgent = navigator.userAgent.toLowerCase();
        if (userAgent.includes('windows') || userAgent.includes('macintosh') || userAgent.includes('linux')) {
          return 'pc';
        }
        return 'web';
      default:
        return 'web';
    }
  }

  /**
   * 获取设备信息对象，用于 API 请求
   * 与 httpService 集成
   */
  public getDeviceHeaders(): Record<string, string> {
    const deviceInfo = this.getDeviceInfo();
    const deviceData = this.getDeviceDataHeader();
    
    return {
      'deviceData': encodeURIComponent(JSON.stringify(deviceData)),
      'x-client-version': deviceInfo.appVersion,
      'x-client-platform': deviceInfo.platform,
      'x-client-native': this.isNative().toString(),
      'x-client-mobile': this.isMobile().toString(),
    };
  }


  
  /**
   * 获取 deviceData 所需的数据（与 httpService 保持一致）
   */
  public getDeviceDataHeader(): any {
    const deviceInfo = this.getDeviceInfo();
    
    return {
      deviceName: this.getDeviceName(),
      deviceType: this.getDeviceType(),
      osType: this.getOsType(),
      channel: ConstantUtil.APP_CHANNEL,
      language: this.getCurrentLanguage(),
      uniqueId: deviceInfo.deviceId,
      bundleId: ConstantUtil.APP_ID,
      version: ConstantUtil.VERSION
    };
  }
  
  /**
   * 重新获取设备信息
   */
  public async refreshDeviceInfo(): Promise<DeviceInfo> {
    try {
      const deviceInfo = await this.collectDeviceInfo();
      this._deviceInfo.next(deviceInfo);
      this.saveToCache(deviceInfo);
      return deviceInfo;
    } catch (error) {
      logService.error('刷新设备信息失败', error);
      return this.getDeviceInfo();
    }
  }
  
  /**
   * 收集完整的设备信息
   * 从 Capacitor 和其他来源收集
   * @private
   */
  private async collectDeviceInfo(): Promise<DeviceInfo> {
    try {
      // 应用信息
      const appInfo = {
        appName: ConstantUtil.APP_NAME,
        appVersion: ConstantUtil.VERSION,
        appId: ConstantUtil.APP_ID,
        appBuild: ConstantUtil.BUILD
      };
      
      // 获取平台信息（整合）
      const platformInfo = this.getPlatformInfo();
      
      // 从 Capacitor 收集信息（如果可用）
      let deviceId = this.getStoredDeviceId();
      let platform = platformInfo.platform;
      let language = navigator.language;
      let deviceDetails: any = {};
      let batteryLevel: number | undefined;
      let networkType = 'unknown';
      let isOnline = navigator.onLine;
      
      try {
        // 设备信息
        const deviceInfo = await Device.getInfo();
        platform = deviceInfo.platform;
        deviceDetails = {
          manufacturer: deviceInfo.manufacturer,
          model: deviceInfo.model,
          operatingSystem: deviceInfo.operatingSystem,
          osVersion: deviceInfo.osVersion,
          webViewVersion: deviceInfo.webViewVersion,
          isVirtual: deviceInfo.isVirtual,
          memoryTotal: deviceInfo.memUsed,
          diskFree: undefined,
          diskTotal: undefined
        };
        
        // 唯一标识
        const idInfo = await Device.getId();
        if (idInfo && idInfo.identifier) {
          // 如果有 identifier，使用它作为设备 ID
          deviceId = idInfo.identifier;
          this.storeDeviceId(deviceId);
        }

        // 电池信息
        const batteryInfo = await Device.getBatteryInfo();
        batteryLevel = batteryInfo.batteryLevel;
        
        // 语言
        const languageInfo = await Device.getLanguageCode();
        language = languageInfo.value;
      } catch (err) {
        logService.debug('Capacitor 设备信息不可用，使用 Web API', err);
      }
      
      // 屏幕信息
      const screenInfo = {
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        pixelDensity: window.devicePixelRatio
      };
      
      // 构建完整设备信息
      const deviceInfo: DeviceInfo = {
        ...appInfo,
        ...deviceDetails,
        platform,
        deviceId,
        uuid: deviceId,
        language,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        batteryLevel,
        networkType,
        isOnline,
        ...screenInfo,
        platformInfo, // 整合平台信息
      };
      
      return deviceInfo;
    } catch (error) {
      logService.error('收集设备信息失败', error);
      return this.getBasicInfo();
    }
  }
  
  /**
   * 获取基本设备信息
   * 当无法获取完整信息时使用
   * @private
   */
  private getBasicInfo(): DeviceInfo {
    let deviceId = this.getStoredDeviceId();
    const platformInfo = this.getPlatformInfo();
    
    return {
      appName: ConstantUtil.APP_NAME,
      appVersion: ConstantUtil.VERSION,
      appId: ConstantUtil.APP_ID,
      appBuild: ConstantUtil.BUILD,
      platform: platformInfo.platform,
      deviceId: deviceId,
      language: navigator.language,
      timestamp: Date.now(),
      isOnline: navigator.onLine,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      platformInfo, // 整合平台信息
    };
  }
  
  /**
   * 从本地存储加载设备信息
   * @private
   */
  private loadFromCache(): void {
    try {
      const deviceInfo = getLocalStorage<DeviceInfo | null>(ConstantUtil.DEVICE_INFO_STORAGE_KEY, null);
      if (deviceInfo) {
        // 如果缓存的设备信息没有平台信息，需要重新获取
        if (!deviceInfo.platformInfo) {
          logService.info('缓存的设备信息缺少平台信息，将重新初始化');
          return;
        }
        this._deviceInfo.next(deviceInfo);
        this.initialized = true;
        logService.debug('已从缓存加载设备信息');
      }
    } catch (error) {
      logService.warn('无法从缓存加载设备信息', error);
    }
  }
  
  /**
   * 保存设备信息到本地存储
   * @param deviceInfo 设备信息
   * @private
   */
  private saveToCache(deviceInfo: DeviceInfo): void {
    try {
      setLocalStorage(ConstantUtil.DEVICE_INFO_STORAGE_KEY, JSON.stringify(deviceInfo));
    } catch (error) {
      logService.warn('无法保存设备信息到缓存', error);
    }
  }
  
  /**
   * 从存储中获取设备 ID
   * @private
   */
  private getStoredDeviceId(): string {
    let deviceId = getLocalStorage<string>(ConstantUtil.DEVICE_ID_STORAGE_KEY, '');
    if (!deviceId) {
      deviceId = this.generateDeviceId();
      this.storeDeviceId(deviceId);
    }
    return deviceId;
  }
  
  /**
   * 将设备 ID 保存到存储
   * @param deviceId 设备 ID
   * @private
   */
  private storeDeviceId(deviceId: string): void {
    try {
      setLocalStorage(ConstantUtil.DEVICE_ID_STORAGE_KEY, deviceId);
    } catch (error) {
      logService.warn('无法保存设备 ID 到存储', error);
    }
  }
  
  /**
   * 生成新的设备 ID
   * @private
   */
  private generateDeviceId(): string {
    return ConstantUtil.uuid();
  }
}

// 导出设备服务单例
export const deviceService = DeviceService.getInstance();

// 导出便捷的平台检测API（兼容原有的 PlatformUtil 使用方式）
export const platform = {
  get: () => deviceService.getPlatform(),
  isNative: () => deviceService.isNative(),
  isWeb: () => deviceService.isWeb(),
  isIOS: () => deviceService.isIOS(),
  isAndroid: () => deviceService.isAndroid(),
  isMobile: () => deviceService.isMobile(),
  isIMBrowser: () => deviceService.isInIMBrowser(),
  capabilities: () => deviceService.getPlatformCapabilities(),
  info: () => deviceService.getPlatformInfo(),
  classes: () => deviceService.getPlatformClasses(),
  config: <T>(configs: Record<PlatformType, T>) => deviceService.getPlatformConfig(configs),
  run: (handlers: any) => deviceService.runPlatformSpecific(handlers),
  onNative: (callback: () => void) => deviceService.onNative(callback),
  onWeb: (callback: () => void) => deviceService.onWeb(callback),
  onPlatform: (platform: PlatformType | PlatformType[], callback: () => void) => deviceService.onPlatform(platform, callback),
};

export default deviceService; 