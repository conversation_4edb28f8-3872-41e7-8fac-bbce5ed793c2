import localforage from 'localforage';
import { getLocalStorage } from './storage';
import { ConstantUtil } from './constantUtil';
import { logService } from '@/services/system/logService';

const forageInstances: Record<string, LocalForage> = {};

/**
 * 取得用戶專屬 localforage 實例
 * @param accountId 用戶帳號ID (可選，如不提供將從 localStorage 中獲取)
 * @returns LocalForage 實例
 */
export function getUserForage(accountId?: string): LocalForage {
  // 如果未提供 accountId，則從 localStorage 中獲取
  const actualAccountId = accountId || getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
  
  if (!actualAccountId) {
    logService.error('無法獲取用戶 accountId，無法創建 localForage 實例');
    // 返回一個默認實例以避免錯誤
    if (!forageInstances['default']) {
      forageInstances['default'] = localforage.createInstance({
        name: 'default_data'
      });
    }
    return forageInstances['default'];
  }
  
  if (!forageInstances[actualAccountId]) {
    forageInstances[actualAccountId] = localforage.createInstance({
      name: `${actualAccountId}_data`
    });
  }
  return forageInstances[actualAccountId];
} 