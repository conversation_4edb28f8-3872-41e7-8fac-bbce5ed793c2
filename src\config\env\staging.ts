import { baseConfig, BaseConfig } from './base';

/**
 * 测试环境配置
 */
export interface StagingConfig extends BaseConfig {
  API_BASE_URL: string;
  SOCKET_URL: string;
  SOCKET_PATH: string;
  VITE_LINE_CHANNEL_ID: string;
  VITE_LINE_REDIRECT_URI: string;
  DEBUG: boolean;
}

export const stagingConfig: StagingConfig = {
  ...baseConfig,
  
  // 测试环境特定配置
  API_BASE_URL: 'https://newaile.dev.aile.cloud',
  SOCKET_URL: 'https://newaile.dev.aile.cloud/aile',
  SOCKET_PATH: '/socketio/socket.io',
  VITE_LINE_CHANNEL_ID: '2007765389',
  VITE_LINE_REDIRECT_URI: 'cloud.aile.aile:/callback',
  DEBUG: true,
  
  // 测试环境覆盖配置
  LOG_LEVEL: 'debug',
  API_TIMEOUT: 45000, // 测试环境适中的超时时间
};
