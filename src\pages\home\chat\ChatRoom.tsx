import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Toast, Image, ImageViewer, ErrorBlock, Modal } from 'antd-mobile';
import ChatInput from '../../../components/ChatInput';
import {
  AddOutline,
  CameraOutline,
  PictureOutline,
  SmileOutline,
  AudioOutline
} from 'antd-mobile-icons';
import { ChatRoomType, IChatRoomProps, IChatMessage } from '../../../types/chat.types';  
import { Type, SystemMessageEventCode } from '@/services/core/chat';
import './ChatRoom.css';
import './ChatRoomIcons.css';
import ChatRoomHeader from './ChatRoomHeader';
import { TasksPanel, ITask } from '../../../components/common';
import { ConstantUtil } from '../../../utils/constantUtil';
import { generateRoute } from '@/config/app/routes';
import { calculateServiceDuration } from '../../../utils/messageUtil';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { authService } from '@/services/core/auth';
import { messageService } from '@/services/core/chat';
import { logService } from '@/services/system/logService';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import snService from '@/services/core/tenant/snService';
import stateService from '@/services/stateService';
import AvatarImage from '@/components/common/AvatarImage';
import { IServiceNumberInfo } from '@/services/core/tenant/snService';
import {
  initializeRoomMessages,
  selectRoomMessages,
  selectRoomLoading,
  selectRoomError,
  selectRoomHasNextPage,
  selectRoomCurrentPage,
  addMessage,
  updateMessageStatus,
  updateMessagesStatusBySequence,
  deleteMessageById,
  loadMoreMessages
} from '@/app/slices/messageSlice';
import { selectBossServiceNumberOwnerId } from '@/app/slices/tenantSlice';
import { updateRoomSessionStatus } from '@/app/slices/roomSlice';
import type { SendMessageRequest } from '@/services/core/chat/messageService';
import { useNavigate } from 'react-router-dom';
import  { getUserById } from '@/services/core/tenant/userService';
import { contactService } from '@/services/core/tenant/index';
import { useTranslation } from 'react-i18next';
import { useOptimizedScroll } from '@/hooks/useOptimizedScroll';
import { deviceService } from '@/services/platform/deviceService';
// Import custom icons
import paperclipIcon from '../../../assets/icons/chat/paperclip-icon.svg';
import idCardIcon from '../../../assets/icons/chat/id-card-icon.svg';
import { RoomType } from '@/services/core/chat/roomService.types';
import { syncChatMemberAndStore } from '@/services/core/chat/roomService';
import type { ChatMemberVO } from '@/services/core/chat/roomService.types';
import { MemberChangedAction } from '@/services/stateService';
import MessageStatusIndicator from '@/components/chat/MessageStatusIndicator';
import MessageActionSheet from '@/components/chat/MessageActionSheet';
// Import channel icons
import iconImFb from '@/assets/icons/channel/icon-im-fb.svg';
import iconImLine from '@/assets/icons/channel/icon-im-line.svg';
import iconImIg from '@/assets/icons/channel/icon-im-ig.svg';
import iconAiwow from '@/assets/icons/channel/icon-aiwow.svg';
import { roomDao } from '@/services/dao';
import { RiSendPlane2Fill } from "react-icons/ri";
import { parseTextWithLinks, LinkPart } from '../../../utils/linkUtil';
import { RoomAction } from '@/services/stateService';
import { RoomVO } from '@/services/core/chat/roomService.types';
import * as messageUtil from '@/utils/messageUtil';
import { SessionStatus } from '@/services/core/chat/roomService';


const channelIconMap: Record<string, string> = {
  Facebook: iconImFb,
  Line: iconImLine,
  Instagram: iconImIg,
  Aiwow: iconAiwow,
};

// 格式化时间的辅助函数
const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// URL 連結轉換組件
const LinkifiedText: React.FC<{ text: string }> = ({ text }) => {
  const parts = parseTextWithLinks(text);

  return (
    <>
      {parts.map((part: LinkPart, index: number) => {
        if (part.isUrl) {
          return (
            <a
              key={index}
              href={part.href}
              target="_blank"
              rel="noopener noreferrer"
              onClick={(e) => {
                // 防止事件冒泡到父元素
                e.stopPropagation();
              }}
            >
              {part.text}
            </a>
          );
        }
        // 普通文本
        return part.text;
      })}
    </>
  );
};

// 模拟任务数据
const mockTasks: ITask[] = [
  {
    id: '1',
    title: '任務標題任務標題任務標題任務標題任務標題任務標題任務標題',
    dueTime: '2小時',
    isUrgent: true
  },
  {
    id: '2',
    title: '任務標題任務標題任務標題任務標題任務標題任務標題',
    dueTime: '4天',
    isUrgent: false
  },
  {
    id: '3',
    title: '任務標題任務標題任務',
    dueTime: '7天',
    isUrgent: false
  }
];

const ChatRoom: React.FC<IChatRoomProps> = ({
  type,
  title,
  roomId,
  roomInfo,
  showTasksBar = false,

  showTeamSelector = false,
  teamUnreadCount = 0,
  teamAvatarSrc = '',
  onBackClick,
  //memberCount,

}) => {
  const dispatch = useAppDispatch();
  const dateGroups = useAppSelector(selectRoomMessages(roomId));
  const isLoading = useAppSelector(selectRoomLoading(roomId));
  const error = useAppSelector(selectRoomError(roomId));
  const hasNextPage = useAppSelector(selectRoomHasNextPage(roomId));
  const currentPage = useAppSelector(selectRoomCurrentPage(roomId));
  const isAdmin = useAppSelector(state => state.tenant.isAdmin);
  const bossServiceNumberOwnerId = useAppSelector(selectBossServiceNumberOwnerId);


  // 历史消息加载状态 - 区分初始加载和历史消息加载
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  
  const [inputValue, setInputValue] = useState('');
  const [showExpandedToolbar, setShowExpandedToolbar] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // 圖片查看相關狀態
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImage, setCurrentImage] = useState<string>('');

  // 消息操作菜单状态
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [selectedFailedMessageId, setSelectedFailedMessageId] = useState<string | null>(null);
  

  const [serviceNumber, setServiceNumber] = useState<IServiceNumberInfo | null>(null);
  // 服務號頭像 ID 映射表
  const [serviceNumberAvatarIds, setServiceNumberAvatarIds] = useState<Record<string, string>>({});
  // 存儲已查詢過的用戶和聯絡人信息
  const [senderInfo, setSenderInfo] = useState<Record<string, { avatarId: string | null, name: string }>>({});
  // 存儲商務號名稱
  const [businessNumberName, setBusinessNumberName] = useState<string>('');


  // 獲取商務號名稱
  useEffect(() => {
    const fetchBusinessNumberName = async () => {
      try {
        const businessNumber = await snService.getCurrentBossServiceNumber();
        if (businessNumber && businessNumber.name) {
          setBusinessNumberName(businessNumber.name);
        }
      } catch (error) {
        logService.error('獲取商務號名稱失敗', { error: error as Error });
      }
    };

    fetchBusinessNumberName();
  }, []);

  // 初始化加載消息 - 使用更安全的初始化策略
  const initializedRef = useRef<Record<string, boolean>>({});

  // 當 roomInfo 从 null 变为有值，或 lastSequence 变化时，重新初始化
  const lastSequenceRef = useRef<number | undefined>(undefined);
  const roomInfoLoadedRef = useRef<boolean>(false);

  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      // 重置当前房间的状态，允许下次进入时重新初始化
      if (roomId) {
        roomInfoLoadedRef.current = false;
        lastSequenceRef.current = undefined;
        logService.debug('🧹 ChatRoom 组件卸载，清理状态', { roomId });
      }
    };
  }, [roomId]);

  useEffect(() => {
    if (!roomId) return;

    logService.debug('🔄 ChatRoom useEffect 触发', {
      roomId,
      hasRoomInfo: !!roomInfo,
      roomInfoLoaded: roomInfoLoadedRef.current,
      lastSequence: roomInfo?.lastSequence,
      lastSequenceRef: lastSequenceRef.current,
      initialized: initializedRef.current[roomId]
    });

    // 情况1: roomInfo 从 null 变为有值（首次加载完成）
    if (roomInfo && !roomInfoLoadedRef.current) {
      roomInfoLoadedRef.current = true;
      lastSequenceRef.current = roomInfo.lastSequence;

      logService.info('roomInfo首次加载完成，初始化消息', {
        roomId,
        lastSequence: roomInfo.lastSequence,
        previouslyInitialized: initializedRef.current[roomId]
      });

      // 只有在未初始化过的情况下才进行初始化
      if (!initializedRef.current[roomId]) {
        initializedRef.current[roomId] = true;
        
        // 先同步成員數據，再初始化消息
        const initializeRoom = async () => {
          try {
            // 同步聊天室成員數據
            const response = await syncChatMemberAndStore({ roomId });
            
            // 初始化消息
            dispatch(initializeRoomMessages(roomId, roomInfo.lastSequence || 0));
            
            // 根據同步到的成員數據更新已讀已到狀態
            if (response.success && response.data?.items) {
              // 給一點延遲確保消息先載入完成
              setTimeout(() => {
                updateReadDeliveredStatusFromMembers(response.data!.items!);
              }, 300);
            } else {
              setTimeout(() => {
                updateReadDeliveredStatusFromDB();
              }, 300);
            }
          } catch (error) {
            // 錯誤處理
            logService.error('聊天室成員同步失敗', { roomId, error });
            dispatch(initializeRoomMessages(roomId, roomInfo.lastSequence || 0));
            
            // 給消息載入一點時間再嘗試更新狀態
            setTimeout(() => {
              updateReadDeliveredStatusFromDB();
            }, 300);
          }
        };
        
        initializeRoom();
      } else {
        logService.warn('⚠️ 房间已经初始化过，跳过重复初始化', {
          roomId,
          lastSequence: roomInfo.lastSequence
        });
      }

      return;
    }

    // 情况2: roomInfo 存在且 lastSequence 发生变化（仅在显著变化时重新初始化）
    if (roomInfo?.lastSequence !== undefined &&
        lastSequenceRef.current !== undefined &&
        lastSequenceRef.current !== roomInfo.lastSequence &&
        Math.abs(roomInfo.lastSequence - lastSequenceRef.current) > 0) {

      logService.info('lastSequence显著变化，重新初始化消息', {
        roomId,
        oldSequence: lastSequenceRef.current,
        newSequence: roomInfo.lastSequence,
        difference: roomInfo.lastSequence - lastSequenceRef.current
      });

      lastSequenceRef.current = roomInfo.lastSequence;
      // 不重置初始化标记，避免重复初始化
      dispatch(initializeRoomMessages(roomId, roomInfo.lastSequence));
    }
  }, [roomId, roomInfo?.lastSequence, dispatch]); // 只依赖 lastSequence，避免其他属性变化导致重复初始化

  // 根据成员数据更新已读已到状态
  const updateReadDeliveredStatusFromMembers = useCallback((members: ChatMemberVO[]) => {
    if (!roomId || !roomInfo?.ownerId) return;

    // 使用聊天室的ownerId与member.memberId匹配
    const ownerMember = members.find(member => member.memberId === roomInfo.ownerId);
    if (!ownerMember) {
      logService.debug('未找到聊天室owner的成员数据', { roomId, ownerId: roomInfo.ownerId });
      return;
    }

    const { lastReadSequence, lastReceivedSequence } = ownerMember;

    logService.info('获取到聊天室owner的已读已到数据', {
      roomId,
      ownerId: roomInfo.ownerId,
      lastReadSequence,
      lastReceivedSequence
    });

    // 更新已到达状态 - 更新右侧消息（当前用户发送的消息）
    if (lastReceivedSequence && lastReceivedSequence > 0) {
      dispatch(updateMessagesStatusBySequence({
        roomId,
        maxSequence: lastReceivedSequence,
        status: 'delivered',
        ownerId: roomInfo.ownerId
      }));
      logService.debug('更新当前用户发送消息的已到达状态', { roomId, maxSequence: lastReceivedSequence, ownerId: roomInfo.ownerId });
    }

    // 更新已读状态 - 更新右侧消息（当前用户发送的消息）
    if (lastReadSequence && lastReadSequence > 0) {
      dispatch(updateMessagesStatusBySequence({
        roomId,
        maxSequence: lastReadSequence,
        status: 'read',
        ownerId: roomInfo.ownerId
      }));
      logService.debug('更新当前用户发送消息的已读状态', { roomId, maxSequence: lastReadSequence, ownerId: roomInfo.ownerId });
    }
  }, [roomId, roomInfo?.ownerId, dispatch]);

  // 从本地数据库获取成员数据并更新已读已到状态
  const updateReadDeliveredStatusFromDB = useCallback(async () => {
    if (!roomId || !roomInfo?.ownerId) return;

    try {
      const localMembers = await roomDao.getChatMembersByRoomId(roomId);
      if (localMembers && localMembers.length > 0) {
        updateReadDeliveredStatusFromMembers(localMembers);
        logService.debug('从本地数据库更新已读已到状态', {
          roomId,
          ownerId: roomInfo.ownerId,
          membersCount: localMembers.length,
          lastReadSequence: localMembers.find(m => m.memberId === roomInfo.ownerId)?.lastReadSequence,
          lastReceivedSequence: localMembers.find(m => m.memberId === roomInfo.ownerId)?.lastReceivedSequence
        });
      } else {
        logService.debug('本地數據庫沒有找到成員數據', { roomId });
      }
    } catch (error) {
      logService.error('从本地数据库获取成员数据失败', { roomId, ownerId: roomInfo.ownerId, error });
    }
  }, [roomId, roomInfo?.ownerId, updateReadDeliveredStatusFromMembers]);
  
  // 獲取服務號頭像
  useEffect(() => {
    const fetchServiceNumberAvatar = async () => {
      if (roomInfo && roomInfo.serviceNumberId && !serviceNumberAvatarIds[roomInfo.serviceNumberId]) {
        try {
          const serviceNumber = await snService.getServiceNumberById(roomInfo.serviceNumberId);
          setServiceNumber(serviceNumber);
          if (serviceNumber && serviceNumber.avatarId) {
            setServiceNumberAvatarIds(prev => ({
              ...prev,
              [roomInfo.serviceNumberId as string]: serviceNumber.avatarId as string
            }));
            logService.debug('獲取服務號頭像成功', { serviceNumberId: roomInfo.serviceNumberId });
          }
        } catch (error) {
          logService.error('獲取服務號頭像失敗', { error: error as Error, serviceNumberId: roomInfo.serviceNumberId });
        }
      }
    };

    fetchServiceNumberAvatar();
  }, [roomInfo?.serviceNumberId]); // 移除 serviceNumberAvatarIds 依賴，避免循環依賴

  // 獲取發送者信息（頭像、名稱等）
  const fetchSenderInfo = useCallback(async (senderId: string) => {
    try {

      // 嘗試作為用戶查詢
      const user = await getUserById(senderId);
      if (user) {
        const userInfo = {
          avatarId: user.avatarId || null,
          name: user.name || user.account || '未知用戶'
        };
        setSenderInfo(prev => {
          // 如果已有此發送者的信息，不再重複設置
          if (prev[senderId]) {
            return prev;
          }
          return {
            ...prev,
            [senderId]: userInfo
          };
        });
        return;
      }

      // 嘗試作為聯絡人查詢
      const contact = await contactService.getContactById(senderId);
      if (contact) {
        const contactInfo = {
          avatarId: contact.avatarId || null,
          name: contact.name || '未知聯絡人'
        };
        setSenderInfo(prev => {
          // 如果已有此發送者的信息，不再重複設置
          if (prev[senderId]) {
            return prev;
          }
          return {
            ...prev,
            [senderId]: contactInfo
          };
        });
        return;
      }

      // 如果都找不到，記錄一個空值避免重複查詢
      setSenderInfo(prev => {
        // 如果已有此發送者的信息，不再重複設置
        if (prev[senderId]) {
          return prev;
        }
        return {
          ...prev,
          [senderId]: { avatarId: null, name: 'Aile小助手' }
        };
      });
    } catch (error) {
      logService.error('獲取發送者信息失敗', { error: error as Error, senderId });
    }
  }, []); // 移除 senderInfo 依赖，避免循环依赖
  
  // 當消息列表更新時，獲取所有發送者的信息
  useEffect(() => {
    if (!dateGroups) return;

    const allSenderIds = new Set<string>();
    dateGroups.forEach(group => {
      group.messages.forEach(message => {
        if(message.type === "Event"){
          const content = message.content;
          if(message.content){
            const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;  
            if(parsedContent.event !== SystemMessageEventCode.SessionStart){
              if(parsedContent.event === SystemMessageEventCode.RoomMemberAdd){
                allSenderIds.add(parsedContent.content.memberId);
                parsedContent.content.memberIds.forEach((id: string) => {
                  allSenderIds.add(id);
                });
              }else{
                allSenderIds.add(parsedContent.content.agentId);
              }
            }
          }
        }else{
          if (message.sender && message.sender.id) {
            allSenderIds.add(message.sender.id);
          }
        }
      });
    });
    // 為所有新的發送者獲取信息（只查詢還沒有緩存的）
    allSenderIds.forEach(senderId => {
      if (!senderInfo[senderId]) {
        fetchSenderInfo(senderId);
      } 
    });
  }, [dateGroups, fetchSenderInfo, senderInfo]); // 添加 senderInfo 依赖以便检查缓存

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  const handleTaskClick = (taskId: string) => {
    console.log('Task clicked:', taskId);
    // 这里可以添加任务点击后的逻辑
  };

  // CSS class prefix based on chat room type
  const prefix = type === ChatRoomType.MY ? '' : `${type}-`;
  
  // 使用基础类和特定类型类的组合
  const getClassName = (baseClass: string) => {
    return `base-${baseClass} ${prefix}${baseClass}`;
  };

  // 根据 isAdmin 状态添加条件 CSS 类的辅助函数
  const getClassNameWithAdminCheck = (baseClass: string, additionalClasses: string = '') => {
    const baseClassName = getClassName(baseClass);
    const nonAdminClass = !isAdmin ? 'non-admin' : '';
    return `${baseClassName} ${nonAdminClass} ${additionalClasses}`.trim();
  };
  
  // 在组件内添加一个函数来判断是否显示头像
  const shouldShowAvatar = (messages: IChatMessage[], index: number) => {
    if (index === 0) return true;
    
    const currentMsg = messages[index];
    const prevMsg = messages[index - 1];
    
    // 如果前一条消息不是同一个发送者，或者时间间隔超过5分钟，则显示头像
    const senderChanged = currentMsg.sender.id !== prevMsg.sender.id;
    const timeDiff = new Date(currentMsg.timestamp).getTime() - new Date(prevMsg.timestamp).getTime();
    const timeThreshold = 5 * 60 * 1000; // 5分钟，单位毫秒
    
    return senderChanged || timeDiff > timeThreshold;
  };

  const { t } = useTranslation();

  // 檢查房間狀態並自動開始服務（如果需要）- 使用 useRef 避免重複初始化
  const serviceInitializedRef = useRef<Record<string, boolean>>({});

  useEffect(() => {
    if (!roomId || !roomInfo || roomInfo.type !== RoomType.Services) return;

    // 避免重複初始化同一個房間
    if (serviceInitializedRef.current[roomId]) return;

    const initializeRoom = async () => {
      try {
        serviceInitializedRef.current[roomId] = true;
        const result = await messageService.checkAndStartService(roomId);
        if (result.success && result.serviceResponse?.success) {
          Toast.show({
            content: t('已接入客服'),
            position: 'bottom'
          });
        }
      } catch (error) {
        logService.error('聊天室初始化錯誤', { error, roomId });
        // 初始化失敗時重置標記，允許重試
        serviceInitializedRef.current[roomId] = false;
      }
    };
    initializeRoom();
  }, [roomId, roomInfo?.type, t]); // 只依賴 roomInfo.type，避免其他屬性變化導致重複初始化
  
  // 將房間設為已讀（unreadCount=0, updateTime=now）
  const markRoomAsRead = useCallback(async (roomId: string) => {
    try {
      const room = await roomDao.getRoomById(roomId);
      if (!room) return;
      room.unreadCount = 0;
      room.updateTime = Date.now();
      await roomDao.upsertRooms([room]);
      logService.info('本地 Room 表已設為已讀', { roomId });
    } catch (error) {
      logService.error('本地 Room 表設為已讀失敗', { error, roomId });
    }
  }, []);

  // 進入聊天室自動設置消息已讀
  useEffect(() => {
    if (!roomId) return;
    let cancelled = false;
    const markAsRead = async () => {
      try {
        const room = await roomDao.getRoomById(roomId);
        const loginUserId = stateService.loginUser()?.id;
        // 若無 room、未讀為 0、或最後一條消息 senderId 為自己，則不發送已讀
        if (!room || room.unreadCount === 0) {
          logService.info('本地 Room 表未讀為 0，跳過已讀上報', { roomId });
          return;
        }
        // 取得最後一條消息 senderId
        const lastMessage = room.lastMessage ? (typeof room.lastMessage === 'string' ? JSON.parse(room.lastMessage) : room.lastMessage) : null;
        if (lastMessage && lastMessage.senderId === loginUserId) {
          logService.info('最後一條消息為自己發送，跳過已讀上報', { roomId });
          return;
        }
        const res = await messageService.messageRead({ roomId });
        logService.info('聊天室已讀狀態上報', { roomId, success: res.success });
        if (res.success && !cancelled) {
          markRoomAsRead(roomId);
        } else if (!res.success && !cancelled) {
          Toast.show({ content: res.msg || '設置已讀失敗', position: 'bottom' });
        }
      } catch (error) {
        if (!cancelled) {
          logService.error('聊天室設置已讀異常', { error, roomId });
          Toast.show({ content: '設置已讀異常', position: 'bottom' });
        }
      }
    };
    markAsRead();
    return () => { cancelled = true; };
  }, [roomId, markRoomAsRead]);
  
  // 判斷消息顯示位置的輔助函數
  const shouldShowOnRight = (senderId: string, chatRoomType: ChatRoomType): boolean => {
    try {
      const userInfo = stateService.loginUser();
      if (!userInfo) return false;
      
      const userId = userInfo.id;
      const systemAccountId = stateService.loginAccount()?.systemAccountId;
      const accountId = stateService.loginAccount()?.accountId;
      
      // 當消息發送者是當前用戶時，顯示在右側
      if (senderId === userId) return true;

      // 當聊天室類型是 MY 或 SYSTEM，且消息發送者是系統賬號時，顯示在右側
      if((chatRoomType === ChatRoomType.MY || chatRoomType === ChatRoomType.SYSTEM) && senderId === accountId){
        return true;
      }
      
      // 當消息發送者是系統賬號，且聊天室類型是 MY 或 SYSTEM 時，顯示在左側
      if (senderId === systemAccountId && 
        (chatRoomType === ChatRoomType.MY || chatRoomType === ChatRoomType.SYSTEM)) {
        return false;
      }


      
      // 當消息發送者是系統賬號，但聊天室類型不是 MY 或 SYSTEM 時，顯示在右側
      if (senderId === systemAccountId) return true;
      
      if(chatRoomType === ChatRoomType.CUSTOMER  && senderId != roomInfo?.ownerId){
        return true;
      }
      
      // 其他情況顯示在左側
      return false;
    } catch (error) {
      logService.error('判斷消息顯示位置錯誤', { error });
      return false; // 預設顯示在左側
    }
  };

  // 使用 useRef 來存儲定時器引用，這樣可以在任何地方清除它
  const serviceTimerRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // 清除服務時長定時器的輔助函數
  const clearServiceTimer = useCallback(() => {
    if (serviceTimerRef.current) {
      clearInterval(serviceTimerRef.current);
      serviceTimerRef.current = undefined;
      logService.info('服務時長定時器已清除', { roomId });
    }
  }, [roomId]);

  // 共享的获取 agentServiced 数据函数
  const fetchAgentServicedData = useCallback(async () => {
    try {
      const agentResponse = await messageService.agentServiced({ roomId });

      if (agentResponse.success && agentResponse.data) {
        setAgentServicedData(agentResponse.data);
        return agentResponse.data;
      } else {
        setAgentServicedData(null);
        return null;
      }
    } catch (error) {
      logService.error('獲取 agentServiced 數據失敗', { error, roomId });
      setAgentServicedData(null);
      return null;
    }
  }, [roomId]);

  useEffect(() => {
    // 訂閱全局消息事件
    const subscribe = stateService.on('messageChanged', (event: any) => {
      const { action, roomId: roomId, message } = event || {};
      if (
        action !== 'add' || // 僅處理新增消息
        !message ||
        message.roomId !== roomId
      ) {
        return;
      }
      // 嚴格參照 sendMessage 組裝欄位
      const newMessage: IChatMessage = {
        id: message.id,
        content: message.content,
        time: message.time || (message.timestamp ? new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''),
        timestamp: message.timestamp || new Date().toISOString(),
        isUser: message.isUser ?? false,
        sender: {
          id: message.senderId || (message.sender && message.sender.id) || '',
          name: message.senderName || (message.sender && message.sender.name) || '',
          avatar: message.senderAvatar || (message.sender && message.sender.avatar) || ''
        },
        status: message.status || 'sent',
        type: message.type,
        metadata: message.metadata ? { ...message.metadata } : undefined,
        avatarSrc: message.avatarSrc,
        avatarText: message.avatarText,
        channel: message.channel,
        sequence: message.sequence // 添加sequence字段
      };
      dispatch(addMessage({ roomId, message: newMessage }));
    });

    // 卸載時取消訂閱
    return () => {
      if (subscribe) subscribe();
    };
  }, [roomId, clearServiceTimer]);

  // 订阅成员状态变化事件（已读已到状态）
  useEffect(() => {
    const unsubscribe = stateService.on('memberChanged', (event: any) => {
      const { action, roomId: eventRoomId, memberId, sequence } = event || {};

      // 只处理当前聊天室的事件
      if (eventRoomId !== roomId || !sequence || !roomInfo?.ownerId) {
        return;
      }

      logService.info('收到聊天室owner成员状态变化事件', {
        action,
        roomId: eventRoomId,
        memberId,
        ownerId: roomInfo.ownerId,
        sequence
      });

      // 根据不同的action更新消息状态
      if (action === MemberChangedAction.Received) {
        // 更新已到达状态：将sequence之前的消息标记为已到达
        dispatch(updateMessagesStatusBySequence({
          roomId,
          maxSequence: sequence,
          status: 'delivered',
          ownerId: roomInfo.ownerId
        }));
        logService.debug('实时更新当前用户发送消息的已到达状态', { roomId, maxSequence: sequence, ownerId: roomInfo.ownerId });
      } else if (action === MemberChangedAction.Readed) {
        // 更新已读状态：将sequence之前的消息标记为已读
        dispatch(updateMessagesStatusBySequence({
          roomId,
          maxSequence: sequence,
          status: 'read',
          ownerId: roomInfo.ownerId
        }));
        logService.debug('实时更新当前用户发送消息的已读状态', { roomId, maxSequence: sequence, ownerId: roomInfo.ownerId });
      }
    });

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [roomId, roomInfo?.ownerId, dispatch]);
  

  // 處理 roomChanged 訂閱
  useEffect(() => {
    const subscribe = stateService.on('roomChanged', async (data: {
      action: RoomAction;
      roomId?: string;
      room?: RoomVO | null;
    }) => {
      if ((data.action === RoomAction.update) && data.roomId && data.room) {
        const room = data.room;
        if (room.sessionStatus === SessionStatus.DistributeActive) {
          const result = await messageService.checkAndStartService(roomId);
          if (result.success && result.serviceResponse?.success) {
            Toast.show({
              content: t('已接入客服'),
              position: 'bottom'
            });
          }
        }
        if ((room.sessionStatus === SessionStatus.AgentActive || room.isTransfer) && room.agentId === authService.getUserId()) {
            // 異步處理開始服務邏輯
            const handleSessionStart = async () => {
              try {
                // 顯示服務頭圖
                setIsAgentActive(true);
                // 只有非 owner 用戶才顯示服務時長
                if (!isAdmin && room.serviceTime) {
                  // 使用 StartServiceResponse.serviceTime 作為開始時間
                  const startTime = room.serviceTime;

                  // 初始計算服務時長
                  const duration = calculateServiceDuration(startTime);
                  setServiceDuration(duration);

                  // 先清除任何現有的定時器
                  clearServiceTimer();

                  // 設置定時器，每秒更新一次服務時長
                  serviceTimerRef.current = setInterval(() => {
                    const updatedDuration = calculateServiceDuration(startTime);
                    setServiceDuration(updatedDuration);
                  }, 1000);
                } else {
                  // owner 用戶不顯示服務時長
                  setServiceDuration('00:00');
                }
              } catch (error) {
                logService.error('調用開始服務接口失敗', {
                  roomId,
                  error: error instanceof Error ? error.message : String(error)
                });
              }
            }
            // 立即執行異步處理
            handleSessionStart();
        }
        if (room.sessionStatus === SessionStatus.AgentStop || room.sessionStatus === SessionStatus.Timeout ) {
            // 立即清除定時器
            clearServiceTimer();
            setServiceDuration('00:00');
        }
      }
    });
    return () => subscribe();
  }, [dispatch]);
  
  // 处理失败消息点击事件
  const handleFailedMessageClick = (messageId: string) => {
    setSelectedFailedMessageId(messageId);
    setActionSheetVisible(true);
  };

  // 删除失败的消息
  const deleteMessage = async (messageId: string) => {
    if (!roomId) return;

    // 从Redux状态中删除消息
    dispatch(deleteMessageById({ roomId, messageId }));

    logService.info('删除失败消息', { messageId, roomId });
    Toast.show({ content: '消息已删除', position: 'bottom' });
  };

  // 重新发送失败的消息
  const retryMessage = async (messageId: string) => {
    if (!roomId) return;

    // 查找失败的消息
    let failedMessage: IChatMessage | null = null;
    for (const group of dateGroups) {
      const message = group.messages.find(msg => msg.id === messageId);
      if (message && message.status === 'failed') {
        failedMessage = message;
        break;
      }
    }

    if (!failedMessage) {
      logService.warn('未找到失败的消息', { messageId });
      return;
    }

    // 更新消息状态为发送中
    dispatch(updateMessageStatus({
      roomId,
      messageId,
      tempId: null,
      status: 'sending'
    }));

    // 重新发送消息
    try {
      const req: SendMessageRequest = {
        roomId,
        content: failedMessage.type === Type.Text ? {"text": failedMessage.content} : failedMessage.content,
        type: failedMessage.type || Type.Text,
        metadata: failedMessage.metadata
      };

      const res = await messageService.fetchChatMessageSend(req);

      // 更新消息状态
      dispatch(updateMessageStatus({
        roomId,
        messageId: res.data?.id || messageId,
        tempId: messageId,
        status: res.success ? 'sent' : 'failed',
        updatedId: messageId,
        sequence: res.data?.sequence
      }));

      if (!res.success) {
        Toast.show({ content: res.msg || '重新發送失敗', position: 'bottom' });
      } else {
        logService.info('消息重新发送成功', { messageId, newMessageId: res.data?.id });
      }
    } catch (err: any) {
      // 更新消息状态为失败
      dispatch(updateMessageStatus({
        roomId,
        messageId,
        tempId: null,
        status: 'failed'
      }));

      Toast.show({ content: '重新發送失敗', position: 'bottom' });
      logService.error('重新发送消息失败', { messageId, error: err });
    }
  };

  // 发送消息
  const sendMessage = async (
    content: string = inputValue.trim(),
    isImage: boolean = false,
    fileInfo?: { fileName: string; fileSize: number; fileType: string }
  ) => {
    if (!content && !isImage && !fileInfo) return;
    if (!roomId) return;

    const now = new Date();
    const formattedTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const tempId = ConstantUtil.uuid();
    const newMessage: IChatMessage = {
      id: tempId,
      content: content,
      time: formattedTime,
      timestamp: now.toISOString(),
      isUser: true,
      sender: {
        id: authService.getUserId() || '',
        name: '我',
        avatar: ''
      },
      status: 'sent', // 直接设置为已发送状态，取消转圈圈动画
      type: isImage ? Type.Image : fileInfo ? Type.File : Type.Text,
      metadata: fileInfo ? { ...fileInfo } : undefined,
      templateId: tempId
      // 注意：不设置sequence，等待WebSocket推送时由服务端提供真实的sequence
    };

    // 添加消息到Redux状态
    dispatch(addMessage({ roomId, message: newMessage }));

    if (!isImage && !fileInfo) {
      setInputValue('');
    }
    setShowExpandedToolbar(false);

    // 調用後端 API 發送消息
    try {
      // 发送请求时使用小写的 type，符合 API SendMessageRequest 接口定义
      const req: SendMessageRequest = {
        roomId,
        content:{"text":content},
        type: isImage ? Type.Image : fileInfo ? Type.File : Type.Text,
        metadata: fileInfo ? { ...fileInfo } : undefined
      };

      // 模拟发送失败（用于测试）- 如果消息内容包含"失败"则模拟失败
      if (content.includes('失败')) {
        throw new Error('模拟发送失败');
      }

      const res = await messageService.fetchChatMessageSend(req);

      // 更新消息状态
      dispatch(updateMessageStatus({
        roomId,
        messageId: res.data?.id || tempId,
        tempId,
        status: res.success ? 'sent' : 'failed',
        updatedId: tempId,
        sequence: res.data?.sequence
        // 注意：sequence字段将在WebSocket消息推送时更新
      }));

      if (!res.success) {
        Toast.show({ content: res.msg || '發送失敗', position: 'bottom' });
      }
    } catch (err: any) {
      // 更新消息状态为失败
      dispatch(updateMessageStatus({
        roomId,
        messageId: tempId,
        tempId,
        status: 'failed'
      }));

      Toast.show({ content: err?.message || '發送失敗', position: 'bottom' });
    }
  };
  
  // 注意：handleKeyPress 已被 ChatInput 組件內部處理，不再需要

  // 处理点击添加按钮
  const toggleExpandedToolbar = () => {
    setShowExpandedToolbar(!showExpandedToolbar);
  };

  // 处理点击附件按钮
  const handleAttachmentClick = () => {
    console.log('Attachment button clicked');
    // 触发文件选择对话框
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理点击名片按钮
  const handleBusinessCardClick = () => {
    console.log('Business card button clicked');
    // 添加发送名片的逻辑
  };

  // 处理拍照
  const handleTakePhoto = async () => {
    try {
      setIsUploading(true);
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera
      });
      
      if (image && image.dataUrl) {
        // 发送图片消息
        sendMessage(image.dataUrl, true);
      }
    } catch (error) {
      console.error('相机错误:', error);
      Toast.show({
        content: '無法使用相機，請檢查相機權限'
      });
    } finally {
      setIsUploading(false);
    }
  };

  // 处理选择图片
  const handleChoosePhoto = async () => {
    try {
      setIsUploading(true);
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Photos
      });
      
      if (image && image.dataUrl) {
        // 发送图片消息
        sendMessage(image.dataUrl, true);
      }
    } catch (error) {
      console.error('选择图片错误:', error);
      Toast.show({
        content: '無法選擇圖片'
      });
    } finally {
      setIsUploading(false);
    }
  };

  // 打開圖片查看器
  const handleImageClick = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    setImageViewerVisible(true);
  };

  // 關閉圖片查看器
  const handleImageViewerClose = () => {
    setImageViewerVisible(false);
  };

  const navigate = useNavigate();

  // 團隊聊天室跳轉
  const openMemberRoomClick = useCallback(() => {
    if (roomInfo?.serviceNumberId) {
      navigate(generateRoute.teamRoom(serviceNumber?.memberRoomId || ''));
    } else {
      Toast.show({ content: '無法獲取團隊聊天室ID', position: 'bottom' });
      logService.warn('無法獲取團隊聊天室ID', { roomInfo });
    }
  }, [navigate, roomInfo, serviceNumber]);

  const [serviceDuration, setServiceDuration] = useState<string>('00:00');
  const [isAgentActive, setIsAgentActive] = useState<boolean>(false);
  const [showStopServiceModal, setShowStopServiceModal] = useState<boolean>(false);

  // 共享的 agentServiced 数据状态
  const [agentServicedData, setAgentServicedData] = useState<{
    sessionStatus: string;
    isOwnerStop: boolean;
    startTime: number;
    serviceNumberAgentId: string;
    warned: boolean;
    result: boolean;
    transferFlag: boolean;
  } | null>(null);

  // 移动端下拉刷新防护相关状态
  const touchStartYRef = useRef(0);
  const isMobile = deviceService.isMobile();

  // 加载更多历史消息的处理函数
  const handleLoadMore = useCallback((isPreload: boolean = false) => {
    if (!roomId || !hasNextPage || isLoading || isLoadingHistory) {
      logService.debug('🚫 跳过加载更多历史消息', {
        roomId,
        hasNextPage,
        isLoading,
        isLoadingHistory,
        isPreload,
        currentPage,
        reason: !roomId ? '无roomId' :
                !hasNextPage ? '无更多页面' :
                isLoading ? '正在初始加载中' : '正在加载历史消息中'
      });
      return;
    }

    logService.info('🔄 触发加载更多历史消息', {
      roomId,
      currentPage,
      hasNextPage,
      isPreload,
      trigger: isPreload ? '預加載' : '滾動到頂部',
      platform: deviceService.getPlatform()
    });

    // 设置历史消息加载状态
    setIsLoadingHistory(true);

    dispatch(loadMoreMessages(roomId,isPreload));
  }, [roomId, hasNextPage, isLoading, isLoadingHistory, currentPage, dispatch]);

  // 使用优化的滚动Hook
  const {
    scrollElementRef: chatContentRef,
    handleScroll,
    scrollToBottomImmediate
  } = useOptimizedScroll({
    loadMoreThreshold: isMobile ? 200 : 150, // 移动端更大的触发距离
    loadMoreThresholdPercent: isMobile ? 0.15 : 0.1, // 移动端15%，桌面端10%
    debounceMs: 16, // 60fps
    onLoadMore: () => handleLoadMore(false), // 明確傳遞false表示這是用戶滾動觸發的加載
    maintainScrollPosition: true,
    autoScrollToBottom: false,
    onScroll: useCallback((state: any) => {
      // 可以在这里添加额外的滚动状态处理
      logService.debug('📊 滚动状态更新', {
        roomId,
        scrollTop: state.scrollTop,
        isNearTop: state.isNearTop,
        isNearBottom: state.isNearBottom,
        isScrolling: state.isScrolling
      });
    }, [roomId])
  });

  // 移动端触摸事件处理 - 防止默认的浏览器下拉刷新
  useEffect(() => {
    if (!isMobile || !chatContentRef.current) return;

    const element = chatContentRef.current;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartYRef.current = e.touches[0].clientY;
    };

    const handleTouchMove = (e: TouchEvent) => {
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - touchStartYRef.current;

      // 如果在顶部且向下拉动，阻止默认行为
      if (element.scrollTop === 0 && deltaY > 0) {
        e.preventDefault();
        logService.debug('阻止浏览器默认下拉刷新', {
          scrollTop: element.scrollTop,
          deltaY,
          reason: '聊天室内部滚动'
        });
      }
    };

    const handleTouchEnd = () => {
      touchStartYRef.current = 0;
    };

    // 添加事件监听器
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    // 清理函数
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isMobile, roomId]);

  // 監聽加載狀態變化，重置歷史消息加載狀態
  useEffect(() => {
    if (!isLoading && isLoadingHistory) {
      // 延迟一点时间再隐藏loading，确保用户能看到反馈
      const timer = setTimeout(() => {
        setIsLoadingHistory(false);
        logService.debug('✅ 历史消息加载完成，隐藏顶部loading', { roomId });
        
        // 歷史消息載入完成後，更新它們的已讀已到狀態
        if (roomId && roomInfo?.ownerId) {
          logService.debug('更新歷史消息的已讀已到狀態', { 
            roomId,
            ownerId: roomInfo.ownerId,
            來源: '歷史消息加載完成',
            說明: '包括預加載和滾動加載的消息' 
          });
          updateReadDeliveredStatusFromDB();
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isLoading, isLoadingHistory, roomId, roomInfo?.ownerId, updateReadDeliveredStatusFromDB]);

  // 监听消息变化，自动滚动到最新消息（仅在初次加载完成时）
  const hasScrolledToBottomRef = useRef<Record<string, boolean>>({});

  useEffect(() => {
    if (!isLoading && dateGroups.length > 0 && roomId && !hasScrolledToBottomRef.current[roomId]) {
      // 标记该房间已经滚动过，避免重复滚动
      hasScrolledToBottomRef.current[roomId] = true;

      // 延迟执行滚动，确保DOM已更新
      const timer = setTimeout(() => {
        scrollToBottomImmediate();
        logService.debug('📍 自动滚动到最新消息', {
          roomId,
          messageGroupsCount: dateGroups.length,
          totalMessages: dateGroups.reduce((total, group) => total + group.messages.length, 0),
          trigger: '初次进入聊天室'
        });
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isLoading, dateGroups.length, roomId, scrollToBottomImmediate]);

  // 獲取服務時長相關資訊
  useEffect(() => {
    if (!roomId || !roomInfo || roomInfo.type !== RoomType.Services) return;

    let isMounted = true; // 防止組件卸載後設置狀態

    const getServiceInfo = async () => {
      try {
        // 先清除任何現有的定時器
        clearServiceTimer();

        // 使用共享函數獲取 agentServiced 數據
        const agentData = await fetchAgentServicedData();

        if (!agentData || !isMounted) {
          setIsAgentActive(false);
          setServiceDuration('00:00');
          return;
        }

        const currentSessionStatus = agentData.sessionStatus;
        const isOwnerStop = agentData.isOwnerStop || false;
        const startTime = agentData.startTime;

        // 服務頭圖的顯示邏輯：
        // 1. 當 sessionStatus === "AgentActive" 都可見
        // 2. 當 sessionStatus === "AgentStop" && isOwnerStop === false && 用戶是 owner 可見
        // 3. 其他都不可見
        const shouldShowAgentActive =
          currentSessionStatus === SessionStatus.AgentActive ||
          (currentSessionStatus === SessionStatus.AgentStop && !isOwnerStop && isAdmin);

        // 服務時長的顯示邏輯：
        // 1. 當 sessionStatus === "AgentActive" && 用戶不是 owner 可見
        // 2. 其他都不可見
        const shouldShowServiceDuration =
          currentSessionStatus === SessionStatus.AgentActive && !isAdmin;

        if (shouldShowAgentActive && isMounted) {
          // 設置 AgentActive 狀態
          setIsAgentActive(true);

          // 根據服務時長顯示邏輯決定是否顯示時長
          if (shouldShowServiceDuration && startTime) {
            // 初始計算服務時長
            const duration = calculateServiceDuration(startTime);
            setServiceDuration(duration);

            // 設置定時器，每秒更新一次服務時長
            serviceTimerRef.current = setInterval(() => {
              if (isMounted) {
                const updatedDuration = calculateServiceDuration(startTime);
                setServiceDuration(updatedDuration);
              }
            }, 1000);
          } else {
            // 不顯示服務時長
            setServiceDuration('00:00');
          }
        } else {
          setIsAgentActive(false);
          setServiceDuration('00:00');
        }
      } catch (error) {
        if (isMounted) {
          logService.error('獲取服務時長資訊失敗', { error, roomId });
          setIsAgentActive(false);
          setServiceDuration('00:00');
        }
      }
    };

    getServiceInfo();

    // 組件卸載時清除定時器
    return () => {
      isMounted = false;
      clearServiceTimer();
    };
  }, [roomId, roomInfo?.type, clearServiceTimer]); // 移除 fetchAgentServicedData 依赖，避免重复执行

  // 額外的保護措施：當服務時長被設置為 '00:00' 時，確保定時器被清除
  useEffect(() => {
    if (serviceDuration === '00:00') {
      clearServiceTimer();
    }
  }, [serviceDuration, clearServiceTimer]);

  // 處理點擊服務頭像
  const handleServiceAvatarClick = async () => {
    try {
      // 獲取當前登錄用戶ID
      const loginUserId = stateService.loginUser()?.id;

      if (!loginUserId) {
        Toast.show({
          content: '無法獲取用戶資訊',
          position: 'bottom',
        });
        return;
      }

      // 使用共享數據或重新獲取最新數據
      let currentAgentData = agentServicedData;
      if (!currentAgentData) {
        currentAgentData = await fetchAgentServicedData();
      }

      if (!currentAgentData) {
        Toast.show({
          content: '無法獲取服務資訊',
          position: 'bottom',
        });
        return;
      }

      const serviceNumberAgentId = currentAgentData.serviceNumberAgentId;

      // 檢查點擊權限：當 isAgentActive && !isAdmin && loginUserId !== serviceNumberAgentId 時無法點擊
      if (isAgentActive && !isAdmin && loginUserId !== serviceNumberAgentId) {
        Toast.show({
          content: '您沒有權限操作此服務',
          position: 'bottom',
        });
        return;
      }

      // 權限檢查通過，顯示停止服務模態框
      setShowStopServiceModal(true);
    } catch (error) {
      logService.error('處理服務頭像點擊失敗', { error, roomId });
      Toast.show({
        content: '操作失敗，請稍後再試',
        position: 'bottom',
      });
    }
  };

  // 處理結束服務
  const handleStopService = async () => {
    if (!roomId) return;

    try {
      const response = await messageService.agentStop({ roomId });
      if (response.success) {
        Toast.show({
          content: '服務已結束',
          position: 'bottom',
        });
        // 立即清除定時器並設置狀態
        clearServiceTimer();
        setIsAgentActive(false);
        setServiceDuration('00:00');

        // 立即更新Redux中的房間sessionStatus
        dispatch(updateRoomSessionStatus({
          roomId,
          sessionStatus: SessionStatus.AgentStop
        }));

        // 同時更新本地數據庫中的sessionStatus
        try {
          const currentRoom = await roomDao.getRoomById(roomId);
          if (currentRoom) {
            const updatedRoom = { ...currentRoom, sessionStatus: SessionStatus.AgentStop };
            await roomDao.upsertRooms([updatedRoom]);
            logService.info('已更新本地數據庫中的房間sessionStatus', { roomId, sessionStatus: SessionStatus.AgentStop });
          }
        } catch (error) {
          logService.error('更新本地數據庫sessionStatus失敗', { error, roomId });
        }

        // 延遲一點時間後重新獲取服務信息，確保後端狀態已更新
        setTimeout(async () => {
          try {
            const agentData = await fetchAgentServicedData();

            if (agentData) {
              const currentSessionStatus = agentData.sessionStatus;
              const isOwnerStop = agentData.isOwnerStop || false;

              // 重新檢查服務頭圖顯示邏輯
              const shouldShowAgentActive =
                currentSessionStatus === SessionStatus.AgentActive ||
                (currentSessionStatus === SessionStatus.AgentStop && !isOwnerStop && isAdmin);

              setIsAgentActive(shouldShowAgentActive);
              // 服務結束後，服務時長應該始終為 '00:00'
              setServiceDuration('00:00');
            }
          } catch (error) {
            logService.error('重新獲取服務狀態失敗', { error, roomId });
          }
        }, 500); // 延遲 500ms
      } else {
        Toast.show({
          content: response.msg || '結束服務失敗',
          position: 'bottom',
        });
      }
    } catch (error) {
      logService.error('結束服務失敗', { error, roomId });
      Toast.show({
        content: '結束服務失敗',
        position: 'bottom',
      });
    } finally {
      setShowStopServiceModal(false);
    }
  };

  return (
    <div className={getClassName('chat-room-page')}>
      {/* 使用共享的头部组件 */}
      <ChatRoomHeader 
        type={type}
        title={title}
        onBackClick={onBackClick || (() => {})}
        badgeCount={type === ChatRoomType.MY ? 4 : 0}
      />
      
      {/* Customer Tasks - Only shown for customer chat */}
      {showTasksBar && (
        <div className={getClassNameWithAdminCheck('tasks-container', 'customer-tasks-container')}>
          <TasksPanel tasks={mockTasks} onTaskClick={handleTaskClick} />
        </div>
      )}
      
      {/* Chat Content */}
      <div
        ref={chatContentRef}
        className={`${getClassNameWithAdminCheck('chat-content')} ${dateGroups.length === 0 && !isLoading && !error ? 'chat-content-empty' : ''}`}
        onScroll={handleScroll}
      >
        {/* 历史消息加载指示器 - 显示在顶部 */}
        {isLoadingHistory && dateGroups.length > 0 && (
          <div className="history-loading-indicator">
            <div className="history-loading-spinner">
              <div className="spinner-dot"></div>
              <div className="spinner-dot"></div>
              <div className="spinner-dot"></div>
            </div>
            <span className="history-loading-text">{t('正在加載歷史消息...')}</span>
          </div>
        )}

        {isLoading && dateGroups.length === 0 ? (
          <div className="chat-skeleton-container">
            {/* 模拟客服消息 */}
            <div className="message-skeleton">
              <div className="skeleton-avatar"></div>
              <div className="skeleton-content">
                <div className="skeleton-line skeleton-line-long"></div>
                <div className="skeleton-line skeleton-line-medium"></div>
              </div>
            </div>

            {/* 模拟用户回复 */}
            <div className="message-skeleton message-skeleton-right">
              <div className="skeleton-content">
                <div className="skeleton-line skeleton-line-short"></div>
              </div>
              <div className="skeleton-avatar"></div>
            </div>

            {/* 模拟客服回复 */}
            <div className="message-skeleton">
              <div className="skeleton-avatar"></div>
              <div className="skeleton-content">
                <div className="skeleton-line skeleton-line-medium"></div>
                <div className="skeleton-line skeleton-line-long"></div>
                <div className="skeleton-line skeleton-line-short"></div>
              </div>
            </div>

            {/* 模拟用户消息 */}
            <div className="message-skeleton message-skeleton-right">
              <div className="skeleton-content">
                <div className="skeleton-line skeleton-line-medium"></div>
                <div className="skeleton-line skeleton-line-short"></div>
              </div>
              <div className="skeleton-avatar"></div>
            </div>

            {/* 模拟客服消息 */}
            <div className="message-skeleton">
              <div className="skeleton-avatar"></div>
              <div className="skeleton-content">
                <div className="skeleton-line skeleton-line-short"></div>
                <div className="skeleton-line skeleton-line-long"></div>
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="chat-error">{error}</div>
        ) : dateGroups.length === 0 ? (
          <div className="chat-empty-container">
            <ErrorBlock
              status='empty'
              title={!hasNextPage ? t('尚無任何訊息') : t('消息正在載入中...')}
              description=''
            />
          </div>
        ) : (
          dateGroups.map((group, groupIndex) => (
            <React.Fragment key={`date-${groupIndex}`}> 
              {/* Date Header */}
              <div className={getClassName('chat-date-header')}>
                <span>{group.date}</span>
              </div>
              {/* Messages */}
              {group.messages.map((message, index) => {
                const showOnRight = shouldShowOnRight(message.sender.id, type);
                const showAvatar = !showOnRight && shouldShowAvatar(group.messages, index);
                const parsedMessage = messageUtil.parseMessageContent(message, senderInfo, bossServiceNumberOwnerId);
                const isSystemMessage = parsedMessage.isSystem;

                // 如果是系統消息，則使用特殊的樣式
                if (isSystemMessage) {
                  return (
                    <div key={message.id} className={`${getClassName('system-message')}`}>
                      <div className={`${getClassName('chat-date-header')}`}>
                        <span>{parsedMessage.text}</span>
                      </div>
                    </div>
                  );
                }

                return (
                  <div 
                    key={message.id} 
                    className={`${getClassName('chat-message-row')} ${showOnRight ? 'user' : ''}`}
                  >
                    {!showOnRight && (
                      <div className={`${getClassName('chat-avatar')}`} style={{ visibility: showAvatar ? 'visible' : 'hidden' }}>
                        <AvatarImage 
                            avatarId={senderInfo[message.sender.id]?.avatarId || null} 
                            size={40}
                            fallbackSrc={message.sender.avatar || ''}
                            className="chat-avatar-img"
                            name={senderInfo[message.sender.id]?.name || message.sender.name}
                          />
                        {type === ChatRoomType.CUSTOMER && (() => {
                          // 取得 channel，優先 appointChannel
                          const channel = message.channel;
                          if (channel && channelIconMap[channel]) {
                            return (
                              <div className="customer-chat-avatar-badge">
                                <img src={channelIconMap[channel]} alt={channel + ' icon'} width={16} height={16} />
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    )}
                    <div className={`${getClassName('chat-message-container')} ${showOnRight ? 'user' : ''}`}>
                      {/* 顯示發送者姓名 - 只在客户聊天室中才显示，且senderId不是当前用户 */}
                      {type === ChatRoomType.CUSTOMER && message.sender.id !== stateService.loginUser()?.id && showOnRight && (
                        <div className={`${getClassName('chat-sender-name')} ${showOnRight ? 'user' : ''}`}>
                          {businessNumberName ?
                            `${businessNumberName}-${senderInfo[message.sender.id]?.name || message.sender.name}` :
                            (senderInfo[message.sender.id]?.name || message.sender.name)
                          }
                        </div>
                      )}
                      <div className={`${getClassName('chat-message-wrapper')} ${showOnRight ? 'user' : ''}`}>
                        {showOnRight && (
                          <div className={`${getClassName('chat-message-time')}`}>
                            {/* 消息状态指示器 - 只在用户消息的右侧显示 */}
                            <MessageStatusIndicator
                              status={message.status}
                              size="small"
                              onFailedClick={message.status === 'failed' ? () => handleFailedMessageClick(message.id) : undefined}
                            />
                            <div>
                              {formatTime(message.timestamp)}
                            </div>
                          </div>
                        )}
                        <div className={`${getClassName('chat-message-bubble')} ${showOnRight ? 'user' : ''} ${showOnRight && !isAdmin ? 'non-admin' : ''}`}>
                          {message.type === Type.Image ? (
                            <div className="chat-image-container">
                              <Image
                                src={message.content}
                                alt="圖片訊息"
                                fit="cover"
                                className="chat-image-message"
                                onClick={() => handleImageClick(message.content)}
                                // onLoad={() => scrollToBottom()}
                              />
                            </div>
                          ) : message.type === Type.File && message.metadata ? (
                            <div className="file-attachment" data-file-type={message.metadata.fileType || ''}>
                              <div className="file-icon">📎</div>
                              <div className="file-details">
                                <div className="file-name">{message.metadata.fileName}</div>
                                <div className="file-size">{(message.metadata.fileSize / 1024).toFixed(1)} KB</div>
                              </div>
                            </div>
                          ) : (
                            <LinkifiedText text={parsedMessage.text} />
                          )}
                        </div>
                        
                        {!showOnRight && (
                          <div className={`${getClassName('chat-message-time')}`}>
                            {formatTime(message.timestamp)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </React.Fragment>
          ))
        )}


      </div>

      {/* Team Selector - Only shown for customer chat */}
      {showTeamSelector && (
        <div
          className={getClassNameWithAdminCheck('team-selector', 'customer-chat-team-selector')}
        >
          <div className="base-team-button customer-chat-team-button" onClick={openMemberRoomClick}>
            <span className="base-team-text customer-chat-team-text">團隊聊天室</span>
            {teamUnreadCount > 0 && (
              <div className="base-team-badge customer-chat-team-badge">{teamUnreadCount}</div>
            )}
          </div>
          {isAgentActive && (
            <div 
              className="base-team-avatar customer-chat-team-avatar"
              onClick={handleServiceAvatarClick}
            >
              <AvatarImage 
                avatarId={roomInfo?.serviceNumberId && serviceNumberAvatarIds[roomInfo.serviceNumberId] ? 
                  serviceNumberAvatarIds[roomInfo.serviceNumberId] : null} 
                size={28}
                fallbackSrc={teamAvatarSrc}
                className="base-team-avatar-img customer-chat-team-avatar-img"
                name={title || "團隊"}
              />
              <div className="base-team-status customer-chat-team-status"></div>
            </div>
          )}
          <div className="service-duration-display" style={{ display: serviceDuration !== '00:00' ? 'block' : 'none' }}>
            {serviceDuration}
          </div>
        </div>
      )}

      {/* 結束服務確認Modal */}
      <Modal
        visible={showStopServiceModal}
        title="是否結束服務"
        content={
          <div className="horizontal-modal-actions">
            <div 
              className="horizontal-modal-btn horizontal-modal-btn-cancel" 
              onClick={() => setShowStopServiceModal(false)}
            >
              取消
            </div>
            <div 
              className="horizontal-modal-btn horizontal-modal-btn-confirm" 
              onClick={handleStopService}
            >
              確認
            </div>
          </div>
        }
        showCloseButton={false}
        closeOnMaskClick
        onClose={() => setShowStopServiceModal(false)}
        actions={[]}
      />
      
      {/* 圖片查看器 */}
      {imageViewerVisible && (
        <ImageViewer
          image={currentImage}
          visible={imageViewerVisible}
          onClose={handleImageViewerClose}
          renderFooter={() => (
            <div className="image-viewer-footer">
              <div className="image-viewer-close" onClick={handleImageViewerClose}>
                關閉
              </div>
            </div>
          )}
        />
      )}

      {/* Chat Input */}
      <div className={getClassName('chat-input-container')}>
        <div className={`${getClassName('chat-input-toolbar')} ${showExpandedToolbar ? 'expanded' : ''}`}>
          <div className="chat-icon toolbar-icon add-icon" onClick={toggleExpandedToolbar}>
            <AddOutline />
          </div>
          <div className="chat-icon toolbar-icon camera-icon" onClick={handleTakePhoto}>
            <CameraOutline />
          </div>
          <div className="chat-icon toolbar-icon pictures-icon" onClick={handleChoosePhoto}>
            <PictureOutline />
          </div>
          <ChatInput
            className={getClassName('chat-input')}
            placeholder="Aa"
            value={inputValue}
            onChange={handleInputChange}
            onSend={() => sendMessage()}
            disabled={isUploading}
            maxRows={4}
          />
          <div className="chat-icon toolbar-icon smile-icon">
            <SmileOutline />
          </div>
          {inputValue.trim() ? (
            <div className="chat-icon toolbar-icon send-icon" onClick={() => sendMessage()}>
              <RiSendPlane2Fill />
            </div>
          ) : (
            <div className="chat-icon toolbar-icon audio-icon">
              <AudioOutline />
            </div>
          )}
        </div>
      </div>
      
      {/* Expanded Toolbar */}
      {showExpandedToolbar && (
        <div className="expanded-toolbar">
          <div className="expanded-toolbar-row">
            <div className="expanded-toolbar-item" onClick={handleAttachmentClick}>
              <div className="expanded-toolbar-icon-container">
                <img src={paperclipIcon} alt="Attachment" className="expanded-toolbar-icon" />
              </div>
              <span className="expanded-toolbar-text">附件</span>
            </div>
            <div className="expanded-toolbar-item" onClick={handleBusinessCardClick}>
              <div className="expanded-toolbar-icon-container">
                <img src={idCardIcon} alt="Business Card" className="expanded-toolbar-icon" />
              </div>
              <span className="expanded-toolbar-text">發送名片</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Hidden file input for native web file selection fallback */}
      <input 
        type="file" 
        ref={fileInputRef} 
        style={{ display: 'none' }} 
        accept="image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain,video/*,audio/*" 
        onChange={(e) => {
          if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            const fileType = file.type;
            const fileName = file.name;
            
            // 图片处理
            if (fileType.startsWith('image/')) {
              const reader = new FileReader();
              reader.onload = (event) => {
                if (event.target && event.target.result) {
                  sendMessage(event.target.result as string, true);
                }
              };
              reader.readAsDataURL(file);
            } 
            // 其他文件类型 - 显示为文件链接
            else {
              // 准备文件信息
              const fileInfo = {
                fileName: fileName,
                fileSize: file.size,
                fileType: fileType
              };
              
              // 创建文件消息内容
              const fileMessage = `📎 附件: ${fileName} (${(file.size / 1024).toFixed(1)} KB)`;
              
              // 发送文件消息
              sendMessage(fileMessage, false, fileInfo);
              
              // 在实际应用中，这里应该有上传文件到服务器的逻辑
              console.log('上传文件:', file);
              Toast.show({
                content: '附件已添加',
                position: 'bottom',
              });
            }
            
            // 清空文件选择，以便再次选择同一文件
            e.target.value = '';
          }
        }}
      />

      {/* 消息操作菜单 */}
      <MessageActionSheet
        visible={actionSheetVisible}
        onClose={() => setActionSheetVisible(false)}
        onRetry={() => {
          if (selectedFailedMessageId) {
            retryMessage(selectedFailedMessageId);
          }
        }}
        onDelete={() => {
          if (selectedFailedMessageId) {
            deleteMessage(selectedFailedMessageId);
          }
        }}
      />
    </div>
  );
};

export default ChatRoom; 