import React from 'react';
import { Button, Avatar, List, Dialog, Toast } from 'antd-mobile';

import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../../app/hooks';
import { logout } from '../../../app/slices/authSlice';
import { clearTenantState } from '../../../app/slices/tenantSlice';
import { logService } from '../../../services/system/logService';
import { ConstantUtil } from '../../../utils/constantUtil';
import userAvatar from '../../../assets/images/avatars/user-avatar.png';
import { 
  UserOutline,
  InformationCircleOutline, 
  ExclamationCircleOutline 
} from 'antd-mobile-icons';
import { ROUTE_LOGIN } from '../../../config/app/routes';

interface AccountTabProps {
  onScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

const AccountTab: React.FC<AccountTabProps> = ({ onScroll }) => {

  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { account: user } = useAppSelector((state) => state.auth);

  /**
   * 顯示用戶詳細資訊
   */
  const showUserDetails = () => {
    if (!user) return;
    
    const details = [
      `用戶ID: ${user.accountId}`,
      `姓名: ${user.name}`,
      `手機: ${ConstantUtil.formatPhoneNumber(user.countryCode || '', user.mobile || '')}`,
      `登入類型: ${ConstantUtil.formatLoginType(user.type || '')}`,
      `線上ID: ${user.onlineId}`,
      `登入狀態: ${ConstantUtil.formatLoginStatus(user.loginStatus)}`,
    ];
    
    if (user.email) {
      details.splice(3, 0, `郵箱: ${user.email}`);
    }
    
    Dialog.alert({
      title: '用戶詳細資訊',
      content: (
        <div style={{ textAlign: 'left', fontSize: '14px', lineHeight: '1.5' }}>
          {details.map((detail, index) => (
            <div key={index} style={{ marginBottom: '8px' }}>
              {detail}
            </div>
          ))}
        </div>
      ),
      confirmText: '確定'
    });
  };

  /**
   * 處理登出
   */
  const handleLogout = async () => {
    try {
      const result = await Dialog.confirm({
        title: '確認登出',
        content: '您確定要登出嗎？',
        confirmText: '確認',
        cancelText: '取消',
      });

      if (result) {
        logService.info('用戶點擊登出按鈕');
        await dispatch(logout());
        dispatch(clearTenantState());
        Toast.show({
          content: '已成功登出',
          duration: 2000,
        });
        // 跳轉到登入頁面（加微小延遲，確保 Redux 狀態已重置）
        setTimeout(() => {
        navigate(ROUTE_LOGIN, { replace: true });
        }, 10);
      }
    } catch (error) {
      logService.error('登出過程發生錯誤', error as Error);
    }
  };

  return (
    <div className="tab-content" onScroll={onScroll} style={{
      padding: '16px',
      backgroundColor: '#f7f8fa',
      height: '100%',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '16px',
      boxSizing: 'border-box',
      overflowY: 'auto'
    }}>
      {/* 用戶資訊卡片 */}
      <div style={{
        backgroundColor: '#fff',
        borderRadius: '16px',
        padding: '24px',
        display: 'flex',
        alignItems: 'center',
        boxShadow: '0 2px 12px rgba(0,0,0,0.05)',
        width: '100%',
        boxSizing: 'border-box'
      }}>
        <div style={{
          position: 'relative',
          marginRight: '20px'
        }}>
          <Avatar 
            src={user?.avatar || userAvatar} 
            style={{ 
              '--size': '70px',
              border: '2px solid #f0f0f0'
            }} 
          />
          {user?.loginStatus && (
            <span style={{
              position: 'absolute',
              bottom: '3px',
              right: '3px',
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: user.loginStatus === ConstantUtil.LOGIN_STATUS_ONLINE ? '#52c41a' : '#999',
              border: '2px solid #fff'
            }}></span>
          )}
        </div>
        <div style={{ flex: 1 }}>
          <div style={{ 
            fontSize: '22px', 
            fontWeight: 'bold', 
            color: '#333',
            marginBottom: '6px'
          }}>
            {user?.name || '用戶'}
          </div>
          <div style={{ 
            fontSize: '15px', 
            color: '#666',
            marginBottom: '8px'
          }}>
            {user?.mobile ? ConstantUtil.formatPhoneNumber(user.countryCode || '', user.mobile || '') : 
             user?.email || 'ID: ' + (user?.accountId || 'unknown')}
          </div>
          <div style={{
            padding: '4px 12px',
            borderRadius: '20px',
            backgroundColor: user?.loginStatus === ConstantUtil.LOGIN_STATUS_ONLINE ? '#e6f7e9' : '#f5f5f5',
            color: user?.loginStatus === ConstantUtil.LOGIN_STATUS_ONLINE ? '#52c41a' : '#999',
            display: 'inline-block',
            fontSize: '12px',
            fontWeight: '500'
          }}>
            {ConstantUtil.formatLoginStatus(user?.loginStatus || '')}
          </div>
        </div>
      </div>

      {/* 功能列表 */}
      <List style={{
        backgroundColor: '#fff',
        borderRadius: '16px',
        overflow: 'hidden',
        boxShadow: '0 2px 12px rgba(0,0,0,0.05)',
        width: '100%',
        boxSizing: 'border-box'
      }}>
        <List.Item 
          prefix={<UserOutline style={{ fontSize: 22, color: '#3b82f6' }} />}
          onClick={showUserDetails}
          arrow
          style={{ padding: '16px' }}
        >
          個人資料
        </List.Item>
      {/*  <List.Item
          prefix={<SetOutline style={{ fontSize: 22, color: '#6b7280' }} />}
          onClick={() => Toast.show({ content: '功能開發中...' })}
          arrow
          style={{ padding: '16px' }}
        >
          設定
        </List.Item>
        <List.Item
          prefix={<QuestionCircleOutline style={{ fontSize: 22, color: '#10b981' }} />}
          onClick={() => Toast.show({ content: '功能開發中...' })}
          arrow
          style={{ padding: '16px' }}
        >
          幫助與支援
        </List.Item>*/}
        <List.Item
          prefix={<InformationCircleOutline style={{ fontSize: 22, color: '#6366f1' }} />}
          onClick={() => {
            Dialog.alert({
              title: '關於我們',
              content: (
                <div style={{ textAlign: 'left', fontSize: '14px', lineHeight: '1.5' }}>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>{ConstantUtil.APP_NAME}</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    版本: {ConstantUtil.VERSION}
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    構建號: {ConstantUtil.BUILD}
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    應用ID: {ConstantUtil.APP_ID}
                  </div>
{/*                  <div style={{ color: '#666', fontSize: '12px', marginTop: '12px' }}>
                    © 2024 Aile Code. All rights reserved.
                  </div>*/}
                </div>
              ),
              confirmText: '確定'
            });
          }}
          arrow
          style={{ padding: '16px' }}
        >
          關於我們
        </List.Item>
      </List>

      {/* 登出按鈕 */}
      <Button
        block
        style={{
          marginBottom: '16px',
          borderRadius: '12px',
          height: '48px',
          fontSize: '16px',
          fontWeight: '500',
          backgroundColor: 'white',
          border: '1px solid #ef4444',
          color: '#ef4444',
          boxShadow: '0 2px 8px rgba(239,68,68,0.1)',
          width: '100%',
          boxSizing: 'border-box'
        }}
        onClick={handleLogout}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
          <ExclamationCircleOutline />
          登出
        </div>
      </Button>
    </div>
  );
};

export default AccountTab; 