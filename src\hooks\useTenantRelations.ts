import { useEffect, useState, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import {
  fetchTenantRelations,
  selectTenantRelations,
  selectTenantRelationsStatus,
  selectTenantRelationsLastFetched
} from '@/app/slices/tenantSlice';
import { RelationTenantVO } from '@/services/core/tenant/tenantTypes';
import { logService } from '@/services/system/logService';

interface UseTenantRelationsOptions {
  /** 是否在組件掛載時自動獲取數據 */
  autoFetch?: boolean;
  /** 強制刷新緩存 */
  forceRefresh?: boolean;
  /** 是否顯示加載狀態（僅在列表為空時顯示） */
  showLoadingOnEmpty?: boolean;
}

/**
 * 租戶關係列表 Hook
 * 
 * 提供租戶關係列表的數據訪問、加載狀態和刷新功能
 */
export const useTenantRelations = (options: UseTenantRelationsOptions = {}) => {
  const {
    autoFetch = true,
    forceRefresh = false,
    showLoadingOnEmpty = true
  } = options;

  const dispatch = useAppDispatch();

  // 從 Redux store 獲取數據和狀態
  const tenantRelations = useAppSelector(selectTenantRelations);
  const status = useAppSelector(selectTenantRelationsStatus);
  const lastFetched = useAppSelector(selectTenantRelationsLastFetched);
  const currentTenantId = useAppSelector(state => state.tenant.currentTenantId);

  // 控制是否顯示加載狀態的額外標誌
  const [showLoading, setShowLoading] = useState<boolean>(false);

  // 用於追蹤當前租戶 ID 變化的 ref
  const prevTenantIdRef = useRef<string | null>(currentTenantId);
  
  // 計算衍生狀態
  const isLoading = status === 'loading' && showLoading;
  const isError = status === 'failed';
  const isSuccess = status === 'succeeded';
  const isEmpty = tenantRelations.length === 0;
  
  // 定義刷新方法
  const refreshTenantRelations = (refresh = true, showLoadingIndicator = false) => {
    logService.info('手動刷新租戶關係列表', { refresh, showLoadingIndicator });

    // 設置是否顯示加載指示器
    setShowLoading(showLoadingIndicator);

    return dispatch(fetchTenantRelations({ forceRefresh: refresh }));
  };

  // 定義強制刷新方法，用於確保獲取最新的服務器狀態
  const forceRefreshFromServer = (showLoadingIndicator = false) => {
    logService.info('強制從服務器刷新租戶關係列表');

    setShowLoading(showLoadingIndicator);

    return dispatch(fetchTenantRelations({ forceRefresh: true }));
  };

  // 在組件掛載時獲取數據
  useEffect(() => {
    if (autoFetch) {
      // 智能缓存策略：检查数据新鲜度
      const now = Date.now();
      const dataAge = lastFetched ? now - lastFetched : Infinity;
      const hasValidData = !isEmpty && dataAge < 5 * 60 * 1000; // 5分钟内的数据认为是新鲜的

      // 只有當列表為空且設置了顯示加載狀態時才顯示加載指示器
      const shouldShowLoading = isEmpty && showLoadingOnEmpty;

      // 如果有新鲜数据，跳过请求
      if (hasValidData && !forceRefresh) {
        logService.debug('租戶關係列表 Hook 使用現有新鮮數據', {
          dataAge: Math.floor(dataAge / 1000 / 60) + '分钟',
          count: tenantRelations.length
        });
        return;
      }

      logService.debug('租戶關係列表 Hook 初始化加載數據', {
        forceRefresh,
        shouldShowLoading,
        hasExistingData: !isEmpty,
        dataAge: dataAge === Infinity ? '无数据' : Math.floor(dataAge / 1000 / 60) + '分钟'
      });

      setShowLoading(shouldShowLoading);
      dispatch(fetchTenantRelations({ forceRefresh }));
    }
  }, [dispatch, autoFetch, forceRefresh, isEmpty, showLoadingOnEmpty, lastFetched, tenantRelations.length]);

  // 監聽當前租戶 ID 變化，但不自動刷新（避免與 applyTenantToken 中的狀態更新衝突）
  useEffect(() => {
    const prevTenantId = prevTenantIdRef.current;

    // 如果租戶 ID 發生變化，只記錄日誌，不自動刷新
    if (currentTenantId && prevTenantId && currentTenantId !== prevTenantId) {
      logService.info('檢測到租戶切換，租戶關係列表狀態已通過 Redux action 更新', {
        prevTenantId,
        currentTenantId
      });
    }

    // 更新 ref
    prevTenantIdRef.current = currentTenantId;
  }, [currentTenantId]);

  // 返回數據和操作方法
  return {
    // 數據
    tenantRelations,
    // 狀態
    status,
    isLoading,
    isError,
    isSuccess,
    isEmpty,
    lastFetched,
    // 操作方法
    refreshTenantRelations,
    forceRefreshFromServer,
    // 工具方法
    getActiveTenant: (): RelationTenantVO | undefined => {
      return tenantRelations.find(t => t.isLastTenant);
    },
    getTenantById: (id: string): RelationTenantVO | undefined => {
      return tenantRelations.find(t => t.id === id);
    },
  };
};

export default useTenantRelations; 