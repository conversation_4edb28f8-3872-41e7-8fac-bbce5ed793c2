import { useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import { fetchTenantRelations, selectTenantRelations, selectTenantRelationsLastFetched } from '@/app/slices/tenantSlice';
import { logService } from '@/services/system/logService';

/**
 * 侧边栏预加载 Hook
 * 专门用于优化侧边栏的数据预加载策略
 */
export const useSidebarPreloader = () => {
  const dispatch = useAppDispatch();
  const tenantRelations = useAppSelector(selectTenantRelations);
  const lastFetched = useAppSelector(selectTenantRelationsLastFetched);
  const preloadedRef = useRef(false);

  useEffect(() => {
    // 只在应用启动后执行一次预加载
    if (preloadedRef.current) return;
    
    const preloadTeamData = async () => {
      try {
        const now = Date.now();
        const hasData = tenantRelations.length > 0;
        const dataAge = lastFetched ? now - lastFetched : Infinity;
        const isDataFresh = dataAge < 10 * 60 * 1000; // 10分钟内认为是新鲜的
        
        if (hasData && isDataFresh) {
          logService.info('侧边栏预加载：数据已存在且新鲜，跳过预加载', {
            count: tenantRelations.length,
            dataAge: Math.floor(dataAge / 1000 / 60) + '分钟'
          });
          return;
        }
        
        logService.info('侧边栏预加载：开始预加载团队数据', {
          hasData,
          dataAge: dataAge === Infinity ? '无数据' : Math.floor(dataAge / 1000 / 60) + '分钟'
        });
        
        // 静默预加载，不显示加载状态
        await dispatch(fetchTenantRelations({ forceRefresh: !hasData })).unwrap();
        
        logService.info('侧边栏预加载：团队数据预加载完成');
      } catch (error) {
        logService.error('侧边栏预加载失败', { error });
      }
    };
    
    // 减少延迟时间，快速预加载
    const timer = setTimeout(preloadTeamData, 100);
    preloadedRef.current = true;
    
    return () => clearTimeout(timer);
  }, [dispatch, tenantRelations.length, lastFetched]);

  return {
    isPreloaded: preloadedRef.current,
    hasData: tenantRelations.length > 0
  };
};

export default useSidebarPreloader;
