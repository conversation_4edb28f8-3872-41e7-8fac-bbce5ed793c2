/* RoomListTab 主容器 */
.roomlist-tab-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #FFFFFF;
  position: relative;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  background-color: #FFFFFF;
  min-height: calc(100vh - 180px);
  max-height: calc(100vh - 140px); /* 当user-info隐藏时的最大高度 */
  -webkit-overflow-scrolling: touch; /* 为iOS添加惯性滚动效果 */
  touch-action: pan-y; /* 允许垂直方向上的触摸滚动 */
  position: relative; /* 确保滚动区域定位正确 */
  user-select: none; /* 防止文本选择干扰拖动 */
  overscroll-behavior: contain; /* 防止滚动边界引起父元素滚动 */
}

/* Mouse drag styling */
.chat-list.grabbing {
  cursor: grabbing !important;
  touch-action: pan-y !important; /* 确保拖动时也允许垂直滚动 */
}

/* 移动端滚动优化 */
.chat-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条以优化移动端体验 */
}

/* 确保在移动端触摸响应 */
.chat-list {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 修复iOS中的滚动问题 */
@supports (-webkit-overflow-scrolling: touch) {
  .chat-list {
    overflow-y: scroll;
  }
}

.chat-item {
  padding: 0;
}

.chat-item .adm-list-item-content {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.chat-item .adm-list-item-content:hover {
  background-color: #F5F7FA;
}

.chat-item .adm-list-item-content:active {
  background-color: #E6F0FF;
}
.chat-item .adm-list-item-content-prefix .chat-avatar{
  font-size: 13px !important;
}
/* Overrides for Ant Design Mobile List */
.chat-list-inner {
  --adm-color-background: transparent;
  --adm-color-border: #EEEEEE;
  background-color: white;
}

.chat-list-inner .adm-list-body {
  border: none;
}

/* First item needs top padding */
.chat-list-inner .adm-list-item:first-child {
  padding-top: 0px;
}
.adm-swipe-action:first-child{
  padding-top: 12px;
}

.chat-list-inner .adm-list-item-content {
  padding: 0 16px;
  border-bottom: 1px solid #EEEEEE;
  width: 100%;
}

/* Remove top border from list items */
.chat-list-inner .adm-list-item-content {
  border-top: none !important;
}

/* Ant Mobile List 组件内部元素触摸优化 */
.chat-list-inner .adm-list-item,
.chat-list-inner .adm-list-item-content {
  touch-action: pan-y;
}

/* Chat avatar styles */
.chat-avatar {
  width: 44px !important;
  height: 44px !important;
  border-radius: 22px !important;
}

.chat-avatar-letter {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  border-radius: 22px;
  color: #FFFFFF;
}

.chat-avatar-letter .letter {
  font-family: 'SF Pro', sans-serif;
  font-size: 17px;
}

.chat-avatar.pinned {
  border: 1px solid #F5F5F5;
}

.chat-avatar-with-status {
  position: relative;
  width: 44px;
  height: 44px;
}

.status-tag {
  position: absolute;
  bottom: 0px;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 17px;
  border-width: 1px;
  padding: 2px 4px;
  border-radius: 40px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 8px;
  color: #FFFFFF;
  gap: 10px;
}

.timeout-tag {
  background-color: #FF3141;
}

.waiting-tag {
  background-color: #FF5B05;
}

.chat-avatar-with-status.timeout .chat-avatar {
  border: 2px solid #FF3141;
}

.chat-avatar-with-status.waiting .chat-avatar {
  border: 2px solid #FF5B05;
}

/* Chat content styles */
.chat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-title-row {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  height: 21px;
}

.chat-title {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 15px;
  color: #333333;
}

.team-count {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 15px;
  color: #333333;
}

.mute-icon {
  width: 14px;
  height: 14px;
}

.chat-message {
  display: flex;
  align-items: center;
  gap: 2px;
  width: 100%;
  height: 18px;
}

.ai-tag {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 14px;
  color: #999999;
}

.edit-icon {
  width: 14px;
  height: 14px;
}

.message-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 13px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 220px;
}

.chat-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  height: 100%;
}

.chat-time {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  height: 24px;
}

.badge {
  margin-top: 4px;
  height: 20px;
  border-radius: 10px !important;
  background-color: #FF3141 !important;
  color: #FFFFFF !important;
  font-size: 12px !important;
  padding: 0 4px !important;
  min-width: unset !important;
  width: auto !important;
}

.badge:has(span:not([data-content="99+"])) {
  width: 20px !important;
  height: 20px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.badge:has(span[data-content="99+"]) {
  width: 31px !important;
}

/* Filter tabs styling */
.filter-tabs {
  display: flex;
  padding-top: 12px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-tab {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 8px 0 10px;
  cursor: pointer;
}

.filter-tab.active .filter-text {
  color: #1677FF;
}

.filter-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.dot-badge {
  width: 10px;
  height: 10px;
  background-color: #FF3141;
  border-radius: 50%;
  position: relative;
  display: inline-block;
  margin-left: 4px;
}

.active-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1677FF;
}

/* ErrorBlock 居中樣式 */
.roomlist-tab-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 0;
  height: 100%;
  padding: 40px 20px;
  box-sizing: border-box;
  background-color: #FFFFFF;
}

.roomlist-tab-empty-state .adm-error-block {
  text-align: center;
}

/* 空列表容器居中樣式 */
.empty-list-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 0;
  height: 100%;
  background-color: #FFFFFF;
  padding: 40px 20px;
  box-sizing: border-box;
}

.empty-list-container .adm-error-block {
  text-align: center;
}

/* unread-badge 樣式 - 參考圖片優化版本 */
.unread-badge {
  margin-top: 0px;
  height: 16px;
  min-width: 16px;
  border-radius: 50% !important;
  background-color: #FF4D4F !important;
  color: #FFFFFF !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  /* padding: 0 4px !important; */
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  line-height: 1 !important;
  box-sizing: border-box !important;
}

/* 單個數字時的圓形樣式 */
.unread-badge:has(span:not([data-content="99+"])) {
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  padding: 0 !important;
}

/* 兩位數時的橢圓形樣式 */
.unread-badge:has(span[data-content="99+"]) {
  width: 24px !important;
  padding: 0 3px !important;
  border-radius: 8px !important;
}

/* 針對兩位數字（10-99）的樣式 */
.unread-badge .adm-badge-content {
  font-size: 10px !important;
  font-weight: 500 !important;
  line-height: 1 !important;
}