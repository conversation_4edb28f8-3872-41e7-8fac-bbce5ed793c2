import { logService } from '../system/logService';
import aileDBService from '../db/aileDBService';
import { Messages } from '../db/initSql';
import type { MessageData } from '@/services/core/chat/messageService';
import { getCurrentTenantId } from '@/utils/tenantUtil';

/**
 * 消息数据访问对象
 * 处理消息的存储、查询等数据库操作
 * 特别设计用于高效处理聊天消息的存储和查询
 */
class MessageDao {
  /**
   * 从数据库获取消息列表
   * @param roomId 聊天室ID
   * @param page 页码（从0开始）
   * @param pageSize 每页条数
   * @param beforeSequence 可选：序列号上限（用于分页）
   * @param sort 排序方式 ('asc' 或 'desc')
   * @returns 消息列表
   */
  public async fetchMessagesFromDB(
    roomId: string,
    page: number = 0,
    pageSize: number = 20,
    beforeSequence?: number,
    sort: 'asc' | 'desc' = 'desc'
  ): Promise<Messages[]> {
    try {
      // 獲取當前租戶ID (多租戶數據隔離)
      const tenantId = getCurrentTenantId();
      
      if (!tenantId) {
        logService.warn('fetchMessagesFromDB: 未獲取到當前租戶Id');
      }
      
      // 構建基礎SQL查詢
      let sql = `SELECT * FROM Messages WHERE roomId = ?`;
      const sqlParams: any[] = [roomId];
      
      // 添加租戶ID過濾條件 (多租戶數據隔離)
      if (tenantId) {
        sql += ` AND tenantId = ?`;
        sqlParams.push(tenantId);
      }
      
      // 如果提供了序列號，則添加序列號條件（获取指定序列号之前的消息）
      if (beforeSequence !== undefined) {
        sql += ` AND sequence <= ?`;
        sqlParams.push(beforeSequence);
      }
      
      // 统一使用序列号排序（性能更好，逻辑更清晰）
      sql += ` ORDER BY sequence ${sort === 'asc' ? 'ASC' : 'DESC'}`;

      // 添加分頁
      sql += ` LIMIT ?`;
      sqlParams.push(pageSize);

      // 執行查詢
      logService.info('🗃️ 执行数据库查询', {
        roomId,
        page,
        pageSize,
        beforeSequence,
        sort,
        sql: sql.replace(/\s+/g, ' '),
        sqlParams,
        offset: page * pageSize
      });

      const messages = await aileDBService.all<Messages>(sql, sqlParams);

      logService.info('📊 数据库查询结果', {
        roomId,
        page,
        pageSize,
        beforeSequence,
        sort,
        查询到的消息数量: messages.length,
        是否达到页面大小: messages.length >= pageSize,
        序列号范围: messages.length > 0 ? {
          最小: Math.min(...messages.map(m => m.sequence || 0)),
          最大: Math.max(...messages.map(m => m.sequence || 0))
        } : '无消息',
        消息时间范围: messages.length > 0 ? {
          最早: new Date(Math.min(...messages.map(m => m.sendTime || 0))).toLocaleString(),
          最晚: new Date(Math.max(...messages.map(m => m.sendTime || 0))).toLocaleString()
        } : '无消息'
      });
      
      return messages;
    } catch (error) {
      logService.error('從數據庫獲取消息列表失敗', {
        error: error as Error,
        roomId,
        page,
        pageSize,
        beforeSequence,
        sort
      });
      return [];
    }
  }

  /**
   * 优化的序列号范围查询方法
   * 专门用于聊天室历史消息的高效查询
   * @param roomId 聊天室ID
   * @param startSequence 开始序列号（包含）
   * @param endSequence 结束序列号（包含）
   * @param limit 限制数量
   * @returns 消息列表（按序列号升序）
   */
  public async fetchMessagesBySequenceRange(
    roomId: string,
    startSequence: number,
    endSequence: number,
    limit: number = 50
  ): Promise<Messages[]> {
    try {
      const tenantId = getCurrentTenantId();

      if (!tenantId) {
        logService.warn('fetchMessagesBySequenceRange: 未獲取到當前租戶Id');
      }

      // 构建优化的范围查询SQL
      let sql = `SELECT * FROM Messages WHERE roomId = ?`;
      const sqlParams: any[] = [roomId];

      // 添加租戶ID過濾
      if (tenantId) {
        sql += ` AND tenantId = ?`;
        sqlParams.push(tenantId);
      }

      // 添加序列号范围条件
      sql += ` AND sequence >= ? AND sequence <= ?`;
      sqlParams.push(startSequence, endSequence);

      // 按序列号升序排序，确保消息顺序正确
      sql += ` ORDER BY sequence ASC`;

      // 添加限制
      sql += ` LIMIT ?`;
      sqlParams.push(limit);

      logService.info('🔍 执行序列号范围查询', {
        roomId,
        startSequence,
        endSequence,
        limit,
        sql: sql.replace(/\s+/g, ' '),
        sqlParams
      });

      const messages = await aileDBService.all<Messages>(sql, sqlParams);

      logService.info('✅ 序列号范围查询完成', {
        roomId,
        startSequence,
        endSequence,
        resultCount: messages.length,
        actualRange: messages.length > 0 ?
          `${messages[0].sequence}-${messages[messages.length - 1].sequence}` : 'empty'
      });

      return messages;
    } catch (error) {
      logService.error('序列号范围查询失败', {
        error: error as Error,
        roomId,
        startSequence,
        endSequence,
        limit
      });
      return [];
    }
  }

  /**
   * 获取房间最新的N条消息（用于初始化显示）
   * @param roomId 聊天室ID
   * @param limit 消息数量限制
   * @param fromSequence 可选：从指定序列号之后获取消息（用于增量同步）
   * @returns 消息列表（按序列号升序）
   */
  public async fetchLatestMessages(
    roomId: string,
    limit: number = 20,
    fromSequence?: number
  ): Promise<Messages[]> {
    try {
      const tenantId = getCurrentTenantId();

      if (!tenantId) {
        logService.warn('fetchLatestMessages: 未獲取到當前租戶Id');
      }

      // 构建查询
      let sql = `SELECT * FROM Messages WHERE roomId = ?`;
      const sqlParams: any[] = [roomId];

      // 添加租戶ID過濾
      if (tenantId) {
        sql += ` AND tenantId = ?`;
        sqlParams.push(tenantId);
      }

      // 如果提供了起始序列号
      if (fromSequence !== undefined) {
        sql += ` AND sequence > ?`;
        sqlParams.push(fromSequence);
      }

      // 先降序取得最新的N条记录
      sql += ` ORDER BY sequence DESC LIMIT ?`;
      sqlParams.push(limit);

      const messages = await aileDBService.all<Messages>(sql, sqlParams);

      // 返回前按序列号升序排序
      return messages.sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
    } catch (error) {
      logService.error('获取最新消息失败', {
        error: error as Error,
        roomId,
        limit,
        fromSequence
      });
      return [];
    }
  }

  /**
   * 统一的消息查询方法，支持多种查询模式
   * @param roomId 聊天室ID
   * @param options 查询选项
   * @returns 消息列表（按序列号排序）
   */
  public async getMessages(
    roomId: string,
    options: {
      type: 'latest' | 'before' | 'after' | 'range';
      sequence?: number;
      startSequence?: number;
      endSequence?: number;
      limit?: number;
    }
  ): Promise<Messages[]> {
    try {
      const tenantId = getCurrentTenantId();
      
      if (!tenantId) {
        logService.warn('getMessages: 未獲取到當前租戶Id');
      }

      // 默认限制条数
      const limit = options.limit || 20;

      // 构建基础查询
      let sql = `SELECT * FROM Messages WHERE roomId = ?`;
      const sqlParams: any[] = [roomId];

      // 添加租戶ID過濾
      if (tenantId) {
        sql += ` AND tenantId = ?`;
        sqlParams.push(tenantId);
      }

      // 根据查询类型添加不同的条件
      switch (options.type) {
        case 'latest':
          // 获取最新的N条消息
          sql += ` ORDER BY sequence DESC LIMIT ?`;
          sqlParams.push(limit);
          break;

        case 'before':
          // 获取指定序列号之前的N条消息
          if (options.sequence === undefined) {
            throw new Error('查询类型为before时必须提供sequence参数');
          }
          sql += ` AND sequence < ? ORDER BY sequence DESC LIMIT ?`;
          sqlParams.push(options.sequence, limit);
          break;

        case 'after':
          // 获取指定序列号之后的N条消息
          if (options.sequence === undefined) {
            throw new Error('查询类型为after时必须提供sequence参数');
          }
          sql += ` AND sequence > ? ORDER BY sequence ASC LIMIT ?`;
          sqlParams.push(options.sequence, limit);
          break;

        case 'range':
          // 获取指定序列号范围内的消息
          if (options.startSequence === undefined || options.endSequence === undefined) {
            throw new Error('查询类型为range时必须提供startSequence和endSequence参数');
          }
          sql += ` AND sequence >= ? AND sequence <= ? ORDER BY sequence ASC LIMIT ?`;
          sqlParams.push(options.startSequence, options.endSequence, limit);
          break;

        default:
          throw new Error(`不支持的查询类型: ${options.type}`);
      }

      const messages = await aileDBService.all<Messages>(sql, sqlParams);

      // 对于'latest'和'before'查询，返回前需要反转为升序
      if (options.type === 'latest' || options.type === 'before') {
        return messages.reverse();
      }

      return messages;
    } catch (error) {
      logService.error('查询消息失败', {
        error: error as Error,
        roomId,
        options
      });
      return [];
    }
  }


  /**
   * 将消息批量保存到本地数据库
   * 包含针对网络波动的重试机制
   * @param messages 消息数据数组
   * @returns 保存结果统计
   */
  public async saveMessagesToDb(messages: MessageData[]): Promise<{
    successCount: number;
    failedCount: number;
    errors: Error[];
  }> {
    if (!messages || messages.length === 0) {
      return { successCount: 0, failedCount: 0, errors: [] };
    }

    const errors: Error[] = [];
    let successCount = 0;
    let failedCount = 0;

    // 获取租户ID用于数据隔离
    const tenantId = getCurrentTenantId();
    
    if (!tenantId) {
      logService.warn('saveMessagesToDb: 未获取当前租户ID，将尝试继续执行');
    }

    for (const message of messages) {
      try {
        // 将MessageData转换为Messages表结构
        // 根據 Messages 表結構創建數據對象
        const dbMessage: Partial<Messages> = {
          id: message.id,
          type: message.type,
          senderName: message.senderName,
          senderId: message.senderId,
          content: typeof message.content === 'string' ?
              message.content :
              JSON.stringify(message.content),
          roomId: message.roomId,
          tenantId: tenantId || undefined,
          sendTime: typeof message.sendTime === 'string' ?
              new Date(message.sendTime).getTime() :
              typeof message.sendTime === 'number' ?
                  message.sendTime :
                  Date.now(),
          // 處理額外字段
          accountId: (message as any).accountId,
          sequence: (message as any).sequence,
          sourceType: (message as any).sourceType,
          osType: (message as any).osType,
          channel: (message as any).channel,
          appointChannel: (message as any).appointChannel,
          tag: (message as any).tag ||
              (message.metadata ? JSON.stringify(message.metadata) : undefined),
          themeId: (message as any).themeId,
          nearMessageId: (message as any).nearMessageId || message.replyToId,
          sessionId: (message as any).sessionId,
          channelMessageId: (message as any).channelMessageId,
          flag: (message as any).flag || 0
        };

        // 過濾掉 undefined 值
        const filteredMessage: Record<string, any> = {};
        for (const key in dbMessage) {
          if (dbMessage[key as keyof typeof dbMessage] !== undefined) {
            filteredMessage[key] = dbMessage[key as keyof typeof dbMessage];
          }
        }

        // 组装 SQL 插入语句，使用 INSERT OR REPLACE 语法
        const keys = Object.keys(filteredMessage);
        const placeholders = keys.map(() => '?').join(',');
        const values = keys.map(key => filteredMessage[key]);

        const sql = `INSERT OR REPLACE INTO Messages (${keys.join(', ')}) VALUES (${placeholders})`;
        await this.executeSingleMessage(sql, values);
        successCount++;
      } catch (error) {
        failedCount++;
        errors.push(error as Error);
        logService.error('保存单条消息失败', {
          error: error as Error,
          messageId: message.id,
          roomId: message.roomId
        });
      }
    }

    // 如果有成功保存的消息，触发保存到持久存储
    if (successCount > 0) {
      try {
        await this.saveToStoreWithRetry();
      } catch (saveError) {
        logService.warn('消息已写入数据库但保存到持久存储失败', { 
          error: saveError as Error,
          successCount
        });
      }
    }

    logService.info('消息保存结果', {
      total: messages.length,
      successCount,
      failedCount,
      errorCount: errors.length
    });

    return {
      successCount,
      failedCount,
      errors
    };
  }

  /**
   * 根据聊天室ID获取本地消息
   * @param roomId 聊天室ID
   * @param page 页码（从0开始）
   * @param pageSize 每页条数
   * @param sort 排序方向
   * @returns 消息列表
   */
  public async getMessagesByRoomId(
    roomId: string,
    page: number = 0,
    pageSize: number = 20,
    sort: 'asc' | 'desc' = 'asc'
  ): Promise<MessageData[]> {
    try {
      const tenantId = getCurrentTenantId();
      
      let sql = `SELECT * FROM Messages WHERE roomId = ?`;
      const sqlParams: any[] = [roomId];

      if (tenantId) {
        sql += ` AND tenantId = ?`;
        sqlParams.push(tenantId);
      }

      sql += ` ORDER BY sequence ${sort.toUpperCase()} LIMIT ? OFFSET ?`;
      sqlParams.push(pageSize, page * pageSize);

      return await aileDBService.all<MessageData>(sql, sqlParams);
    } catch (error) {
      logService.error('获取聊天室消息失败', {
        error: error as Error,
        roomId,
        page,
        pageSize,
        sort
      });
      return [];
    }
  }

  /**
   * 带重试机制执行单条消息保存
   * 解决 SQLite 事务冲突问题
   * @param sql SQL语句
   * @param values 参数值
   * @param retryCount 当前重试次数
   */
  private async executeSingleMessage(sql: string, values: any[], retryCount = 0): Promise<void> {
    const maxRetries = 3;
    const baseDelay = 10; // 基础延迟毫秒

    try {
      // 添加随机延迟避免并发冲突
      if (retryCount > 0) {
        const delay = baseDelay * Math.pow(2, retryCount) + Math.random() * 10;
        await new Promise(resolve => setTimeout(resolve, delay));

        logService.warn('检测到事务冲突，准备重试', {
          messageId: values[0],
          retryCount: retryCount,
          delay
        });
      }

      // 使用 run 方法执行 INSERT 语句
      await aileDBService.run(sql, values);

      // 如果是重试成功，记录日志
      if (retryCount > 0) {
        logService.info('重试执行成功', {
          messageId: values[0],
          retryCount
        });
      }

    } catch (error) {
      const errorMessage = (error as Error).message;

      // 如果是事务错误且还有重试次数，则重试
      if (errorMessage.includes('transaction') && retryCount < maxRetries) {
        logService.warn('检测到事务冲突，准备重试', {
          messageId: values[0],
          retryCount: retryCount + 1,
          error: errorMessage
        });
        return this.executeSingleMessage(sql, values, retryCount + 1);
      }

      // 其他错误或重试次数用完，直接抛出
      logService.error('执行消息保存失败', {
        messageId: values[0],
        retryCount,
        error: errorMessage,
        sql: sql.substring(0, 100) + '...'
      });
      throw error;
    }
  }

  /**
   * 带重试机制的数据持久化保存
   * @param retries 重试次数
   */
  private async saveToStoreWithRetry(retries = 2): Promise<void> {
    try {
      await aileDBService.saveToStore();
    } catch (error) {
      if (retries > 0) {
        // 指数退避重试
        const delay = 200 * Math.pow(2, 3 - retries);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.saveToStoreWithRetry(retries - 1);
      }
      throw error;
    }
  }
}

// 导出单例
const messageDao = new MessageDao();
export default messageDao; 