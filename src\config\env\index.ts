/// <reference types="vite/client" />

import { developmentConfig } from './development';
import { productionConfig } from './production';
import { stagingConfig } from './staging';
import { localConfig } from './local';
import { ConstantUtil } from '../../utils/constantUtil';
import { getLocalStorage } from '../../utils/storage';

// 导入类型用于内部使用
import type { DevelopmentConfig } from './development';
import type { ProductionConfig } from './production';
import type { StagingConfig } from './staging';
import type { LocalConfig } from './local';

// 重新导出类型
export type { BaseConfig } from './base';
export type { DevelopmentConfig } from './development';
export type { ProductionConfig } from './production';
export type { StagingConfig } from './staging';
export type { LocalConfig } from './local';

// 统一的环境配置类型
export type EnvConfig = DevelopmentConfig | ProductionConfig | StagingConfig | LocalConfig;

/**
 * 获取当前环境配置
 */
function getEnvConfig(): EnvConfig {
  // 首先嘗試從 localStorage 讀取用戶設置的環境
  const preferredEnv = typeof window !== 'undefined' 
    ? getLocalStorage<string | null>(ConstantUtil.PREFERRED_ENV_KEY, null)
    : null;
  
  // 確定使用的環境模式
  const mode = preferredEnv || import.meta.env.MODE;
  
  // 根據環境模式選擇對應配置
  switch (mode) {
    case 'production':
      return productionConfig;
    case 'staging':
      return stagingConfig;
    case 'dev-local':
      return localConfig;
    default:
      return developmentConfig;
  }
}

// 导出当前环境配置
export const envConfig = getEnvConfig();

// 默认导出
export default envConfig;
