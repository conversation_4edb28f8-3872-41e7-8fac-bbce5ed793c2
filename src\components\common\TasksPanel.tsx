import React, { useState } from 'react';
import './TasksPanel.css';
import taskIcon from '../../assets/icons/tasks/task-icon.svg';
import arrowDownIcon from '../../assets/icons/tasks/arrow-down.svg';

export interface ITask {
  id: string;
  title: string;
  dueTime: string;
  isUrgent: boolean;
}

interface TasksPanelProps {
  tasks: ITask[];
  onTaskClick?: (taskId: string) => void;
}

const TasksPanel: React.FC<TasksPanelProps> = ({ tasks, onTaskClick }) => {
  const [expanded, setExpanded] = useState(false);

  const handleHeaderClick = () => {
    setExpanded(!expanded);
  };

  return (
    <div className={`tasks-panel ${expanded ? 'expanded' : ''}`}>
      <div className="tasks-header" onClick={handleHeaderClick}>
        <div className="tasks-icon-container">
          <img src={taskIcon} alt="任務" className="tasks-icon" />
        </div>
        <div className="tasks-title">客戶相關任務 ({tasks.length})</div>
        <div className={`tasks-arrow ${expanded ? 'expanded' : ''}`}>
          <img src={arrowDownIcon} alt="展開" />
        </div>
      </div>
      
      {expanded && (
        <div className="tasks-list-overlay" data-testid="tasks-list-overlay">
          <div className="tasks-list">
            {tasks.map((task) => (
              <div 
                key={task.id} 
                className="task-item"
                onClick={() => onTaskClick && onTaskClick(task.id)}
              >
                <div className="task-content">
                  <div className="task-title" style={{ color: task.isUrgent ? '#FF3141' : '#000000' }}>
                    {task.title}
                  </div>
                  <div className="task-due-time" style={{ color: task.isUrgent ? '#FF3141' : '#999999' }}>
                    {task.dueTime}
                  </div>
                </div>
              </div>
            ))}
            <div className="task-item view-more">
              <div className="task-content">
                <div className="task-title">查看更多任務</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TasksPanel; 