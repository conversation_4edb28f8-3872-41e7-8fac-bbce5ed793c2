/**
 * 掃描器調試工具
 * 用於測試和調試原生掃描功能
 */

import { Capacitor } from '@capacitor/core';
import { BarcodeScanner } from '@capacitor-mlkit/barcode-scanning';
import deviceService from '@/services/platform/deviceService';
import { logService } from '@/services/system/logService';

export interface ScannerDebugInfo {
  isNativePlatform: boolean;
  platform: string;
  hasBarcodeScanner: boolean;
  shouldUseNative: boolean;
  userAgent: string;
  capacitorVersion?: string;
  pluginVersion?: string;
}

export class ScannerDebugger {
  /**
   * 獲取掃描器調試信息
   */
  static getDebugInfo(): ScannerDebugInfo {
    const isNativePlatform = Capacitor.isNativePlatform();
    const platform = deviceService.getPlatform();
    const hasBarcodeScanner = typeof BarcodeScanner !== 'undefined' && !!BarcodeScanner;
    const shouldUseNative = isNativePlatform && hasBarcodeScanner && (platform === 'android' || platform === 'ios');

    const debugInfo: ScannerDebugInfo = {
      isNativePlatform,
      platform,
      hasBarcodeScanner,
      shouldUseNative,
      userAgent: navigator.userAgent,
    };

    // 嘗試獲取 Capacitor 版本信息
    try {
      // @ts-ignore
      debugInfo.capacitorVersion = Capacitor.version || 'unknown';
    } catch (error) {
      debugInfo.capacitorVersion = 'error getting version';
    }

    return debugInfo;
  }

  /**
   * 測試原生掃描器可用性
   */
  static async testNativeScanner(): Promise<{
    available: boolean;
    error?: string;
    details?: any;
  }> {
    try {
      const debugInfo = this.getDebugInfo();
      
      logService.info('測試原生掃描器', debugInfo);

      if (!debugInfo.shouldUseNative) {
        return {
          available: false,
          error: '不應該使用原生掃描器',
          details: debugInfo
        };
      }

      if (!BarcodeScanner) {
        return {
          available: false,
          error: 'BarcodeScanner 插件不可用',
          details: debugInfo
        };
      }

      // 嘗試檢查插件是否支持
      const { supported } = await BarcodeScanner.isSupported();
      if (!supported) {
        return {
          available: false,
          error: '設備不支持條碼掃描',
          details: debugInfo
        };
      }

      return {
        available: true,
        details: debugInfo
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logService.error('測試原生掃描器失敗', { error: errorMsg });
      
      return {
        available: false,
        error: errorMsg,
        details: this.getDebugInfo()
      };
    }
  }

  /**
   * 模擬掃描器初始化過程
   */
  static async simulateInitialization(): Promise<{
    success: boolean;
    steps: string[];
    error?: string;
  }> {
    const steps: string[] = [];
    
    try {
      steps.push('開始初始化掃描器');
      
      const debugInfo = this.getDebugInfo();
      steps.push(`平台檢測: ${JSON.stringify(debugInfo)}`);
      
      if (debugInfo.shouldUseNative) {
        steps.push('選擇使用原生掃描器');
        
        const testResult = await this.testNativeScanner();
        steps.push(`原生掃描器測試: ${JSON.stringify(testResult)}`);
        
        if (testResult.available) {
          steps.push('原生掃描器可用，準備啟動');
          return { success: true, steps };
        } else {
          steps.push(`原生掃描器不可用: ${testResult.error}`);
          steps.push('回退到 Web 掃描器');
          return { success: false, steps, error: testResult.error };
        }
      } else {
        steps.push('選擇使用 Web 掃描器');
        return { success: true, steps };
      }
      
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      steps.push(`初始化失敗: ${errorMsg}`);
      return { success: false, steps, error: errorMsg };
    }
  }

  /**
   * 生成調試報告
   */
  static async generateDebugReport(): Promise<string> {
    const debugInfo = this.getDebugInfo();
    const testResult = await this.testNativeScanner();
    const initResult = await this.simulateInitialization();

    const report = `
# 掃描器調試報告

## 環境信息
- 是否為原生平台: ${debugInfo.isNativePlatform}
- 平台: ${debugInfo.platform}
- 用戶代理: ${debugInfo.userAgent}
- Capacitor 版本: ${debugInfo.capacitorVersion}

## 插件狀態
- BarcodeScanner 可用: ${debugInfo.hasBarcodeScanner}
- 應該使用原生掃描器: ${debugInfo.shouldUseNative}

## 原生掃描器測試
- 可用性: ${testResult.available}
- 錯誤: ${testResult.error || '無'}

## 初始化模擬
- 成功: ${initResult.success}
- 錯誤: ${initResult.error || '無'}
- 步驟:
${initResult.steps.map(step => `  - ${step}`).join('\n')}

## 建議
${this.generateRecommendations(debugInfo, testResult)}
    `.trim();

    return report;
  }

  /**
   * 生成建議
   */
  private static generateRecommendations(
    debugInfo: ScannerDebugInfo,
    testResult: any,
  ): string {
    const recommendations: string[] = [];

    if (!debugInfo.isNativePlatform) {
      recommendations.push('當前在瀏覽器環境中運行，無法使用原生掃描器');
      recommendations.push('請在真機上測試原生功能');
    }

    if (!debugInfo.hasBarcodeScanner) {
      recommendations.push('BarcodeScanner 插件未加載');
      recommendations.push('檢查插件是否正確安裝: npx cap ls');
      recommendations.push('重新同步: npx cap sync');
    }

    if (!testResult.available && debugInfo.shouldUseNative) {
      recommendations.push('原生掃描器不可用，檢查以下項目:');
      recommendations.push('  - 相機權限是否已授予');
      recommendations.push('  - 插件版本是否兼容');
      recommendations.push('  - 原生應用是否正確構建');
    }

    if (recommendations.length === 0) {
      recommendations.push('配置看起來正常，如果仍有問題請檢查真機測試結果');
    }

    return recommendations.join('\n');
  }
}

// 導出便捷函數
export const debugScanner = () => ScannerDebugger.getDebugInfo();
export const testNativeScanner = () => ScannerDebugger.testNativeScanner();
export const generateScannerReport = () => ScannerDebugger.generateDebugReport();
