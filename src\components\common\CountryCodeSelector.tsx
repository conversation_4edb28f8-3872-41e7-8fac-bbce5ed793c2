/**
 * @file CountryCodeSelector.tsx
 * @description 国家代码选择器组件 - 基于移动端设计
 * <AUTHOR> Team
 */

import React, { useState, useMemo, useEffect } from 'react';
import './CountryCodeSelector.css';

interface CountryCode {
  name: string;
  code: string;
  dial_code: string;
}

interface CountryCodeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (countryCode: CountryCode) => void;
  selectedCode?: string;
}

// 完整的国家代码数据 - 基于用户提供的截图参考
const COUNTRY_CODES: CountryCode[] = [
  // 中华地区优先显示
  { name: '中国', code: 'CN', dial_code: '+86' },
  { name: '中国香港', code: 'HK', dial_code: '+852' },
  { name: '中国澳门', code: 'MO', dial_code: '+853' },
  { name: '台湾', code: 'TW', dial_code: '+886' },
  
  // A
  { name: '阿尔巴尼亚', code: 'AL', dial_code: '+355' },
  { name: '阿尔及利亚', code: 'DZ', dial_code: '+213' },
  { name: '阿富汗', code: 'AF', dial_code: '+93' },
  { name: '阿根廷', code: 'AR', dial_code: '+54' },
  { name: '阿拉伯联合酋长国', code: 'AE', dial_code: '+971' },
  { name: '阿鲁巴岛', code: 'AW', dial_code: '+297' },
  { name: '阿曼', code: 'OM', dial_code: '+968' },
  { name: '阿塞拜疆', code: 'AZ', dial_code: '+994' },
  { name: '阿森松', code: 'AC', dial_code: '+247' },
  { name: '埃及', code: 'EG', dial_code: '+20' },
  { name: '埃塞俄比亚', code: 'ET', dial_code: '+251' },
  { name: '爱尔兰', code: 'IE', dial_code: '+353' },
  { name: '爱沙尼亚', code: 'EE', dial_code: '+372' },
  { name: '安道尔', code: 'AD', dial_code: '+376' },
  { name: '安哥拉', code: 'AO', dial_code: '+244' },
  { name: '安圭拉', code: 'AI', dial_code: '+1264' },
  { name: '安提瓜和巴布达', code: 'AG', dial_code: '+1268' },
  { name: '澳大利亚', code: 'AU', dial_code: '+61' },
  { name: '奥地利', code: 'AT', dial_code: '+43' },

  // B
  { name: '巴巴多斯', code: 'BB', dial_code: '+1246' },
  { name: '巴布亚新几内亚', code: 'PG', dial_code: '+675' },
  { name: '巴哈马', code: 'BS', dial_code: '+1242' },
  { name: '巴基斯坦', code: 'PK', dial_code: '+92' },
  { name: '巴拉圭', code: 'PY', dial_code: '+595' },
  { name: '巴勒斯坦', code: 'PS', dial_code: '+970' },
  { name: '巴林', code: 'BH', dial_code: '+973' },
  { name: '巴拿马', code: 'PA', dial_code: '+507' },
  { name: '巴西', code: 'BR', dial_code: '+55' },
  { name: '白俄罗斯', code: 'BY', dial_code: '+375' },
  { name: '百慕大', code: 'BM', dial_code: '+1441' },
  { name: '保加利亚', code: 'BG', dial_code: '+359' },
  { name: '贝宁', code: 'BJ', dial_code: '+229' },
  { name: '比利时', code: 'BE', dial_code: '+32' },
  { name: '冰岛', code: 'IS', dial_code: '+354' },
  { name: '波多黎各', code: 'PR', dial_code: '+1787' },
  { name: '波兰', code: 'PL', dial_code: '+48' },
  { name: '波斯尼亚和黑塞哥维那', code: 'BA', dial_code: '+387' },
  { name: '玻利维亚', code: 'BO', dial_code: '+591' },
  { name: '博茨瓦纳', code: 'BW', dial_code: '+267' },
  { name: '伯利兹', code: 'BZ', dial_code: '+501' },
  { name: '不丹', code: 'BT', dial_code: '+975' },
  { name: '布基纳法索', code: 'BF', dial_code: '+226' },
  { name: '布隆迪', code: 'BI', dial_code: '+257' },

  // C-D
  { name: '朝鲜', code: 'KP', dial_code: '+850' },
  { name: '赤道几内亚', code: 'GQ', dial_code: '+240' },
  { name: '丹麦', code: 'DK', dial_code: '+45' },
  { name: '德国', code: 'DE', dial_code: '+49' },
  { name: '东帝汶', code: 'TL', dial_code: '+670' },
  { name: '多哥', code: 'TG', dial_code: '+228' },
  { name: '多米尼加', code: 'DO', dial_code: '+1809' },
  { name: '多米尼克', code: 'DM', dial_code: '+1767' },

  // E-F
  { name: '俄罗斯', code: 'RU', dial_code: '+7' },
  { name: '厄瓜多尔', code: 'EC', dial_code: '+593' },
  { name: '厄立特里亚', code: 'ER', dial_code: '+291' },
  { name: '法国', code: 'FR', dial_code: '+33' },
  { name: '法罗群岛', code: 'FO', dial_code: '+298' },
  { name: '法属圭亚那', code: 'GF', dial_code: '+594' },
  { name: '法属波利尼西亚', code: 'PF', dial_code: '+689' },
  { name: '梵蒂冈', code: 'VA', dial_code: '+39' },
  { name: '菲律宾', code: 'PH', dial_code: '+63' },
  { name: '斐济', code: 'FJ', dial_code: '+679' },
  { name: '芬兰', code: 'FI', dial_code: '+358' },
  { name: '佛得角', code: 'CV', dial_code: '+238' },

  // G-H
  { name: '冈比亚', code: 'GM', dial_code: '+220' },
  { name: '刚果共和国', code: 'CG', dial_code: '+242' },
  { name: '刚果民主共和国', code: 'CD', dial_code: '+243' },
  { name: '哥伦比亚', code: 'CO', dial_code: '+57' },
  { name: '哥斯达黎加', code: 'CR', dial_code: '+506' },
  { name: '格林纳达', code: 'GD', dial_code: '+1473' },
  { name: '格陵兰', code: 'GL', dial_code: '+299' },
  { name: '格鲁吉亚', code: 'GE', dial_code: '+995' },
  { name: '古巴', code: 'CU', dial_code: '+53' },
  { name: '瓜德罗普', code: 'GP', dial_code: '+590' },
  { name: '关岛', code: 'GU', dial_code: '+1671' },
  { name: '圭亚那', code: 'GY', dial_code: '+592' },
  { name: '哈萨克斯坦', code: 'KZ', dial_code: '+7' },
  { name: '海地', code: 'HT', dial_code: '+509' },
  { name: '韩国', code: 'KR', dial_code: '+82' },
  { name: '荷兰', code: 'NL', dial_code: '+31' },
  { name: '荷属安的列斯', code: 'AN', dial_code: '+599' },
  { name: '黑山', code: 'ME', dial_code: '+382' },
  { name: '洪都拉斯', code: 'HN', dial_code: '+504' },

  // I-J
  { name: '基里巴斯', code: 'KI', dial_code: '+686' },
  { name: '吉布提', code: 'DJ', dial_code: '+253' },
  { name: '吉尔吉斯斯坦', code: 'KG', dial_code: '+996' },
  { name: '几内亚', code: 'GN', dial_code: '+224' },
  { name: '几内亚比绍', code: 'GW', dial_code: '+245' },
  { name: '加拿大', code: 'CA', dial_code: '+1' },
  { name: '加纳', code: 'GH', dial_code: '+233' },
  { name: '加蓬', code: 'GA', dial_code: '+241' },
  { name: '柬埔寨', code: 'KH', dial_code: '+855' },
  { name: '捷克', code: 'CZ', dial_code: '+420' },
  { name: '津巴布韦', code: 'ZW', dial_code: '+263' },
  { name: '印度', code: 'IN', dial_code: '+91' },
  { name: '印度尼西亚', code: 'ID', dial_code: '+62' },
  { name: '英国', code: 'GB', dial_code: '+44' },
  { name: '约旦', code: 'JO', dial_code: '+962' },
  { name: '越南', code: 'VN', dial_code: '+84' },
  { name: '意大利', code: 'IT', dial_code: '+39' },
  { name: '以色列', code: 'IL', dial_code: '+972' },
  { name: '伊朗', code: 'IR', dial_code: '+98' },
  { name: '伊拉克', code: 'IQ', dial_code: '+964' },
  { name: '日本', code: 'JP', dial_code: '+81' },

  // 其他重要国家
  { name: '美国', code: 'US', dial_code: '+1' },
  { name: '新加坡', code: 'SG', dial_code: '+65' },
  { name: '泰国', code: 'TH', dial_code: '+66' },
  { name: '马来西亚', code: 'MY', dial_code: '+60' },
  { name: '澳大利亚', code: 'AU', dial_code: '+61' },
  { name: '新西兰', code: 'NZ', dial_code: '+64' },
  { name: '加拿大', code: 'CA', dial_code: '+1' },
  { name: '瑞士', code: 'CH', dial_code: '+41' },
  { name: '瑞典', code: 'SE', dial_code: '+46' },
  { name: '挪威', code: 'NO', dial_code: '+47' },
];

export const CountryCodeSelector: React.FC<CountryCodeSelectorProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedCode = '+886'
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // 当下拉框关闭时清空搜索
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
    }
  }, [isOpen]);

  const filteredCountries = useMemo(() => {
    if (!searchTerm) return COUNTRY_CODES;
    
    const term = searchTerm.toLowerCase();
    return COUNTRY_CODES.filter(country =>
      country.name.toLowerCase().includes(term) ||
      country.dial_code.includes(term) ||
      country.code.toLowerCase().includes(term)
    );
  }, [searchTerm]);

  const handleSelect = (country: CountryCode) => {
    onSelect(country);
    setSearchTerm(''); // 清空搜索
    onClose();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  if (!isOpen) return null;

  return (
    <div className="country-code-selector">
      {/* 点击外部关闭 */}
      {isOpen && <div className="country-code-selector__overlay" onClick={onClose} />}
      
      {/* 下拉框 */}
      {isOpen && (
        <div className="country-code-selector__dropdown">
          {/* Search Bar */}
          <div className="country-code-selector__search">
            <div className="country-code-selector__search-wrapper">
              <svg 
                className="country-code-selector__search-icon" 
                width="16" 
                height="16" 
                viewBox="0 0 16 16" 
                fill="none"
              >
                <path 
                  d="M14 14L10 10M11.3333 6.66667C11.3333 9.244 9.244 11.3333 6.66667 11.3333C4.08934 11.3333 2 9.244 2 6.66667C2 4.08934 4.08934 2 6.66667 2C9.244 2 11.3333 4.08934 11.3333 6.66667Z" 
                  stroke="#8B949E" 
                  strokeWidth="1.2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
              <input
                type="text"
                placeholder="搜索國家..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="country-code-selector__search-input"
                autoFocus
              />
            </div>
          </div>

          {/* Country List */}
          <div className="country-code-selector__list">
            {filteredCountries.map((country, index) => (
              <div
                key={`${country.code}-${country.dial_code}-${index}`}
                className={`country-code-selector__item ${
                  selectedCode === country.dial_code ? 'country-code-selector__item--selected' : ''
                }`}
                onClick={() => handleSelect(country)}
              >
                <span className="country-code-selector__country-name">
                  {country.name}
                </span>
                <span className="country-code-selector__dial-code">
                  {country.dial_code}
                </span>
              </div>
            ))}
            {filteredCountries.length === 0 && (
              <div className="country-code-selector__item country-code-selector__item--no-results">
                <span className="country-code-selector__country-name">
                  未找到匹配的國家或地區
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CountryCodeSelector; 