/* 导入基础样式 */
@import './chat/ChatRoom.css';
@import './chat/ChatRoomIcons.css';

/* 特定样式覆盖 */
.customer-chat-room-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #E4F4FD;
}

/* Navbar Styles */
.customer-chat-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
}

.customer-chat-navbar-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 6px 16px 6px 0;
}

.customer-chat-back-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.customer-chat-room-title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4em;
  color: #333333;
}

.customer-chat-navbar-right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.customer-chat-navbar-search .customer-chat-navbar-icon {
  width: 19.41px;
  height: 19.42px;
}

.customer-chat-navbar-menu .customer-chat-navbar-icon {
  width: 17.5px;
  height: 13.5px;
}

.customer-chat-navbar-search,
.customer-chat-navbar-menu {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Customer Tasks */
.customer-tasks-container {
  padding: 8px;
}

.customer-tasks-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 12px;
  background-color: #FFFFFF;
  border-radius: 8px;
  width: 100%;
}

.customer-tasks-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
}

.customer-tasks-text {
  flex: 1;
  padding: 12px 0;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #333333;
}

.customer-tasks-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 13px;
  padding-left: 4px;
}

/* Chat Content */
.customer-chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-bottom: 16px;
}

.customer-chat-date-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  width: 100%;
}

.customer-chat-date-header span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4em;
  color: #666666;
}

/* Message Styles */
.customer-chat-message-row {
  display: flex;
  gap: 8px;
  width: 100%;
}

.customer-chat-message-row.user {
  justify-content: flex-end;
}

/* 客户头像样式覆盖 */
.customer-chat-avatar {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 16px;
  overflow: hidden;
}

.customer-chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.customer-chat-avatar-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.customer-chat-message-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.customer-chat-message-container.user {
  align-items: flex-end;
}

.customer-chat-message-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 8px;
}

/* 客户消息样式覆盖 */
.customer-chat-message-bubble {
  background-color: #FFFFFF;
  border-radius: 0 8px 8px 8px;
  padding: 8px 12px;
}

.customer-chat-message-bubble.user {
  background-color: #386591;
  border-radius: 8px 0 8px 8px;
}

.customer-chat-message-bubble span {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
  color: #333333;
}

.customer-chat-message-bubble.user span {
  color: #FFFFFF;
}

.customer-chat-message-time {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 9px;
  line-height: 1.4em;
  color: #999999;
}

/* Chat Input */
.customer-chat-input-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 团队选择器特定样式 */
.customer-chat-team-selector {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 8px;
  background-color: rgba(228, 244, 253, 0.5);
  height: 40px;
}

.customer-chat-team-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
}

.customer-chat-team-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4em;
  color: #333333;
}

.customer-chat-team-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FF3141;
  border-radius: 100px;
  padding: 1px 4px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 9px;
  line-height: 1.4em;
  color: #FFFFFF;
  min-width: 13px;
  height: 13px;
}

.customer-chat-team-avatar {
  position: relative;
  width: 28px;
  height: 28px;
  border-radius: 14px;
  overflow: hidden;
  margin-left: auto; /* 讓頭像靠右對齊 */
  margin-right: 8px; /* 讓頭像更接近右側的時長顯示 */
}

.customer-chat-team-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.customer-chat-team-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: #4CD964;
  border-radius: 50%;
  border: 1px solid #FFFFFF;
}

/* 服務時長顯示樣式 */
.service-duration-display {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4em;
  color: #333333;
  margin-left: 0; /* 確保與頭像緊密相鄰 */
}

.customer-chat-input-toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #FFFFFF;
}

.customer-chat-toolbar-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
}

.customer-chat-toolbar-icon img {
  width: 24px;
  height: 24px;
}

.customer-chat-input {
  flex: 1;
  padding: 8px 12px;
  background-color: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #333333;
  min-height: 36px;
  max-height: 120px;
  resize: none;
}

.customer-chat-input .adm-text-area {
  border: none;
  background: transparent;
  padding: 0;
}

.customer-chat-input .adm-text-area-element {
  min-height: 20px !important;
  line-height: 20px;
  font-size: 14px;
  padding: 0;
  border: none;
  background: transparent;
  resize: none;
}

.customer-chat-input::placeholder {
  color: #999999;
}

/* 保留错误状态样式，移除不再使用的loading样式 */

/* 页面错误状态样式 */
.page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #E4F4FD;
  color: #ff4d4f;
  font-size: 14px;
  gap: 16px;
  padding: 20px;
  text-align: center;
}

.page-error button {
  background-color: #1677FF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.page-error button:hover {
  background-color: #0958d9;
}

