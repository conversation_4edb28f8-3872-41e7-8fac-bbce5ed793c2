import { MessageType } from './message.types';

/**
 * 事件监听器类型
 */
export type EventListener = (data: any) => void;

/**
 * Socket消息接口
 */
export interface SocketMessage {
  id: string;
  type: MessageType;
  content: any;
  sender?: string;
  timestamp: number;
  channel?: string;
}

/**
 * 连接状态
 */
export enum ConnectionStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error'
}

/**
 * 认证信息
 */
export interface AuthInfo {
  userId: string;
  pushToken?: string;
  deviceId?: string;
}

/**
 * 聊天室类型
 */
export interface Room {
  id: string;
  roomType?: string;
  name?: string;
  avatarId?: string;
  memberCount?: number;
  members?: any[];
  lastMessage?: any;
  unreadCount?: number;
  isTop?: boolean;
  isMute?: boolean;
  createTime?: number;
  updateTime?: number;
  tenantId?: string;
  status?: string;
  isPin?: boolean; // 僅 UI 層 pin 狀態
  [key: string]: any;
}

/**
 * 進程消息事件類型
 */
export interface ProcessMessageEvent {
  name: string;
  code: string;
  event: string;
  sequence: number;
  content: any;
  tenantId: string;
  callback: any;
  isOff: any;
} 