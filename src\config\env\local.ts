import { baseConfig, BaseConfig } from './base';

/**
 * 本地環境配置
 */
export interface LocalConfig extends BaseConfig {
  API_BASE_URL: string;
  SOCKET_URL: string;
  SOCKET_PATH: string;
  VITE_LINE_CHANNEL_ID: string;
  VITE_LINE_REDIRECT_URI: string;
  DEBUG: boolean;
}

export const localConfig: LocalConfig = {
  ...baseConfig,
  
  // 本地環境特定配置
  API_BASE_URL: 'http://192.168.30.106:8000',
  SOCKET_URL: 'http://192.168.30.106:8000/aile',
  SOCKET_PATH: '/socketio/socket.io',
  VITE_LINE_CHANNEL_ID: '2007749135',
  VITE_LINE_REDIRECT_URI: 'cloud.aile.aile:/callback',
  DEBUG: true,
  
  // 本地環境覆蓋配置
  LOG_LEVEL: 'debug',
  API_TIMEOUT: 60000, // 本地環境延長超時時間
}; 