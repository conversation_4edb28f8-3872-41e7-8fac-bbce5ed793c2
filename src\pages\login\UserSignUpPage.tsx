import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON>, Toast } from 'antd-mobile';
import { Form } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '../../app/hooks';
import { setAccount } from '../../app/slices/authSlice';
import { logService } from '../../services/system/logService';
import authService from '../../services/core/auth/authService';
import { ROUTE_BUSINESS_REGISTER } from '../../config/app/routes';

// 图片导入
import backArrow from '../../assets/images/back_arrow.svg';

import './UserSignUpPage.css';

/**
 * 个人注册页面组件
 * 提供头像上传和姓名输入功能
 * 更新：填写姓名后可直接跳转到商务号注册页面
 */
const UserSignUpPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [form] = Form.useForm();
  
  // 状态管理
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarError, setAvatarError] = useState('');

  
  // 验证状态
  const [isFormValid, setIsFormValid] = useState(false);
  
  // 监听表单字段变化，更新按钮状态
  const updateFormValidity = () => {
    const name = form.getFieldValue('name') || '';
    const nameValid = name.trim() !== '' && name.length <= 18;
    setIsFormValid(nameValid);
  };
  
  // 首字显示（用于头像）
  const getFirstChar = () => {
    const name = form.getFieldValue('name') || '';
    return name.trim() ? name.trim()[0] : '';
  };
  
  /**
   * 处理返回按钮点击
   */
  const handleBackClick = () => {
    logService.info('用户点击返回按钮');
    navigate(-1);
  };
  
  /**
   * 处理姓名输入
   */
  const handleNameChange = (value: string) => {
    form.setFieldValue('name', value);
    updateFormValidity();
  };
  
  /**
   * 处理姓名输入框失焦
   */
  const handleNameBlur = () => {

    form.validateFields(['name']);
  };
  
  /**
   * 处理头像点击，触发文件选择
   */
  const handleAvatarClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  /**
   * 处理头像文件选择
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setAvatarError('');
    
    if (!file) {
      return;
    }
    
    // 验证文件类型
    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      setAvatarError('請上傳圖片格式文件');
      return;
    }
    
    // 验证文件大小（2MB = 2 * 1024 * 1024 bytes）
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      setAvatarError('頭像請選擇小於2M的圖片');
      return;
    }
    
    // 创建预览URL
    const objectUrl = URL.createObjectURL(file);
    setAvatarUrl(objectUrl);
    setAvatarFile(file);
    
    logService.info('用户上传了头像', { fileSize: file.size, fileType: file.type });
  };
  
  /**
   * 处理继续按钮点击
   */
  const handleContinue = async () => {
    try {
      // 验证表单
      const values = await form.validateFields();
      logService.info('用户点击继续按钮', { name: values.name, hasAvatar: !!avatarFile });

      // 获取当前用户 accountId
      const account = authService.getAccount();
      if (!account?.accountId) {
        Toast.show({ content: '找不到用戶ID，請重新登入', duration: 3000 });
        return;
      }

      // 調用 API
      const response = await authService.updateUserProfile({
        name: values.name,
        avatar: avatarFile
      });

      if (response.success) {
        // 更新 Redux user 狀態
        dispatch(setAccount({ name: values.name, isInitial: false }));
        navigate(ROUTE_BUSINESS_REGISTER);
      } else {
        Toast.show({ content: response.msg || '註冊失敗，請稍後再試', duration: 3000 });
      }
    } catch (error: any) {
      logService.error('注册失败', error as Error);
      Toast.show({ content: error?.message || '註冊失敗，請稍後再試', duration: 3000 });
    }
  };
  
  // 清理预览URL
  useEffect(() => {
    return () => {
      if (avatarUrl) {
        URL.revokeObjectURL(avatarUrl);
      }
    };
  }, [avatarUrl]);
  
  return (
    <div className="register-page">
      {/* 顶部导航 */}
      <div className="register-page__navbar">
        <div 
          className="register-page__back-button"
          onClick={handleBackClick}
        >
          <img src={backArrow} alt="返回" className="register-page__back-icon" />
        </div>
        <div className="register-page__title-container">
          <h1 className="register-page__title">請輸入您的姓名</h1>
        </div>
        <div className="register-page__spacer"></div> {/* 为了对称留出空间 */}
      </div>

      {/* 表单区域 */}
      <Form 
        form={form}
        className="register-page__form"
        requiredMarkStyle="none"
        layout="vertical"
        onFinish={handleContinue}
        initialValues={{ name: '' }}
        onValuesChange={updateFormValidity}
      >
        {/* 头像上传区域 */}
        <div className="register-page__avatar-container">
          <div 
            className="register-page__avatar"
            onClick={handleAvatarClick}
            style={{ 
              backgroundColor: avatarUrl ? 'transparent' : (getFirstChar() ? '#AB9AFF' : '#EEEEEE') 
            }}
          >
            {avatarUrl ? (
              <img src={avatarUrl} alt="Avatar" className="register-page__avatar-image" />
            ) : getFirstChar() ? (
              <span className="register-page__avatar-text">{getFirstChar()}</span>
            ) : (
            <svg width="40" height="40" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M24 4H23.2L22.87 4.01C18.36 4.19 14.8 7.94 14.8 12.5V17.23L14.81 17.65C14.92 19.86 15.86 21.95 17.45 23.5L19.55 25.55L19.69 25.7C19.95 26.01 20.09 26.4 20.09 26.81V27.1L20.08 27.31C20.01 27.86 19.67 28.35 19.18 28.6L5.96 35.4L5.73 35.53C4.69 36.17 4.05 37.31 4.05 38.55V39.14L4.06 39.37C4.17 40.78 5.34 41.89 6.77 41.89H40.23C41.74 41.89 42.96 40.67 42.96 39.14V38.55C42.96 37.22 42.22 36 41.05 35.4L27.82 28.6C27.26 28.31 26.91 27.73 26.91 27.1V26.81C26.91 26.34 27.1 25.88 27.44 25.55L29.54 23.5C31.22 21.85 32.17 19.59 32.17 17.23V12.5C32.17 7.85 28.43 4.07 23.8 4H24Z" fill="#999999"/>
            </svg>
            )}
          </div>
          <input 
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="register-page__avatar-input"
          />
        </div>
        
        {/* 头像错误提示 */}
        {avatarError && (
          <div className="register-page__error-text">
            {avatarError}
          </div>
        )}

        {/* 姓名输入 */}
        <Form.Item
          name="name"
          label="姓名"
          className="register-page__form-group"
          rules={[
            { 
              required: true, 
              message: '請輸入您的姓名' 
            },
            { 
              max: 18, 
              message: '姓名字數上限18個字元' 
            }
          ]}
        >
          <input
            type="text"
            onChange={(e) => handleNameChange(e.target.value)}
            onBlur={handleNameBlur}
            placeholder="請輸入您的姓名"
            className="register-page__input"
            maxLength={18}
          />
        </Form.Item>
      </Form>

      {/* 底部固定按钮 */}
      <div className="register-page__button-container">
        <Button
          block
          color="primary"
          disabled={!isFormValid}
          onClick={() => form.submit()}
          className="register-page__continue-button"
          style={{ 
            opacity: isFormValid ? 1 : 0.4,
            backgroundColor: '#1677FF'
          }}
        >
          繼續
        </Button>
      </div>
    </div>
  );
};

export default UserSignUpPage; 