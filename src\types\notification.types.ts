/**
 * 通知权限状态
 */
export enum NotificationPermission {
  DEFAULT = 'default',
  GRANTED = 'granted',
  DENIED = 'denied'
}

/**
 * 通知优先级
 */
export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high'
}

/**
 * 通知类型
 */
export enum NotificationType {
  CHAT = 'chat',
  SYSTEM = 'system',
  TASK = 'task',
  REMINDER = 'reminder'
}

/**
 * 推送状态
 */
export enum PushStatus {
  REGISTERED = 'registered',
  UNREGISTERED = 'unregistered',
  DENIED = 'denied',
  ERROR = 'error'
}

/**
 * 通知动作
 */
export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

/**
 * Service Worker 通知选项（扩展标准 NotificationOptions）
 */
export interface ServiceWorkerNotificationOptions extends NotificationOptions {
  actions?: NotificationAction[];
  silent?: boolean;
  data?: any;
}

/**
 * 应用通知选项
 */
export interface AppNotificationOptions {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  tag?: string;
  data?: any;
  priority?: NotificationPriority;
  requireInteraction?: boolean;
  type?: NotificationType;
  actions?: NotificationAction[];
}

/**
 * 推送注册信息
 */
export interface PushRegistration {
  endpoint: string;
  pushToken: string;
  keys: {
    auth: string;
    p256dh: string;
  };
  deviceId: string;
  userId?: string;
  createdAt: string;
}

/**
 * 通知点击处理器类型
 */
export type NotificationClickHandler = (notification: Notification, action?: string) => void; 