import { test, expect } from '@playwright/test';

test.describe('掃一掃頁面', () => {
  test('應該正確載入掃描頁面並顯示所有元素', async ({ page }) => {
    // 導航到掃一掃頁面
    await page.goto('/sidebar/qrscan');
    
    // 等待頁面載入完成
    await page.waitForSelector('.qr-scanner-container');
    
    // 驗證頁面標題
    const title = await page.locator('.scanner-title');
    await expect(title).toHaveText('掃一掃');
    
    // 驗證掃描框存在
    await expect(page.locator('.scan-box')).toBeVisible();
    
    // 驗證掃描動畫線存在
    await expect(page.locator('.scan-line')).toBeVisible();
    
    // 驗證底部輸入區域存在
    await expect(page.locator('.input-container')).toBeVisible();
    
    // 驗證輸入框和按鈕存在
    await expect(page.locator('.code-input')).toBeVisible();
    await expect(page.locator('.submit-button')).toBeVisible();
    await expect(page.locator('.submit-button')).toHaveText('送出');

    // 驗證默認情況下按鈕應該被禁用
    await expect(page.locator('.submit-button')).toBeDisabled();
  });

  test('當輸入邀請碼時按鈕應該啟用', async ({ page }) => {
    // 導航到掃一掃頁面
    await page.goto('/sidebar/qrscan');
    
    // 等待頁面載入完成
    await page.waitForSelector('.qr-scanner-container');
    
    // 檢查按鈕初始狀態為禁用
    await expect(page.locator('.submit-button')).toBeDisabled();
    
    // 輸入邀請碼
    await page.locator('.code-input').fill('TEST123');
    
    // 檢查按鈕啟用狀態
    await expect(page.locator('.submit-button')).toBeEnabled();
    
    // 清空輸入
    await page.locator('.code-input').fill('');
    
    // 檢查按鈕再次被禁用
    await expect(page.locator('.submit-button')).toBeDisabled();
  });

  test('點擊返回按鈕應該返回上一頁', async ({ page }) => {
    // 模擬從首頁進入掃一掃頁面
    await page.goto('/home');
    await page.goto('/sidebar/qrscan');
    
    // 等待頁面載入完成
    await page.waitForSelector('.qr-scanner-container');
    
    // 保存當前URL以便後續比較
    const currentUrl = page.url();
    
    // 點擊返回按鈕
    await page.locator('.back-button').click();
    
    // 驗證已返回上一頁
    await expect(page).not.toHaveURL(currentUrl);
  });
}); 