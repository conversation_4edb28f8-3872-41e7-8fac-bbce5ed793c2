import { logService } from '../../system/logService';
import type { Tenant } from '../../db/initSql';
import { RelationTenantVO } from './tenantTypes';
import { ConstantUtil } from '../../../utils/constantUtil';
import { Aile<PERSON>pi } from '../../api/aileApi';
import { CryptoUtil } from '../../../utils/cryptoUtil';
import { getLocalStorage } from '../../../utils/storage';
import { tenantDao } from '../../dao';
import aileDBService from '../../db/aileDBService';
import kvStoreService from '../../system/kvStoreService';
import { LoginUser } from '../../stateService';

/**
 * 租戶緩存配置
 */
interface TenantCacheConfig {
  /** 緩存過期時間（毫秒），默認 30 分鐘 */
  ttl: number;
  /** 強制刷新間隔（毫秒），默認 2 小時 */
  forceRefreshInterval: number;
  /** 緩存鍵前綴 */
  cacheKeyPrefix: string;
}

/**
 * 租戶緩存數據結構
 */
interface TenantCacheData {
  /** 租戶列表數據 */
  data: RelationTenantVO[];
  /** 緩存時間戳 */
  timestamp: number;
  /** 數據版本號 */
  version: number;
}

/**
 * 租戶服務類
 * 提供租戶相關操作的服務，包含智能緩存機制
 */
class TenantService {
  // 緩存配置
  private readonly cacheConfig: TenantCacheConfig = {
    ttl: 30 * 60 * 1000, // 30 分鐘
    forceRefreshInterval: 2 * 60 * 60 * 1000, // 2 小時
    cacheKeyPrefix: 'tenant_relation_list'
  };

  // 內存緩存
  private memoryCache: TenantCacheData | null = null;

  // 請求防抖：防止同時多個請求
  private fetchPromise: Promise<RelationTenantVO[] | null> | null = null;
  

  /**
   * 獲取關聯租戶列表
   * @param params 查詢參數
   * @returns 關聯租戶列表
   */
  public async getTenantRelationList(params: {
    pageIndex?: number;
    pageSize?: number;
    key?: string;
    refreshTime?: number;
    accountId?: string;
    channel?: string;
  }): Promise<{
    status: number;
    success: boolean;
    data: Array<Tenant & { relationId?: string; openId?: string }>;
    msg: string;
    code: string;
    timeCost?: number;
  }> {
    try {
      const startTime = Date.now();
      
      // 使用 tenantDao 查詢數據
      const tenants = await tenantDao.getTenantList(params);

      // 計算耗時
      const timeCost = Date.now() - startTime;

      logService.info('查詢關聯租戶列表成功', { count: tenants.length, timeCost });

      return {
        status: 0,
        success: true,
        data: tenants,
        msg: '查詢成功',
        code: '0',
        timeCost
      };
    } catch (error) {
      logService.error('查詢關聯租戶列表失敗', { error: error as Error, params });
      return {
        status: -1,
        success: false,
        data: [],
        msg: (error as Error).message || '查詢失敗',
        code: '-1'
      };
    }
  }

  /**
   * 根據ID獲取租戶詳情
   * @param id 租戶ID
   * @returns 租戶詳情
   */
  public async getTenantById(id: string): Promise<Tenant | null> {
    if (!id) {
      logService.warn('嘗試使用空ID查詢租戶');
      return null;
    }
    
    try {
      logService.debug('嘗試從數據庫獲取租戶詳情', { id });
      // 使用 tenantDao 獲取租戶詳情
      const tenant = await tenantDao.getTenantById(id);
      
      if (!tenant) {
        logService.warn('未找到指定ID的租戶', { id });
      } else {
        logService.debug('成功獲取租戶詳情', { id, name: tenant.name });
      }
      
      return tenant;
    } catch (error) {
      logService.error('獲取租戶詳情失敗', { error: error as Error, id });
      return null;
    }
  }

  /**
   * 保存租戶信息
   * @param tenant 租戶信息
   * @returns 是否保存成功
   */
  public async saveTenant(tenant: Tenant): Promise<boolean> {
    try {
      // 使用 tenantDao 保存租戶
      return await tenantDao.saveTenant(tenant);
    } catch (error) {
      logService.error('保存租戶信息失敗', { error: error as Error, tenant });
      return false;
    }
  }

  /**
   * 刪除租戶
   * @param id 租戶ID
   * @returns 是否刪除成功
   */
  public async deleteTenant(id: string): Promise<boolean> {
    try {
      // 使用 tenantDao 刪除租戶
      return await tenantDao.deleteTenant(id);
    } catch (error) {
      logService.error('刪除租戶失敗', { error: error as Error, id });
      return false;
    }
  }

  /**
   * 將租戶信息列表保存到數據庫
   * @param tenantInfoList 租戶信息列表
   * @returns 保存成功的租戶數量
   */
  public async saveTenantInfo(tenantInfoList: RelationTenantVO[] | undefined): Promise<number> {
    if (!tenantInfoList || !Array.isArray(tenantInfoList) || tenantInfoList.length === 0) {
      logService.warn('沒有租戶信息需要保存');
      return 0;
    }
    try {
      logService.info('開始將租戶信息保存到數據庫', { count: tenantInfoList.length });
      
      // 轉換 RelationTenantVO 為 Tenant 對象
      const tenants: Tenant[] = tenantInfoList.map(info => {
        if (!info.id) {
          logService.warn('租戶資料缺少主鍵 id，已跳過', info);
          return null;
        }
        
        // 從 officialServiceNumberInfo 獲取 id，如果 officialServiceNumberId 為空
        const officialServiceNumberId = 
          info.officialServiceNumberId || 
          (info.officialServiceNumberInfo?.id || '');
          
        // 從 manageServiceNumberInfo 獲取 id，如果 manageServiceNumberId 為空
        const manageServiceNumberId = 
          info.manageServiceNumberId || 
          (info.manageServiceNumberInfo?.id || '');
          
        return {
          id: info.id,
          type: info.type || '',
          description: info.description || '',
          industry: info.industry || '',
          industrySub: info.industrySub || '',
          scale: info.scale || '',
          code: info.code || '',
          name: info.name || '',
          shortName: info.shortName || '',
          avatarId: info.avatarId || '',
          isLastTenant: typeof info.isLastTenant === 'boolean' ? info.isLastTenant : false,
          unReadCount: typeof info.unReadCount === 'number' ? info.unReadCount : 0,
          officialServiceNumberId: officialServiceNumberId,
          manageServiceNumberId: manageServiceNumberId,
          accountId: info.accountId || '',
        };
      }).filter(Boolean) as Tenant[];
      
      // 記錄轉換後的租戶數據
      if (tenants.length > 0) {
        logService.info('轉換後的租戶數據示例', {
          firstTenant: {
            id: tenants[0].id,
            name: tenants[0].name,
            officialServiceNumberId: tenants[0].officialServiceNumberId
          },
          count: tenants.length
        });
      }
      
      // 使用 tenantDao 批量保存租戶
      const successCount = await tenantDao.saveTenants(tenants);
      
      // 檢查保存結果
      if (successCount !== tenants.length) {
        logService.warn('部分租戶數據保存失敗', { 
          total: tenants.length, 
          success: successCount,
          failed: tenants.length - successCount
        });
      }
      
      // 批量寫入後立即保存到持久存儲
      if (successCount > 0) {
        try {
          await aileDBService.saveToStore();
          logService.info('租戶信息批量寫入後已保存到持久存儲', { successCount });
        } catch (saveError) {
          logService.warn('保存到持久存儲失敗，但租戶信息已寫入數據庫', { 
            error: saveError as Error, 
            successCount 
          });
        }
      }
      
      logService.info('租戶信息保存完成', { successCount });
      return successCount;
    } catch (error) {
      logService.error('保存租戶信息失敗', { error: error as Error, tenantInfoList });
      return 0;
    }
  }

  /**
   * 註冊租戶（局部加密 FormData：除 file 外其他欄位加密放 args）
   */
  public async createPersonTenant(params: {
    companyName: string;
    companyAddress?: string;
    companyUrl?: string;
    companyPhone?: string;
    file: File;
  }): Promise<any> {
    const formData = new FormData();
    // 將除 file 外欄位組成 JSON
    const argsObj: Record<string, any> = {
      name: params.companyName,
      address: params.companyAddress || '',
      website: params.companyUrl || '',
      phone: params.companyPhone || ''
    };

    const encryptedArgs = CryptoUtil.encryptApiRequest(argsObj);
    formData.append('args', encryptedArgs);
    formData.append('file', params.file);
    // 調用 API
    return await AileApi.createPersonTenant(formData);
  }

  /**
   * 如果需要，選擇租戶 ID
   */
  public async selectTenantIdIfNeeded(tenantInfoList?: Array<{ id?: string; isLastTenant?: boolean }>): Promise<string | undefined> {
    const localTenantId = getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
    if (localTenantId) return localTenantId;
    
    let targetTenantId: string | undefined;
    if (Array.isArray(tenantInfoList) && tenantInfoList.length > 0) {
      const lastTenant = tenantInfoList.find(t => t.isLastTenant);
      const firstTenant = tenantInfoList[0];
      targetTenantId = lastTenant?.id || firstTenant.id;
    } else {
      const localTenantsResult = await this.getTenantRelationList({ pageIndex: 0, pageSize: 20 });
      if (localTenantsResult.success && Array.isArray(localTenantsResult.data) && localTenantsResult.data.length > 0) {
        const lastTenant = localTenantsResult.data.find(t => t.isLastTenant);
        const firstTenant = localTenantsResult.data[0];
        targetTenantId = lastTenant?.id || firstTenant.id;
      }
    }
    return targetTenantId;
  }

  /**
   * 獲取緩存鍵
   */
  private getCacheKey(): string {
    const accountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
    return `${this.cacheConfig.cacheKeyPrefix}_${accountId || 'default'}`;
  }

  /**
   * 從持久化存儲獲取緩存數據
   */
  private async getCachedData(): Promise<TenantCacheData | null> {
    try {
      const cacheKey = this.getCacheKey();
      const cachedStr = await kvStoreService.getValue(cacheKey);

      if (!cachedStr) {
        return null;
      }

      const cached: TenantCacheData = JSON.parse(cachedStr);
      const now = Date.now();

      // 檢查緩存是否過期
      if (now - cached.timestamp > this.cacheConfig.ttl) {
        logService.debug('租戶緩存已過期', {
          age: now - cached.timestamp,
          ttl: this.cacheConfig.ttl
        });
        return null;
      }

      return cached;
    } catch (error) {
      logService.error('獲取租戶緩存數據失敗', { error });
      return null;
    }
  }

  /**
   * 保存數據到緩存
   */
  private async setCachedData(data: RelationTenantVO[]): Promise<void> {
    try {
      const now = Date.now();
      const cacheData: TenantCacheData = {
        data,
        timestamp: now,
        version: 1
      };

      // 更新內存緩存
      this.memoryCache = cacheData;

      // 保存到持久化存儲
      const cacheKey = this.getCacheKey();
      await kvStoreService.setValue(cacheKey, JSON.stringify(cacheData));

      logService.debug('租戶數據已緩存', {
        count: data.length,
        cacheKey,
        timestamp: now
      });
    } catch (error) {
      logService.error('保存租戶緩存數據失敗', { error });
    }
  }

  /**
   * 清除緩存
   */
  public async clearTenantCache(): Promise<void> {
    try {
      // 清除內存緩存
      this.memoryCache = null;

      // 清除持久化緩存
      const cacheKey = this.getCacheKey();
      await kvStoreService.deleteValue(cacheKey);

      logService.info('租戶緩存已清除');
    } catch (error) {
      logService.error('清除租戶緩存失敗', { error });
    }
  }

  /**
   * 檢查是否需要強制刷新
   */
  private shouldForceRefresh(cached: TenantCacheData): boolean {
    const now = Date.now();
    const age = now - cached.timestamp;
    return age > this.cacheConfig.forceRefreshInterval;
  }

  /**
   * 從 API 獲取租戶關聯列表（原始方法）
   */
  private async fetchTenantRelationListFromApi(): Promise<RelationTenantVO[] | null> {
    try {
      logService.info('從 API 獲取租戶關聯列表');

      const response = await AileApi.fetchTenantRelationList({});

      const tenantList: RelationTenantVO[] = response?.data || [];

      if (tenantList.length > 0) {
        const count = await this.saveTenantInfo(tenantList);
        logService.info('租戶資料已同步到數據庫', { count });
      }

      return tenantList;
    } catch (error) {
      logService.error('從 API 獲取租戶資料失敗', { error });
      return null;
    }
  }

  /**
   * 獲取租戶關聯列表（帶緩存）
   * @param options 選項
   * @returns 租戶列表
   */
  public async fetchTenantRelationList(options: {
    /** 是否強制刷新緩存 */
    forceRefresh?: boolean;
    /** 是否使用緩存 */
    useCache?: boolean;
  } = {}): Promise<RelationTenantVO[] | null> {
    const { forceRefresh = false, useCache = true } = options;

    // 如果有正在進行的請求，等待其完成（防抖）
    if (this.fetchPromise) {
      logService.debug('等待正在進行的租戶數據請求');
      return this.fetchPromise;
    }

    this.fetchPromise = this.doFetchTenantRelationList(forceRefresh, useCache);

    try {
      const result = await this.fetchPromise;
      return result;
    } finally {
      this.fetchPromise = null;
    }
  }

  /**
   * 實際執行獲取租戶關聯列表的邏輯
   */
  private async doFetchTenantRelationList(
    forceRefresh: boolean,
    useCache: boolean
  ): Promise<RelationTenantVO[] | null> {
    try {
      // 如果不使用緩存，直接從 API 獲取
      if (!useCache || forceRefresh) {
        logService.info('跳過緩存，直接從 API 獲取租戶數據', { forceRefresh, useCache });
        const apiData = await this.fetchTenantRelationListFromApi();
        if (apiData) {
          await this.setCachedData(apiData);
        }
        return apiData;
      }

      // 1. 檢查內存緩存
      if (this.memoryCache) {
        const now = Date.now();
        if (now - this.memoryCache.timestamp <= this.cacheConfig.ttl) {
          logService.debug('從內存緩存獲取租戶數據', {
            count: this.memoryCache.data.length,
            age: now - this.memoryCache.timestamp
          });
          return this.memoryCache.data;
        } else {
          // 內存緩存過期，清除
          this.memoryCache = null;
        }
      }

      // 2. 檢查持久化緩存
      const cachedData = await this.getCachedData();
      if (cachedData) {
        // 更新內存緩存
        this.memoryCache = cachedData;

        // 檢查是否需要後台刷新
        if (this.shouldForceRefresh(cachedData)) {
          logService.info('緩存數據較舊，啟動後台刷新');
          // 後台異步刷新，不等待結果
          this.fetchTenantRelationListFromApi()
            .then(apiData => {
              if (apiData) {
                this.setCachedData(apiData);
              }
            })
            .catch(error => {
              logService.error('後台刷新租戶數據失敗', { error });
            });
        }

        logService.debug('從持久化緩存獲取租戶數據', {
          count: cachedData.data.length,
          age: Date.now() - cachedData.timestamp
        });
        return cachedData.data;
      }

      // 3. 緩存未命中，從 API 獲取
      logService.info('緩存未命中，從 API 獲取租戶數據');
      const apiData = await this.fetchTenantRelationListFromApi();
      if (apiData) {
        await this.setCachedData(apiData);
      }
      return apiData;

    } catch (error) {
      logService.error('獲取租戶關聯列表失敗', { error });

      // 如果 API 失敗，嘗試返回過期的緩存數據
      if (this.memoryCache) {
        logService.warn('API 失敗，返回過期的內存緩存數據');
        return this.memoryCache.data;
      }

      const staleCache = await this.getCachedData();
      if (staleCache) {
        logService.warn('API 失敗，返回過期的持久化緩存數據');
        return staleCache.data;
      }

      return null;
    }
  }

  /**
   * 檢查租戶是否存在於數據庫
   * @param id 租戶 ID
   * @returns 是否存在
   */
  public async hasTenantInDb(id: string): Promise<boolean> {
    if (!id) {
      return false;
    }
    try {
      // 使用 tenantDao 檢查租戶是否存在
      return await tenantDao.hasTenant(id);
    } catch (error) {
      logService.error('檢查租戶是否存在失敗', { error: error as Error, id });
      return false;
    }
  }
  /*判斷tenants中租戶是否有自己的租戶*/
  public async hasOwnerTenant(tenants:RelationTenantVO[]): Promise<boolean> {
    if (!Array.isArray(tenants) || tenants.length === 0) {
      return false;
    }
    const accountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
    if (!accountId) {
      return false;
    }
    const hasOwnTenant = tenants.some(t => t.accountId === accountId);
    return hasOwnTenant;
  }

  /**
   * 根據租戶陣列查詢 lastTenantId，優先 isLastTenant，否則第一個
   * @param tenants 租戶陣列
   * @returns lastTenantId 或 undefined
   * todo 是否需要查询类型为tenant.type === 'Person'
   */
  public getLastTenantId(tenants:RelationTenantVO[]): string | undefined {
    if (!Array.isArray(tenants) || tenants.length === 0) return undefined;
    const lastTenant = tenants.find(t => t.isLastTenant);
    if (lastTenant?.id) return lastTenant.id;
    return tenants[0]?.id;
  }

  public getCurrentTenantId(): string | null {
    return getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
  }

  public getCurrentBossServiceNumberId(): string | null {
    return getLocalStorage<string | null>(ConstantUtil.BOSS_SERVICENUMBERID, null);
  }

 
  /**
   * 執行 API 調用以應用租戶令牌
   * 該方法僅負責與 API 通信，不處理狀態更新或數據同步
   * @param tenantId 租戶ID
   * @returns 原始 API 響應結果
   */
  public async applyTokenRaw(tenantId: string): Promise<{
    status: number;
    success: boolean;
    data?: {
      user?: LoginUser;
      tenantInfo?: RelationTenantVO;
      bossServiceNumberId?: string;
    };
    msg: string;
    code: string;
    timeCost?: number;
  }> {
    if (!tenantId) {
      logService.warn('嘗試切換租戶但未提供有效的租戶ID');
      return {
        status: -1,
        success: false,
        msg: '未提供有效的租戶ID',
        code: '-1'
      };
    }

    try {
      logService.info('開始調用租戶Token API', { tenantId });
      const startTime = Date.now();

      // 準備請求參數
      const params = { tenantId };

      // 調用 API
      const response = await AileApi.applyToken(params);

      const timeCost = Date.now() - startTime;
      
      if (response?.success) {
        logService.info('租戶Token API調用成功', { 
          tenantId,
          timeCost
        });
      } else {
        logService.warn('租戶Token API調用失敗', {
          tenantId,
          status: response?.status,
          msg: response?.msg,
          code: response?.code,
          timeCost
        });
      }

      return {
        ...response,
        timeCost
      };
    } catch (error) {
      logService.error('調用租戶Token API時發生錯誤', { error: error as Error, tenantId });
      return {
        status: -1,
        success: false,
        msg: (error as Error).message || '切換租戶失敗',
        code: '-1'
      };
    }
  }

  /**
   * 創建邀請碼
   * @param type 邀請類型，例如 "JoinServiceNumber"
   * @param serviceNumberId 服務號ID
   * @returns 邀請碼創建結果
   */
  public async createInvitation(params: {
    type: string;
    serviceNumberId: string;
    tenantId: string;
  }): Promise<{
    status: number;
    success: boolean;
    data?: {
      code: string;
      type: string;
      text: string;
    };
    msg: string;
    code: string;
    timeCost?: number;
  }> {
    if (!params.type || !params.serviceNumberId || !params.tenantId) {
      logService.warn('創建邀請碼參數不完整', { params });
      return {
        status: -1,
        success: false,
        msg: '參數不完整',
        code: 'INVALID_PARAMS'
      };
    }

    try {
      logService.info('開始創建邀請碼', { params });
      const startTime = Date.now();

      // 準備請求參數
      const requestData = {
        type: params.type,
        serviceNumberId: params.serviceNumberId,
        tenantId: params.tenantId
      };

      // 調用 API
      const response = await AileApi.createTenantInvitation(requestData);

      const timeCost = Date.now() - startTime;

      if (response?.success) {
        logService.info('創建邀請碼成功', {
          params,
          inviteCode: response.data?.code,
          timeCost
        });
      } else {
        logService.warn('創建邀請碼失敗', {
          params,
          status: response?.status,
          msg: response?.msg,
          code: response?.code,
          timeCost
        });
      }

      return {
        ...response,
        timeCost
      };
    } catch (error) {
      logService.error('調用創建邀請碼API時發生錯誤', { error: error as Error, params });
      return {
        status: -1,
        success: false,
        msg: (error as Error).message || '創建邀請碼失敗',
        code: 'API_ERROR'
      };
    }
  }

  /**
   * 觸發邀請碼加入新團隊
   * @param code 邀請碼
   * @returns 觸發邀請結果
   */
  public async triggerInvitation(code: string): Promise<{
    status: number;
    success: boolean;
    data?: boolean;
    msg: string;
    code: string;
    timeCost?: number;
  }> {
    if (!code) {
      logService.warn('觸發邀請碼參數不完整', { inviteCode: code });
      return {
        status: -1,
        success: false,
        msg: '邀請碼不能為空',
        code: 'INVALID_PARAMS'
      };
    }

    try {
      logService.info('開始觸發邀請碼', { inviteCode: code });
      const startTime = Date.now();

      // 準備請求參數
      const requestData = {
        code: code
      };

      // 調用 API
      const response = await AileApi.triggerTenantInvitation(requestData);

      const timeCost = Date.now() - startTime;

      if (response?.success) {
        logService.info('觸發邀請碼成功', {
          inviteCode: code,
          result: response.data,
          timeCost
        });
      } else {
        logService.warn('觸發邀請碼失敗', {
          inviteCode: code,
          status: response?.status,
          msg: response?.msg,
          responseCode: response?.code,
          timeCost
        });
      }

      return {
        ...response,
        timeCost
      };
    } catch (error) {
      logService.error('調用觸發邀請碼API時發生錯誤', { error: error as Error, inviteCode: code });
      return {
        status: -1,
        success: false,
        msg: (error as Error).message || '觸發邀請碼失敗',
        code: 'API_ERROR'
      };
    }
  }

  /**
   * 切換租戶 (Legacy方法，推薦使用Redux Thunk: applyTenantToken)
   * @deprecated 請改用 Redux 中的 applyTenantToken
   * @param tenantId 租戶ID
   * @returns 切換結果，包含用戶信息、租戶信息等
   */
  public async applyToken(tenantId: string): Promise<{
    status: number;
    success: boolean;
    data?: {
      user?: LoginUser;
      tenantInfo?:RelationTenantVO;
      bossServiceNumberId?: string;
    };
    msg: string;
    code: string;
    timeCost?: number;
  }> {
    try {
      // 調用底層API
      const response = await this.applyTokenRaw(tenantId);
      
      // 移除Redux處理，只記錄結果
      logService.info('租戶Token申請完成', {
        tenantId,
        success: response.success,
        msg: response.msg
      });
      
      return response;
    } catch (error) {
      logService.error('切換租戶時發生錯誤', { error: error as Error, tenantId });
      return {
        status: -1,
        success: false,
        msg: (error as Error).message || '切換租戶失敗',
        code: '-1'
      };
    }
  }
}

// 導出單例實例
export default new TenantService(); 