import { httpService, RequestResult } from '../../system/httpService';
import { API_ENDPOINTS } from '../../api/aileApi';
import { logService } from '../../system/logService';
import { Observable, Subject, map } from 'rxjs';
import { CryptoUtil } from '../../../utils/cryptoUtil';

/**
 * 附件服務 - 頭像下載
 * @description 提供下載頭像附件的 API 封裝
 */
class AttachmentService {
  /**
   * 下載頭像附件（Apifox 標準）
   * @param avatarId 頭像ID
   * @param size 尺寸
   * @param config 可選 axios 配置
   * @param cancelSignal 可選取消信號
   * @returns Observable<RequestResult<Blob>>
   */
  public fetchAvatarAttachment(
    params: { avatarId: string; size: string },
    config: Record<string, any> = {},
    cancelSignal?: Subject<void>
  ): Observable<RequestResult<Blob>> {
    logService.info('發起頭像附件下載請求', { params });
    // Apifox 標準：POST application/json，響應為 Blob
    return httpService.downloadFile(
      API_ENDPOINTS.ATTACHMENT.AVATAR_DOWNLOAD,
      params,
      config,
      cancelSignal
    );
  }

  /**
   * 獲取頭像 Base64 數據（Apifox 標準）
   * @param avatarId 頭像ID
   * @param size 尺寸
   * @param config 可選 axios 配置
   * @returns Observable<string>
   */
  public fetchAvatarBase64(
    params: { avatarId: string; size: string },
    config: Record<string, any> = {}
  ): Observable<string> {
    logService.info('發起頭像 Base64 數據請求', { params });
    // 使用 API_BASE_ATTACHMENT_AVATAR_BASE64 端點
    return httpService.post<string>(
      API_ENDPOINTS.ATTACHMENT.AVATAR_BASE64,
      params,
      config,
      true // 加密請求
    ).pipe(
      map(response => {
        try {
          // 嘗試解密 API 回應
          logService.debug('頭像 Base64 數據回應解密前', { responseType: typeof response });
          const decryptedResponse = CryptoUtil.decryptApiResponse<{ status: number; success: boolean; data: string; msg: string; code: string }>(response);
          
          // 檢查請求是否成功
          if (!decryptedResponse.success) {
            logService.warn('頭像 Base64 API 請求失敗', { 
              status: decryptedResponse.status,
              code: decryptedResponse.code,
              message: decryptedResponse.msg
            });
            throw new Error(`頭像 Base64 API 請求失敗: ${decryptedResponse.msg}`);
          }
          
          logService.info('頭像 Base64 數據回應解密成功，請求成功');
          
          // 返回解密後的 base64 字串
          return decryptedResponse.data;
        } catch (decryptError) {
          // 解密失敗，嘗試直接使用回應數據
          logService.warn('頭像 Base64 數據回應解密失敗，嘗試直接使用回應數據', { error: decryptError });
          
          // 如果是原始格式的回應，也嘗試檢查成功狀態
          if (typeof response === 'object' && response !== null) {
            const rawResponse = response as any;
            if (rawResponse.success === false) {
              logService.warn('頭像 Base64 API 請求失敗（原始格式）', { 
                status: rawResponse.status,
                code: rawResponse.code,
                message: rawResponse.msg
              });
              throw new Error(`頭像 Base64 API 請求失敗: ${rawResponse.msg || '未知錯誤'}`);
            }
            
            // 如果有 data 字段，返回它
            if (rawResponse.data) {
              return rawResponse.data;
            }
          }
          
          return response as any;
        }
      })
    );
  }
}

export const attachmentService = new AttachmentService();
export default attachmentService; 