[2025-07-22 00:02:26.837 INFO] [Http-1734277:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 00:03:25.649 INFO] [Http-1734287:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 00:21:03.816 INFO] [Http-1734470:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 00:21:03.817 INFO] [Http-1734470:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 00:21:03.905 INFO] [Http-1734470:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":0,"data":{"tenants":[{"app_Key":"12c17894-2126-4c8e-8088-3622754f6e27","tenant_Id":"*********-0001","password":"1qaz@WSX","available":true,"remark":"拾憶窗藝"},{"app_Key":"3a9da49c-ea69-4882-a6bd-1cefc33b3603","tenant_Id":"*********-0001","password":"1qaz@WSX","available":true,"remark":"AILE租戶"},{"app_Key":"bc73b969-c809-4c0b-b7f2-d60446d4a1ec","tenant_Id":"R36051994-01","password":"1qaz@WSX","available":true,"remark":"林森67租户使用"},{"app_Key":"1e5656a5-6a3e-4d24-be63-84e4901a89fb","tenant_Id":"R202308010001","password":"1qaz@WSX","available":true,"remark":"奈德"},{"app_Key":"1c1de095-810b-41ae-aac2-b7c1cc132c3b","tenant_Id":"R********01","password":"1qaz@WSX","available":true,"remark":"程曦"},{"app_Key":"6ff65d5f-aef7-4d59-b7df-e0adda6858c4","tenant_Id":"R202503250001","password":"1qaz@WSX","available":true,"remark":"馬可艾思"},{"app_Key":"5aead62b-6faa-4f4f-b53f-256f230d135e","tenant_Id":"R202308290001","password":"1qaz@WSX","available":true,"remark":"uupon租戶"},{"app_Key":"03919349-253c-45d7-b025-fd12c48c6b1c","tenant_Id":"R202212120001","password":"1qaz@WSX","available":true,"remark":"Ai3"},{"app_Key":"fe295655-fe64-46ab-887d-eaf5b9725408","tenant_Id":"R20211216-0001","password":"1qaz@WSX","available":true,"remark":"Aile團隊"}]},"message":"success"}
[2025-07-22 00:21:03.952 INFO] [Thread-5105729] com.chainsea.aileai.robot.util.RedisSubConfig - subscribe redis channel (R********01:import)
[2025-07-22 00:47:31.338 INFO] [Http-1734986:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 00:54:38.748 INFO] [Http-1735054:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 01:09:05.551 INFO] [Http-1735190:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:YGrnzEoSfoZRWRzgW73EHaHm5fIjxBX/5LY5bStr0VY=
[2025-07-22 01:09:05.551 INFO] [Http-1735190:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 01:09:05.551 INFO] [Http-1735190:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"U+kOOGuplfrhRBDseG1TBE0Du57r3CHkqi/a3I2qX9Ew1/D+2qbAr/rkguGmSOST"}}
[2025-07-22 01:09:05.551 INFO] [Http-1735190:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 01:09:07.922 INFO] [Http-1735194:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 01:09:07.922 INFO] [Http-1735194:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 01:09:08.007 INFO] [Http-1735194:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":0,"data":{"tenants":[{"app_Key":"6ff65d5f-aef7-4d59-b7df-e0adda6858c4","tenant_Id":"R202503250001","password":"1qaz@WSX","available":true,"remark":"馬可艾思"},{"app_Key":"5aead62b-6faa-4f4f-b53f-256f230d135e","tenant_Id":"R202308290001","password":"1qaz@WSX","available":true,"remark":"uupon租戶"},{"app_Key":"03919349-253c-45d7-b025-fd12c48c6b1c","tenant_Id":"R202212120001","password":"1qaz@WSX","available":true,"remark":"Ai3"},{"app_Key":"fe295655-fe64-46ab-887d-eaf5b9725408","tenant_Id":"R20211216-0001","password":"1qaz@WSX","available":true,"remark":"Aile團隊"},{"app_Key":"1c1de095-810b-41ae-aac2-b7c1cc132c3b","tenant_Id":"R********01","password":"1qaz@WSX","available":true,"remark":"程曦"},{"app_Key":"3a9da49c-ea69-4882-a6bd-1cefc33b3603","tenant_Id":"*********-0001","password":"1qaz@WSX","available":true,"remark":"AILE租戶"},{"app_Key":"bc73b969-c809-4c0b-b7f2-d60446d4a1ec","tenant_Id":"R36051994-01","password":"1qaz@WSX","available":true,"remark":"林森67租户使用"},{"app_Key":"1e5656a5-6a3e-4d24-be63-84e4901a89fb","tenant_Id":"R202308010001","password":"1qaz@WSX","available":true,"remark":"奈德"},{"app_Key":"12c17894-2126-4c8e-8088-3622754f6e27","tenant_Id":"*********-0001","password":"1qaz@WSX","available":true,"remark":"拾憶窗藝"}]},"message":"success"}
[2025-07-22 01:09:08.082 INFO] [Thread-5108416] com.chainsea.aileai.robot.util.RedisSubConfig - subscribe redis channel (R********01:import)
[2025-07-22 02:18:08.055 INFO] [Http-1735863:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:uDOAr0yHPtXqhzwIDcaYphfrnNfYJv6TLaYwkY0ozV8=
[2025-07-22 02:18:08.055 INFO] [Http-1735863:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 02:18:08.055 INFO] [Http-1735863:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"DVLYdIhtnDlDSZH5nXCctYTzNdXMAd+d/jY0fHwM4XA9UaDSkMNRz/pYaPERMMRu"}}
[2025-07-22 02:18:08.055 INFO] [Http-1735863:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 02:18:10.904 INFO] [Http-1735869:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 02:18:10.904 INFO] [Http-1735869:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 02:18:10.990 INFO] [Http-1735869:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":0,"data":{"tenants":[{"app_Key":"5aead62b-6faa-4f4f-b53f-256f230d135e","tenant_Id":"R202308290001","password":"1qaz@WSX","available":true,"remark":"uupon租戶"},{"app_Key":"03919349-253c-45d7-b025-fd12c48c6b1c","tenant_Id":"R202212120001","password":"1qaz@WSX","available":true,"remark":"Ai3"},{"app_Key":"fe295655-fe64-46ab-887d-eaf5b9725408","tenant_Id":"R20211216-0001","password":"1qaz@WSX","available":true,"remark":"Aile團隊"},{"app_Key":"1c1de095-810b-41ae-aac2-b7c1cc132c3b","tenant_Id":"R********01","password":"1qaz@WSX","available":true,"remark":"程曦"},{"app_Key":"6ff65d5f-aef7-4d59-b7df-e0adda6858c4","tenant_Id":"R202503250001","password":"1qaz@WSX","available":true,"remark":"馬可艾思"},{"app_Key":"bc73b969-c809-4c0b-b7f2-d60446d4a1ec","tenant_Id":"R36051994-01","password":"1qaz@WSX","available":true,"remark":"林森67租户使用"},{"app_Key":"1e5656a5-6a3e-4d24-be63-84e4901a89fb","tenant_Id":"R202308010001","password":"1qaz@WSX","available":true,"remark":"奈德"},{"app_Key":"12c17894-2126-4c8e-8088-3622754f6e27","tenant_Id":"*********-0001","password":"1qaz@WSX","available":true,"remark":"拾憶窗藝"},{"app_Key":"3a9da49c-ea69-4882-a6bd-1cefc33b3603","tenant_Id":"*********-0001","password":"1qaz@WSX","available":true,"remark":"AILE租戶"}]},"message":"success"}
[2025-07-22 02:18:11.069 INFO] [Thread-5111644] com.chainsea.aileai.robot.util.RedisSubConfig - subscribe redis channel (R********01:import)
[2025-07-22 02:30:00.735 INFO] [Timer-97081:com.chainsea.ecp.handler.timer.IncomingInfoRoutine] com.chainsea.ecp.aile.util.AileUtil - signature:pjNOqVvDaaXP2EnYdisdo9Jmenz4LLbd4wCLQ/lPOjs=
[2025-07-22 02:30:00.735 INFO] [Timer-97081:com.chainsea.ecp.handler.timer.IncomingInfoRoutine] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 02:30:00.735 INFO] [Timer-97081:com.chainsea.ecp.handler.timer.IncomingInfoRoutine] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"IncomingInfo","pageSize":500,"startTime":1753027200000,"tenantCode":"********-01","endTime":1753113600000}}
[2025-07-22 02:30:00.735 INFO] [Timer-97081:com.chainsea.ecp.handler.timer.IncomingInfoRoutine] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 02:30:00.951 INFO] [Timer-97085:com.chainsea.ecp.handler.timer.AdvertisementRoutine] com.chainsea.ecp.aile.util.AileUtil - signature:t+quNr7Dk2xo0K7FJVjMb/CYdBdffZowJt5LZ218rYQ=
[2025-07-22 02:30:00.951 INFO] [Timer-97085:com.chainsea.ecp.handler.timer.AdvertisementRoutine] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 02:30:00.951 INFO] [Timer-97085:com.chainsea.ecp.handler.timer.AdvertisementRoutine] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"Referral","pageSize":500,"startTime":1753027200000,"tenantCode":"********-01","endTime":1753113600000}}
[2025-07-22 02:30:00.951 INFO] [Timer-97085:com.chainsea.ecp.handler.timer.AdvertisementRoutine] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 03:29:53.808 INFO] [Http-1736565:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:YGrnzEoSfoZRWRzgW73EHaHm5fIjxBX/5LY5bStr0VY=
[2025-07-22 03:29:53.808 INFO] [Http-1736565:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 03:29:53.808 INFO] [Http-1736565:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"U+kOOGuplfrhRBDseG1TBE0Du57r3CHkqi/a3I2qX9Ew1/D+2qbAr/rkguGmSOST"}}
[2025-07-22 03:29:53.808 INFO] [Http-1736565:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 03:29:56.312 INFO] [Http-1736569:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 03:29:56.312 INFO] [Http-1736569:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 03:29:56.402 INFO] [Http-1736569:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 04:02:37.899 INFO] [Http-1736912:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:02:37.899 INFO] [Http-1736911:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:02:42.837 INFO] [Http-1736914:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:03:32.015 INFO] [Http-1736923:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:07:38.161 INFO] [Http-1736961:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:07:54.148 INFO] [Http-1736965:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:07:54.151 INFO] [Http-1736966:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 04:12:54.556 INFO] [Http-1737013:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 05:01:25.851 INFO] [Http-1737469:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 05:01:25.868 INFO] [Http-1737470:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 06:02:27.930 INFO] [Http-1738039:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:D1Nxu0mRrcg5C6lMCN1sz70ESwlqhR51WJYMvNXjxOQ=
[2025-07-22 06:02:27.930 INFO] [Http-1738039:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 06:02:27.930 INFO] [Http-1738039:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"WwA24hM9OJ4HyVBexVqcJ/HDjYveS6ZCNGt2oiOYbqqcrL3oqnBxUaES4DEBM3U1"}}
[2025-07-22 06:02:27.930 INFO] [Http-1738039:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 06:02:30.274 INFO] [Http-1738046:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 06:02:30.274 INFO] [Http-1738046:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 06:02:30.357 INFO] [Http-1738046:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 06:36:49.504 INFO] [Http-1738412:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:uDOAr0yHPtXqhzwIDcaYphfrnNfYJv6TLaYwkY0ozV8=
[2025-07-22 06:36:49.505 INFO] [Http-1738412:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 06:36:49.505 INFO] [Http-1738412:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"DVLYdIhtnDlDSZH5nXCctYTzNdXMAd+d/jY0fHwM4XA9UaDSkMNRz/pYaPERMMRu"}}
[2025-07-22 06:36:49.505 INFO] [Http-1738412:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 06:36:52.195 INFO] [Http-1738418:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 06:36:52.195 INFO] [Http-1738418:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 06:36:52.281 INFO] [Http-1738418:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 06:52:37.681 INFO] [Http-1738606:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:jZ2Z1j950C8z+aEuy84oFPrW3jJtkiEtretYtXkInR0=
[2025-07-22 06:52:37.681 INFO] [Http-1738606:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 06:52:37.681 INFO] [Http-1738606:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"zTk/r3fVoIs/rjFgYup8/kwwTGPgy+1YwzxW/ipUIxSe5S/2LenotC4XxUR/kuWk"}}
[2025-07-22 06:52:37.681 INFO] [Http-1738606:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 06:52:39.929 INFO] [Http-1738611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 06:52:39.929 INFO] [Http-1738611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 06:52:40.011 INFO] [Http-1738611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 07:44:01.783 INFO] [Http-1739142:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 07:44:01.783 INFO] [Http-1739142:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 07:44:01.877 INFO] [Http-1739142:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 07:49:49.569 INFO] [Http-1739237:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:ETm95I3ZbquY8a5l7zZ8BR6pGnU6wIwCukx0KypJHvs=
[2025-07-22 07:49:49.569 INFO] [Http-1739237:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 07:49:49.569 INFO] [Http-1739237:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"User","pageSize":500,"customData":{"openId":"67173d1d0738cee4467f626f"},"tenantCode":"********-01","type":"employee"}}
[2025-07-22 07:49:49.569 INFO] [Http-1739237:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 07:56:06.988 INFO] [Http-1739308:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 07:56:06.988 INFO] [Http-1739308:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 07:56:07.071 INFO] [Http-1739308:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 07:56:34.707 INFO] [Http-1739356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 07:56:34.707 INFO] [Http-1739356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 07:56:34.799 INFO] [Http-1739356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 07:56:36.269 INFO] [Http-1739383:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 07:56:36.269 INFO] [Http-1739383:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 07:56:36.349 INFO] [Http-1739383:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 07:57:27.252 INFO] [Thread-5129100] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@7410e0df, recipient = 謝嘉謙 (高級軟體設計工程師), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 07:57:27.278 INFO] [Thread-5129104] com.chainsea.ecp.aile.util.AileUtil - signature:VseES3Jks+fsz++wRlhlxnVimmI0PdzNvSdi0fAWjH8=
[2025-07-22 07:57:27.278 INFO] [Thread-5129104] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 07:57:27.278 INFO] [Thread-5129104] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"軟協專案ECP無滿意度資料","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%**********-e71e-11ee-bdac-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-e5e8-27f7-3802-6fa1697facd4%22%2C%22FAccountId%22%3A%22ffffff18-e5e8-3e6d-4802-6fa1697facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E8%AC%9D%E5%98%89%E8%AC%99%22%2C%22FName%22%3A%22%E8%AC%9D%E5%98%89%E8%AC%99%20%28%E9%AB%98%E7%B4%9A%E8%BB%9F%E9%AB%94%E8%A8%AD%E8%A8%88%E5%B7%A5%E7%A8%8B%E5%B8%AB%29%22%7D%2C%22unitCode%22%3A%22Ecp.Task%22%2C%22entityId%22%3A%22ffffff19-82b3-c8a2-4002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.Task.View.page%22%7D"}]}}],"from":{},"to":{"code":"662076b9d33861ae20c422d1","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 07:57:27.278 INFO] [Thread-5129104] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 07:57:28.796 INFO] [Thread-5129112] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@1d776f63, recipient = 謝嘉謙 (高級軟體設計工程師), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 07:57:28.813 INFO] [Thread-5129116] com.chainsea.ecp.aile.util.AileUtil - signature:+EEJ96avImAnw9bN99STgPeoG0IjwjFKEgiVLxk5mcc=
[2025-07-22 07:57:28.813 INFO] [Thread-5129116] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 07:57:28.813 INFO] [Thread-5129116] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"回覆: 1988賑災基金會_報表中心及通話紀錄調整及新增報表及功能","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%**********-e71e-11ee-bdac-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-e5e8-27f7-3802-6fa1697facd4%22%2C%22FAccountId%22%3A%22ffffff18-e5e8-3e6d-4802-6fa1697facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E8%AC%9D%E5%98%89%E8%AC%99%22%2C%22FName%22%3A%22%E8%AC%9D%E5%98%89%E8%AC%99%20%28%E9%AB%98%E7%B4%9A%E8%BB%9F%E9%AB%94%E8%A8%AD%E8%A8%88%E5%B7%A5%E7%A8%8B%E5%B8%AB%29%22%7D%2C%22unitCode%22%3A%22Ecp.Task%22%2C%22entityId%22%3A%22ffffff19-81b3-90a6-0002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.Task.View.page%22%7D"}]}}],"from":{},"to":{"code":"662076b9d33861ae20c422d1","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 07:57:28.813 INFO] [Thread-5129116] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 07:57:30.425 INFO] [Thread-5129124] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@2e6494f8, recipient = 謝絮如 (軟體設計工程師), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 07:57:30.438 INFO] [Thread-5129129] com.chainsea.ecp.aile.util.AileUtil - signature:4M35P42q/+y2gFVmjgXPEkNbAy0BHMZhOpCQUp9qFZ8=
[2025-07-22 07:57:30.438 INFO] [Thread-5129129] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 07:57:30.438 INFO] [Thread-5129129] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"<服請>農業部專案通話紀錄中專案細項項目需加入通話紀錄的報表中","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%2208a483c5-3072-11f0-96c5-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-6ccc-c4c2-0006-1707cb7facd4%22%2C%22FAccountId%22%3A%22ffffff19-6ccc-b8e0-6806-1707cb7facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E8%AC%9D%E7%B5%AE%E5%A6%82%22%2C%22FName%22%3A%22%E8%AC%9D%E7%B5%AE%E5%A6%82%20%28%E8%BB%9F%E9%AB%94%E8%A8%AD%E8%A8%88%E5%B7%A5%E7%A8%8B%E5%B8%AB%29%22%7D%2C%22unitCode%22%3A%22Ecp.Task%22%2C%22entityId%22%3A%22ffffff19-81ba-e632-6002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.Task.View.page%22%7D"}]}}],"from":{},"to":{"code":"682a97f107381cc533631758","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 07:57:30.438 INFO] [Thread-5129129] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 07:58:03.722 INFO] [Http-1739514:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:+WXfN2h++WjzIVIlO7sHdMjMN8Tzk1pP42zueUP7gc0=
[2025-07-22 07:58:03.722 INFO] [Http-1739514:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 07:58:03.722 INFO] [Http-1739514:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"X3XnO+wK3ZhzAhWT6MfgqH8P0iO7DLyQrlLfqKq/vJ+4u1tUSe8z1mSwa+UUgDUb"}}
[2025-07-22 07:58:03.722 INFO] [Http-1739514:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 07:58:06.922 INFO] [Http-1739523:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 07:58:06.922 INFO] [Http-1739523:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 07:58:07.007 INFO] [Http-1739523:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 07:59:37.525 INFO] [Http-1739647:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 07:59:37.525 INFO] [Http-1739647:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 07:59:37.617 INFO] [Http-1739647:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:00:09.557 INFO] [Http-1739696:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:00:09.557 INFO] [Http-1739696:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:00:09.682 INFO] [Http-1739696:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:06:33.217 INFO] [Http-1739820:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:06:33.217 INFO] [Http-1739820:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:06:33.303 INFO] [Http-1739820:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:06:42.379 INFO] [Http-1739862:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:06:42.379 INFO] [Http-1739862:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:06:42.470 INFO] [Http-1739862:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:08:41.546 INFO] [Http-1739924:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:EXOZIy8pIe63iCFivOFKX5HJwRbr3BoURmHaiM+WGB8=
[2025-07-22 08:08:41.546 INFO] [Http-1739924:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:08:41.546 INFO] [Http-1739924:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"P2C82gfTabOSMPRZfPavJYKRu+pUJjn74zSxkWG1U2eUiugD3ysG/RKk3JyhQSKY"}}
[2025-07-22 08:08:41.546 INFO] [Http-1739924:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:08:43.454 INFO] [Http-1739929:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:08:43.454 INFO] [Http-1739929:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:08:43.539 INFO] [Http-1739929:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:09:31.660 INFO] [Http-1739953:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:5J3lL9PG7BRgY7zVLaxj8Ir0iHYCn+0tV7gPNVVGPS4=
[2025-07-22 08:09:31.660 INFO] [Http-1739953:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:09:31.660 INFO] [Http-1739953:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"X3iT0OGAj9jIKQwgWbK3qg8PjK9Tsnv/Z/wZBMs7FzjjDfeTIAK+P/IO+Ep17hxs"}}
[2025-07-22 08:09:31.660 INFO] [Http-1739953:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:09:34.501 INFO] [Http-1739957:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:09:34.501 INFO] [Http-1739957:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:09:34.585 INFO] [Http-1739957:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:12:18.466 INFO] [Http-1740020:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:8jIGAK3KVctW6xkivGhvkVWIGLtXuDShXSFskwbmSNk=
[2025-07-22 08:12:18.466 INFO] [Http-1740020:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:12:18.466 INFO] [Http-1740020:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"qHaB3aXo+8/sifkAfuAThp2kNHI63R1M+CXphLX7RKKwAk74gK+YC654a0wWa4c6"}}
[2025-07-22 08:12:18.466 INFO] [Http-1740020:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:12:21.032 INFO] [Http-1740026:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:12:21.032 INFO] [Http-1740026:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:12:21.116 INFO] [Http-1740026:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:18:49.684 INFO] [Http-1740144:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:18:49.684 INFO] [Http-1740144:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:18:49.775 INFO] [Http-1740144:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:19:19.728 INFO] [Http-1740196:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:19:19.728 INFO] [Http-1740196:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:19:19.812 INFO] [Http-1740196:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:19:36.404 INFO] [Http-1740242:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:19:36.404 INFO] [Http-1740242:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:19:36.493 INFO] [Http-1740242:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:24:31.472 INFO] [Http-1740337:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:BACkzmfnhzMQQc9nL9AeyQuKEUZQzVOFyNxUcegGUc4=
[2025-07-22 08:24:31.472 INFO] [Http-1740337:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:24:31.472 INFO] [Http-1740337:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"d8wX7FpyfoMjiRCEBzCImalumchE2Fn8WuQ9KV7CO2YwSeXU99B36qfZ5G015CRS"}}
[2025-07-22 08:24:31.472 INFO] [Http-1740337:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:24:33.985 INFO] [Http-1740343:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:24:33.985 INFO] [Http-1740343:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:24:34.069 INFO] [Http-1740343:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:24:50.780 INFO] [Http-1740389:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:24:50.780 INFO] [Http-1740389:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:24:50.862 INFO] [Http-1740389:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:27:43.975 INFO] [Http-1740473:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:27:43.975 INFO] [Http-1740473:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:27:44.065 INFO] [Http-1740473:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:30:24.822 INFO] [Http-1740554:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:30:24.822 INFO] [Http-1740554:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:30:24.913 INFO] [Http-1740554:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:30:38.741 INFO] [Http-1740606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:30:38.741 INFO] [Http-1740606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:30:38.834 INFO] [Http-1740606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:30:42.542 INFO] [Http-1740620:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:+np8MVy6hrROtjOCxfM3xlB0lboijrnjW8uRJVHN4kQ=
[2025-07-22 08:30:42.542 INFO] [Http-1740620:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:30:42.542 INFO] [Http-1740620:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"O1e4eC/8S5ZBeLenBvfRpdmTuZdGqxEHMkbtnHdonN1TallNLp5EI8bQvl4tMbBv"}}
[2025-07-22 08:30:42.542 INFO] [Http-1740620:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:30:47.499 INFO] [Http-1740642:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:30:47.499 INFO] [Http-1740642:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:30:47.589 INFO] [Http-1740642:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:31:53.665 INFO] [Http-1740701:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 08:33:46.903 INFO] [Http-1740722:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:r0XqE1ooFrtI0vYtU2AZgsjWUeeeYUFf0rPLM7l0KJc=
[2025-07-22 08:33:46.903 INFO] [Http-1740722:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:33:46.903 INFO] [Http-1740722:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"4Q4xYxS+inF0k5m+H2iz8v+UIZHZWw9+Ic3gGZ2knL4qgQ71/zRY6Xc5Sea2kHAj"}}
[2025-07-22 08:33:46.903 INFO] [Http-1740722:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:33:49.988 INFO] [Http-1740728:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:33:49.988 INFO] [Http-1740728:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:33:50.071 INFO] [Http-1740728:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:35:16.428 INFO] [Http-1740789:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:35:16.428 INFO] [Http-1740789:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:35:16.523 INFO] [Http-1740789:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:36:03.848 INFO] [Http-1740837:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:36:03.848 INFO] [Http-1740837:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:36:03.940 INFO] [Http-1740837:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:36:19.523 INFO] [Http-1740889:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:36:19.523 INFO] [Http-1740889:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:36:19.614 INFO] [Http-1740889:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:37:02.033 INFO] [Http-1740945:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:37:02.033 INFO] [Http-1740945:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:37:02.116 INFO] [Http-1740945:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:37:16.024 INFO] [Http-1740993:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:37:16.024 INFO] [Http-1740993:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:37:16.114 INFO] [Http-1740993:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:37:24.226 INFO] [Http-1741032:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:KqJxp40Uyvp/VYRhfD3rZ/Nj78TFmyZPt1fzk/34tQY=
[2025-07-22 08:37:24.226 INFO] [Http-1741032:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:37:24.226 INFO] [Http-1741032:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"d0XShXQ2Pg784agkx0Vi9LzIdV+hnxc2sVTkTQIfXUhIbNTD8QPu9H6e3Swunj0G"}}
[2025-07-22 08:37:24.226 INFO] [Http-1741032:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:37:26.986 INFO] [Http-1741040:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:37:26.986 INFO] [Http-1741040:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:37:27.068 INFO] [Http-1741040:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:37:49.459 INFO] [Http-1741103:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:37:49.459 INFO] [Http-1741103:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:37:49.541 INFO] [Http-1741103:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:38:44.706 INFO] [Http-1741171:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:RxsS/fFBRAUhMXV5Q+eA6dQs9+GD46W6PFHxMSNXXMw=
[2025-07-22 08:38:44.706 INFO] [Http-1741171:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:38:44.706 INFO] [Http-1741171:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"GlCbCur2nJIvII4fQ4Gd85rHpID74L921cxretzXna+0OKgF4ua5RL47QnPOA+Tg"}}
[2025-07-22 08:38:44.706 INFO] [Http-1741171:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:38:48.300 INFO] [Http-1741179:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:38:48.300 INFO] [Http-1741179:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:38:48.385 INFO] [Http-1741179:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:41:07.690 INFO] [Http-1741269:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:41:07.690 INFO] [Http-1741269:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:41:07.774 INFO] [Http-1741269:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:41:26.422 INFO] [Http-1741321:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:41:26.422 INFO] [Http-1741321:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:41:26.511 INFO] [Http-1741321:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:42:14.879 INFO] [Http-1741374:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:NySJUCMpgaPDay4vEusDr+lTOr9Skeq8HlEgcZHY9iY=
[2025-07-22 08:42:14.879 INFO] [Http-1741374:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:42:14.879 INFO] [Http-1741374:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"QPiL+8J5CHmFMe+hFtDg0kn/nNjAm6clBZZhJ7jm9EVki/SepY1LuoWxmlBsP2L9"}}
[2025-07-22 08:42:14.879 INFO] [Http-1741374:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:44:19.142 INFO] [Http-1741420:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:44:19.142 INFO] [Http-1741420:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:44:19.228 INFO] [Http-1741420:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:45:04.218 INFO] [Http-1741471:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:45:04.218 INFO] [Http-1741471:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:45:04.310 INFO] [Http-1741471:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:45:48.973 INFO] [Http-1741530:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:45:48.973 INFO] [Http-1741530:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:45:49.058 INFO] [Http-1741530:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:46:21.991 INFO] [Http-1741580:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:0KyMCL/eQh9wILSZgMPh+eVIS5UjMluxOZY9lQpINjs=
[2025-07-22 08:46:21.991 INFO] [Http-1741580:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:46:21.991 INFO] [Http-1741580:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"gd6RrdnwqF3h+Usf4Nf2jl1VowhXQKBRS6Pb9EU6WEuK5U+nqbaie5JitED9M6jV"}}
[2025-07-22 08:46:21.991 INFO] [Http-1741580:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:46:24.590 INFO] [Http-1741586:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:46:24.590 INFO] [Http-1741586:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:46:24.673 INFO] [Http-1741586:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:46:34.047 INFO] [Http-1741641:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:46:34.047 INFO] [Http-1741641:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:46:34.137 INFO] [Http-1741641:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:46:48.082 INFO] [Http-1741703:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:46:48.082 INFO] [Http-1741703:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:46:48.166 INFO] [Http-1741703:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:47:25.508 INFO] [Http-1741773:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:V8ytPucZrlPp1FNVva6pMjlfE25bCve8E7w+8lHP6iI=
[2025-07-22 08:47:25.508 INFO] [Http-1741773:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:47:25.508 INFO] [Http-1741773:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"atGO3Jo3rhssJ2j4yaxQKJukNi24mEehbHH7IKr0plrutIyEOaVEVcCOAASkmxlA"}}
[2025-07-22 08:47:25.508 INFO] [Http-1741773:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:47:30.585 INFO] [Http-1741781:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:47:30.585 INFO] [Http-1741781:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:47:30.667 INFO] [Http-1741781:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:48:16.563 INFO] [Http-1741877:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:48:16.564 INFO] [Http-1741877:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:48:16.655 INFO] [Http-1741877:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:48:26.774 INFO] [Thread-5134649] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@55d8fe6c, recipient = 解大毅, notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:48:26.792 INFO] [Thread-5134652] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@55d8fe6c, recipient = 解大毅 (助理), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:48:26.815 INFO] [Thread-5134656] com.chainsea.ecp.aile.util.AileUtil - signature:dzaFDck+tUKDFTxLqwPcPdWuYJhsoo6o+Qu1kh2yAuE=
[2025-07-22 08:48:26.815 INFO] [Thread-5134656] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:48:26.815 INFO] [Thread-5134656] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"解大毅，補休8.0小時。2025-07-12 10:00~2025-07-12 18:00","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%2293c8fd9f-8771-11ef-aa2d-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-2793-688f-2005-85414f7facd4%22%2C%22FAccountId%22%3A%22ffffff19-2793-9af2-2005-85414f7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E8%A7%A3%E5%A4%A7%E6%AF%85%22%2C%22FName%22%3A%22%E8%A7%A3%E5%A4%A7%E6%AF%85%20%28%E5%8A%A9%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-7ed1-af8d-3002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"670c768d0738cee44677a5f1","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:48:26.815 INFO] [Thread-5134656] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:48:27.304 INFO] [Thread-5134649] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@55d8fe6c, recipient = 曹瑋玲 (業務企劃經理/部門主管), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:48:27.322 INFO] [Thread-5134662] com.chainsea.ecp.aile.util.AileUtil - signature:6VBtkFrEd0LhVCn+7FXQlXqjup0Vx16QjVqre72pado=
[2025-07-22 08:48:27.322 INFO] [Thread-5134662] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:48:27.322 INFO] [Thread-5134662] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"解大毅，補休8.0小時。2025-07-12 10:00~2025-07-12 18:00","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d8bfdfcc-0ac0-11ea-9186-0a79a042dc0a%22%2C%22FEntityId%22%3A%227b655da4-c5d9-0440-57a8-ffffff155b42%22%2C%22FAccountId%22%3A%22d8bafc19-0ac0-11ea-9186-0a79a042dc0a%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E6%9B%B9%E7%91%8B%E7%8E%B2%22%2C%22FName%22%3A%22%E6%9B%B9%E7%91%8B%E7%8E%B2%20%28%E6%A5%AD%E5%8B%99%E4%BC%81%E5%8A%83%E7%B6%93%E7%90%86%2F%E9%83%A8%E9%96%80%E4%B8%BB%E7%AE%A1%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-7ed1-af8d-3002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6218488c073847ba610c8422","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:48:27.322 INFO] [Thread-5134662] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:48:46.240 INFO] [Http-1741941:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:48:46.240 INFO] [Http-1741941:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:48:46.324 INFO] [Http-1741941:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:48:54.086 INFO] [Http-1742005:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:48:54.086 INFO] [Http-1742005:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:48:54.176 INFO] [Http-1742005:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:49:03.443 INFO] [Http-1742061:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:49:03.443 INFO] [Http-1742061:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:49:03.532 INFO] [Http-1742061:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:49:05.491 INFO] [Thread-5134790] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = null, recipient = 王思鈴 (產品行銷經理), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:49:05.502 INFO] [Thread-5134796] com.chainsea.ecp.aile.util.AileUtil - signature:ElDAxE0Mv28FZOTX0R2BeaduuI8L7nUtN4M2v3yUBDA=
[2025-07-22 08:49:05.502 INFO] [Thread-5134796] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:49:05.502 INFO] [Thread-5134796] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"陳詩涵，補休1.0小時。2025-07-07 19:00~2025-07-07 20:00","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%224c2e3c66-c73b-11ef-aa2d-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-41b4-49f9-0005-e0a4577facd4%22%2C%22FAccountId%22%3A%22ffffff19-41b4-295f-3805-e0a4577facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E7%8E%8B%E6%80%9D%E9%88%B4%22%2C%22FName%22%3A%22%E7%8E%8B%E6%80%9D%E9%88%B4%20%28%E7%94%A2%E5%93%81%E8%A1%8C%E9%8A%B7%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-7f1f-f660-7802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"677b65ff07386c2c25edcbed","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:49:05.502 INFO] [Thread-5134796] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:49:05.515 INFO] [Thread-5134793] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@8dbe692, recipient = 陳詩涵 (行銷企劃專員), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:49:05.528 INFO] [Thread-5134798] com.chainsea.ecp.aile.util.AileUtil - signature:wOjYm2wPI5anbj40WKUKFylB1zbdJzGm7V2OhFMDAMg=
[2025-07-22 08:49:05.528 INFO] [Thread-5134798] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:49:05.528 INFO] [Thread-5134798] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"陳詩涵，補休1.0小時。2025-07-07 19:00~2025-07-07 20:00","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22a7b6b8ba-be7c-11ee-b37e-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-d543-a014-7005-fcfc6d7facd4%22%2C%22FAccountId%22%3A%22ffffff18-d543-bca7-7805-fcfc6d7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E9%99%B3%E8%A9%A9%E6%B6%B5%22%2C%22FName%22%3A%22%E9%99%B3%E8%A9%A9%E6%B6%B5%20%28%E8%A1%8C%E9%8A%B7%E4%BC%81%E5%8A%83%E5%B0%88%E5%93%A1%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-7f1f-f660-7802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"65d31be7e4b0733e6229de3b","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:49:05.528 INFO] [Thread-5134798] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:49:06.029 INFO] [Thread-5134790] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@8dbe692, recipient = 陳詩涵, notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:49:30.006 INFO] [Http-1742135:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:49:30.006 INFO] [Http-1742135:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:49:30.099 INFO] [Http-1742135:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:49:45.788 INFO] [Http-1742184:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:49:45.788 INFO] [Http-1742184:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:49:45.880 INFO] [Http-1742184:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:50:31.803 INFO] [Http-1742244:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:50:31.803 INFO] [Http-1742244:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:50:31.892 INFO] [Http-1742244:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:50:44.370 INFO] [Http-1742283:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:50:44.370 INFO] [Http-1742283:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:50:44.460 INFO] [Http-1742283:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:51:00.926 INFO] [Http-1742334:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:51:00.926 INFO] [Http-1742334:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:51:01.019 INFO] [Http-1742334:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:52:01.256 INFO] [Http-1742410:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:52:01.256 INFO] [Http-1742410:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:52:01.346 INFO] [Http-1742410:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:52:18.097 INFO] [Thread-5135424] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@65d775ba, recipient = 柳宇賸, notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:52:18.101 INFO] [Thread-5135427] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@65d775ba, recipient = 柳宇賸 (人資副理), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:52:18.109 INFO] [Thread-5135434] com.chainsea.ecp.aile.util.AileUtil - signature:xpUbo0nu2hrSBJ2xNRQCFgUc9RivYQNsXH/Y0GzB9R4=
[2025-07-22 08:52:18.109 INFO] [Thread-5135434] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:52:18.109 INFO] [Thread-5135434] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"柳宇賸，補休1.0小時。2025-07-17 19:00~2025-07-17 20:00","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22ffffff17-8496-4e09-5003-ab0c94f0385e%22%2C%22FEntityId%22%3A%22ffffff17-83e0-34fb-1806-c85b93f0385e%22%2C%22FAccountId%22%3A%22d8bc32bf-0ac0-11ea-9186-0a79a042dc0a%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E6%9F%B3%E5%AE%87%E8%B3%B8%22%2C%22FName%22%3A%22%E6%9F%B3%E5%AE%87%E8%B3%B8%20%28%E4%BA%BA%E8%B3%87%E5%89%AF%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-81af-ef86-0002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6110a4b78f0876b0c24320ac","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:52:18.109 INFO] [Thread-5135434] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:52:18.624 INFO] [Thread-5135424] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@65d775ba, recipient = 蘇緯弘 (部門經理), notice = {FId=5da6c6f9-053d-4bb6-a554-bdce5df5e029, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您送出的流程“${process.FName}”已經完成。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您送出的流程已經完成。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流程名稱：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;完成時間：${process.FEndTime}。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp;&nbsp;&nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues=, FCardTitle=您送出的流程已經完成。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=false, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=流程結束通知, FShortMessageContent=您送出的流程“${process.FName}”已經完成。}
[2025-07-22 08:52:18.632 INFO] [Thread-5135441] com.chainsea.ecp.aile.util.AileUtil - signature:I32LJTb3RvfRwcVhYxsAu7SJuBuZxm1nxF+HOyFT9vs=
[2025-07-22 08:52:18.632 INFO] [Thread-5135441] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:52:18.632 INFO] [Thread-5135441] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"柳宇賸，補休1.0小時。2025-07-17 19:00~2025-07-17 20:00","type":"buttons","title":"您送出的流程已經完成。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22562669d1-50ac-11ef-bdac-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-1124-602f-6000-599a657facd4%22%2C%22FAccountId%22%3A%22ffffff19-1124-7703-3000-599a657facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E8%98%87%E7%B7%AF%E5%BC%98%22%2C%22FName%22%3A%22%E8%98%87%E7%B7%AF%E5%BC%98%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-81af-ef86-0002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"66b093c40738c977deaca244","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:52:18.632 INFO] [Thread-5135441] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:52:21.268 INFO] [Http-1742482:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:52:21.268 INFO] [Http-1742482:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:52:21.350 INFO] [Http-1742482:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:52:50.072 INFO] [Http-1742544:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:ipzd4Z0Nr8TDGfz2HfMy4it5/em2pkOdfBGTUo6wzok=
[2025-07-22 08:52:50.072 INFO] [Http-1742544:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:52:50.072 INFO] [Http-1742544:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"N1QyYT/MITw7AuyKt2JrfkME9TvSgTvQPKeC9Vy5M3quMaV8tRzkLyLgaBK+f3P4"}}
[2025-07-22 08:52:50.072 INFO] [Http-1742544:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:52:54.414 INFO] [Http-1742552:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:52:54.414 INFO] [Http-1742552:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:52:54.496 INFO] [Http-1742552:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:53:08.975 INFO] [Http-1742611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:53:08.975 INFO] [Http-1742611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:53:09.064 INFO] [Http-1742611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:53:35.281 INFO] [Http-1742670:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:53:35.281 INFO] [Http-1742670:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:53:35.373 INFO] [Http-1742670:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:53:45.641 INFO] [Http-1742721:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:53:45.642 INFO] [Http-1742721:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:53:45.732 INFO] [Http-1742721:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:53:52.591 INFO] [Http-1742762:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 08:54:37.833 INFO] [Http-1742810:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:54:37.833 INFO] [Http-1742810:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:54:37.916 INFO] [Http-1742810:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:54:50.990 INFO] [Http-1742868:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:54:50.990 INFO] [Http-1742868:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:54:51.071 INFO] [Http-1742868:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:55:11.426 INFO] [Http-1742942:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:55:11.426 INFO] [Http-1742942:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:55:11.511 INFO] [Http-1742942:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:55:13.583 INFO] [Thread-5136034] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@39ab9bf2, recipient = 陳靖潔, notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 08:55:31.061 INFO] [Http-1743033:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:55:31.061 INFO] [Http-1743033:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:55:31.154 INFO] [Http-1743033:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:55:31.672 INFO] [Http-1743044:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:55:31.672 INFO] [Http-1743044:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:55:31.755 INFO] [Http-1743044:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:55:35.714 INFO] [Http-1743124:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:55:35.714 INFO] [Http-1743124:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:55:35.795 INFO] [Http-1743124:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:55:36.601 INFO] [Http-1743128:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:55:36.601 INFO] [Http-1743128:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:55:36.682 INFO] [Http-1743128:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:55:37.932 INFO] [Thread-5136139] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@576ee2f7, recipient = 彭品誠 (商務長), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 08:55:37.945 INFO] [Thread-5136144] com.chainsea.ecp.aile.util.AileUtil - signature:hRSjLfRa34AiUT7+mX+UynsSfvM6U4IVoPC0Qpiig0M=
[2025-07-22 08:55:37.945 INFO] [Thread-5136144] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:55:37.945 INFO] [Thread-5136144] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"移工一站式(龍潭)費用","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%2298644a4e-3a76-11ef-bdac-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-080b-97f9-7807-0b2a987facd4%22%2C%22FAccountId%22%3A%22ffffff19-080b-a998-2007-0b2a987facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%BD%AD%E5%93%81%E8%AA%A0%22%2C%22FName%22%3A%22%E5%BD%AD%E5%93%81%E8%AA%A0%20%28%E5%95%86%E5%8B%99%E9%95%B7%29%22%7D%2C%22unitCode%22%3A%22Ecp.Expense%22%2C%22entityId%22%3A%22ffffff19-82bd-c7c6-3002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.Expense.View.page%22%7D"}]}}],"from":{},"to":{"code":"6694c48307384b3f9a5ceb0d","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:55:37.945 INFO] [Thread-5136144] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:55:57.755 INFO] [Http-1743227:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:55:57.755 INFO] [Http-1743227:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:55:57.846 INFO] [Http-1743227:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:56:03.982 INFO] [Http-1743274:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:56:03.982 INFO] [Http-1743274:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:56:04.073 INFO] [Http-1743274:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:56:25.350 INFO] [Http-1743335:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:56:25.350 INFO] [Http-1743335:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:56:25.440 INFO] [Http-1743335:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:56:31.674 INFO] [Http-1743405:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:56:31.674 INFO] [Http-1743405:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:56:31.756 INFO] [Http-1743405:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:56:58.371 INFO] [Http-1743463:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:yBeUh8JiFAmperkL3fAzSQfZWRgOebCbZ3zW3NAtok0=
[2025-07-22 08:56:58.371 INFO] [Http-1743463:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:56:58.371 INFO] [Http-1743463:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"IIGRuArbQyZ02Z3zghY+7lpweW32+awrlsG6mFKQOX6vJqiAWM9YbZO65LZnFhkm"}}
[2025-07-22 08:56:58.371 INFO] [Http-1743463:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:57:00.790 INFO] [Http-1743480:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:57:00.790 INFO] [Http-1743480:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:57:00.875 INFO] [Http-1743480:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:57:18.678 INFO] [Http-1743544:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:57:18.678 INFO] [Http-1743544:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:57:18.770 INFO] [Http-1743544:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:57:25.721 INFO] [Http-1743606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:57:25.721 INFO] [Http-1743606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:57:25.808 INFO] [Http-1743606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:57:53.851 INFO] [Http-1743676:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:57:53.851 INFO] [Http-1743676:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:57:53.942 INFO] [Http-1743676:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:57:56.544 INFO] [Http-1743695:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:57:56.544 INFO] [Http-1743695:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:57:56.627 INFO] [Http-1743695:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:58:06.886 INFO] [Http-1743763:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 08:59:04.499 INFO] [Http-1743796:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:04.499 INFO] [Http-1743796:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:04.591 INFO] [Http-1743796:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:09.560 INFO] [Http-1743815:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:upefX6Qer0kzmX1jma9RnlTVaFiiLGLKmWb4uiIxBA4=
[2025-07-22 08:59:09.560 INFO] [Http-1743815:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:59:09.560 INFO] [Http-1743815:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"4IXUOLrLjvDjYDZbx3jwyT7XBUXNfDGZhUh7NSqrnFrlGqe0x3+SNSujcOKf9c4W"}}
[2025-07-22 08:59:09.560 INFO] [Http-1743815:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:59:11.577 INFO] [Http-1743827:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:HfXE3oZhp/uZ069+dCjQtIl8WTmG1CwuTRQ2CZzImOY=
[2025-07-22 08:59:11.577 INFO] [Http-1743827:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:59:11.577 INFO] [Http-1743827:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"OAQpzqPRp5xY4EiL/RA6+mqwpLkygaNxxQ3BcCwrDCb2l7dScoFEvvi4E+n5LZAZ"}}
[2025-07-22 08:59:11.577 INFO] [Http-1743827:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:59:13.484 INFO] [Http-1743839:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:13.484 INFO] [Http-1743839:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:13.567 INFO] [Http-1743839:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:13.848 INFO] [Http-1743845:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:13.848 INFO] [Http-1743845:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:13.928 INFO] [Http-1743845:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:24.877 INFO] [Http-1743931:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:24.877 INFO] [Http-1743931:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:24.967 INFO] [Http-1743931:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:32.981 INFO] [Http-1743973:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:IlWcIz86g9IUwTZ3HYM4LCfREKvEDoyxYiSiggqiQMA=
[2025-07-22 08:59:32.981 INFO] [Http-1743973:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 08:59:32.981 INFO] [Http-1743973:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"1htD6NQzvo+7Jtg3HPNfo+Yy9fkoGYdM6IoHob6CCxVgY5GcvzDgvOBGuYLwAeoN"}}
[2025-07-22 08:59:32.981 INFO] [Http-1743973:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:59:35.436 INFO] [Http-1743982:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:35.436 INFO] [Http-1743982:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:35.521 INFO] [Http-1743982:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:37.060 INFO] [Thread-5137036] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@7cd0f6e9, recipient = 陳冠享 (課主管), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 08:59:37.071 INFO] [Thread-5137039] com.chainsea.ecp.aile.util.AileUtil - signature:YVb97xhn9A8h6KVS4YHls1ac4ULUP5HqQLZAvq4Px+Y=
[2025-07-22 08:59:37.071 INFO] [Thread-5137039] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 08:59:37.071 INFO] [Thread-5137039] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"陳彥中 (客服襄理)的申請單","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d8c0da39-0ac0-11ea-9186-0a79a042dc0a%22%2C%22FEntityId%22%3A%22dfbe04a9-bbe2-4c30-8f41-2ce94150b1fd%22%2C%22FAccountId%22%3A%22d8bbc976-0ac0-11ea-9186-0a79a042dc0a%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E9%99%B3%E5%86%A0%E4%BA%AB%22%2C%22FName%22%3A%22%E9%99%B3%E5%86%A0%E4%BA%AB%20%28%E8%AA%B2%E4%B8%BB%E7%AE%A1%29%22%7D%2C%22unitCode%22%3A%22EFF.SharedForm%22%2C%22entityId%22%3A%22ffffff19-82c4-aa55-0802-9991f07facd4%22%2C%22pageCode%22%3A%22EFF.SharedForm.Form.page%22%7D"}]}}],"from":{},"to":{"code":"621ef0d8073847ba610d6f16","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 08:59:37.071 INFO] [Thread-5137039] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 08:59:44.933 INFO] [Http-1744036:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:44.933 INFO] [Http-1744036:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:45.002 INFO] [Http-1744042:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:45.002 INFO] [Http-1744042:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:45.030 INFO] [Http-1744036:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:45.082 INFO] [Http-1744042:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:49.599 INFO] [Http-1744120:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:49.599 INFO] [Http-1744120:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:49.683 INFO] [Http-1744120:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 08:59:52.672 INFO] [Http-1744147:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 08:59:52.672 INFO] [Http-1744147:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 08:59:52.755 INFO] [Http-1744147:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:04.129 INFO] [Http-1744222:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:04.129 INFO] [Http-1744222:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:04.224 INFO] [Http-1744222:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:04.246 INFO] [Http-1744224:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:dQLFyBX/QyKJyNWm5zL2OIrVJ1jFq6CmCvrm29pdm/s=
[2025-07-22 09:00:04.246 INFO] [Http-1744224:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:00:04.246 INFO] [Http-1744224:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"bW8vlPvvc+L6Ts8lFcnIXpimRyj8MZsSfcnuO8bUk9QieJhXt1Jaej5YiQQ5bb3R"}}
[2025-07-22 09:00:04.246 INFO] [Http-1744224:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:00:09.372 INFO] [Http-1744279:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:09.372 INFO] [Http-1744279:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:09.455 INFO] [Http-1744279:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:21.247 INFO] [Http-1744370:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:21.247 INFO] [Http-1744370:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:21.338 INFO] [Http-1744370:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:31.664 INFO] [Http-1744463:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:31.664 INFO] [Http-1744463:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:31.749 INFO] [Http-1744463:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:49.624 INFO] [Http-1744596:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:49.624 INFO] [Http-1744596:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:49.706 INFO] [Http-1744596:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:53.202 INFO] [Http-1744648:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:MTOSFngRyruK+bwPj5Vty2Tbjw7qnyfJbQjJHGvtIL4=
[2025-07-22 09:00:53.202 INFO] [Http-1744648:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:00:53.202 INFO] [Http-1744648:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"bNKxKHIpXopt66UxyDK4pRCY6Bg4ND/ezLkiK6IDO3GY+fIaar3gYtB98RvRqu8f"}}
[2025-07-22 09:00:53.202 INFO] [Http-1744648:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:00:55.587 INFO] [Http-1744671:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:55.587 INFO] [Http-1744671:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:55.734 INFO] [Http-1744671:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:00:56.987 INFO] [Http-1744689:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:eddilmhEtdbsgSdZTHTzKSPSqx25g+S3mox6Gi31qmM=
[2025-07-22 09:00:56.987 INFO] [Http-1744689:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 09:00:56.987 INFO] [Http-1744689:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"User","pageSize":500,"customData":{"openId":"67c93bc70738f110c7085467"},"tenantCode":"********-01","type":"employee"}}
[2025-07-22 09:00:56.987 INFO] [Http-1744689:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:00:57.244 INFO] [Http-1744699:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:00:57.244 INFO] [Http-1744699:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:00:57.327 INFO] [Http-1744699:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:01:04.718 INFO] [Http-1744794:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:01:04.718 INFO] [Http-1744794:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:01:04.807 INFO] [Http-1744794:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:01:34.104 INFO] [Http-1744967:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:01:34.104 INFO] [Http-1744967:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:01:34.193 INFO] [Http-1744967:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:01:46.615 INFO] [Http-1745067:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:bvB/rzlxcs3BTMcVtAQI/z4/VyKGUgU6S+atTWyHX3Q=
[2025-07-22 09:01:46.615 INFO] [Http-1745067:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:01:46.615 INFO] [Http-1745067:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"q/qgVdxfP/u9lFVjr51KoQWbxQaYwmjkHtuKR6OB7ME3Yk+vW+hXZphPWwOR+lqI"}}
[2025-07-22 09:01:46.615 INFO] [Http-1745067:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:01:49.257 INFO] [Http-1745089:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:01:49.257 INFO] [Http-1745089:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:01:49.339 INFO] [Http-1745089:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:01:54.220 INFO] [Http-1745146:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:01:54.220 INFO] [Http-1745146:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:01:54.310 INFO] [Http-1745146:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:01:58.302 INFO] [Http-1745174:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:01:58.302 INFO] [Http-1745174:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:01:58.384 INFO] [Http-1745174:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:02:32.587 INFO] [Http-1745399:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:uI4G7whaqcx+RY8k1MeZMen7+EroGC1sUDT0Y1XrqSk=
[2025-07-22 09:02:32.587 INFO] [Http-1745399:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 09:02:32.587 INFO] [Http-1745399:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"User","pageSize":500,"customData":{"openId":"640fed908b38d4524e0a223a"},"tenantCode":"********-01","type":"employee"}}
[2025-07-22 09:02:32.587 INFO] [Http-1745399:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:02:53.619 INFO] [Http-1745487:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:RP3O0jiEjDP9JUX8+NDicjkxVsZkUbQs1yz3H88P1K0=
[2025-07-22 09:02:53.619 INFO] [Http-1745487:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 09:02:53.619 INFO] [Http-1745487:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"User","pageSize":500,"customData":{"openId":"628f15770738afa1363daccb"},"tenantCode":"********-01","type":"employee"}}
[2025-07-22 09:02:53.619 INFO] [Http-1745487:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:03:20.245 INFO] [Http-1745602:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:03:20.245 INFO] [Http-1745602:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:03:20.337 INFO] [Http-1745602:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:03:47.048 INFO] [Http-1745668:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:03:47.048 INFO] [Http-1745668:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:03:47.132 INFO] [Http-1745668:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:03:49.379 INFO] [Http-1745686:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:03:49.379 INFO] [Http-1745686:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:03:49.460 INFO] [Http-1745686:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:03:50.039 INFO] [Http-1745707:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:03:50.039 INFO] [Http-1745707:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:03:50.121 INFO] [Http-1745707:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:03:55.122 INFO] [Http-1745797:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:03:55.122 INFO] [Http-1745797:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:03:55.213 INFO] [Http-1745797:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:04:06.948 INFO] [Http-1745856:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:04:06.948 INFO] [Http-1745856:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:04:07.039 INFO] [Http-1745856:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:04:23.323 INFO] [Http-1745918:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:04:23.323 INFO] [Http-1745918:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:04:23.415 INFO] [Http-1745918:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:04:40.073 INFO] [Http-1745966:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:dD3s5XduBCfWJHaNgzdiDxqbx9fWo8pZurY9FO+fNGg=
[2025-07-22 09:04:40.073 INFO] [Http-1745966:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:04:40.073 INFO] [Http-1745966:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"B8rG+An3DY1HpicFrwvDN720xhDS+7211Xzd3yY10dbLEHcvzYemmAROaXu9wD/F"}}
[2025-07-22 09:04:40.073 INFO] [Http-1745966:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:04:43.127 INFO] [Http-1745979:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:04:43.127 INFO] [Http-1745979:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:04:43.211 INFO] [Http-1745979:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:05:04.541 INFO] [Http-1746037:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:05:04.541 INFO] [Http-1746037:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:05:04.632 INFO] [Http-1746037:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:05:07.969 INFO] [Http-1746062:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:RP3O0jiEjDP9JUX8+NDicjkxVsZkUbQs1yz3H88P1K0=
[2025-07-22 09:05:07.969 INFO] [Http-1746062:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 09:05:07.969 INFO] [Http-1746062:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"User","pageSize":500,"customData":{"openId":"628f15770738afa1363daccb"},"tenantCode":"********-01","type":"employee"}}
[2025-07-22 09:05:07.969 INFO] [Http-1746062:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:05:20.805 INFO] [Http-1746086:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:05:20.805 INFO] [Http-1746086:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:05:20.894 INFO] [Http-1746086:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:05:23.680 INFO] [Http-1746117:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - signature:WK0VSbhondBnmI2mToVqcLWFRXF8bgwP6ip8ci3jJZo=
[2025-07-22 09:05:23.680 INFO] [Http-1746117:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:05:23.680 INFO] [Http-1746117:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"kzB2JvhgT7jpgkl2D+/461iWVs60pyt2gpgSEJYo84Io+FIwBLH0Rdfy52Ty5B1r"}}
[2025-07-22 09:05:23.680 INFO] [Http-1746117:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:05:32.799 INFO] [Http-1746145:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - signature:WK0VSbhondBnmI2mToVqcLWFRXF8bgwP6ip8ci3jJZo=
[2025-07-22 09:05:32.799 INFO] [Http-1746145:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:05:32.799 INFO] [Http-1746145:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"kzB2JvhgT7jpgkl2D+/461iWVs60pyt2gpgSEJYo84Io+FIwBLH0Rdfy52Ty5B1r"}}
[2025-07-22 09:05:32.799 INFO] [Http-1746145:openapi/application/apply] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:06:46.829 INFO] [Thread-5139321] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@7fd87061, recipient = 王思鈴 (產品行銷經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:06:46.843 INFO] [Thread-5139324] com.chainsea.ecp.aile.util.AileUtil - signature:Pwh2asty+Phjm0XpISYx2dlthjy9YSzIG3z16qiStTc=
[2025-07-22 09:06:46.843 INFO] [Thread-5139324] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:06:46.843 INFO] [Thread-5139324] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"張琪甄的[預補上班卡], 預補的打卡日期為：2025/07/22 09:06:45","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%224c2e3c66-c73b-11ef-aa2d-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-41b4-49f9-0005-e0a4577facd4%22%2C%22FAccountId%22%3A%22ffffff19-41b4-295f-3805-e0a4577facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E7%8E%8B%E6%80%9D%E9%88%B4%22%2C%22FName%22%3A%22%E7%8E%8B%E6%80%9D%E9%88%B4%20%28%E7%94%A2%E5%93%81%E8%A1%8C%E9%8A%B7%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.CheckIn%22%2C%22entityId%22%3A%22ffffff19-82fa-b356-3002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.CheckIn.View.page%22%7D"}]}}],"from":{},"to":{"code":"677b65ff07386c2c25edcbed","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:06:46.843 INFO] [Thread-5139324] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:07:33.066 INFO] [Http-1746232:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:07:33.066 INFO] [Http-1746232:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:07:33.158 INFO] [Http-1746232:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:07:59.157 INFO] [Http-1746307:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:07:59.157 INFO] [Http-1746307:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:07:59.242 INFO] [Http-1746307:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:08:22.387 INFO] [Http-1746356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:08:22.387 INFO] [Http-1746356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:08:22.479 INFO] [Http-1746356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:09:08.632 INFO] [Http-1746426:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:q8aKumpWYIaOGLVQgTECtxiP3QGW7eK81HmvsRdvgn0=
[2025-07-22 09:09:08.632 INFO] [Http-1746426:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:09:08.632 INFO] [Http-1746426:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"ToJBLVqCTarCHtqIFCyQMcFpxoen1Ne9sAn5qOGMQ7mZCcrKVzFAZr5WAB5Rpro7"}}
[2025-07-22 09:09:08.632 INFO] [Http-1746426:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:09:12.446 INFO] [Http-1746436:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:09:12.446 INFO] [Http-1746436:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:09:12.530 INFO] [Http-1746436:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:09:20.663 INFO] [Http-1746480:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:09:20.663 INFO] [Http-1746480:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:09:20.753 INFO] [Http-1746480:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:09:46.032 INFO] [Http-1746538:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:09:46.032 INFO] [Http-1746538:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:09:46.125 INFO] [Http-1746538:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:09:57.074 INFO] [Http-1746576:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:QE6VNzohxwS1MVaZJT+YcbUiCTHsRmLL1bmCtBmm15I=
[2025-07-22 09:09:57.074 INFO] [Http-1746576:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:09:57.074 INFO] [Http-1746576:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"qnFnRccuCHB0rMlXfOl6zJkUeF5yKS2Pnc1a39uz3zf913tQ21knDjjBYS2AAO6A"}}
[2025-07-22 09:09:57.074 INFO] [Http-1746576:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:09:59.914 INFO] [Http-1746585:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:09:59.914 INFO] [Http-1746585:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:09:59.998 INFO] [Http-1746585:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:10:05.633 INFO] [Http-1746625:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:JHkM6QWbK7xX5AbtGKwY6/nD5I+fS82TWqLBxXzJidw=
[2025-07-22 09:10:05.633 INFO] [Http-1746625:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:10:05.633 INFO] [Http-1746625:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"meIIWB2e7hDtGGjl7F3yytQy4yCOBVMnu/gHyc0j2LVlQ2uykyQkE01l9x9QI/Uu"}}
[2025-07-22 09:10:05.633 INFO] [Http-1746625:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:10:18.462 INFO] [Http-1746650:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:10:18.462 INFO] [Http-1746650:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:10:18.552 INFO] [Http-1746650:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:10:56.268 INFO] [Http-1746748:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:10:56.268 INFO] [Http-1746748:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:10:56.352 INFO] [Http-1746748:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:10:57.349 INFO] [Http-1746762:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:10:57.349 INFO] [Http-1746762:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:10:57.430 INFO] [Http-1746762:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:11:14.055 INFO] [Http-1746847:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:11:14.055 INFO] [Http-1746847:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:11:14.151 INFO] [Http-1746847:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:11:17.625 INFO] [Http-1746898:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:jjVoNQ6rGJQMcOJGtSCj4xoijosLuyZvDF/R/MKCrSI=
[2025-07-22 09:11:17.625 INFO] [Http-1746898:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:11:17.625 INFO] [Http-1746898:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"1/7ExCShfSszg5wv/u5KGycbWAEAQ/NqQNuBwpe1TO6ov8sZvI3NU0pwtuTj27bX"}}
[2025-07-22 09:11:17.625 INFO] [Http-1746898:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:11:17.834 INFO] [Http-1746903:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:11:17.834 INFO] [Http-1746903:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:11:17.916 INFO] [Http-1746903:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:11:19.872 INFO] [Http-1746937:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:11:19.872 INFO] [Http-1746937:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:11:19.953 INFO] [Http-1746937:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:11:32.226 INFO] [Http-1747002:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:WycU1r3CVkQ+3r2ksg15g6iWDkA7fJYsHF6ABPl9tVM=
[2025-07-22 09:11:32.226 INFO] [Http-1747002:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:11:32.226 INFO] [Http-1747002:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"0/h/wfV5uO+uWg7oZR1W+h2PmaqWhVc3ZkOSXmjuICHpOed3Rjq9iimf3nt2Bqlb"}}
[2025-07-22 09:11:32.226 INFO] [Http-1747002:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:11:35.917 INFO] [Http-1747028:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:11:35.917 INFO] [Http-1747028:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:11:35.999 INFO] [Http-1747028:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:11:44.925 INFO] [Thread-5140759] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@34d11e44, recipient = 陳逸寧, notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:12:35.938 INFO] [Http-1747133:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:12:35.939 INFO] [Http-1747133:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:12:36.028 INFO] [Http-1747133:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:12:38.396 INFO] [Http-1747171:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:12:38.396 INFO] [Http-1747171:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:12:38.479 INFO] [Http-1747171:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:12:42.030 INFO] [Http-1747213:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:GiooDxaxq+hX1rb3nYxotBfXfjK8b4gayrBcJCSFgGU=
[2025-07-22 09:12:42.030 INFO] [Http-1747213:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:12:42.030 INFO] [Http-1747213:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"nw3HR+4iWOIKyNRrp1HsqJ/6cmWm3e03hoFNAbHmnQvUnSwvrug827mrRHYG4pfy"}}
[2025-07-22 09:12:42.030 INFO] [Http-1747213:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:12:44.241 INFO] [Http-1747219:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:12:44.241 INFO] [Http-1747219:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:12:44.323 INFO] [Http-1747219:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:12:53.926 INFO] [Http-1747264:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:12:53.926 INFO] [Http-1747264:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:12:54.018 INFO] [Http-1747264:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:12:55.610 INFO] [Http-1747287:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:12:55.610 INFO] [Http-1747287:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:12:55.694 INFO] [Http-1747287:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:13:07.767 INFO] [Http-1747357:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:13:07.767 INFO] [Http-1747357:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:13:07.856 INFO] [Http-1747357:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:13:13.253 INFO] [Http-1747395:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:51/kgYSwKAR83K22wTss3Pl0i/ozWLyWH9yNunoTuV0=
[2025-07-22 09:13:13.253 INFO] [Http-1747395:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/sync/oncesync
[2025-07-22 09:13:13.253 INFO] [Http-1747395:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"pageIndex":1,"dataType":"User","pageSize":500,"customData":{"openId":"622ebfe107388fa2d134c492"},"tenantCode":"********-01","type":"employee"}}
[2025-07-22 09:13:13.253 INFO] [Http-1747395:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:13:25.232 INFO] [Http-1747408:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:13:25.232 INFO] [Http-1747408:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:13:25.323 INFO] [Http-1747408:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:13:26.849 INFO] [Http-1747425:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:13:26.849 INFO] [Http-1747425:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:13:26.930 INFO] [Http-1747425:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:14:09.162 INFO] [Http-1747551:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:14:09.162 INFO] [Http-1747551:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:14:09.246 INFO] [Http-1747551:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:14:20.823 INFO] [Http-1747606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:14:20.823 INFO] [Http-1747606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:14:20.912 INFO] [Http-1747606:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:14:33.411 INFO] [Http-1747664:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:14:33.411 INFO] [Http-1747664:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:14:33.503 INFO] [Http-1747664:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:15:04.989 INFO] [Http-1747791:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:15:04.989 INFO] [Http-1747791:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:15:05.084 INFO] [Http-1747791:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:15:06.152 INFO] [Http-1747806:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:15:06.152 INFO] [Http-1747806:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:15:06.233 INFO] [Http-1747806:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:15:10.266 INFO] [Http-1747882:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:15:10.266 INFO] [Http-1747882:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:15:10.346 INFO] [Http-1747882:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:15:13.062 INFO] [Http-1747926:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:15:13.062 INFO] [Http-1747926:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:15:13.142 INFO] [Http-1747926:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:15:17.849 INFO] [Http-1747977:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:15:17.849 INFO] [Http-1747977:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:15:17.940 INFO] [Http-1747977:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:16:02.680 INFO] [Http-1748083:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:16:02.680 INFO] [Http-1748083:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:16:02.761 INFO] [Http-1748083:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:16:15.813 INFO] [Http-1748153:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:Fy5dFZxbE6VK3LLzfSeQqSvqNenBA1VezxuM0gN8D9I=
[2025-07-22 09:16:15.813 INFO] [Http-1748153:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:16:15.813 INFO] [Http-1748153:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"Fh/UMzSgNWTaHf7A3NMNw0lbEBtgBn5zj5Zi2iDLx9r6xWSY68WwR2Zi+AuvXq/r"}}
[2025-07-22 09:16:15.813 INFO] [Http-1748153:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:16:19.284 INFO] [Http-1748159:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:16:19.284 INFO] [Http-1748159:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:16:19.366 INFO] [Http-1748159:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:16:44.120 INFO] [Http-1748222:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 09:16:44.247 INFO] [Http-1748223:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 09:16:52.634 INFO] [Http-1748229:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 09:17:11.796 INFO] [Thread-5142359] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@4467e100, recipient = 彭品誠 (商務長), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:17:11.804 INFO] [Thread-5142363] com.chainsea.ecp.aile.util.AileUtil - signature:ERT9AK9ON7EyZ6diAxkcOopD+sgGK5OPvEoKMW4oDtE=
[2025-07-22 09:17:11.804 INFO] [Thread-5142363] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:17:11.804 INFO] [Thread-5142363] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"陳吟如，請假5.0小時，2025-07-22 09:00~2025-07-22 14:59","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%2298644a4e-3a76-11ef-bdac-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff19-080b-97f9-7807-0b2a987facd4%22%2C%22FAccountId%22%3A%22ffffff19-080b-a998-2007-0b2a987facd4%22%2C%22FDefault%22%3Afalse%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%BD%AD%E5%93%81%E8%AA%A0%22%2C%22FName%22%3A%22%E5%BD%AD%E5%93%81%E8%AA%A0%20%28%E5%95%86%E5%8B%99%E9%95%B7%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82fa-f99c-5802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6694c48307384b3f9a5ceb0d","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:17:11.804 INFO] [Thread-5142363] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:17:41.365 INFO] [Http-1748348:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:17:41.365 INFO] [Http-1748348:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:17:41.454 INFO] [Http-1748348:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:17:48.702 INFO] [Http-1748405:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:0un6+ZfUqa/AbFf4mzyYqYdu57GM2Lb57qVLXm0coM0=
[2025-07-22 09:17:48.702 INFO] [Http-1748405:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:17:48.702 INFO] [Http-1748405:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"l6G1iQoXnr10C8fvMTES6gEYguCqpcODLzf/MmEibaHWNKqG3TAjjACJzQoCuqh1"}}
[2025-07-22 09:17:48.702 INFO] [Http-1748405:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:17:53.027 INFO] [Http-1748424:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:17:53.027 INFO] [Http-1748424:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:17:53.111 INFO] [Http-1748424:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:17:55.118 INFO] [Http-1748444:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:17:55.118 INFO] [Http-1748444:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:17:55.145 INFO] [Http-1748449:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:17:55.145 INFO] [Http-1748449:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:17:55.209 INFO] [Http-1748444:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:17:55.225 INFO] [Http-1748449:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:18:36.762 INFO] [Http-1748611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:18:36.762 INFO] [Http-1748611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:18:36.854 INFO] [Http-1748611:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:18:49.049 INFO] [Http-1748659:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:18:49.049 INFO] [Http-1748659:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:18:49.133 INFO] [Http-1748659:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:18:56.560 INFO] [Http-1748714:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:18:56.560 INFO] [Http-1748714:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:18:56.648 INFO] [Http-1748714:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:18:57.483 INFO] [Http-1748729:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:18:57.483 INFO] [Http-1748729:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:18:57.566 INFO] [Http-1748729:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:19:12.602 INFO] [Http-1748825:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:19:12.602 INFO] [Http-1748825:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:19:12.694 INFO] [Http-1748825:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:19:48.237 INFO] [Http-1748922:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:19:48.237 INFO] [Http-1748922:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:19:48.322 INFO] [Http-1748922:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:19:58.713 INFO] [Http-1748974:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:19:58.713 INFO] [Http-1748974:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:19:58.795 INFO] [Http-1748974:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:20:11.455 INFO] [Http-1749041:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:20:11.455 INFO] [Http-1749041:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:20:11.545 INFO] [Http-1749041:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:20:26.088 INFO] [Http-1749111:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:20:26.088 INFO] [Http-1749111:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:20:26.185 INFO] [Http-1749111:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:20:46.791 INFO] [Thread-5143527] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@6b762f97, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:20:46.801 INFO] [Thread-5143531] com.chainsea.ecp.aile.util.AileUtil - signature:2r4Y8XJ7F5c4X411sIpCmoehRSQJsmyqLO6PtPfyT74=
[2025-07-22 09:20:46.801 INFO] [Thread-5143531] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:20:46.801 INFO] [Thread-5143531] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假3.0小時，2025-07-21 09:00~2025-07-21 12:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-8a60-3002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:20:46.801 INFO] [Thread-5143531] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:20:52.923 INFO] [Thread-5143587] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@6b8c34f2, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:20:52.932 INFO] [Thread-5143591] com.chainsea.ecp.aile.util.AileUtil - signature:TQnCYHtcu7e5P2NNd/0q5dDZdLL/ce/f5g6ZaOyWd5g=
[2025-07-22 09:20:52.932 INFO] [Thread-5143591] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:20:52.932 INFO] [Thread-5143591] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假1.0小時，2025-07-15 09:00~2025-07-15 10:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-805c-3002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:20:52.932 INFO] [Thread-5143591] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:00.878 INFO] [Thread-5143640] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@22e03cd1, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:00.886 INFO] [Thread-5143644] com.chainsea.ecp.aile.util.AileUtil - signature:CpenUn1TqPIz5ht0lbbieCsanEl9DpHpvmvugWj+7vI=
[2025-07-22 09:21:00.886 INFO] [Thread-5143644] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:00.886 INFO] [Thread-5143644] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假1.0小時，2025-07-14 09:00~2025-07-14 10:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-77af-1002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:00.886 INFO] [Thread-5143644] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:09.801 INFO] [Thread-5143694] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@********, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:09.819 INFO] [Thread-5143698] com.chainsea.ecp.aile.util.AileUtil - signature:psFk8whBM6rVhtDRs7lnoaRTcyi/CJdAjvv90HRluUE=
[2025-07-22 09:21:09.819 INFO] [Thread-5143698] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:09.819 INFO] [Thread-5143698] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假1.0小時，2025-07-08 09:00~2025-07-08 10:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-6297-4002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:09.819 INFO] [Thread-5143698] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:16.223 INFO] [Thread-5143740] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@20d57dfd, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:16.233 INFO] [Thread-5143745] com.chainsea.ecp.aile.util.AileUtil - signature:o+Z5VkW0XkncNBGRoOOwATSIhuaQZh3ScbzxWiOsaWs=
[2025-07-22 09:21:16.233 INFO] [Thread-5143745] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:16.233 INFO] [Thread-5143745] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假3.0小時，2025-07-07 09:00~2025-07-07 12:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-58e5-5802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:16.233 INFO] [Thread-5143745] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:22.332 INFO] [Thread-5143785] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@5998a2f6, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:22.347 INFO] [Thread-5143789] com.chainsea.ecp.aile.util.AileUtil - signature:zfwbgd0HE054UO6fAZhUbBQo2OE5MbBpFW/7VagZq1c=
[2025-07-22 09:21:22.347 INFO] [Thread-5143789] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:22.347 INFO] [Thread-5143789] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假3.0小時，2025-07-04 09:00~2025-07-04 12:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-4ec5-3802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:22.347 INFO] [Thread-5143789] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:26.449 INFO] [Http-1749341:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:OZT8h7HekSc/lT3BmseKda93va/7+spCn3PkwwDRZCg=
[2025-07-22 09:21:26.449 INFO] [Http-1749341:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:21:26.449 INFO] [Http-1749341:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"hwbftvMV6zUO0KTBhH8MeJfP/vIx7w34IF3wASLiIuTiYDmGci/BypLJyqFISuy8"}}
[2025-07-22 09:21:26.449 INFO] [Http-1749341:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:29.703 INFO] [Thread-5143825] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@6867ba44, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:29.713 INFO] [Thread-5143829] com.chainsea.ecp.aile.util.AileUtil - signature:W55W+PGoRguZ7MHibDEZbdO6CQjrpT4e5A7XXTWdcU4=
[2025-07-22 09:21:29.713 INFO] [Thread-5143829] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:29.713 INFO] [Thread-5143829] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假1.0小時，2025-07-03 09:00~2025-07-03 10:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-3ec5-1002-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:29.713 INFO] [Thread-5143829] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:30.724 INFO] [Http-1749356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:21:30.724 INFO] [Http-1749356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:21:30.806 INFO] [Http-1749356:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:21:34.598 INFO] [Http-1749414:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:21:34.598 INFO] [Http-1749414:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:21:34.682 INFO] [Http-1749414:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:21:36.355 INFO] [Thread-5143872] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@305d4eaa, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:36.366 INFO] [Thread-5143878] com.chainsea.ecp.aile.util.AileUtil - signature:gnmp8S6yRDHXD5sUHocuR42A+difvS6AXU32gdY/ILw=
[2025-07-22 09:21:36.366 INFO] [Thread-5143878] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:36.366 INFO] [Thread-5143878] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假1.0小時，2025-07-02 09:00~2025-07-02 10:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-2e1b-6802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:36.366 INFO] [Thread-5143878] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:42.597 INFO] [Thread-5143922] com.chainsea.ecp.handler.application.AileNoticeMedia - Send aile message: wfc = com.jeedsoft.quicksilver.message.type.WorkflowContext@2d6b71df, recipient = 呂家華 (部門經理), notice = {FId=4bac11b3-6779-4cdc-992d-fd843634f9f8, FEmailBcc=null, FCardType=buttons, FOrientation=Vertical, FSystemMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。, FMessageType=Template, FEmailContent=<p>${recipient.FName}：</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;您有新的待辦事項。</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;流　程：${process.FName}<br />
&nbsp;&nbsp;&nbsp;&nbsp;啟動者：${process.FUserId.FName}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;請盡快登錄系統處理。</p>

<p>&nbsp; &nbsp;${systemParameter.QsSystemUrl}</p>
, FSystemMessageFieldValues={FUnitId:${activity.FEntityUnitId}, FEntityId:${activity.FEntityId}, FTimeoutTime:${activity.FTimeoutTime}}, FCardTitle=您有新的待辦事項。, FText=${process.FName}, FEmailCc=null, FMedias=Email,SystemMessage,Aile, FSendEmail=true, FSenderType=System, FNoticeType=Inside, FSendSystemMessage=true, FImageId=null, FSendShortMessage=false, FReceiverType=Employee, FSubject=工作項通知, FShortMessageContent=您有新的待辦事項。流程：${process.FName}，啟動者：${process.FUserId.FName}。請及時登錄系統處理。}
[2025-07-22 09:21:42.606 INFO] [Thread-5143926] com.chainsea.ecp.aile.util.AileUtil - signature:Td3WJmf4ATyzs1W/sFAgWv1yVncPPca/jT1y9BC1InY=
[2025-07-22 09:21:42.606 INFO] [Thread-5143926] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/chat/message/template/send
[2025-07-22 09:21:42.606 INFO] [Thread-5143926] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"sender":{"code":"","type":"System"},"messages":[{"altText":"","type":"Template","content":{"orientation":"Vertical","imageUrl":"","text":"謝嘉謙，請假1.0小時，2025-07-01 09:00~2025-07-01 10:00","type":"buttons","title":"您有新的待辦事項。","actions":[{"label":"查看详情","type":"aiff","url":"https://econtact.chainsea.com.tw/ecp/Ecp.AileNotice.Redirect.page?param=%7B%22identity%22%3A%7B%22FId%22%3A%22d83005fe-d433-11ed-b2e8-0af4ffad7ac5%22%2C%22FEntityId%22%3A%22ffffff18-754d-2cef-0806-7b689b7facd4%22%2C%22FAccountId%22%3A%22ffffff18-754d-4809-3806-7b689b7facd4%22%2C%22FDefault%22%3Atrue%2C%22FIsMainDuty%22%3Atrue%2C%22FIdentityTypeId%24%22%3A%22%E8%81%B7%E5%8B%99%22%2C%22FIdentityTypeId%22%3A%22564cf69e-76d6-4baf-b584-6e04c2911dae%22%2C%22FAccountId%24%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%22%2C%22FName%22%3A%22%E5%91%82%E5%AE%B6%E8%8F%AF%20%28%E9%83%A8%E9%96%80%E7%B6%93%E7%90%86%29%22%7D%2C%22unitCode%22%3A%22Ecp.LeavePermit%22%2C%22entityId%22%3A%22ffffff19-82c6-14dd-0802-9991f07facd4%22%2C%22pageCode%22%3A%22Ecp.LeavePermit.View.page%22%7D"}]}}],"from":{},"to":{"code":"6433d3b78b38b0c6318fc86e","userType":"employee","type":"User"},"type":"Message"}}
[2025-07-22 09:21:42.606 INFO] [Thread-5143926] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:21:44.453 INFO] [Http-1749489:openapi/business/webhook] com.chainsea.ecp.aile.api.impl.AileApiImpl - request server ip:************
[2025-07-22 09:21:58.904 INFO] [Http-1749531:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:21:58.904 INFO] [Http-1749531:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:21:58.988 INFO] [Http-1749531:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:22:07.068 INFO] [Http-1749580:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:22:07.068 INFO] [Http-1749580:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:22:07.156 INFO] [Http-1749580:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:22:18.992 INFO] [Http-1749629:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:22:18.992 INFO] [Http-1749629:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:22:19.085 INFO] [Http-1749629:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:22:19.389 INFO] [Http-1749640:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:22:19.389 INFO] [Http-1749640:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:22:19.470 INFO] [Http-1749640:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:22:25.218 INFO] [Http-1749721:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:PFAp1VVG43l6Sl9H2viHMBPRboIi4NjDZ8mMZ4I6AsM=
[2025-07-22 09:22:25.218 INFO] [Http-1749721:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:22:25.218 INFO] [Http-1749721:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"ef3FbV8UK29DGMMQFZWLscgBjxDcFFCLpdifMRcgpWKh4ZWgPWCmF3fG1hW6cDwX"}}
[2025-07-22 09:22:25.218 INFO] [Http-1749721:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:22:27.995 INFO] [Http-1749726:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:22:27.995 INFO] [Http-1749726:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:22:28.081 INFO] [Http-1749726:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
[2025-07-22 09:23:16.437 INFO] [Http-1749829:Qs.ClusterNode.List.page] com.jeedsoft.quicksilver.registry.Registry - Create proxy: Qs.ClusterNode.Action
[2025-07-22 09:23:34.455 INFO] [Http-1749860:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - signature:aFimUTi0INUaEAiY9I+KFoY7FK65pr0IepDMic7al2k=
[2025-07-22 09:23:34.455 INFO] [Http-1749860:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery url:https://csce.qbicloud.com/ce/openapi/token/validate
[2025-07-22 09:23:34.455 INFO] [Http-1749860:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - HttpQuery args:{"tenantCode":"********-01","content":{"authToken":"li2Uva8tyEiTalK7Se9pQUwyJA2zgUEiVrx5Nrm9pS+YNfQdj6aEKvYd29eOW4qJ"}}
[2025-07-22 09:23:34.455 INFO] [Http-1749860:Ecp.Aile.login.data] com.chainsea.ecp.aile.util.AileUtil - secret:********
[2025-07-22 09:23:37.139 INFO] [Http-1749865:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery url:http://*************:8083/svc/tenants.svc/tenants/list
[2025-07-22 09:23:37.139 INFO] [Http-1749865:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery args:{}
[2025-07-22 09:23:37.223 INFO] [Http-1749865:AileAi.Robot.getMenuTabStrip.data] com.chainsea.aileai.util.RemoteApiUtil - HttpQuery result:{"code":-6,"message":"Failed to post content"}
