
import { render, screen, fireEvent } from '@testing-library/react';

// Mock all dependencies before importing the component
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

jest.mock('@/services/system/logService', () => ({
  logService: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}));

jest.mock('@/services/tenant/tenantService', () => ({
  applyToken: jest.fn()
}));

jest.mock('@/hooks/useTenantRelations', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    tenantRelations: [
      {
        id: 'tenant1',
        name: '我的團隊',
        accountId: 'account1',
        isLastTenant: true,
        avatarId: 'avatar1',
        unReadCount: 0
      },
      {
        id: 'tenant2',
        name: '其他團隊',
        accountId: 'account2',
        isLastTenant: false,
        avatarId: 'avatar2',
        unReadCount: 5
      }
    ],
    isLoading: false,
    isEmpty: false,
    isError: false,
    refreshTenantRelations: jest.fn()
  }))
}));

jest.mock('@/app/hooks/useTenantSwitcher', () => ({
  useTenantSwitcher: jest.fn(() => ({
    switchTenant: jest.fn()
  }))
}));

jest.mock('@/services/stateService', () => ({
  loginUser: jest.fn(() => ({
    id: 'user1',
    name: '測試用戶',
    accountId: 'account1',
    avatarId: 'avatar1',
    tenantId: 'tenant1'
  }))
}));

jest.mock('antd-mobile', () => ({
  DotLoading: ({ color }: { color: string }) => <div data-testid="dot-loading" style={{ color }}>Loading...</div>,
  Toast: {
    show: jest.fn()
  }
}));

jest.mock('../../assets/icons/sidebar/add-icon.svg', () => 'add-icon.svg');
jest.mock('../../assets/icons/sidebar/user-add-icon.svg', () => 'user-add-icon.svg');

// Mock AvatarImage component
jest.mock('@/components/common', () => ({
  AvatarImage: ({ name, alt }: { name: string; alt: string }) => (
    <div data-testid="avatar-image" data-name={name} data-alt={alt}>
      {name}
    </div>
  )
}));

// Now import the component
import Sidebar from './Sidebar';

describe('Sidebar 基本功能測試', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('應該正確渲染側邊欄', () => {
    render(<Sidebar isOpen={true} onClose={mockOnClose} />);
    
    expect(screen.getByText('測試用戶')).toBeInTheDocument();
    expect(screen.getByText('團隊')).toBeInTheDocument();
    expect(screen.getByText('加入其他團隊')).toBeInTheDocument();
  });

  test('應該顯示租戶列表', () => {
    render(<Sidebar isOpen={true} onClose={mockOnClose} />);
    
    expect(screen.getByText('我的團隊')).toBeInTheDocument();
    expect(screen.getByText('其他團隊')).toBeInTheDocument();
  });

  test('應該顯示用戶頭像', () => {
    render(<Sidebar isOpen={true} onClose={mockOnClose} />);
    
    const avatar = screen.getByTestId('avatar-image');
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveAttribute('data-name', '測試用戶');
  });

  test('點擊側邊欄背景應該關閉側邊欄', () => {
    render(<Sidebar isOpen={true} onClose={mockOnClose} />);
    
    const sidebar = screen.getByRole('generic');
    fireEvent.click(sidebar);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  test('當 isOpen 為 false 時側邊欄應該存在但不顯示', () => {
    render(<Sidebar isOpen={false} onClose={mockOnClose} />);
    
    const sidebar = screen.getByRole('generic');
    expect(sidebar).toBeInTheDocument();
    expect(sidebar).not.toHaveClass('open');
  });

  test('點擊加入團隊按鈕應該顯示加載狀態', () => {
    render(<Sidebar isOpen={true} onClose={mockOnClose} />);
    
    const joinButton = screen.getByText('加入其他團隊');
    fireEvent.click(joinButton);
    
    // 應該顯示加載狀態
    expect(screen.getByTestId('dot-loading')).toBeInTheDocument();
  });
}); 