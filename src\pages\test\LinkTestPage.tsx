import React, { useState } from 'react';
import { parseTextWithLinks, LinkPart } from '../../utils/linkUtil';

// URL 連結轉換組件
const LinkifiedText: React.FC<{ text: string }> = ({ text }) => {
  const parts = parseTextWithLinks(text);
  
  return (
    <div style={{ lineHeight: '1.5' }}>
      {parts.map((part: LinkPart, index: number) => {
        if (part.isUrl) {
          return (
            <a
              key={index}
              href={part.href}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: '#1677FF',
                textDecoration: 'underline',
                wordBreak: 'break-all'
              }}
              onClick={(e) => {
                // 防止事件冒泡到父元素
                e.stopPropagation();
              }}
            >
              {part.text}
            </a>
          );
        }
        // 普通文本
        return <span key={index}>{part.text}</span>;
      })}
    </div>
  );
};

const LinkTestPage: React.FC = () => {
  const [inputText, setInputText] = useState('');
  
  // 測試用例
  const testCases = [
    '請訪問 https://www.google.com 獲取更多信息',
    '查看我們的網站 www.example.com 了解詳情',
    '下載文件：ftp://files.example.com/download.zip',
    '多個連結：https://github.com 和 www.stackoverflow.com 都很有用',
    '連結後有標點符號：訪問 https://example.com, 然後查看 www.test.com!',
    '沒有連結的普通文本',
    'HTTP: https://example.com, FTP: ftp://files.com, WWW: www.test.com',
    '電子郵件不會被轉換：<EMAIL>',
    '混合內容：請訪問 https://www.google.com 搜索，或者去 www.bing.com 試試。'
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>URL 連結轉換測試頁面</h1>
      
      <div style={{ marginBottom: '30px' }}>
        <h2>自定義測試</h2>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="輸入包含 URL 的文本進行測試..."
          style={{
            width: '100%',
            height: '100px',
            padding: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
        <div style={{ 
          marginTop: '10px', 
          padding: '15px', 
          border: '1px solid #e0e0e0', 
          borderRadius: '4px',
          backgroundColor: '#f9f9f9',
          minHeight: '50px'
        }}>
          <strong>轉換結果：</strong>
          <div style={{ marginTop: '10px' }}>
            <LinkifiedText text={inputText} />
          </div>
        </div>
      </div>

      <div>
        <h2>預設測試用例</h2>
        {testCases.map((testCase, index) => (
          <div key={index} style={{ 
            marginBottom: '20px', 
            padding: '15px', 
            border: '1px solid #e0e0e0', 
            borderRadius: '4px',
            backgroundColor: '#f9f9f9'
          }}>
            <div style={{ marginBottom: '10px' }}>
              <strong>原文：</strong>
              <div style={{ 
                fontFamily: 'monospace', 
                backgroundColor: '#fff', 
                padding: '8px', 
                border: '1px solid #ddd',
                borderRadius: '3px',
                marginTop: '5px'
              }}>
                {testCase}
              </div>
            </div>
            <div>
              <strong>轉換結果：</strong>
              <div style={{ marginTop: '5px' }}>
                <LinkifiedText text={testCase} />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LinkTestPage;
